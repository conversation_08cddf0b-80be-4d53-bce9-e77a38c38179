{"name": "测试open facebook能正常执行", "status": "passed", "description": "open facebook", "steps": [{"name": "执行命令: open facebook", "status": "passed", "steps": [{"name": "执行命令: open facebook", "status": "passed", "start": 1756794631363, "stop": 1756794660582}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e89d6a63-812b-490e-8b1e-972ad716283c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "23cc6e58-9871-4743-ba45-c99da178c283-attachment.png", "type": "image/png"}], "start": 1756794660582, "stop": 1756794660804}], "start": 1756794631363, "stop": 1756794660804}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1756794660804, "stop": 1756794660806}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3b9d5a8a-7b60-4b70-b7b1-259e6c08a734-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "023a216d-12cb-4d51-b43d-66882309236a-attachment.png", "type": "image/png"}], "start": 1756794660806, "stop": 1756794661004}], "attachments": [{"name": "stdout", "source": "d28420f8-374e-4025-9b57-4e4639e010bc-attachment.txt", "type": "text/plain"}], "start": 1756794631363, "stop": 1756794661004, "uuid": "e197b60c-29a5-4878-9dee-a52c3d5c7ec4", "historyId": "4933b925ec694ecfcd17b3423ac28184", "testCaseId": "4933b925ec694ecfcd17b3423ac28184", "fullName": "testcases.test_ella.third_coupling.test_open_facebook.TestEllaCommandConcise#test_open_facebook", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_open_facebook"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_open_facebook"}]}