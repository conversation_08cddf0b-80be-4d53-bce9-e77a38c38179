{"name": "测试take a selfie能正常执行", "status": "passed", "description": "take a selfie", "steps": [{"name": "执行命令: take a selfie", "status": "passed", "steps": [{"name": "执行命令: take a selfie", "status": "passed", "start": 1756791948260, "stop": 1756791987551}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1f26abb3-ffcd-4982-9eec-6086b897b9b0-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2fb756d4-7593-4bed-be94-021649b5a64c-attachment.png", "type": "image/png"}], "start": 1756791987551, "stop": 1756791987781}], "start": 1756791948260, "stop": 1756791987781}, {"name": "验证应用已打开", "status": "passed", "start": 1756791987781, "stop": 1756791987781}, {"name": "验证文件存在", "status": "passed", "start": 1756791987781, "stop": 1756791987781}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "12992144-4735-4cc3-86c3-0698da61d7f3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ce8fd50b-7a6a-4f7f-9384-b4fe3ebe619a-attachment.png", "type": "image/png"}], "start": 1756791987781, "stop": 1756791988016}], "attachments": [{"name": "stdout", "source": "f48f2de8-2840-4a49-973a-389316fdcea4-attachment.txt", "type": "text/plain"}], "start": 1756791948260, "stop": 1756791988016, "uuid": "2a8a172c-bf6b-4745-9376-d355d9742bf3", "historyId": "6c46a38570672e3c21f37ef82690d639", "testCaseId": "6c46a38570672e3c21f37ef82690d639", "fullName": "testcases.test_ella.system_coupling.test_take_a_selfie.TestEllaTakeSelfie#test_take_a_selfie", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_take_a_selfie"}, {"name": "subSuite", "value": "TestEllaTakeSelfie"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_take_a_selfie"}]}