{"name": "测试check status updates on whatsapp能正常执行", "status": "passed", "description": "check status updates on whatsapp", "steps": [{"name": "执行命令: check status updates on whatsapp", "status": "passed", "steps": [{"name": "执行命令: check status updates on whatsapp", "status": "passed", "start": 1756784854708, "stop": 1756784878836}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2c369962-c9e8-4642-be5b-42c2adc4a728-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3cbaa8ae-91eb-4aa5-ab94-82825747a1f9-attachment.png", "type": "image/png"}], "start": 1756784878836, "stop": 1756784879043}], "start": 1756784854708, "stop": 1756784879043}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756784879043, "stop": 1756784879044}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b66f8679-5ea7-416f-9bfd-a1ab3a4ab61a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "20d820c2-dccf-4d6a-8aa1-3f176b74f59c-attachment.png", "type": "image/png"}], "start": 1756784879044, "stop": 1756784879274}], "attachments": [{"name": "stdout", "source": "8808008c-ca47-4fb8-9c3b-ad44a40ba4c8-attachment.txt", "type": "text/plain"}], "start": 1756784854708, "stop": 1756784879274, "uuid": "1479d9c3-41aa-4acc-bec2-4ebd7aa7591d", "historyId": "861f0c94cdad5a5d60cd9fc71e2429d6", "testCaseId": "861f0c94cdad5a5d60cd9fc71e2429d6", "fullName": "testcases.test_ella.dialogue.test_check_status_updates_on_whatsapp.TestEllaCheckStatusUpdatesWhatsapp#test_check_status_updates_on_whatsapp", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_check_status_updates_on_whatsapp"}, {"name": "subSuite", "value": "TestEllaCheckStatusUpdatesWhatsapp"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_check_status_updates_on_whatsapp"}]}