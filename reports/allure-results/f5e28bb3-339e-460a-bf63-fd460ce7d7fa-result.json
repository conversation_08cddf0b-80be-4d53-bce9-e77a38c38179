{"name": "测试switched to data mode能正常执行", "status": "passed", "description": "switched to data mode", "steps": [{"name": "执行命令: switched to data mode", "status": "passed", "steps": [{"name": "执行命令: switched to data mode", "status": "passed", "start": 1756791844457, "stop": 1756791865620}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b5e9c2cf-15b2-4303-b5a8-af63a3340f6b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "32b43f20-476d-4a45-8564-3504ed732d19-attachment.png", "type": "image/png"}], "start": 1756791865620, "stop": 1756791865811}], "start": 1756791844457, "stop": 1756791865811}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756791865811, "stop": 1756791865813}, {"name": "验证应用已打开", "status": "passed", "start": 1756791865813, "stop": 1756791865813}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b38eb8bd-a941-4140-98e1-3bf1b3b9222a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "80384388-d6ff-4d59-b3b2-e2f0aa727496-attachment.png", "type": "image/png"}], "start": 1756791865813, "stop": 1756791865995}], "attachments": [{"name": "stdout", "source": "a153b59e-8955-4dc2-8378-30354f98f7a3-attachment.txt", "type": "text/plain"}], "start": 1756791844457, "stop": 1756791865995, "uuid": "2d030045-86e6-4ff4-9a00-8037af076b5a", "historyId": "063471c2e7f2d00ecd08e780860e0cf2", "testCaseId": "063471c2e7f2d00ecd08e780860e0cf2", "fullName": "testcases.test_ella.system_coupling.test_switched_to_data_mode.TestEllaSwitchedDataMode#test_switched_to_data_mode", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switched_to_data_mode"}, {"name": "subSuite", "value": "TestEllaSwitchedDataMode"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switched_to_data_mode"}]}