{"name": "测试who is j k rowling能正常执行", "status": "passed", "description": "who is j k rowling", "steps": [{"name": "执行命令: who is j k rowling", "status": "passed", "steps": [{"name": "执行命令: who is j k rowling", "status": "passed", "start": 1756787843573, "stop": 1756787867047}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1764082f-d7e3-4acc-86b9-b1180f54db8f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1e3d30ca-30ab-4830-beb8-611c946618c9-attachment.png", "type": "image/png"}], "start": 1756787867047, "stop": 1756787867293}], "start": 1756787843573, "stop": 1756787867293}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756787867293, "stop": 1756787867295}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0ea0ce30-0205-4a2a-9e98-b870b527ba66-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b91ed70e-c627-49f0-9d9d-ff5cd862686a-attachment.png", "type": "image/png"}], "start": 1756787867295, "stop": 1756787867523}], "attachments": [{"name": "stdout", "source": "1673f136-8f88-422c-b3fe-062ce95997b5-attachment.txt", "type": "text/plain"}], "start": 1756787843573, "stop": 1756787867523, "uuid": "8642be55-1c95-44d6-a9bc-333aa5b0df61", "historyId": "1a5cbbb97cbe59e003ae71750a8d910f", "testCaseId": "1a5cbbb97cbe59e003ae71750a8d910f", "fullName": "testcases.test_ella.dialogue.test_who_is_j_k_rowling.TestEllaWhoIsJKRowling#test_who_is_j_k_rowling", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_who_is_j_k_rowling"}, {"name": "subSuite", "value": "TestEllaWhoIsJKRowling"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_who_is_j_k_rowling"}]}