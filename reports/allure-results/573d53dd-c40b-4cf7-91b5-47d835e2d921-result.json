{"name": "测试clear junk files命令", "status": "passed", "description": "使用简化的测试框架测试phonemaster开启命令，验证响应包含Done且实际打开PhoneMaster", "steps": [{"name": "执行命令: clear junk files", "status": "passed", "steps": [{"name": "执行命令: clear junk files", "status": "passed", "start": 1756789060568, "stop": 1756789093940}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "619e52b4-2c30-47fb-8de7-8dae8b910a91-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b6cab706-ef82-4c9f-b508-2b147f9299f6-attachment.png", "type": "image/png"}], "start": 1756789093940, "stop": 1756789094272}], "start": 1756789060568, "stop": 1756789094272}, {"name": "验证响应包含Done", "status": "passed", "start": 1756789094272, "stop": 1756789094273}, {"name": "验证PhoneMaster应用已打开", "status": "passed", "start": 1756789094273, "stop": 1756789094273}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1b15ab0a-477b-48d3-a367-606fd15d6ea6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c9512f3c-9784-493e-9f7b-770ce5576382-attachment.png", "type": "image/png"}], "start": 1756789094273, "stop": 1756789094490}], "attachments": [{"name": "stdout", "source": "114fefb0-26f1-4514-bc7a-337281bfb9e0-attachment.txt", "type": "text/plain"}], "start": 1756789060568, "stop": 1756789094490, "uuid": "c0d1b7df-5d84-46e6-ab57-f2907dede0f5", "historyId": "7413abdce214459d0e44671ef65b660b", "testCaseId": "7413abdce214459d0e44671ef65b660b", "fullName": "testcases.test_ella.system_coupling.test_clear_junk_files.TestEllaClearJunkFiles#test_clear_junk_files", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_clear_junk_files"}, {"name": "subSuite", "value": "TestEllaClearJunkFiles"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_clear_junk_files"}]}