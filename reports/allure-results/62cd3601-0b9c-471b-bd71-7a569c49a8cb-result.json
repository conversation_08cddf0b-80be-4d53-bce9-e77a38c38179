{"name": "测试There are transparent, glowing multicolored soap bubbles around it", "status": "passed", "description": "测试There are transparent, glowing multicolored soap bubbles around it指令", "steps": [{"name": "执行命令: There are transparent, glowing multicolored soap bubbles around it", "status": "passed", "steps": [{"name": "执行命令: There are transparent, glowing multicolored soap bubbles around it", "status": "passed", "start": 1756803751509, "stop": 1756803775836}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dad027e0-99e6-4d44-861d-1471b3cc4cc9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "498d350e-0fc1-4767-b9e2-9fe8b0883c61-attachment.png", "type": "image/png"}], "start": 1756803775836, "stop": 1756803776051}], "start": 1756803751509, "stop": 1756803776051}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756803776051, "stop": 1756803776052}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4f44a04e-9304-4ab3-9894-33714fbc517f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "729031be-ca5e-4ad9-9aeb-46e2e48849f8-attachment.png", "type": "image/png"}], "start": 1756803776052, "stop": 1756803776315}], "attachments": [{"name": "stdout", "source": "12e7b42a-317b-4a69-88d8-e2f3248a3373-attachment.txt", "type": "text/plain"}], "start": 1756803751509, "stop": 1756803776316, "uuid": "7a0e2ae6-404b-41a6-b2f2-88eaaec8050d", "historyId": "a2c60aff2518d86c9e5686c943130f40", "testCaseId": "a2c60aff2518d86c9e5686c943130f40", "fullName": "testcases.test_ella.unsupported_commands.test_there_are_transparent_glowing_multicolored_soap_bubbles_around_it.TestEllaOpenPlayPoliticalNews#test_there_are_transparent_glowing_multicolored_soap_bubbles_around_it", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_there_are_transparent_glowing_multicolored_soap_bubbles_around_it"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_there_are_transparent_glowing_multicolored_soap_bubbles_around_it"}]}