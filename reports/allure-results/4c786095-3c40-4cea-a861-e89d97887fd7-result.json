{"name": "测试stop workout能正常执行", "status": "passed", "description": "stop workout", "steps": [{"name": "执行命令: stop workout", "status": "passed", "steps": [{"name": "执行命令: stop workout", "status": "passed", "start": 1756787027702, "stop": 1756787050422}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4c7d1a84-f55c-41bd-8e60-c47c7a00e39a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b0015660-e515-461c-9376-6e7d57242824-attachment.png", "type": "image/png"}], "start": 1756787050422, "stop": 1756787050648}], "start": 1756787027702, "stop": 1756787050648}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756787050648, "stop": 1756787050650}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d09446f0-0b3b-4849-9741-c895f8b3e796-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "239afacc-6506-44fe-b3f1-0cf41adf46ab-attachment.png", "type": "image/png"}], "start": 1756787050650, "stop": 1756787050849}], "attachments": [{"name": "stdout", "source": "0f4d24d0-9530-4891-9c32-910fbb1dd709-attachment.txt", "type": "text/plain"}], "start": 1756787027702, "stop": 1756787050850, "uuid": "54a9663e-39fb-44cb-b491-bca6eab7e317", "historyId": "95e68fb1d20b8d7ff190c67b0bbc2ee8", "testCaseId": "95e68fb1d20b8d7ff190c67b0bbc2ee8", "fullName": "testcases.test_ella.dialogue.test_stop_workout.TestEllaStopWorkout#test_stop_workout", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_stop_workout"}, {"name": "subSuite", "value": "TestEllaStopWorkout"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_stop_workout"}]}