{"name": "测试switch to flash notification能正常执行", "status": "passed", "description": "switch to flash notification", "steps": [{"name": "执行命令: switch to flash notification", "status": "passed", "steps": [{"name": "执行命令: switch to flash notification", "status": "passed", "start": 1756791657519, "stop": 1756791678301}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "595fca76-f80d-4307-9f93-d0ec39dcbb24-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "94b5b801-e758-4b12-8b19-5c1b13602732-attachment.png", "type": "image/png"}], "start": 1756791678301, "stop": 1756791678507}], "start": 1756791657519, "stop": 1756791678507}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1756791678507, "stop": 1756791678508}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e9c77689-9f94-4ebc-9a18-d5f2ec228e49-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "890c075f-d3e4-4544-a0ff-413dcb94ed76-attachment.png", "type": "image/png"}], "start": 1756791678508, "stop": 1756791678713}], "attachments": [{"name": "stdout", "source": "ec3c2881-d8ec-4453-97a9-8079e479b47c-attachment.txt", "type": "text/plain"}], "start": 1756791657518, "stop": 1756791678713, "uuid": "dc2044f2-c20e-4c8d-a9fb-6b3f6ea9e616", "historyId": "b2f52f3c587a626460e5698cac861baf", "testCaseId": "b2f52f3c587a626460e5698cac861baf", "fullName": "testcases.test_ella.system_coupling.test_switch_to_flash_notification.TestEllaSwitchToFlashNotification#test_switch_to_flash_notification", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_flash_notification"}, {"name": "subSuite", "value": "TestEllaSwitchToFlashNotification"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_flash_notification"}]}