{"name": "测试What's the weather like today能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['the high is forecast', '℃']，实际响应: '[\"What's the weather like today\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.dialogue.test_what_s_the_weather_like_today.TestEllaWhatSWeatherLikeShanghaiToday object at 0x0000018BC606E500>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6B651B0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_what_s_the_weather_like_today(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['the high is forecast', '℃']，实际响应: '[\"What's the weather like today\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_what_s_the_weather_like_today.py:37: AssertionError"}, "description": "What's the weather like today", "steps": [{"name": "执行命令: What's the weather like today", "status": "passed", "steps": [{"name": "执行命令: What's the weather like today", "status": "passed", "start": 1756787545605, "stop": 1756787574649}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f89d5aa1-a3b1-404d-9521-41bb6322bf8a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1a30a980-0b46-4561-8b38-ff393f5f4b8d-attachment.png", "type": "image/png"}], "start": 1756787574649, "stop": 1756787574863}], "start": 1756787545605, "stop": 1756787574863}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['the high is forecast', '℃']，实际响应: '[\"What's the weather like today\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\dialogue\\test_what_s_the_weather_like_today.py\", line 37, in test_what_s_the_weather_like_today\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756787574863, "stop": 1756787574866}], "attachments": [{"name": "stdout", "source": "5a7f099d-d849-46eb-8d4a-d266ffd00857-attachment.txt", "type": "text/plain"}], "start": 1756787545605, "stop": 1756787574866, "uuid": "f8baf32b-1ea9-4101-9f2c-921fac403642", "historyId": "ac6792be4ebb553a5655a2c44aca218c", "testCaseId": "ac6792be4ebb553a5655a2c44aca218c", "fullName": "testcases.test_ella.dialogue.test_what_s_the_weather_like_today.TestEllaWhatSWeatherLikeShanghaiToday#test_what_s_the_weather_like_today", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_s_the_weather_like_today"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherLikeShanghaiToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_s_the_weather_like_today"}]}