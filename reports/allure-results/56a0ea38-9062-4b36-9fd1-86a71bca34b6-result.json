{"name": "测试download basketball能正常执行", "status": "passed", "description": "download basketball", "steps": [{"name": "执行命令: download basketball", "status": "passed", "steps": [{"name": "执行命令: download basketball", "status": "passed", "start": 1756794302665, "stop": 1756794327199}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f450ce43-ecd4-4c42-93af-e4a187566272-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0f180174-3474-4146-abbe-bf44d2dfb630-attachment.png", "type": "image/png"}], "start": 1756794327200, "stop": 1756794327403}], "start": 1756794302665, "stop": 1756794327403}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1756794327403, "stop": 1756794327404}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d09bb8ba-c499-49ca-8ad7-246efcd8e212-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "436ca127-747a-4f08-8d9a-c17a1b07a5fb-attachment.png", "type": "image/png"}], "start": 1756794327404, "stop": 1756794327606}], "attachments": [{"name": "stdout", "source": "2a00151c-3842-4fe1-8f71-c982c2983c18-attachment.txt", "type": "text/plain"}], "start": 1756794302665, "stop": 1756794327607, "uuid": "a546bf94-b3b7-481f-80c8-318629c04595", "historyId": "6f2c4144233271771cdd01a5c48ea3ca", "testCaseId": "6f2c4144233271771cdd01a5c48ea3ca", "fullName": "testcases.test_ella.third_coupling.test_download_basketball.TestEllaDownloadBasketball#test_download_basketball", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_download_basketball"}, {"name": "subSuite", "value": "TestEllaDownloadBasketball"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_download_basketball"}]}