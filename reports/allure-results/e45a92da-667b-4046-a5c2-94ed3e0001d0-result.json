{"name": "测试set cover screen apps返回正确的不支持响应", "status": "passed", "description": "验证set cover screen apps指令返回预期的不支持响应", "steps": [{"name": "执行命令: set cover screen apps", "status": "passed", "steps": [{"name": "执行命令: set cover screen apps", "status": "passed", "start": 1756801648413, "stop": 1756801670888}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b3f6ed72-25c4-424c-8db1-7ed99a884366-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "78aaacad-5fc0-43fa-95c9-c4babee81a10-attachment.png", "type": "image/png"}], "start": 1756801670888, "stop": 1756801671167}], "start": 1756801648413, "stop": 1756801671168}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756801671168, "stop": 1756801671171}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "05e12fb2-4c20-4e21-9885-b7b0f1e27942-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "67e30339-72e4-4cbc-bed0-1e1cd62db874-attachment.png", "type": "image/png"}], "start": 1756801671171, "stop": 1756801671414}], "attachments": [{"name": "stdout", "source": "0c54a15d-ebfe-48c7-8002-525acb2698ca-attachment.txt", "type": "text/plain"}], "start": 1756801648413, "stop": 1756801671415, "uuid": "10c9899f-0f7d-4c41-abbe-ec696291d925", "historyId": "154a720f41d8f5a908552e8c7cf8e781", "testCaseId": "154a720f41d8f5a908552e8c7cf8e781", "fullName": "testcases.test_ella.unsupported_commands.test_set_cover_screen_apps.TestEllaSetCoverScreenApps#test_set_cover_screen_apps", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_cover_screen_apps"}, {"name": "subSuite", "value": "TestEllaSetCoverScreenApps"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_cover_screen_apps"}]}