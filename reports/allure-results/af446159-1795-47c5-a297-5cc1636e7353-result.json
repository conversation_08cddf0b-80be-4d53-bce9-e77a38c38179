{"name": "测试turn down the brightness to the min能正常执行", "status": "passed", "description": "turn down the brightness to the min", "steps": [{"name": "执行命令: turn down the brightness to the min", "status": "passed", "steps": [{"name": "执行命令: turn down the brightness to the min", "status": "passed", "start": 1756792187133, "stop": 1756792209150}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ff282706-58b8-4f6f-8799-a85354f0298f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ad111440-20ca-467c-a5b2-3dc7237b4378-attachment.png", "type": "image/png"}], "start": 1756792209150, "stop": 1756792209341}], "start": 1756792187133, "stop": 1756792209342}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756792209342, "stop": 1756792209343}, {"name": "验证应用已打开", "status": "passed", "start": 1756792209343, "stop": 1756792209343}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b6d8592e-224b-449d-85aa-425645702d0f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f452fe58-4df2-4992-940e-d2183134a16f-attachment.png", "type": "image/png"}], "start": 1756792209343, "stop": 1756792209541}], "attachments": [{"name": "stdout", "source": "e567241e-26b2-42f4-8e9f-73c8da513522-attachment.txt", "type": "text/plain"}], "start": 1756792187133, "stop": 1756792209542, "uuid": "6db48781-0976-436a-9787-4f373347bef2", "historyId": "fdcf3737e32a4361e11902caf25fed5f", "testCaseId": "fdcf3737e32a4361e11902caf25fed5f", "fullName": "testcases.test_ella.system_coupling.test_turn_down_the_brightness_to_the_min.TestEllaTurnDownBrightnessMin#test_turn_down_the_brightness_to_the_min", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_down_the_brightness_to_the_min"}, {"name": "subSuite", "value": "TestEllaTurnDownBrightnessMin"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_down_the_brightness_to_the_min"}]}