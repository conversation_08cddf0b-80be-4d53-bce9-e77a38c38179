{"name": "测试parking space能正常执行", "status": "passed", "description": "parking space", "steps": [{"name": "执行命令: parking space", "status": "passed", "steps": [{"name": "执行命令: parking space", "status": "passed", "start": 1756800346065, "stop": 1756800368454}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a3f543f0-70c6-48ff-be92-b21c73089e1c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9352778e-7425-4168-a90e-4b30345355a0-attachment.png", "type": "image/png"}], "start": 1756800368454, "stop": 1756800368680}], "start": 1756800346065, "stop": 1756800368680}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756800368680, "stop": 1756800368682}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5d2ad00c-d711-4f9f-9b07-5da0183ce286-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1cad81c5-2557-4cbe-abc3-d459e4e89941-attachment.png", "type": "image/png"}], "start": 1756800368682, "stop": 1756800368924}], "attachments": [{"name": "stdout", "source": "f65d027c-499f-492a-a851-f5935064a8d4-attachment.txt", "type": "text/plain"}], "start": 1756800346065, "stop": 1756800368925, "uuid": "80f23dd4-99e4-4f39-8407-e0b8a895afe1", "historyId": "217ee9f7b3be9f625903076716d45106", "testCaseId": "217ee9f7b3be9f625903076716d45106", "fullName": "testcases.test_ella.unsupported_commands.test_parking_space.TestEllaParkingSpace#test_parking_space", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "ella技能"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_parking_space"}, {"name": "subSuite", "value": "TestEllaParkingSpace"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_parking_space"}]}