{"name": "测试what's your name", "status": "passed", "description": "测试what's your name指令", "steps": [{"name": "执行命令: what's your name", "status": "passed", "steps": [{"name": "执行命令: what's your name", "status": "passed", "start": 1756804346857, "stop": 1756804371630}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ae053d82-93c6-4557-92af-690b010dc254-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d7c816fe-edb9-463b-a7cc-2e614642ddce-attachment.png", "type": "image/png"}], "start": 1756804371630, "stop": 1756804371884}], "start": 1756804346857, "stop": 1756804371884}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756804371884, "stop": 1756804371884}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d8a3dc5d-4fb1-4722-8b4f-3948e388547e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d13c704a-cd8d-48a1-bf5f-16dd603686dc-attachment.png", "type": "image/png"}], "start": 1756804371884, "stop": 1756804372157}], "attachments": [{"name": "stdout", "source": "483f2b3d-0432-46d1-92ed-89d163b39369-attachment.txt", "type": "text/plain"}], "start": 1756804346857, "stop": 1756804372157, "uuid": "4efaaa9f-322d-4026-bfe3-71b99dde37b5", "historyId": "929b39cceeb7a01f602573951f5fef39", "testCaseId": "929b39cceeb7a01f602573951f5fef39", "fullName": "testcases.test_ella.unsupported_commands.test_what_s_your_name.TestEllaOpenPlayPoliticalNews#test_what_s_your_name", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_what_s_your_name"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_what_s_your_name"}]}