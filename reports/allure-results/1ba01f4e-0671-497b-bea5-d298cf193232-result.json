{"name": "测试disable auto pickup返回正确的不支持响应", "status": "passed", "description": "验证disable auto pickup指令返回预期的不支持响应", "steps": [{"name": "执行命令: disable auto pickup", "status": "passed", "steps": [{"name": "执行命令: disable auto pickup", "status": "passed", "start": 1756796335299, "stop": 1756796357654}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "091c4aa5-a3f5-4c52-a2ba-99423ffb8694-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7db7e44c-d5f8-4fce-aa63-8e2a66511fa8-attachment.png", "type": "image/png"}], "start": 1756796357654, "stop": 1756796357852}], "start": 1756796335299, "stop": 1756796357852}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756796357852, "stop": 1756796357853}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "07b0d91c-3d57-422a-a606-27c32daa5727-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "53428c7b-08b1-4ce9-a80f-61bd7110db38-attachment.png", "type": "image/png"}], "start": 1756796357853, "stop": 1756796358154}], "attachments": [{"name": "stdout", "source": "d6934da4-5333-47fe-b4c3-67653016b667-attachment.txt", "type": "text/plain"}], "start": 1756796335298, "stop": 1756796358154, "uuid": "769d8490-a4ec-4776-be1f-b2844b82e6bd", "historyId": "891e31ec8bf99ceed4f462ce0c8629db", "testCaseId": "891e31ec8bf99ceed4f462ce0c8629db", "fullName": "testcases.test_ella.unsupported_commands.test_disable_auto_pickup.TestEllaDisableAutoPickup#test_disable_auto_pickup", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_auto_pickup"}, {"name": "subSuite", "value": "TestEllaDisableAutoPickup"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_auto_pickup"}]}