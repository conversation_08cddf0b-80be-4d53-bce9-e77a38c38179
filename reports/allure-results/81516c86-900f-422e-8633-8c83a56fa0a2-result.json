{"name": "测试next channel能正常执行", "status": "passed", "description": "next channel", "steps": [{"name": "执行命令: next channel", "status": "passed", "steps": [{"name": "执行命令: next channel", "status": "passed", "start": 1756783200668, "stop": 1756783221108}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4e5556ef-aff2-45fd-b0a4-418df8dfe0aa-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9acbd150-101e-4ece-80b1-a1bde8a9de30-attachment.png", "type": "image/png"}], "start": 1756783221108, "stop": 1756783221323}], "start": 1756783200668, "stop": 1756783221323}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756783221323, "stop": 1756783221325}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e121aff6-a7bf-4d61-8905-b626586cb4db-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "82ef39cf-71b6-40d4-8ae6-56322a148855-attachment.png", "type": "image/png"}], "start": 1756783221325, "stop": 1756783221540}], "attachments": [{"name": "stdout", "source": "d59d1b98-9086-4ada-993b-a7586b3af3d6-attachment.txt", "type": "text/plain"}], "start": 1756783200668, "stop": 1756783221540, "uuid": "a7897800-e3c4-4fd0-90f8-39dd44befdea", "historyId": "1d15cba90ae0426fa12e3218f1c542a6", "testCaseId": "1d15cba90ae0426fa12e3218f1c542a6", "fullName": "testcases.test_ella.component_coupling.test_next_channel.TestEllaNextChannel#test_next_channel", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_next_channel"}, {"name": "subSuite", "value": "TestEllaNextChannel"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_next_channel"}]}