{"name": "测试Set an alarm at 10 am tomorrow", "status": "passed", "description": "测试Set an alarm at 10 am tomorrow指令", "steps": [{"name": "执行命令:  delete all the alarms", "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "status": "passed", "start": 1756790724218, "stop": 1756790744848}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a96a3d1d-07cf-43d3-95d2-ad52c7b0cca4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2f12b2ac-a3a0-49b8-b380-c5998bf3dfb4-attachment.png", "type": "image/png"}], "start": 1756790744848, "stop": 1756790745048}], "start": 1756790724218, "stop": 1756790745048}, {"name": "执行命令: Set an alarm at 10 am tomorrow", "status": "passed", "steps": [{"name": "执行命令: Set an alarm at 10 am tomorrow", "status": "passed", "start": 1756790745048, "stop": 1756790764646}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d1c36e29-ee56-4435-8d9e-8ea9c57e4109-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8bc68c3d-6f32-4012-b737-eab6fcf42c59-attachment.png", "type": "image/png"}], "start": 1756790764646, "stop": 1756790764852}], "start": 1756790745048, "stop": 1756790764852}, {"name": "执行命令: get all the alarms", "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "status": "passed", "start": 1756790764852, "stop": 1756790784543}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0b0656be-5ac4-4207-9b62-85f4a1eaf9e3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "627b31c0-a05b-416f-89ef-8373e30f522e-attachment.png", "type": "image/png"}], "start": 1756790784543, "stop": 1756790784731}], "start": 1756790764852, "stop": 1756790784731}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790784731, "stop": 1756790784733}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fb2dc8ee-b3e0-4806-9d37-9a35c94fc1fe-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d3b1521f-e6fe-4027-9ed2-eb84380c5140-attachment.png", "type": "image/png"}], "start": 1756790784733, "stop": 1756790784912}], "attachments": [{"name": "stdout", "source": "e6875f6d-4d52-476d-9bcb-8ac0a780a60d-attachment.txt", "type": "text/plain"}], "start": 1756790724218, "stop": 1756790784913, "uuid": "a3ce9ef8-c83c-4202-bf29-d1082a04f8e1", "historyId": "822b86779d55ac868fa4493183547c8f", "testCaseId": "822b86779d55ac868fa4493183547c8f", "fullName": "testcases.test_ella.system_coupling.test_set_an_alarm_at_am_tomorrow.TestEllaOpenSetAnAlarmAmTomorrow#test_set_an_alarm_at_am_tomorrow", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_an_alarm_at_am_tomorrow"}, {"name": "subSuite", "value": "TestEllaOpenSetAnAlarmAmTomorrow"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_an_alarm_at_am_tomorrow"}]}