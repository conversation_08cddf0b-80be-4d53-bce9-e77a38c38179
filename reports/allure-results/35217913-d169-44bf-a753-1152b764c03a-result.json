{"name": "测试add this number to lucy", "status": "passed", "description": "测试Ask Screen功能: add this number to lucy", "steps": [{"name": "准备测试数据", "status": "passed", "start": 1756793656096, "stop": 1756793664234}, {"name": "执行Ask Screen命令: add this number to lucy", "status": "passed", "steps": [{"name": "执行浮窗命令: add this number to lucy", "status": "passed", "steps": [{"name": "执行命令: add this number to lucy", "status": "passed", "start": 1756793664235, "stop": 1756793673451}, {"name": "等待并获取AI响应", "status": "passed", "start": 1756793673451, "stop": 1756793689541}, {"name": "验证响应内容", "status": "passed", "attachments": [{"name": "关键词验证结果", "source": "7bf3392e-a812-4abf-937d-7a651654160e-attachment.txt", "type": "text/plain"}], "start": 1756793689541, "stop": 1756793689544}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "浮窗测试总结", "source": "4db600e9-f76e-40fd-b2f1-52fe85e55aea-attachment.txt", "type": "text/plain"}, {"name": "AI响应内容", "source": "f80d7a33-39b5-4a2b-8019-8c511ad6f8f6-attachment.txt", "type": "text/plain"}], "start": 1756793689544, "stop": 1756793689546}], "start": 1756793664234, "stop": 1756793689546}, {"name": "截图记录测试完成状态", "status": "passed", "attachments": [{"name": "floating_test_completed", "source": "d43e746b-3463-4e4c-a6fd-4608cbb8717e-attachment.png", "type": "image/png"}], "start": 1756793689546, "stop": 1756793689742}], "start": 1756793664234, "stop": 1756793690971}, {"name": "验证测试结果", "status": "passed", "start": 1756793690971, "stop": 1756793690971}], "attachments": [{"name": "stdout", "source": "4baaade9-077a-48f5-8e57-07c5771e1ee1-attachment.txt", "type": "text/plain"}], "start": 1756793656096, "stop": 1756793690972, "uuid": "1d5834b3-349f-4b45-9f5e-54f71a3db18c", "historyId": "d4f6d299fdb0e6fd724a5069e0b36670", "testCaseId": "d4f6d299fdb0e6fd724a5069e0b36670", "fullName": "testcases.test_ella.test_ask_screen.contact.test_add_this_number_to_lucy.TestAskScreenAddNumberLucy#test_add_this_number_to_lucy", "labels": [{"name": "epic", "value": "Ella浮窗测试"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ask Screen功能"}, {"name": "story", "value": "联系人相关"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.test_ask_screen.contact"}, {"name": "suite", "value": "test_add_this_number_to_lucy"}, {"name": "subSuite", "value": "TestAskScreenAddNumberLucy"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_ask_screen.contact.test_add_this_number_to_lucy"}]}