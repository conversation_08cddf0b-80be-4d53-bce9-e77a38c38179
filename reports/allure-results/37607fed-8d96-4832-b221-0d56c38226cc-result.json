{"name": "测试end exercising能正常执行", "status": "passed", "description": "end exercising", "steps": [{"name": "执行命令: end exercising", "status": "passed", "steps": [{"name": "执行命令: end exercising", "status": "passed", "start": 1756797359538, "stop": 1756797381558}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "062824bb-41a8-4814-a113-a455c5cca01c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7faa184c-3ae9-4283-b419-1ea7e3d09e10-attachment.png", "type": "image/png"}], "start": 1756797381558, "stop": 1756797381768}], "start": 1756797359538, "stop": 1756797381768}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756797381768, "stop": 1756797381769}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "26f980e6-f686-4233-9f49-81dfcbd1a863-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2596defd-b76a-431a-ac01-5ab28a47818f-attachment.png", "type": "image/png"}], "start": 1756797381769, "stop": 1756797381997}], "attachments": [{"name": "stdout", "source": "52827462-07bc-4754-8902-7b10a6b1dcf0-attachment.txt", "type": "text/plain"}], "start": 1756797359537, "stop": 1756797381997, "uuid": "f160069f-1711-4ae0-8f8c-6832adf59c95", "historyId": "04263bf3ad5402ebd901c9c0e6682325", "testCaseId": "04263bf3ad5402ebd901c9c0e6682325", "fullName": "testcases.test_ella.unsupported_commands.test_end_exercising.TestEllaEndExercising#test_end_exercising", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_end_exercising"}, {"name": "subSuite", "value": "TestEllaEndExercising"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_end_exercising"}]}