{"name": "测试Summarize what I'm reading", "status": "passed", "description": "测试Summarize what I'm reading指令", "steps": [{"name": "执行命令: Summarize what I'm reading", "status": "passed", "steps": [{"name": "执行命令: Summarize what I'm reading", "status": "passed", "start": 1756803286194, "stop": 1756803309451}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b1f53a38-268d-489e-ac72-c319b6760f21-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7407335b-90b0-4e18-95bc-b832bb829398-attachment.png", "type": "image/png"}], "start": 1756803309451, "stop": 1756803309703}], "start": 1756803286194, "stop": 1756803309703}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756803309703, "stop": 1756803309705}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d28b2301-ad2d-45a7-a0a3-4b64d41ae006-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0ba70a30-1de9-4acf-b9ba-3334542f509c-attachment.png", "type": "image/png"}], "start": 1756803309705, "stop": 1756803309943}], "attachments": [{"name": "stdout", "source": "cb337262-5b96-4c78-b0db-e5a103506e04-attachment.txt", "type": "text/plain"}], "start": 1756803286194, "stop": 1756803309943, "uuid": "a9d608f4-f8a5-4061-abc6-62fc018479c3", "historyId": "07dafe2b3e3ed9841a34e9fd19de58be", "testCaseId": "07dafe2b3e3ed9841a34e9fd19de58be", "fullName": "testcases.test_ella.unsupported_commands.test_summarize_what_i_m_reading.TestEllaOpenPlayPoliticalNews#test_summarize_what_i_m_reading", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_summarize_what_i_m_reading"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_summarize_what_i_m_reading"}]}