{"name": "测试check ram information", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['check ram information', \"You've reached the image generation limit for today.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_check_ram_information.TestEllaOpenPlayPoliticalNews object at 0x0000018BC6432AA0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC953E9E0>\n\n    @allure.title(\"测试check ram information\")\n    @allure.description(\"测试check ram information指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_check_system_update(self, ella_app):\n        \"\"\"测试check ram information命令\"\"\"\n        command = \"check ram information\"\n        expected_text = ['Done']\n        # expected_text =  ['Sorry','Oops','out of my reach','Generated by AI, for reference only']\n        app_name = 'settings'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n    \n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['check ram information', \"You've reached the image generation limit for today.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_check_ram_information.py:33: AssertionError"}, "description": "测试check ram information指令", "steps": [{"name": "执行命令: check ram information", "status": "passed", "steps": [{"name": "执行命令: check ram information", "status": "passed", "start": 1756795904173, "stop": 1756795926291}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6ab99fe2-b55b-43fc-b843-850f975e82fc-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e0a5201d-ca04-4b94-aae3-f12272091577-attachment.png", "type": "image/png"}], "start": 1756795926291, "stop": 1756795926504}], "start": 1756795904173, "stop": 1756795926504}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['check ram information', \"You've reached the image generation limit for today.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_check_ram_information.py\", line 33, in test_check_system_update\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756795926504, "stop": 1756795926506}], "attachments": [{"name": "stdout", "source": "f9e19897-32aa-4248-93e1-bd676ab2bf04-attachment.txt", "type": "text/plain"}], "start": 1756795904173, "stop": 1756795926507, "uuid": "b96baaea-d6da-4d93-bd34-2dc454d8a9dd", "historyId": "11875095b9997cfc7edbe407c3074b7e", "testCaseId": "11875095b9997cfc7edbe407c3074b7e", "fullName": "testcases.test_ella.unsupported_commands.test_check_ram_information.TestEllaOpenPlayPoliticalNews#test_check_system_update", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_ram_information"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_ram_information"}]}