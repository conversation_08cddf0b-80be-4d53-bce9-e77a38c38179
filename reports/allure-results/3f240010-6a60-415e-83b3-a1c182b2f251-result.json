{"name": "测试Summarize what I'm reading能正常执行", "status": "passed", "description": "Summarize what I'm reading", "steps": [{"name": "执行命令: Summarize what I'm reading", "status": "passed", "steps": [{"name": "执行命令: Summarize what I'm reading", "status": "passed", "start": 1756788758796, "stop": 1756788827208}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "af4dd6ac-0da0-4a18-a762-955c674c23e5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9c728200-a9a0-4116-8f13-620ee483f3e7-attachment.png", "type": "image/png"}], "start": 1756788827208, "stop": 1756788827443}], "start": 1756788758796, "stop": 1756788827443}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756788827444, "stop": 1756788827445}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8289a929-c468-42ee-87f3-263297b32ff6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0f24edc2-202a-4870-937c-7c6361ebac45-attachment.png", "type": "image/png"}], "start": 1756788827445, "stop": 1756788827676}], "attachments": [{"name": "stdout", "source": "ec3c629b-9be6-4108-977a-0ae030e15515-attachment.txt", "type": "text/plain"}], "start": 1756788758796, "stop": 1756788827676, "uuid": "46fca77a-9123-41aa-b9ab-4c7b19ba322d", "historyId": "981b71ad744b9a603c31ab4832ff9439", "testCaseId": "981b71ad744b9a603c31ab4832ff9439", "fullName": "testcases.test_ella.self_function.test_summarize_what_i_m_reading.TestEllaSummarizeWhatIMReading#test_summarize_what_i_m_reading", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.self_function"}, {"name": "suite", "value": "test_summarize_what_i_m_reading"}, {"name": "subSuite", "value": "TestEllaSummarizeWhatIMReading"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.self_function.test_summarize_what_i_m_reading"}]}