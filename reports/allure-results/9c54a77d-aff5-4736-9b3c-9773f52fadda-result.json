{"name": "测试set off a firework能正常执行", "status": "passed", "description": "set off a firework", "steps": [{"name": "执行命令: set off a firework", "status": "passed", "steps": [{"name": "执行命令: set off a firework", "status": "passed", "start": 1756802306543, "stop": 1756802331901}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "71abc56c-8e06-4d53-8c34-41b6ef7ee412-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "76d07276-59e7-4270-9ea1-d1a945789720-attachment.png", "type": "image/png"}], "start": 1756802331901, "stop": 1756802332211}], "start": 1756802306543, "stop": 1756802332212}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756802332212, "stop": 1756802332214}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "eb47cf17-94d9-4fb8-a0d2-8ebc8fd41c9c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "28050657-760e-45b0-a61c-0d3badbf32de-attachment.png", "type": "image/png"}], "start": 1756802332214, "stop": 1756802332463}], "attachments": [{"name": "stdout", "source": "85254145-22a8-405d-8758-3aae6275b313-attachment.txt", "type": "text/plain"}], "start": 1756802306542, "stop": 1756802332464, "uuid": "51f98081-4cf6-4235-862e-05550cc86531", "historyId": "e7de7a828ef1b59725204585ed7e1d64", "testCaseId": "e7de7a828ef1b59725204585ed7e1d64", "fullName": "testcases.test_ella.unsupported_commands.test_set_off_a_firework.TestEllaSetOffFirework#test_set_off_a_firework", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_off_a_firework"}, {"name": "subSuite", "value": "TestEllaSetOffFirework"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_off_a_firework"}]}