{"name": "测试go on playing fm能正常执行", "status": "passed", "description": "go on playing fm", "steps": [{"name": "执行命令: go on playing fm", "status": "passed", "steps": [{"name": "执行命令: go on playing fm", "status": "passed", "start": 1756785132323, "stop": 1756785156487}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "90ce0729-9560-4245-8458-465251022851-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1beff5ec-3234-433f-970e-37a2e2c9e67e-attachment.png", "type": "image/png"}], "start": 1756785156487, "stop": 1756785156696}], "start": 1756785132322, "stop": 1756785156697}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756785156697, "stop": 1756785156698}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "950ccfcc-0b62-4db2-a884-5e4388f6f049-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7f863c3a-c5d0-4ea9-9128-ca369c29db04-attachment.png", "type": "image/png"}], "start": 1756785156698, "stop": 1756785156929}], "attachments": [{"name": "stdout", "source": "3be847f5-d0d6-42d2-a95f-9a626cc932ce-attachment.txt", "type": "text/plain"}], "start": 1756785132322, "stop": 1756785156930, "uuid": "3295ffdf-278d-4d2c-815c-4c3c4aaa5122", "historyId": "d60fcab377d9b5093e0f03ecf20f5d10", "testCaseId": "d60fcab377d9b5093e0f03ecf20f5d10", "fullName": "testcases.test_ella.dialogue.test_go_on_playing_fm.TestEllaHelloHello#test_go_on_playing_fm", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_go_on_playing_fm"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_go_on_playing_fm"}]}