{"name": "测试open notification ringtone settings返回正确的不支持响应", "status": "passed", "description": "验证open notification ringtone settings指令返回预期的不支持响应", "steps": [{"name": "执行命令: open notification ringtone settings", "status": "passed", "steps": [{"name": "执行命令: open notification ringtone settings", "status": "passed", "start": 1756800073453, "stop": 1756800105034}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5c1f9583-9b5f-49f2-83d6-0186c10dcab8-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3ce4e6f7-ec6c-4b96-90d8-a2b4d26d5883-attachment.png", "type": "image/png"}], "start": 1756800105034, "stop": 1756800105320}], "start": 1756800073453, "stop": 1756800105321}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756800105321, "stop": 1756800105322}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "547f019a-3218-40dd-a167-15ff0c14cdc3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cffdf0d1-3709-488d-9c27-d3da9ddeb8ed-attachment.png", "type": "image/png"}], "start": 1756800105322, "stop": 1756800105559}], "attachments": [{"name": "stdout", "source": "3d22da1f-8c87-49ca-8e25-e8301977790d-attachment.txt", "type": "text/plain"}], "start": 1756800073453, "stop": 1756800105560, "uuid": "ed507d03-1fd5-4a0a-96ca-55d7cf4a01a0", "historyId": "ff8706df57207971727cf6e1326d4a26", "testCaseId": "ff8706df57207971727cf6e1326d4a26", "fullName": "testcases.test_ella.unsupported_commands.test_open_notification_ringtone_settings.TestEllaOpenSettings#test_open_notification_ringtone_settings", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_open_notification_ringtone_settings"}, {"name": "subSuite", "value": "TestEllaOpenSettings"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_open_notification_ringtone_settings"}]}