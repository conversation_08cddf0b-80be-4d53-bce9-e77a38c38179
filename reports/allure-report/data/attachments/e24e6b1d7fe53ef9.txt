2025-09-02 12:44:22 | INFO | core.base_page:__init__:38 | 初始化页面: ella - dialogue_page
2025-09-02 12:44:22 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:299 | 🧹 开始清除手机上所有运行中的应用进程...
2025-09-02 12:44:22 | INFO | tools.adb_process_monitor:clear_all_running_processes:1955 | 🧹 开始清除手机上所有运行中的应用进程...
2025-09-02 12:44:22 | INFO | tools.adb_process_monitor:clear_all_running_processes:1971 | ⚡ 优先使用命令直接清理...
2025-09-02 12:44:25 | INFO | tools.adb_process_monitor:clear_all_running_processes:1977 | 💪 强制停止顽固应用...
2025-09-02 12:44:30 | INFO | tools.adb_process_monitor:clear_all_running_processes:1987 | 🎉 应用进程清理完成，共清理 30 个应用
2025-09-02 12:44:32 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:301 | ✅ 应用进程清理完成
2025-09-02 12:44:32 | INFO | pages.apps.ella.dialogue_page:start_app:215 | 启动Ella应用
2025-09-02 12:44:32 | INFO | pages.apps.ella.dialogue_page:start_app:223 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-09-02 12:44:36 | INFO | pages.apps.ella.dialogue_page:_check_app_started:281 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-09-02 12:44:36 | INFO | pages.apps.ella.dialogue_page:start_app:228 | ✅ Ella应用启动成功（指定Activity）
2025-09-02 12:44:36 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:302 | 等待Ella页面加载完成 (超时: 15秒)
2025-09-02 12:44:36 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:312 | ✅ 确认当前在Ella应用中
2025-09-02 12:44:36 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:321 | ✅ 主输入框已出现，页面加载完成
2025-09-02 12:44:36 | INFO | testcases.test_ella.base_ella_test:ella_app:333 | ✅ Ella应用启动成功
2025-09-02 12:44:36 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:371 | 初始状态None- 使用命令extend the image，状态: 
2025-09-02 12:44:36 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:770 | 确保在对话页面...
2025-09-02 12:44:36 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-09-02 12:44:37 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-09-02 12:44:37 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-09-02 12:44:37 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-09-02 12:44:37 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:781 | ✅ 已在对话页面
2025-09-02 12:44:37 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-09-02 12:44:37 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-09-02 12:44:37 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:379 | 🎯 检测到多模态指令: gallery
2025-09-02 12:44:37 | INFO | testcases.test_ella.base_ella_test:_execute_multimodal_operation:1234 | 🚀 开始执行多模态操作: gallery
2025-09-02 12:44:37 | INFO | pages.apps.ella.ella_multimodal_handler:execute_multimodal_function:49 | 执行多模态功能: gallery
2025-09-02 12:44:37 | INFO | pages.apps.ella.ella_multimodal_handler:_open_multimodal_entrance:75 | 点击多模态入口
2025-09-02 12:44:37 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [多模态入口], 超时时间: 5秒
2025-09-02 12:44:37 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 12:44:37 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [多模态入口]
2025-09-02 12:44:37 | INFO | core.base_element:click:231 | 点击元素成功 [多模态入口]
2025-09-02 12:44:40 | INFO | pages.apps.ella.ella_multimodal_handler:_open_multimodal_entrance:86 | ✅ 成功点击多模态入口
2025-09-02 12:44:40 | INFO | pages.apps.ella.ella_multimodal_handler:_handle_gallery_flow:136 | 执行图库功能流程
2025-09-02 12:44:40 | INFO | pages.apps.ella.ella_multimodal_handler:_prepare_test_images:754 | 开始推送测试图片到设备
2025-09-02 12:44:40 | INFO | tools.file_pusher:check_device_connection:444 | 检测到 1 个连接的设备
2025-09-02 12:44:40 | INFO | tools.file_pusher:push_images_to_device:106 | 开始推送图片文件到设备
2025-09-02 12:44:41 | INFO | tools.file_pusher:_push_single_file:420 | ✅ 文件推送成功: IMG_20250812_151520_276.jpg
2025-09-02 12:44:41 | INFO | tools.file_pusher:_push_single_file:420 | ✅ 文件推送成功: IMG_20250812_151520_276.jpg
2025-09-02 12:44:41 | INFO | tools.file_pusher:_push_single_file:420 | ✅ 文件推送成功: qr_code.png
2025-09-02 12:44:41 | INFO | tools.file_pusher:_push_single_file:420 | ✅ 文件推送成功: qr_code.png
2025-09-02 12:44:42 | INFO | tools.file_pusher:_refresh_media_library:641 | 刷新媒体库: /sdcard/DCIM/Camera
2025-09-02 12:44:42 | INFO | tools.file_pusher:_refresh_media_library:648 | ✅ 媒体扫描器扫描成功
2025-09-02 12:44:42 | WARNING | tools.file_pusher:_refresh_media_library:659 | 媒体库刷新失败: 
Exception occurred while executing 'broadcast':
java.lang.SecurityException: Permission Denial: not allowed to send broadcast android.intent.action.MEDIA_MOUNTED from pid=1399, uid=2000
	at com.android.server.am.BroadcastController.broadcastIntentLockedTraced(BroadcastController.java:1132)
	at com.android.server.am.BroadcastController.broadcastIntentLocked(BroadcastController.java:870)
	at com.android.server.am.BroadcastController.broadcastIntentWithFeature(BroadcastController.java:782)
	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:15398)
	at com.android.server.am.ActivityManagerShellCommand.runSendBroadcast(ActivityManagerShellCommand.java:1096)
	at com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:282)
	at com.android.modules.utils.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:97)
	at android.os.ShellCommand.exec(ShellCommand.java:38)
	at com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:11040)
	at android.os.Binder.shellCommand(Binder.java:1151)
	at android.os.Binder.onTransact(Binder.java:953)
	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:6120)
	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2932)
	at android.os.Binder.execTransactInternal(Binder.java:1426)
	at android.os.Binder.execTransact(Binder.java:1365)

2025-09-02 12:44:42 | INFO | tools.file_pusher:_refresh_media_library:666 | ✅ 媒体存储服务重启成功
2025-09-02 12:44:45 | INFO | tools.file_pusher:push_images_to_device:136 | 图片推送完成: 4/4 个文件成功
2025-09-02 12:44:45 | INFO | pages.apps.ella.ella_multimodal_handler:_prepare_test_images:764 | ✅ 测试图片推送成功
2025-09-02 12:44:47 | INFO | pages.apps.ella.ella_multimodal_handler:_prepare_test_images:772 | 设备中的图片: ['IMG_20250812_151520_276.jpg', 'IMG_20250901_212402_024.jpg', 'qr_code.png']
2025-09-02 12:44:47 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [多模态-gallery], 超时时间: 5秒
2025-09-02 12:44:47 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 12:44:47 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [多模态-gallery]
2025-09-02 12:44:48 | INFO | core.base_element:click:231 | 点击元素成功 [多模态-gallery]
2025-09-02 12:44:53 | INFO | pages.apps.ella.ella_multimodal_handler:_handle_gallery_flow:150 | ✅ 点击gallery按钮成功
2025-09-02 12:44:53 | INFO | pages.apps.ella.ella_multimodal_handler:_select_photo:498 | 尝试选择照片
2025-09-02 12:45:01 | INFO | pages.apps.ella.ella_multimodal_handler:_select_any_image:552 | 尝试选择任何可用图片
2025-09-02 12:45:03 | INFO | pages.apps.ella.ella_multimodal_handler:_select_any_image:571 | ✅ 选择图片 (第2个ImageView)
2025-09-02 12:45:03 | INFO | pages.apps.ella.ella_multimodal_handler:_try_confirm_photo_selection:616 | 尝试确认照片选择
2025-09-02 12:45:33 | INFO | pages.apps.ella.ella_multimodal_handler:_try_confirm_photo_selection:651 | 未找到确认按钮，照片可能已自动选择
2025-09-02 12:45:33 | INFO | pages.apps.ella.ella_multimodal_handler:_handle_gallery_flow:157 | ✅ 图库功能流程完成
2025-09-02 12:45:33 | INFO | testcases.test_ella.base_ella_test:_execute_multimodal_operation:1245 | ✅ 多模态功能执行成功: gallery
2025-09-02 12:45:35 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:770 | 确保在对话页面...
2025-09-02 12:45:35 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-09-02 12:45:36 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-09-02 12:45:36 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-09-02 12:45:36 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-09-02 12:45:36 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:781 | ✅ 已在对话页面
2025-09-02 12:45:36 | INFO | testcases.test_ella.base_ella_test:_execute_command:887 | 📝 执行文本命令: extend the image
2025-09-02 12:45:36 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: extend the image
2025-09-02 12:45:36 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-09-02 12:45:36 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-09-02 12:45:36 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: extend the image
2025-09-02 12:45:36 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-09-02 12:45:36 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 12:45:36 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-09-02 12:45:36 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-09-02 12:45:36 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-09-02 12:45:36 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 12:45:36 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-09-02 12:45:37 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: extend the image
2025-09-02 12:45:37 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-09-02 12:45:37 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-09-02 12:45:38 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-09-02 12:45:38 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 12:45:38 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-09-02 12:45:39 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-09-02 12:45:39 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-09-02 12:45:39 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-09-02 12:45:39 | INFO | testcases.test_ella.base_ella_test:_execute_command:891 | ✅ 成功执行命令: extend the image (语音模式: False)
2025-09-02 12:45:39 | INFO | testcases.test_ella.base_ella_test:_handle_popup_after_command:178 | handle_popup_after_command:处理弹窗
2025-09-02 12:45:39 | INFO | core.popup_tool:detect_and_close_popup_once:738 | 执行单次弹窗检测和关闭
2025-09-02 12:45:40 | INFO | core.popup_tool:detect_and_close_popup_once:742 | 未检测到弹窗，无需处理
2025-09-02 12:45:40 | INFO | testcases.test_ella.base_ella_test:_get_response_timeout:1281 | 🎯 多模态命令 (gallery) 使用专用超时时间: 30秒
2025-09-02 12:46:10 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:35 | 等待AI响应，超时时间: 30秒
2025-09-02 12:46:11 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:60 | ✅ 通过TTS按钮检测到响应
2025-09-02 12:46:14 | INFO | testcases.test_ella.base_ella_test:_get_final_status_with_page_info:445 | 状态检查时当前应用包名: com.transsion.aivoiceassistant
2025-09-02 12:46:14 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:563 | 状态检查完成，现在获取响应文本
2025-09-02 12:46:14 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:567 | 第1次尝试确保在Ella页面以获取响应
2025-09-02 12:46:14 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:770 | 确保在对话页面...
2025-09-02 12:46:14 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-09-02 12:46:15 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-09-02 12:46:15 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-09-02 12:46:15 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-09-02 12:46:15 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:781 | ✅ 已在对话页面
2025-09-02 12:46:15 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:571 | ✅ 已确认在Ella对话页面，可以获取响应
2025-09-02 12:46:15 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-09-02 12:46:15 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-09-02 12:46:15 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-09-02 12:46:15 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-09-02 12:46:15 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:1335 | 检查是否在Ella页面...
2025-09-02 12:46:15 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-09-02 12:46:16 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-09-02 12:46:16 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-09-02 12:46:16 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-09-02 12:46:16 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:1344 | ✅ 当前在Ella页面
2025-09-02 12:46:16 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:140 | 获取AI响应文本
2025-09-02 12:46:17 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | asr_txt节点不存在，已达到最大重试次数
2025-09-02 12:46:18 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | robot_text节点不存在，已达到最大重试次数
2025-09-02 12:46:19 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | function_name节点不存在，已达到最大重试次数
2025-09-02 12:46:20 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | function_control节点不存在，已达到最大重试次数
2025-09-02 12:46:22 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | tv_card_chat_gpt节点不存在，已达到最大重试次数
2025-09-02 12:46:23 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | tv_top节点不存在，已达到最大重试次数
2025-09-02 12:46:24 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | tv_banner节点不存在，已达到最大重试次数
2025-09-02 12:46:25 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | tv_text节点不存在，已达到最大重试次数
2025-09-02 12:46:26 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | alarm_time_tv节点不存在，已达到最大重试次数
2025-09-02 12:46:28 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | alarm_day_tv节点不存在，已达到最大重试次数
2025-09-02 12:46:29 | WARNING | pages.apps.ella.ella_response_handler:_get_element_checked_with_retry:530 | alarm_switch节点不存在，已达到最大重试次数
2025-09-02 12:46:29 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:168 | 尝试获取其他有效的响应文本
2025-09-02 12:46:29 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:1049 | 从TextView元素获取响应
2025-09-02 12:46:29 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:1054 | 从TextView获取响应: Dialogue
2025-09-02 12:46:29 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:180 | ✅ 获取到响应文本: Dialogue
2025-09-02 12:46:29 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:1065 | 查找RecyclerView中的最新消息
2025-09-02 12:46:30 | INFO | pages.apps.ella.ella_response_handler:_extract_text_from_check_area_dump:897 | 从dump正则提取文本: Dialogue Explore Swipe down to view earlier chats Generated by AI, for reference only Remove the people from the image Exit Generation and Q&A DeepSeek-R1 Feel free to ask me any questions…
2025-09-02 12:46:30 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:180 | ✅ 获取到响应文本: Dialogue Explore Swipe down to view earlier chats Generated by AI, for reference only Remove the people from the image Exit Generation and Q&A DeepSeek-R1 Feel free to ask me any questions…
2025-09-02 12:46:30 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:185 | 未获取到有效的响应文本
2025-09-02 12:46:30 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:601 | 最终获取的AI响应: '['', '', '', '', '', '', '', '', '', '', '', 'Dialogue', 'Dialogue Explore Swipe down to view earlier chats Generated by AI, for reference only Remove the people from the image Exit Generation and Q&A DeepSeek-R1 Feel free to ask me any questions…']'
2025-09-02 12:46:30 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:412 | ✅ 多模态操作执行完成: gallery
2025-09-02 12:46:30 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\app_test\reports/screenshots\TestEllaExtendImage\test_completed.png
2025-09-02 12:46:30 | INFO | testcases.test_ella.base_ella_test:simple_command_test:1401 | 🎉 extend the image 测试完成 (多模态: gallery)
2025-09-02 12:46:30 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:1111 | verify_expected_in_response_advanced 响应类型: <class 'list'>, 搜索模式: combined, 匹配模式: 任意匹配
2025-09-02 12:46:30 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:1154 | ✅ [合并模式] 找到期望内容: 'Generated by AI'
2025-09-02 12:46:30 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:1175 | 🎉 [任意匹配模式] 找到期望内容，验证通过: 'Generated by AI'
2025-09-02 12:46:30 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\app_test\reports/screenshots\TestEllaExtendImage\test_completed.png
2025-09-02 12:46:30 | INFO | pages.apps.ella.dialogue_page:stop_app:389 | 停止Ella应用
2025-09-02 12:46:32 | INFO | pages.apps.ella.dialogue_page:stop_app:400 | ✅ Ella应用已成功停止
