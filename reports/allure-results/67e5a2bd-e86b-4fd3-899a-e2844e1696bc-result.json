{"name": "测试Generate an image of a chubby orange cat chef with a round body and an endearing appearance", "status": "passed", "description": "测试Generate an image of a chubby orange cat chef with a round body and an endearing appearance指令", "steps": [{"name": "执行命令: Generate an image of a chubby orange cat chef with a round body and an endearing appearance", "status": "passed", "steps": [{"name": "执行命令: Generate an image of a chubby orange cat chef with a round body and an endearing appearance", "status": "passed", "start": 1756797636563, "stop": 1756797660279}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "cb735a59-7512-4175-a097-807572d89452-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "83ba882b-72d7-4c9d-96d0-2721d02021f0-attachment.png", "type": "image/png"}], "start": 1756797660279, "stop": 1756797660495}], "start": 1756797636563, "stop": 1756797660496}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756797660496, "stop": 1756797660497}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "70605ce5-100b-4948-8ef5-33d174f766f5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6e1adc49-f7b1-4890-8a7c-53674f302d86-attachment.png", "type": "image/png"}], "start": 1756797660497, "stop": 1756797660696}], "attachments": [{"name": "stdout", "source": "98d71bdd-3b26-407c-91de-ebf6b1df4c1e-attachment.txt", "type": "text/plain"}], "start": 1756797636563, "stop": 1756797660697, "uuid": "2a7048bc-f8c2-4c62-a84b-e6f3e2d20465", "historyId": "00495562396e9306113e5f37378ac991", "testCaseId": "00495562396e9306113e5f37378ac991", "fullName": "testcases.test_ella.unsupported_commands.test_generate_an_image_of_a_chubby_orange_cat_chef.TestEllaOpenPlayPoliticalNews#test_generate_an_image_of_a_chubby_orange_cat_chef", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_generate_an_image_of_a_chubby_orange_cat_chef"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_generate_an_image_of_a_chubby_orange_cat_chef"}]}