{"name": "测试maximum volume能正常执行", "status": "passed", "description": "maximum volume", "steps": [{"name": "执行命令: maximum volume", "status": "passed", "steps": [{"name": "执行命令: maximum volume", "status": "passed", "start": 1756790017955, "stop": 1756790039692}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "93ec65d9-fee8-4e01-ae43-3c62c219785e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2ba006b2-979e-4904-a6f9-3a318a7d5fe0-attachment.png", "type": "image/png"}], "start": 1756790039692, "stop": 1756790039916}], "start": 1756790017955, "stop": 1756790039916}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790039916, "stop": 1756790039918}, {"name": "验证应用已打开", "status": "passed", "start": 1756790039918, "stop": 1756790039918}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7df4cc96-c613-416f-9006-d6edbdb0f830-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4ef1b9d4-f529-4bef-b3bf-3bca345e8ba6-attachment.png", "type": "image/png"}], "start": 1756790039918, "stop": 1756790040141}], "attachments": [{"name": "stdout", "source": "6f1d1cae-d828-4dd4-bfd9-e4f4f0bca1e5-attachment.txt", "type": "text/plain"}], "start": 1756790017955, "stop": 1756790040142, "uuid": "8796722f-0f5d-40b6-b8d3-a777719ba754", "historyId": "548362627ce690e10e5f8ca35d247c62", "testCaseId": "548362627ce690e10e5f8ca35d247c62", "fullName": "testcases.test_ella.system_coupling.test_maximum_volume.TestEllaMaximumVolume#test_maximum_volume", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_maximum_volume"}, {"name": "subSuite", "value": "TestEllaMaximumVolume"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_maximum_volume"}]}