{"name": "测试set compatibility mode返回正确的不支持响应", "status": "passed", "description": "验证set compatibility mode指令返回预期的不支持响应", "steps": [{"name": "执行命令: set compatibility mode", "status": "passed", "steps": [{"name": "执行命令: set compatibility mode", "status": "passed", "start": 1756801607392, "stop": 1756801630874}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "57f18597-fcb9-4567-baf9-ea6944b624c2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "14303d95-b3f4-4951-b0cd-b2030930befa-attachment.png", "type": "image/png"}], "start": 1756801630874, "stop": 1756801631124}], "start": 1756801607392, "stop": 1756801631125}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756801631125, "stop": 1756801631128}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "47e72644-6ae3-4442-a11a-40bb5150cf96-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fda8bef8-c68d-4246-84c2-f0a9754fa3ad-attachment.png", "type": "image/png"}], "start": 1756801631128, "stop": 1756801631387}], "attachments": [{"name": "stdout", "source": "713e8a4e-3940-489b-b887-d10b8302151a-attachment.txt", "type": "text/plain"}], "start": 1756801607391, "stop": 1756801631387, "uuid": "16053ff5-ce8e-4ef0-aee8-e9a40744bb21", "historyId": "aecf9a6f67cd29766190cbcc133448d2", "testCaseId": "aecf9a6f67cd29766190cbcc133448d2", "fullName": "testcases.test_ella.unsupported_commands.test_set_compatibility_mode.TestEllaSetCompatibilityMode#test_set_compatibility_mode", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_compatibility_mode"}, {"name": "subSuite", "value": "TestEllaSetCompatibilityMode"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_compatibility_mode"}]}