{"name": "测试play rock music", "status": "passed", "description": "测试play rock music指令", "steps": [{"name": "执行命令: play rock music", "status": "passed", "steps": [{"name": "执行命令: play rock music", "status": "passed", "start": 1756783932607, "stop": 1756783968605}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "291f902c-b726-4070-8e0e-c98a1bcf438b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b7d09dfa-ecd4-4c07-9228-751071306680-attachment.png", "type": "image/png"}], "start": 1756783968605, "stop": 1756783968807}], "start": 1756783932607, "stop": 1756783968808}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756783968808, "stop": 1756783968808}, {"name": "验证visha已打开", "status": "passed", "start": 1756783968809, "stop": 1756783968809}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0bbd384b-15cd-4e9d-b705-a28aa1fabfd7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "15ae24dc-c100-4839-babc-add605d7b1de-attachment.png", "type": "image/png"}], "start": 1756783968809, "stop": 1756783969015}], "attachments": [{"name": "stdout", "source": "303b5796-18c6-45d5-ad2c-d86f8b8ac216-attachment.txt", "type": "text/plain"}], "start": 1756783932607, "stop": 1756783969015, "uuid": "792e21a2-6839-4c92-9eeb-2f82c4842f01", "historyId": "6075008522e5d0ae1667c4ac4be759eb", "testCaseId": "6075008522e5d0ae1667c4ac4be759eb", "fullName": "testcases.test_ella.component_coupling.test_play_rock_music.TestEllaOpenVisha#test_play_rock_music", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_rock_music"}, {"name": "subSuite", "value": "TestEllaOpen<PERSON>a"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_rock_music"}]}