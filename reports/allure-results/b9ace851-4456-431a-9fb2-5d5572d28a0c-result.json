{"name": "测试order a burger能正常执行", "status": "passed", "description": "order a burger", "steps": [{"name": "执行命令: order a burger", "status": "passed", "steps": [{"name": "执行命令: order a burger", "status": "passed", "start": 1756794722876, "stop": 1756794744114}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "744e7faf-76dd-4102-a6a6-b493bbcdb56e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5045d16e-b040-4222-891c-ce7b63fcfd93-attachment.png", "type": "image/png"}], "start": 1756794744114, "stop": 1756794744330}], "start": 1756794722876, "stop": 1756794744331}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1756794744331, "stop": 1756794744332}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "453f03a0-0fbb-4dbe-91b5-806690f8b13e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c22f89a6-ca6d-470d-8e82-775fb2bf7f88-attachment.png", "type": "image/png"}], "start": 1756794744332, "stop": 1756794744545}], "attachments": [{"name": "stdout", "source": "9616fb8a-9d91-4665-8309-969eb14d66cf-attachment.txt", "type": "text/plain"}], "start": 1756794722876, "stop": 1756794744545, "uuid": "9dd22f06-608b-444a-a8a8-39dc5b6840ba", "historyId": "09f397887d36f3ef1e86e3c78272f1d6", "testCaseId": "09f397887d36f3ef1e86e3c78272f1d6", "fullName": "testcases.test_ella.third_coupling.test_order_a_burger.TestEllaCommandConcise#test_order_a_burger", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_order_a_burger"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_order_a_burger"}]}