{"uid": "83edc06c07f9ae9e47eb6dd1b683e4e2", "name": "packages", "children": [{"name": "testcases.test_ella", "children": [{"name": "component_coupling", "children": [{"name": "test_close_aivana", "children": [{"name": "测试close aivana能正常执行", "uid": "4ececa0fe4e031b5", "parentUid": "0a654e8ba5d406f4dced08e54877f4f8", "status": "passed", "time": {"start": 1756782764531, "stop": 1756782797316, "duration": 32785}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0a654e8ba5d406f4dced08e54877f4f8"}, {"name": "test_close_ella", "children": [{"name": "测试close ella能正常执行", "uid": "bcc6ef82ce024cd4", "parentUid": "b5a2cabf288d90878566c5ce33227175", "status": "passed", "time": {"start": 1756782813143, "stop": 1756782844882, "duration": 31739}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b5a2cabf288d90878566c5ce33227175"}, {"name": "test_close_folax", "children": [{"name": "测试close folax能正常执行", "uid": "6638be2e62d31245", "parentUid": "3b1f73acd32162c3bc6cd626dc7f1272", "status": "passed", "time": {"start": 1756782860950, "stop": 1756782892306, "duration": 31356}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3b1f73acd32162c3bc6cd626dc7f1272"}, {"name": "test_close_phonemaster", "children": [{"name": "测试close phonemaster能正常执行", "uid": "8256d09d814b048d", "parentUid": "e2aa70cb4f489d6ed135a8d54afbd69e", "status": "passed", "time": {"start": 1756782908521, "stop": 1756782929351, "duration": 20830}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e2aa70cb4f489d6ed135a8d54afbd69e"}, {"name": "test_continue_music", "children": [{"name": "测试continue music能正常执行", "uid": "687bbbe1f23f5a55", "parentUid": "6a23546ce5380ec0616c9f82a57a36ad", "status": "passed", "time": {"start": 1756782944839, "stop": 1756782965520, "duration": 20681}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6a23546ce5380ec0616c9f82a57a36ad"}, {"name": "test_create_a_metting_schedule_at_tomorrow", "children": [{"name": "测试create a metting schedule at tomorrow能正常执行", "uid": "e521c1cc203b2823", "parentUid": "6ef10c8d834a3fd9e2b41931a6d5b667", "status": "passed", "time": {"start": 1756782981491, "stop": 1756783003465, "duration": 21974}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6ef10c8d834a3fd9e2b41931a6d5b667"}, {"name": "test_delete_the_8_o_clock_alarm", "children": [{"name": "测试delete the 8 o'clock alarm", "uid": "1381636e1a26ffa3", "parentUid": "744e7a2785bdcb064b4cb0104fffb47a", "status": "passed", "time": {"start": 1756783019070, "stop": 1756783060646, "duration": 41576}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "744e7a2785bdcb064b4cb0104fffb47a"}, {"name": "test_disable_call_on_hold", "children": [{"name": "测试disable call on hold返回正确的不支持响应", "uid": "e1176be1fa1f77b9", "parentUid": "77a12ec993f1aecf9afde2b5350a0a71", "status": "passed", "time": {"start": 1756783076500, "stop": 1756783097619, "duration": 21119}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "77a12ec993f1aecf9afde2b5350a0a71"}, {"name": "test_display_the_route_go_company", "children": [{"name": "测试display the route go company", "uid": "8f4486399f2f43b", "parentUid": "e22797f7f98b084598882fb5cec440fe", "status": "passed", "time": {"start": 1756783113791, "stop": 1756783140848, "duration": 27057}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e22797f7f98b084598882fb5cec440fe"}, {"name": "test_my_phone_is_too_slow", "children": [{"name": "测试my phone is too slow能正常执行", "uid": "83e7ea6af17c7aa5", "parentUid": "9df0629aeb726d588255055629526bcb", "status": "failed", "time": {"start": 1756783156637, "stop": 1756783184599, "duration": 27962}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9df0629aeb726d588255055629526bcb"}, {"name": "test_next_channel", "children": [{"name": "测试next channel能正常执行", "uid": "6480a2d6059a8062", "parentUid": "3f23899abed5acee33c72c68b61ec080", "status": "passed", "time": {"start": 1756783200668, "stop": 1756783221540, "duration": 20872}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f23899abed5acee33c72c68b61ec080"}, {"name": "test_open_camera", "children": [{"name": "测试open camera能正常执行", "uid": "ed9712ed2962e052", "parentUid": "470a45e0cfa4244680b09ac2a8c782a2", "status": "passed", "time": {"start": 1756783237327, "stop": 1756783270237, "duration": 32910}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "470a45e0cfa4244680b09ac2a8c782a2"}, {"name": "test_open_clock", "children": [{"name": "open clock", "uid": "c254a39f81a0e8ad", "parentUid": "50daf9198779f130ca593a54f87ec128", "status": "passed", "time": {"start": 1756783288217, "stop": 1756783322503, "duration": 34286}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "50daf9198779f130ca593a54f87ec128"}, {"name": "test_open_contact", "children": [{"name": "测试open contact命令", "uid": "ecc4c8297ce83965", "parentUid": "c18b5d7a76803510c6e0d2868a096cc6", "status": "passed", "time": {"start": 1756783338391, "stop": 1756783373432, "duration": 35041}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c18b5d7a76803510c6e0d2868a096cc6"}, {"name": "test_open_countdown", "children": [{"name": "测试open countdown能正常执行", "uid": "833092c955ef1999", "parentUid": "34dd669acbf251ecc3b4bcb8039abeb9", "status": "passed", "time": {"start": 1756783389245, "stop": 1756783411084, "duration": 21839}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34dd669acbf251ecc3b4bcb8039abeb9"}, {"name": "test_open_dialer", "children": [{"name": "测试open dialer能正常执行", "uid": "b98574eddb739721", "parentUid": "9072ee3e27d0b6c6a2866a718c2df078", "status": "passed", "time": {"start": 1756783427072, "stop": 1756783457631, "duration": 30559}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9072ee3e27d0b6c6a2866a718c2df078"}, {"name": "test_open_ella", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "340e44219094e4e0", "parentUid": "4ee54c4b3bb105e867eb3473949bd494", "status": "passed", "time": {"start": 1756783473804, "stop": 1756783494209, "duration": 20405}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4ee54c4b3bb105e867eb3473949bd494"}, {"name": "test_open_folax", "children": [{"name": "测试open folax能正常执行", "uid": "d40b3ff68a0762c6", "parentUid": "e02fc6e8f5b7a4fa147ce01474445b5b", "status": "passed", "time": {"start": 1756783510373, "stop": 1756783531794, "duration": 21421}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e02fc6e8f5b7a4fa147ce01474445b5b"}, {"name": "test_open_phone", "children": [{"name": "测试open contact命令", "uid": "a884ae420d086a2e", "parentUid": "342f26d794168c8355629898d89335fe", "status": "passed", "time": {"start": 1756783548048, "stop": 1756783578842, "duration": 30794}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "342f26d794168c8355629898d89335fe"}, {"name": "test_pause_fm", "children": [{"name": "测试pause fm能正常执行", "uid": "ef317590cd47e820", "parentUid": "83a06f2be77f3b44f6a099f43b575dbf", "status": "passed", "time": {"start": 1756783594695, "stop": 1756783615603, "duration": 20908}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83a06f2be77f3b44f6a099f43b575dbf"}, {"name": "test_pause_music", "children": [{"name": "测试pause music能正常执行", "uid": "770c79cb377aa8fa", "parentUid": "a3026ab8ba7d276b771465a73b8cd69f", "status": "passed", "time": {"start": 1756783631531, "stop": 1756783652296, "duration": 20765}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a3026ab8ba7d276b771465a73b8cd69f"}, {"name": "test_pause_song", "children": [{"name": "测试pause song能正常执行", "uid": "588d065cdd20825e", "parentUid": "1a65a9bcb8b35f4b071220b80ad32958", "status": "passed", "time": {"start": 1756783668536, "stop": 1756783688728, "duration": 20192}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1a65a9bcb8b35f4b071220b80ad32958"}, {"name": "test_phone_boost", "children": [{"name": "测试phone boost能正常执行", "uid": "89a8de8c8fe0463d", "parentUid": "e9cea1e20f3779ed164d139ee8b570ab", "status": "failed", "time": {"start": 1756783705036, "stop": 1756783732834, "duration": 27798}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e9cea1e20f3779ed164d139ee8b570ab"}, {"name": "test_play_afro_strut", "children": [{"name": "测试play afro strut", "uid": "d5ea57909850627e", "parentUid": "51e1e1cd02cc1154b480c322b15c41c7", "status": "passed", "time": {"start": 1756783749054, "stop": 1756783786399, "duration": 37345}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "51e1e1cd02cc1154b480c322b15c41c7"}, {"name": "test_play_jay_chou_s_music", "children": [{"name": "测试play jay chou's music", "uid": "e41046bfd275ea61", "parentUid": "80ffd53a52b22963552b5b4f8ad4a69c", "status": "passed", "time": {"start": 1756783801827, "stop": 1756783832526, "duration": 30699}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "80ffd53a52b22963552b5b4f8ad4a69c"}, {"name": "test_play_jay_chou_s_music_by_spotify", "children": [{"name": "测试play jay chou's music by spotify", "uid": "2def690b31b4a7f4", "parentUid": "fb3c6fdc98f3f31c0cad30d7f977b25b", "status": "passed", "time": {"start": 1756783847748, "stop": 1756783870607, "duration": 22859}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb3c6fdc98f3f31c0cad30d7f977b25b"}, {"name": "test_play_music", "children": [{"name": "测试play music", "uid": "b3955e865876adaa", "parentUid": "012cce9a22125b4f9274376f463a21a6", "status": "passed", "time": {"start": 1756783886271, "stop": 1756783916818, "duration": 30547}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "012cce9a22125b4f9274376f463a21a6"}, {"name": "test_play_rock_music", "children": [{"name": "测试play rock music", "uid": "21a57458a1be039e", "parentUid": "5fe073acfe44a1d8a05ea050b2df638f", "status": "passed", "time": {"start": 1756783932607, "stop": 1756783969015, "duration": 36408}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5fe073acfe44a1d8a05ea050b2df638f"}, {"name": "test_play_sun_be_song_of_jide_chord", "children": [{"name": "测试play sun be song of jide chord", "uid": "8476c90c6af75578", "parentUid": "e61c3229af1d310b3286353485e7dc26", "status": "passed", "time": {"start": 1756783984131, "stop": 1756784015214, "duration": 31083}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e61c3229af1d310b3286353485e7dc26"}, {"name": "test_previous_music", "children": [{"name": "测试previous music能正常执行", "uid": "56e85e9a688e148", "parentUid": "a7e1f637551ed2330d6d67ed54a71f33", "status": "passed", "time": {"start": 1756784030802, "stop": 1756784050900, "duration": 20098}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a7e1f637551ed2330d6d67ed54a71f33"}, {"name": "test_record_audio_for_seconds", "children": [{"name": "测试record audio for 5 seconds能正常执行", "uid": "f25f9b79b50dcf3f", "parentUid": "cfa8b6a3fd06497697d78e23fab251dd", "status": "failed", "time": {"start": 1756784066621, "stop": 1756784087815, "duration": 21194}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cfa8b6a3fd06497697d78e23fab251dd"}, {"name": "test_resume_music", "children": [{"name": "测试resume music能正常执行", "uid": "8e9729d713bd0d91", "parentUid": "0f680c0e3c91eaed54cda04d37208ed3", "status": "passed", "time": {"start": 1756784103472, "stop": 1756784125072, "duration": 21600}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0f680c0e3c91eaed54cda04d37208ed3"}, {"name": "test_set_an_alarm_at_8_am", "children": [{"name": "测试set an alarm at 8 am", "uid": "7da06f5f51b44735", "parentUid": "934efa6c18aa0ed520853ffbd4ef3bd7", "status": "passed", "time": {"start": 1756784140777, "stop": 1756784203117, "duration": 62340}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "934efa6c18aa0ed520853ffbd4ef3bd7"}, {"name": "test_stop_playing", "children": [{"name": "测试stop playing", "uid": "bfdaecc28ba1ba81", "parentUid": "c598fac3e058ad96b70d8acf40dc13d8", "status": "passed", "time": {"start": 1756784219175, "stop": 1756784241712, "duration": 22537}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c598fac3e058ad96b70d8acf40dc13d8"}, {"name": "test_take_a_screenshot", "children": [{"name": "测试take a screenshot能正常执行", "uid": "923407fa8707e094", "parentUid": "c1793294d28b52937b4f0aa9a0fee952", "status": "passed", "time": {"start": 1756784258037, "stop": 1756784283710, "duration": 25673}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c1793294d28b52937b4f0aa9a0fee952"}, {"name": "test_turn_off_the_7_am_alarm", "children": [{"name": "测试turn off the 7AM alarm", "uid": "fa36a673ebbfe035", "parentUid": "32bfb55cdaddbf6d8fd789b810c908d9", "status": "passed", "time": {"start": 1756784300586, "stop": 1756784388849, "duration": 88263}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "32bfb55cdaddbf6d8fd789b810c908d9"}, {"name": "test_turn_off_the_8_am_alarm", "children": [{"name": "测试turn off the 8 am alarm", "uid": "7cbe0c58d8f729b", "parentUid": "568593413cf95986c665f08593ea21e9", "status": "passed", "time": {"start": 1756784404787, "stop": 1756784489893, "duration": 85106}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "568593413cf95986c665f08593ea21e9"}, {"name": "test_turn_on_the_alarm_at_8_am", "children": [{"name": "测试turn on the alarm at 8 am", "uid": "5da98a53de32cd9f", "parentUid": "739e8257af5cd9a36c4e8e1ddaad5c24", "status": "passed", "time": {"start": 1756784505778, "stop": 1756784592029, "duration": 86251}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "739e8257af5cd9a36c4e8e1ddaad5c24"}, {"name": "test_what_s_the_weather_like_in_shanghai_today", "children": [{"name": "测试What's the weather like in Shanghai today能正常执行", "uid": "a893c0e2b18cf2e9", "parentUid": "85d39690a527a90b7d30ade2fdd4a8fc", "status": "passed", "time": {"start": 1756784607957, "stop": 1756784638578, "duration": 30621}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "85d39690a527a90b7d30ade2fdd4a8fc"}], "uid": "155057a0afc640ed94e8c8d7c444a680"}, {"name": "dialogue", "children": [{"name": "test_appeler_maman", "children": [{"name": "测试appeler maman能正常执行", "uid": "5d899eb4a5cb81e", "parentUid": "7954f72724ef95cff1433b0efbee5646", "status": "passed", "time": {"start": 1756784654317, "stop": 1756784675971, "duration": 21654}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7954f72724ef95cff1433b0efbee5646"}, {"name": "test_book_a_flight_to_paris", "children": [{"name": "测试book a flight to paris返回正确的不支持响应", "uid": "d29d064c93a63c", "parentUid": "8f0bd634d6e7676a3a49d6dff49497fa", "status": "passed", "time": {"start": 1756784691763, "stop": 1756784716011, "duration": 24248}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8f0bd634d6e7676a3a49d6dff49497fa"}, {"name": "test_call_mom_through_whatsapp", "children": [{"name": "测试call mom through whatsapp能正常执行", "uid": "70eca5e92b601f79", "parentUid": "8541ffcf150af606538b232e3aea7434", "status": "failed", "time": {"start": 1756784731984, "stop": 1756784762486, "duration": 30502}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8541ffcf150af606538b232e3aea7434"}, {"name": "test_can_you_give_me_a_coin", "children": [{"name": "测试can you give me a coin能正常执行", "uid": "7729eaed52204675", "parentUid": "7e22a70303a1dcd81523a4e5a786a8c4", "status": "passed", "time": {"start": 1756784778659, "stop": 1756784801846, "duration": 23187}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7e22a70303a1dcd81523a4e5a786a8c4"}, {"name": "test_cannot_login_in_google_email_box", "children": [{"name": "测试cannot login in google email box能正常执行", "uid": "fb0f70d6750cab8f", "parentUid": "8c0c4d699c270397d14ca2a01dc1ee04", "status": "passed", "time": {"start": 1756784817685, "stop": 1756784838777, "duration": 21092}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c0c4d699c270397d14ca2a01dc1ee04"}, {"name": "test_check_status_updates_on_whatsapp", "children": [{"name": "测试check status updates on whatsapp能正常执行", "uid": "8d5587d0aef71df8", "parentUid": "4ea0f2069633d579cb2aa7e3f4dee562", "status": "passed", "time": {"start": 1756784854708, "stop": 1756784879274, "duration": 24566}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4ea0f2069633d579cb2aa7e3f4dee562"}, {"name": "test_close_whatsapp", "children": [{"name": "测试close whatsapp能正常执行", "uid": "25d9486b74a6bc6", "parentUid": "254be578575e3bd49816bef9d50f165f", "status": "passed", "time": {"start": 1756784895037, "stop": 1756784917606, "duration": 22569}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "254be578575e3bd49816bef9d50f165f"}, {"name": "test_continue_playing", "children": [{"name": "测试continue playing能正常执行", "uid": "b8aa8895f47b3ea0", "parentUid": "d1d509a5f33ff7082393c4843543ffff", "status": "passed", "time": {"start": 1756784933392, "stop": 1756784958016, "duration": 24624}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d1d509a5f33ff7082393c4843543ffff"}, {"name": "test_could_you_please_search_an_for_me", "children": [{"name": "测试could you please search an for me能正常执行", "uid": "3349acc16a24c334", "parentUid": "7327508f376f094b8e8fcbe0310288ab", "status": "passed", "time": {"start": 1756784973965, "stop": 1756784997341, "duration": 23376}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7327508f376f094b8e8fcbe0310288ab"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "测试disable magic voice changer能正常执行", "uid": "ebd852d7bf2f6c5f", "parentUid": "3622231925f5d7d87dfc0a1200f98b66", "status": "passed", "time": {"start": 1756785013375, "stop": 1756785035555, "duration": 22180}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3622231925f5d7d87dfc0a1200f98b66"}, {"name": "test_give_me_some_money", "children": [{"name": "测试give me some money能正常执行", "uid": "20e4493209526e19", "parentUid": "690fab1bc7060b99c06d337a10d44d74", "status": "passed", "time": {"start": 1756785051540, "stop": 1756785075700, "duration": 24160}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "690fab1bc7060b99c06d337a10d44d74"}, {"name": "test_global_gdp_trends", "children": [{"name": "测试global gdp trends能正常执行", "uid": "8418a814d2f54251", "parentUid": "76c541180f5aa46cc8bcc707c7d9c16f", "status": "passed", "time": {"start": 1756785091617, "stop": 1756785116395, "duration": 24778}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "76c541180f5aa46cc8bcc707c7d9c16f"}, {"name": "test_go_on_playing_fm", "children": [{"name": "测试go on playing fm能正常执行", "uid": "4d2bb9ea5aabddff", "parentUid": "f0bcda9b9eeb890ffe415969c037c977", "status": "passed", "time": {"start": 1756785132322, "stop": 1756785156930, "duration": 24608}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f0bcda9b9eeb890ffe415969c037c977"}, {"name": "test_hello_hello", "children": [{"name": "测试hello hello能正常执行", "uid": "686460907e75164b", "parentUid": "b9ee9e8a38234ec69db09f78700d7fbb", "status": "failed", "time": {"start": 1756785173872, "stop": 1756785200265, "duration": 26393}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b9ee9e8a38234ec69db09f78700d7fbb"}, {"name": "test_help_me_write_an_email_to_make_an_appointment_for_a_visit", "children": [{"name": "测试Help me write an email to make an appointment for a visit能正常执行", "uid": "3a70a41434693d91", "parentUid": "c54c88063ac1edf2e569991ff95d16e9", "status": "passed", "time": {"start": 1756785217254, "stop": 1756785257561, "duration": 40307}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c54c88063ac1edf2e569991ff95d16e9"}, {"name": "test_hi", "children": [{"name": "测试hi能正常执行", "uid": "df5cb6fa8a521be5", "parentUid": "acfbc4c8f3f5adee7b09787ceaa2dd0d", "status": "passed", "time": {"start": 1756785273643, "stop": 1756785300926, "duration": 27283}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "acfbc4c8f3f5adee7b09787ceaa2dd0d"}, {"name": "test_how_is_the_weather_today", "children": [{"name": "测试how is the weather today能正常执行", "uid": "67de07d83afe565a", "parentUid": "35fc4938c7d094c99682c376792faffe", "status": "failed", "time": {"start": 1756785316739, "stop": 1756785345320, "duration": 28581}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "35fc4938c7d094c99682c376792faffe"}, {"name": "test_how_is_the_wheather_today", "children": [{"name": "测试how is the wheather today能正常执行", "uid": "22e78af1ed0eda9d", "parentUid": "ccd640573d805b985ffb9441df6e5fe3", "status": "failed", "time": {"start": 1756785361085, "stop": 1756785383364, "duration": 22279}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ccd640573d805b985ffb9441df6e5fe3"}, {"name": "test_how_s_the_weather_today", "children": [{"name": "测试how's the weather today?返回正确的不支持响应", "uid": "82a3530fe03c9589", "parentUid": "addae1e5b6cb9e45cb30b52b0519d0df", "status": "failed", "time": {"start": 1756785399612, "stop": 1756785427655, "duration": 28043}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "addae1e5b6cb9e45cb30b52b0519d0df"}, {"name": "test_how_s_the_weather_today_in_shanghai", "children": [{"name": "测试how's the weather today in shanghai能正常执行", "uid": "10b95a85115cb321", "parentUid": "5cbfc9081ff73a6bd530b2831a9e3384", "status": "passed", "time": {"start": 1756785443641, "stop": 1756785473745, "duration": 30104}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5cbfc9081ff73a6bd530b2831a9e3384"}, {"name": "test_how_to_say_hello_in_french", "children": [{"name": "测试how to say hello in french能正常执行", "uid": "59c466aa5d047c4e", "parentUid": "58f878e12ecf6b0d82d8bb49999f4d47", "status": "passed", "time": {"start": 1756785489373, "stop": 1756785513555, "duration": 24182}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "58f878e12ecf6b0d82d8bb49999f4d47"}, {"name": "test_how_to_say_i_love_you_in_french", "children": [{"name": "测试how to say i love you in french能正常执行", "uid": "be03d3832b019f11", "parentUid": "82fbe34286468477e9134feb14dde382", "status": "passed", "time": {"start": 1756785529738, "stop": 1756785554992, "duration": 25254}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "82fbe34286468477e9134feb14dde382"}, {"name": "test_i_wanna_be_rich", "children": [{"name": "测试i wanna be rich能正常执行", "uid": "bd3a290a28dd8d2d", "parentUid": "e8c194f64f4bc6de5e59e9c7b048cfe4", "status": "passed", "time": {"start": 1756785571644, "stop": 1756785596581, "duration": 24937}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e8c194f64f4bc6de5e59e9c7b048cfe4"}, {"name": "test_i_want_to_listen_to_fm", "children": [{"name": "测试i want to listen to fm能正常执行", "uid": "d7bf4937d6857b76", "parentUid": "62c1ecd6121de1385415edcb14e97d2c", "status": "passed", "time": {"start": 1756785612556, "stop": 1756785634414, "duration": 21858}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "62c1ecd6121de1385415edcb14e97d2c"}, {"name": "test_i_want_to_make_a_call", "children": [{"name": "测试i want to make a call能正常执行", "uid": "da5f18b688d5052d", "parentUid": "3a5af3715ffe87f7125f4fc9d4a39a79", "status": "passed", "time": {"start": 1756785650765, "stop": 1756785672845, "duration": 22080}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3a5af3715ffe87f7125f4fc9d4a39a79"}, {"name": "test_i_want_to_watch_fireworks", "children": [{"name": "测试i want to watch fireworks能正常执行", "uid": "aa4f63f89a01889f", "parentUid": "2d9bf69b53882c59bc26f9d5bf23b4c7", "status": "passed", "time": {"start": 1756785689138, "stop": 1756785714039, "duration": 24901}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d9bf69b53882c59bc26f9d5bf23b4c7"}, {"name": "test_introduce_yourself", "children": [{"name": "测试introduce yourself能正常执行", "uid": "6152ce64ee8a0ae9", "parentUid": "472715b25ea329acf3f408d9a469274a", "status": "passed", "time": {"start": 1756785730270, "stop": 1756785754333, "duration": 24063}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "472715b25ea329acf3f408d9a469274a"}, {"name": "test_last_channel", "children": [{"name": "测试last channel能正常执行", "uid": "b011767611ee9cbe", "parentUid": "1673f8e5a00bba040872744c10466a1a", "status": "passed", "time": {"start": 1756785770913, "stop": 1756785794521, "duration": 23608}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1673f8e5a00bba040872744c10466a1a"}, {"name": "test_listen_to_fm", "children": [{"name": "测试listen to fm能正常执行", "uid": "aed36461473d14ce", "parentUid": "3f8cc40b215a2f65db4811e729bc65fd", "status": "passed", "time": {"start": 1756785810730, "stop": 1756785833128, "duration": 22398}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f8cc40b215a2f65db4811e729bc65fd"}, {"name": "test_make_a_call", "children": [{"name": "测试make a call能正常执行", "uid": "af7d50665a230f0c", "parentUid": "11ee179329ce36ecafe38100e52ec70d", "status": "passed", "time": {"start": 1756785848657, "stop": 1756785870248, "duration": 21591}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "11ee179329ce36ecafe38100e52ec70d"}, {"name": "test_measure_blood_oxygen", "children": [{"name": "测试measure blood oxygen", "uid": "64d3523a0b23c672", "parentUid": "2707f2dd651484f2df0cab1faf1ff403", "status": "passed", "time": {"start": 1756785886173, "stop": 1756785907891, "duration": 21718}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2707f2dd651484f2df0cab1faf1ff403"}, {"name": "test_measure_heart_rate", "children": [{"name": "测试measure heart rate", "uid": "3f4f9f42b17ddb4e", "parentUid": "df37b64c34a0bb9e92aee8469cf785c9", "status": "passed", "time": {"start": 1756785923733, "stop": 1756785945259, "duration": 21526}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df37b64c34a0bb9e92aee8469cf785c9"}, {"name": "test_next_music", "children": [{"name": "测试next music能正常执行", "uid": "53f845ae02607e8c", "parentUid": "23537c9370c1a822549b7a79eb682856", "status": "passed", "time": {"start": 1756785961291, "stop": 1756785985151, "duration": 23860}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "23537c9370c1a822549b7a79eb682856"}, {"name": "test_next_song", "children": [{"name": "测试next song能正常执行", "uid": "9e71aa520d7f5088", "parentUid": "036f43ecd5f7343046e2af56838f0b2c", "status": "passed", "time": {"start": 1756786001256, "stop": 1756786024035, "duration": 22779}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "036f43ecd5f7343046e2af56838f0b2c"}, {"name": "test_open_app", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "efedcfdc8f1999a4", "parentUid": "c969d4a520c25845a9e33a24d4e3b7a4", "status": "passed", "time": {"start": 1756786039977, "stop": 1756786063372, "duration": 23395}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c969d4a520c25845a9e33a24d4e3b7a4"}, {"name": "test_pause_music", "children": [{"name": "测试pause music能正常执行", "uid": "19ae44f5cafb8a3a", "parentUid": "80d0cfc5ba53853ee4e5c374ac3ca7b4", "status": "passed", "time": {"start": 1756786079262, "stop": 1756786101475, "duration": 22213}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "80d0cfc5ba53853ee4e5c374ac3ca7b4"}, {"name": "test_play_music_by_VLC", "children": [{"name": "测试play music by VLC", "uid": "7572fb71f999e818", "parentUid": "0cffe8d8bb255395290a9dd513723b30", "status": "passed", "time": {"start": 1756786117164, "stop": 1756786141617, "duration": 24453}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0cffe8d8bb255395290a9dd513723b30"}, {"name": "test_play_music_by_boomplay", "children": [{"name": "测试play music by boomplay", "uid": "d17d13a14891bba0", "parentUid": "973892d18717ef61a9c60231ae0888fd", "status": "passed", "time": {"start": 1756786157378, "stop": 1756786181634, "duration": 24256}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "973892d18717ef61a9c60231ae0888fd"}, {"name": "test_play_music_by_visha", "children": [{"name": "测试play music by visha", "uid": "6eba3a48a4188e1d", "parentUid": "7b9d8d24ad1638d28448c5d76170b0db", "status": "passed", "time": {"start": 1756786197618, "stop": 1756786230984, "duration": 33366}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b9d8d24ad1638d28448c5d76170b0db"}, {"name": "test_play_music_by_yandex_music", "children": [{"name": "测试play music by yandex music", "uid": "797a83e0c356b255", "parentUid": "480e35527d7a53156164d862f3505bc3", "status": "passed", "time": {"start": 1756786246613, "stop": 1756786270510, "duration": 23897}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "480e35527d7a53156164d862f3505bc3"}, {"name": "test_play_music_on_boomplayer", "children": [{"name": "测试play music on boomplayer", "uid": "b875b96724b58334", "parentUid": "b9e3972a06a525f9a243e4a5ad6d8284", "status": "passed", "time": {"start": 1756786286433, "stop": 1756786310288, "duration": 23855}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b9e3972a06a525f9a243e4a5ad6d8284"}, {"name": "test_play_music_on_visha", "children": [{"name": "测试play music on visha", "uid": "cb95f5ad0b201d87", "parentUid": "45b5c17a13f2f2c58c18e0c5403b8174", "status": "passed", "time": {"start": 1756786325948, "stop": 1756786358825, "duration": 32877}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "45b5c17a13f2f2c58c18e0c5403b8174"}, {"name": "test_play_news", "children": [{"name": "测试play news", "uid": "819174788d9bc35", "parentUid": "0545a1fc4a946cb1c690d92255face5c", "status": "passed", "time": {"start": 1756786374822, "stop": 1756786399999, "duration": 25177}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0545a1fc4a946cb1c690d92255face5c"}, {"name": "test_play_political_news", "children": [{"name": "测试play political news", "uid": "1316579bfb181bf1", "parentUid": "350bcef11f4fb6eb99f97d3fa6fe1d47", "status": "passed", "time": {"start": 1756786415721, "stop": 1756786442462, "duration": 26741}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "350bcef11f4fb6eb99f97d3fa6fe1d47"}, {"name": "test_previous_song", "children": [{"name": "测试previous song能正常执行", "uid": "cfc2b0b0313517b6", "parentUid": "4277679ed8d0b781135c825e64cd8c91", "status": "passed", "time": {"start": 1756786458235, "stop": 1756786480142, "duration": 21907}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4277679ed8d0b781135c825e64cd8c91"}, {"name": "test_remove_alarms", "children": [{"name": "测试remove alarms能正常执行", "uid": "f82326702960f3e2", "parentUid": "a37f4999e35eb21b851cb519de2c19b5", "status": "passed", "time": {"start": 1756786495456, "stop": 1756786559922, "duration": 64466}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a37f4999e35eb21b851cb519de2c19b5"}, {"name": "test_search_my_gallery_for_food_pictures", "children": [{"name": "测试search my gallery for food pictures能正常执行", "uid": "dc837eb6bb86fa70", "parentUid": "cdb79778de81160b7231a87afe6bd1ba", "status": "passed", "time": {"start": 1756786576006, "stop": 1756786602331, "duration": 26325}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cdb79778de81160b7231a87afe6bd1ba"}, {"name": "test_search_picture_in_my_gallery", "children": [{"name": "测试search picture in my gallery能正常执行", "uid": "bbdccaafdedef1be", "parentUid": "2ee81b68d6fe8782f4c16078e5ded646", "status": "failed", "time": {"start": 1756786618211, "stop": 1756786662130, "duration": 43919}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2ee81b68d6fe8782f4c16078e5ded646"}, {"name": "test_searching_for_a_method_of_violent_murder", "children": [{"name": "测试searching for a method of violent murder能正常执行", "uid": "e1ef419137abc43d", "parentUid": "f3e7c7fb3655fd1be27c6d251a6f492c", "status": "passed", "time": {"start": 1756786678450, "stop": 1756786700646, "duration": 22196}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f3e7c7fb3655fd1be27c6d251a6f492c"}, {"name": "test_send_my_recent_photos_to_mom_through_whatsapp", "children": [{"name": "测试send my recent photos to mom through whatsapp能正常执行", "uid": "4b0ca3a6abd6b339", "parentUid": "11264fcbfd285641cac64cf3c0f1f4a8", "status": "passed", "time": {"start": 1756786716846, "stop": 1756786742170, "duration": 25324}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "11264fcbfd285641cac64cf3c0f1f4a8"}, {"name": "test_show_me_premier_le<PERSON><PERSON>_goal_ranking", "children": [{"name": "测试show me premier leaguage goal ranking能正常执行", "uid": "3125024931f92f88", "parentUid": "51407e55f461bb75e0ea1d0bcd974a5d", "status": "passed", "time": {"start": 1756786758546, "stop": 1756786788998, "duration": 30452}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "51407e55f461bb75e0ea1d0bcd974a5d"}, {"name": "test_show_my_all_alarms", "children": [{"name": "测试show my all alarms能正常执行", "uid": "8b2d8b53a744bc7c", "parentUid": "7d58aebf2bec28bf77ceb0d213b422ee", "status": "passed", "time": {"start": 1756786805458, "stop": 1756786851333, "duration": 45875}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7d58aebf2bec28bf77ceb0d213b422ee"}, {"name": "test_show_scores_between_livepool_and_manchester_city", "children": [{"name": "测试show scores between livepool and manchester city能正常执行", "uid": "295e8404d7e3f49a", "parentUid": "390af3b968f43ff01d77f8b7960b3916", "status": "passed", "time": {"start": 1756786867722, "stop": 1756786893218, "duration": 25496}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "390af3b968f43ff01d77f8b7960b3916"}, {"name": "test_start_countdown", "children": [{"name": "测试start countdown能正常执行", "uid": "d4aea90612e8f58e", "parentUid": "7f509a668a017ed18e7c0a0484490f1d", "status": "passed", "time": {"start": 1756786909154, "stop": 1756786932079, "duration": 22925}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7f509a668a017ed18e7c0a0484490f1d"}, {"name": "test_stop_music", "children": [{"name": "测试stop music能正常执行", "uid": "1323d7692a2d53a", "parentUid": "77eaeb306e34a7a9bfb4cedeba8ee35f", "status": "passed", "time": {"start": 1756786948780, "stop": 1756786971565, "duration": 22785}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "77eaeb306e34a7a9bfb4cedeba8ee35f"}, {"name": "test_stop_run", "children": [{"name": "测试stop run能正常执行", "uid": "3ce21b38743deb23", "parentUid": "ccf9f3b4d1f905064aacc7d3127597b9", "status": "passed", "time": {"start": 1756786988325, "stop": 1756787010694, "duration": 22369}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ccf9f3b4d1f905064aacc7d3127597b9"}, {"name": "test_stop_workout", "children": [{"name": "测试stop workout能正常执行", "uid": "ec1bded3e5bd05b6", "parentUid": "8ee49369bb9d601da7f062855654c904", "status": "passed", "time": {"start": 1756787027702, "stop": 1756787050850, "duration": 23148}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8ee49369bb9d601da7f062855654c904"}, {"name": "test_summarize_content_on_this_page", "children": [{"name": "测试summarize content on this page能正常执行", "uid": "6b3eee10e2840156", "parentUid": "b6a6c5c1cc3bbfd99be0b798596d90fd", "status": "passed", "time": {"start": 1756787067617, "stop": 1756787090469, "duration": 22852}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b6a6c5c1cc3bbfd99be0b798596d90fd"}, {"name": "test_take_a_joke", "children": [{"name": "测试take a joke能正常执行", "uid": "e8922b9c77833bc3", "parentUid": "a1543d72151cd8524884aba6ef4613f6", "status": "passed", "time": {"start": 1756787106916, "stop": 1756787131818, "duration": 24902}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a1543d72151cd8524884aba6ef4613f6"}, {"name": "test_take_a_note_on_how_to_build_a_treehouse", "children": [{"name": "测试take a note on how to build a treehouse能正常执行", "uid": "b2dcd70c69442a2d", "parentUid": "218955f9fce08124722f8aa63ee5293a", "status": "passed", "time": {"start": 1756787148348, "stop": 1756787172498, "duration": 24150}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "218955f9fce08124722f8aa63ee5293a"}, {"name": "test_take_notes_on_how_to_build_a_treehouse", "children": [{"name": "测试take notes on how to build a treehouse能正常执行", "uid": "f54bc623999fcd83", "parentUid": "acd0d4c0ff78ae721b07111ac720141b", "status": "passed", "time": {"start": 1756787189128, "stop": 1756787216843, "duration": 27715}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "acd0d4c0ff78ae721b07111ac720141b"}, {"name": "test_tell_me_a_joke", "children": [{"name": "测试tell me a joke能正常执行", "uid": "7ab2f693890d19f2", "parentUid": "1efd42262e16a36d4ac595eed9b7e185", "status": "passed", "time": {"start": 1756787233084, "stop": 1756787257461, "duration": 24377}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1efd42262e16a36d4ac595eed9b7e185"}, {"name": "test_unset_alarms", "children": [{"name": "测试unset alarms能正常执行", "uid": "68461f73317f8741", "parentUid": "8eb34f4ad03f2b3301605936af5f6db6", "status": "passed", "time": {"start": 1756787273934, "stop": 1756787319086, "duration": 45152}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8eb34f4ad03f2b3301605936af5f6db6"}, {"name": "test_video_call_mom_through_whatsapp", "children": [{"name": "测试video call mom through whatsapp能正常执行", "uid": "bfa07e72898e462", "parentUid": "f8daa61044336f258fd904820f097662", "status": "passed", "time": {"start": 1756787335445, "stop": 1756787365973, "duration": 30528}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f8daa61044336f258fd904820f097662"}, {"name": "test_view_recent_alarms", "children": [{"name": "测试view recent alarms能正常执行", "uid": "8354314c9786cba4", "parentUid": "42362c26e7ec72bda0a34e7ccd060ca6", "status": "passed", "time": {"start": 1756787382267, "stop": 1756787450233, "duration": 67966}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "42362c26e7ec72bda0a34e7ccd060ca6"}, {"name": "test_what_is_apec", "children": [{"name": "测试what is apec?能正常执行", "uid": "3f2448227e5c6448", "parentUid": "4567e57227f42cc7993892d86e523dbf", "status": "passed", "time": {"start": 1756787466410, "stop": 1756787490849, "duration": 24439}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4567e57227f42cc7993892d86e523dbf"}, {"name": "test_what_languages_do_you_support", "children": [{"name": "测试What languages do you support能正常执行", "uid": "884af117c9f1e64a", "parentUid": "20b964271968b9aba9b13a138825e688", "status": "passed", "time": {"start": 1756787507248, "stop": 1756787529170, "duration": 21922}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "20b964271968b9aba9b13a138825e688"}, {"name": "test_what_s_the_weather_like_today", "children": [{"name": "测试What's the weather like today能正常执行", "uid": "212c475735d838d9", "parentUid": "51399e7e2539b443ff346f3e8db98136", "status": "failed", "time": {"start": 1756787545605, "stop": 1756787574866, "duration": 29261}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "51399e7e2539b443ff346f3e8db98136"}, {"name": "test_what_s_the_weather_today", "children": [{"name": "测试what·s the weather today？能正常执行", "uid": "9f56622ca38b4d9a", "parentUid": "af2f763310dd868fb7293de462763b64", "status": "failed", "time": {"start": 1756787591318, "stop": 1756787619867, "duration": 28549}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "af2f763310dd868fb7293de462763b64"}, {"name": "test_what_s_the_wheather_today", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "14dca835eaa3e362", "parentUid": "b880b898b5ae6eb8576adc4f20cdd8c6", "status": "failed", "time": {"start": 1756787636254, "stop": 1756787659044, "duration": 22790}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b880b898b5ae6eb8576adc4f20cdd8c6"}, {"name": "test_what_s_your_name", "children": [{"name": "测试what's your name？能正常执行", "uid": "ed7c80d2c53bd6ea", "parentUid": "4b64931505303134ebf07e49f3a9c0af", "status": "failed", "time": {"start": 1756787675291, "stop": 1756787699652, "duration": 24361}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4b64931505303134ebf07e49f3a9c0af"}, {"name": "test_what_time_is_it_now", "children": [{"name": "测试what time is it now能正常执行", "uid": "269a59f7fb8e877", "parentUid": "6a367b30c7c610464ef1a8acfa0f93c9", "status": "passed", "time": {"start": 1756787716190, "stop": 1756787739310, "duration": 23120}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6a367b30c7c610464ef1a8acfa0f93c9"}, {"name": "test_whats_the_weather_today", "children": [{"name": "测试what's the weather today?能正常执行", "uid": "4b3f570b5f7eb87c", "parentUid": "d38e79c83442fa3e23fba8b6ba34de56", "status": "failed", "time": {"start": 1756787755960, "stop": 1756787785774, "duration": 29814}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d38e79c83442fa3e23fba8b6ba34de56"}, {"name": "test_who_is_harry_potter", "children": [{"name": "测试who is harry potter能正常执行", "uid": "78a6c4053c880c26", "parentUid": "651e0dc624a6f14649ebc34a90907bc1", "status": "passed", "time": {"start": 1756787802282, "stop": 1756787827315, "duration": 25033}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "651e0dc624a6f14649ebc34a90907bc1"}, {"name": "test_who_is_j_k_rowling", "children": [{"name": "测试who is j k rowling能正常执行", "uid": "b27ee5d04f31f563", "parentUid": "9e3d19482db247c568f3393a2db09f1f", "status": "passed", "time": {"start": 1756787843573, "stop": 1756787867523, "duration": 23950}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e3d19482db247c568f3393a2db09f1f"}, {"name": "test_why_is_my_phone_not_ringing_on_incoming_calls", "children": [{"name": "测试why is my phone not ringing on incoming calls能正常执行", "uid": "ec30640219f0fab9", "parentUid": "d08c8b89f0cacddcb2f61563717a9da6", "status": "passed", "time": {"start": 1756787884067, "stop": 1756787908431, "duration": 24364}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d08c8b89f0cacddcb2f61563717a9da6"}, {"name": "test_why_my_charging_is_so_slow", "children": [{"name": "测试why my charging is so slow能正常执行", "uid": "5bd17856a6066ce", "parentUid": "015300f55fc1d98de4057611dffc7dd0", "status": "passed", "time": {"start": 1756787925066, "stop": 1756787947951, "duration": 22885}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "015300f55fc1d98de4057611dffc7dd0"}], "uid": "9c28642eff039d4ac586a58d4f4a0368"}, {"name": "self_function", "children": [{"name": "test_a_cute_little_boy_is_skiing", "children": [{"name": "测试A cute little boy is skiing 能正常执行", "uid": "ac27b297a34a5503", "parentUid": "5dce92d62b80cb6553f1512dba0fb4c1", "status": "passed", "time": {"start": 1756787964251, "stop": 1756788051466, "duration": 87215}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5dce92d62b80cb6553f1512dba0fb4c1"}, {"name": "test_change_the_style_of_this_image_to_d_cartoon", "children": [{"name": "测试Change the style of this image to 3D cartoon能正常执行", "uid": "be59cc9d8c4f9334", "parentUid": "df3998e3ff6eea9977d2eb96f3dafe5c", "status": "passed", "time": {"start": 1756788067685, "stop": 1756788178402, "duration": 110717}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df3998e3ff6eea9977d2eb96f3dafe5c"}, {"name": "test_document_summary", "children": [{"name": "测试document summary能正常执行", "uid": "905848e63b513aca", "parentUid": "c53eeb531d41bb17ebbb8c4a5a1c0c7f", "status": "passed", "time": {"start": 1756788194088, "stop": 1756788260941, "duration": 66853}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c53eeb531d41bb17ebbb8c4a5a1c0c7f"}, {"name": "test_extend_the_image", "children": [{"name": "测试extend the image能正常执行", "uid": "a8e25dada3cb366d", "parentUid": "424f36be71517de9612f5c1c4bbfd51b", "status": "passed", "time": {"start": 1756788276687, "stop": 1756788390979, "duration": 114292}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "424f36be71517de9612f5c1c4bbfd51b"}, {"name": "test_puppy", "children": [{"name": "测试puppy能正常执行", "uid": "3336fc9681d6992", "parentUid": "5c4a74895e8f5f371499f67901869735", "status": "passed", "time": {"start": 1756788407565, "stop": 1756788489323, "duration": 81758}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5c4a74895e8f5f371499f67901869735"}, {"name": "test_scan_the_qr_code_in_the_image", "children": [{"name": "测试Scan the QR code in the image 能正常执行", "uid": "790e35cbf8e3e6e3", "parentUid": "6d5e42acbf3b14ff9c7a1cb039af1eca", "status": "passed", "time": {"start": 1756788505152, "stop": 1756788615429, "duration": 110277}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6d5e42acbf3b14ff9c7a1cb039af1eca"}, {"name": "test_scan_this_qr_code", "children": [{"name": "测试Scan this QR code 能正常执行", "uid": "dca32016d3c610bc", "parentUid": "e69f8752d4c2fff88f531d31725048e7", "status": "passed", "time": {"start": 1756788632641, "stop": 1756788742548, "duration": 109907}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e69f8752d4c2fff88f531d31725048e7"}, {"name": "test_summarize_what_i_m_reading", "children": [{"name": "测试Summarize what I'm reading能正常执行", "uid": "a72a27bc3daf3dfd", "parentUid": "4a719e0465c60ccc21a482f8a248c354", "status": "passed", "time": {"start": 1756788758796, "stop": 1756788827676, "duration": 68880}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a719e0465c60ccc21a482f8a248c354"}], "uid": "d4441fc536166659c8ba844cb229b96d"}, {"name": "system_coupling", "children": [{"name": "test_adjustment_the_brightness_to", "children": [{"name": "测试Adjustment the brightness to 50%能正常执行", "uid": "33025e5cf02aca61", "parentUid": "f42ec0f584493c7e8f49de225d028d41", "status": "failed", "time": {"start": 1756788843750, "stop": 1756788866879, "duration": 23129}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f42ec0f584493c7e8f49de225d028d41"}, {"name": "test_adjustment_the_brightness_to_maximun", "children": [{"name": "测试adjustment the brightness to maximun能正常执行", "uid": "f0fbf3dfa5252821", "parentUid": "0a124f1e35221e2f7fc58638efac4609", "status": "passed", "time": {"start": 1756788882646, "stop": 1756788905574, "duration": 22928}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0a124f1e35221e2f7fc58638efac4609"}, {"name": "test_adjustment_the_brightness_to_minimun", "children": [{"name": "测试adjustment the brightness to minimun能正常执行", "uid": "d0b07a8f9836209d", "parentUid": "6cabd7a2575b3a49ba1d7e27cbebac2c", "status": "passed", "time": {"start": 1756788921764, "stop": 1756788945055, "duration": 23291}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6cabd7a2575b3a49ba1d7e27cbebac2c"}, {"name": "test_boost_phone", "children": [{"name": "测试boost phone能正常执行", "uid": "e06a6c9ea846f9ba", "parentUid": "084463cd25c5bdf79a97950326707013", "status": "failed", "time": {"start": 1756788961194, "stop": 1756788994079, "duration": 32885}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "084463cd25c5bdf79a97950326707013"}, {"name": "test_check_front_camera_information", "children": [{"name": "测试check front camera information能正常执行", "uid": "9a74d594b22a471d", "parentUid": "a3c3dc73a42e85d88737ae9cfea06ccf", "status": "passed", "time": {"start": 1756789010221, "stop": 1756789044749, "duration": 34528}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a3c3dc73a42e85d88737ae9cfea06ccf"}, {"name": "test_clear_junk_files", "children": [{"name": "测试clear junk files命令", "uid": "86fc6aeced2cf58", "parentUid": "79b6df9a290803cad2cc59c26f69f0d5", "status": "passed", "time": {"start": 1756789060568, "stop": 1756789094490, "duration": 33922}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "79b6df9a290803cad2cc59c26f69f0d5"}, {"name": "test_close_airplane", "children": [{"name": "测试close airplane能正常执行", "uid": "bbe2d4d36780fd8", "parentUid": "472d53b48cd9911b61f6571aa3fabc81", "status": "passed", "time": {"start": 1756789110493, "stop": 1756789133717, "duration": 23224}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "472d53b48cd9911b61f6571aa3fabc81"}, {"name": "test_close_bluetooth", "children": [{"name": "测试close bluetooth能正常执行", "uid": "d3ab8f9da337262c", "parentUid": "43c3e77fe7845ae372f8b29b479e9c36", "status": "passed", "time": {"start": 1756789149504, "stop": 1756789172911, "duration": 23407}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43c3e77fe7845ae372f8b29b479e9c36"}, {"name": "test_close_flashlight", "children": [{"name": "测试close flashlight能正常执行", "uid": "7eeb2e3866a443fd", "parentUid": "05e7de1f6a30400f4ba09b6b927c0c5f", "status": "passed", "time": {"start": 1756789188448, "stop": 1756789212009, "duration": 23561}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "05e7de1f6a30400f4ba09b6b927c0c5f"}, {"name": "test_countdown_min", "children": [{"name": "测试countdown 5 min能正常执行", "uid": "9c48b2cfb1a058ed", "parentUid": "5acc0b94de2365f9212477ba5756e434", "status": "passed", "time": {"start": 1756789227818, "stop": 1756789282715, "duration": 54897}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5acc0b94de2365f9212477ba5756e434"}, {"name": "test_decrease_the_brightness", "children": [{"name": "测试decrease the brightness能正常执行", "uid": "c7761abaf3f6c596", "parentUid": "71d6a5aa3a46022e7d7ccf02f396978b", "status": "passed", "time": {"start": 1756789299776, "stop": 1756789346567, "duration": 46791}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "71d6a5aa3a46022e7d7ccf02f396978b"}, {"name": "test_decrease_the_volume_to_the_minimun", "children": [{"name": "测试decrease the volume to the minimun能正常执行", "uid": "68ebdca8e9ca136b", "parentUid": "20521c4eded335a33ef2e1e8ba13e750", "status": "passed", "time": {"start": 1756789363382, "stop": 1756789386595, "duration": 23213}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "20521c4eded335a33ef2e1e8ba13e750"}, {"name": "test_delete_the_o_clock_alarm", "children": [{"name": "测试delete the 8 o'clock alarm", "uid": "8512aa875947c6a2", "parentUid": "12518816be651c552209d9b7b24539ad", "status": "passed", "time": {"start": 1756789402998, "stop": 1756789469811, "duration": 66813}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12518816be651c552209d9b7b24539ad"}, {"name": "test_end_screen_recording", "children": [{"name": "stop  screen recording能正常执行", "uid": "276fed41da8a610d", "parentUid": "62823fc182e440a2772cd17c42ab4c29", "status": "failed", "time": {"start": 1756789486343, "stop": 1756789544934, "duration": 58591}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "62823fc182e440a2772cd17c42ab4c29"}, {"name": "test_help_me_take_a_long_screenshot", "children": [{"name": "测试help me take a long screenshot能正常执行", "uid": "c218ea72eac84b28", "parentUid": "1349bd48475843b043511c26d6b12b24", "status": "passed", "time": {"start": 1756789560784, "stop": 1756789582597, "duration": 21813}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1349bd48475843b043511c26d6b12b24"}, {"name": "test_help_me_take_a_screenshot", "children": [{"name": "测试help me take a screenshot能正常执行", "uid": "b297a80a5ae2fcb4", "parentUid": "f5c6c4880b5d8ab718c388b911880945", "status": "passed", "time": {"start": 1756789598757, "stop": 1756789622085, "duration": 23328}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f5c6c4880b5d8ab718c388b911880945"}, {"name": "test_increase_notification_volume", "children": [{"name": "测试increase notification volume能正常执行", "uid": "9094b97778f1a7bb", "parentUid": "c820439150f9f3eeda53e313099e370f", "status": "passed", "time": {"start": 1756789637579, "stop": 1756789659713, "duration": 22134}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c820439150f9f3eeda53e313099e370f"}, {"name": "test_increase_screen_brightness", "children": [{"name": "测试increase screen brightness能正常执行", "uid": "492f97d058f34f28", "parentUid": "c07e39e268fcc0b13f001cda95d292f1", "status": "passed", "time": {"start": 1756789675900, "stop": 1756789698158, "duration": 22258}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c07e39e268fcc0b13f001cda95d292f1"}, {"name": "test_increase_the_brightness", "children": [{"name": "测试increase the brightness能正常执行", "uid": "ded9a646b383116a", "parentUid": "318cb9a601f399198ad7b64303d6a804", "status": "passed", "time": {"start": 1756789713894, "stop": 1756789736385, "duration": 22491}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "318cb9a601f399198ad7b64303d6a804"}, {"name": "test_increase_the_volume_to_the_maximun", "children": [{"name": "测试increase the volume to the maximun能正常执行", "uid": "45ba6042bf3a7373", "parentUid": "6daa3caef60cbff270b232fec14c929c", "status": "passed", "time": {"start": 1756789752104, "stop": 1756789772809, "duration": 20705}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6daa3caef60cbff270b232fec14c929c"}, {"name": "test_long_screenshot", "children": [{"name": "测试long screenshot能正常执行", "uid": "c01f7948bc85ea61", "parentUid": "eee53365e608da87e8c1a38cd7dffd82", "status": "passed", "time": {"start": 1756789788707, "stop": 1756789811813, "duration": 23106}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eee53365e608da87e8c1a38cd7dffd82"}, {"name": "test_make_the_phone_mute", "children": [{"name": "测试make the phone mute能正常执行", "uid": "1d0ee1fc9eb1b558", "parentUid": "0b44ef42d6ad53a2bea3bbed424e41ee", "status": "passed", "time": {"start": 1756789827374, "stop": 1756789848368, "duration": 20994}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0b44ef42d6ad53a2bea3bbed424e41ee"}, {"name": "test_max_alarm_clock_volume", "children": [{"name": "测试max alarm clock volume", "uid": "9d2a34638cb2122d", "parentUid": "cb770bbd2e94c64f600ababeb4f0ee6a", "status": "passed", "time": {"start": 1756789864162, "stop": 1756789885705, "duration": 21543}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cb770bbd2e94c64f600ababeb4f0ee6a"}, {"name": "test_max_brightness", "children": [{"name": "测试max brightness能正常执行", "uid": "a7975b187defba53", "parentUid": "bc6c7f74ec9c5d95879c2d1cf9880a19", "status": "passed", "time": {"start": 1756789901320, "stop": 1756789923780, "duration": 22460}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bc6c7f74ec9c5d95879c2d1cf9880a19"}, {"name": "test_max_notifications_volume", "children": [{"name": "测试max notifications volume能正常执行", "uid": "b6fbaed39b0b8cf6", "parentUid": "d168a4bb52fda427e53b993ad648e85a", "status": "failed", "time": {"start": 1756789939434, "stop": 1756789960948, "duration": 21514}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d168a4bb52fda427e53b993ad648e85a"}, {"name": "test_max_ring_volume", "children": [{"name": "测试max ring volume能正常执行", "uid": "15b51821380ce18d", "parentUid": "446c602b84cd3dd1f96b31e0108411fb", "status": "passed", "time": {"start": 1756789977223, "stop": 1756790000216, "duration": 22993}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "446c602b84cd3dd1f96b31e0108411fb"}, {"name": "test_maximum_volume", "children": [{"name": "测试maximum volume能正常执行", "uid": "c598ec1927a46c64", "parentUid": "df87e0d39fb14600720a434db404fb7b", "status": "passed", "time": {"start": 1756790017955, "stop": 1756790040142, "duration": 22187}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df87e0d39fb14600720a434db404fb7b"}, {"name": "test_memory_cleanup", "children": [{"name": "测试memory cleanup能正常执行", "uid": "524d9c64d9e4e06f", "parentUid": "3060b8cc7f41dc69110dfc534e6b31d5", "status": "passed", "time": {"start": 1756790057727, "stop": 1756790090376, "duration": 32649}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3060b8cc7f41dc69110dfc534e6b31d5"}, {"name": "test_min_alarm_clock_volume", "children": [{"name": "测试min alarm clock volume", "uid": "29dd1b71426b2644", "parentUid": "fd1b7fb10ddbe5eadf93766446821b65", "status": "passed", "time": {"start": 1756790106569, "stop": 1756790128087, "duration": 21518}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fd1b7fb10ddbe5eadf93766446821b65"}, {"name": "test_min_brightness", "children": [{"name": "测试min brightness能正常执行", "uid": "4a1588e9ca567be1", "parentUid": "e5eb14b295c00a0d59407203918f4795", "status": "passed", "time": {"start": 1756790143951, "stop": 1756790165628, "duration": 21677}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e5eb14b295c00a0d59407203918f4795"}, {"name": "test_min_notifications_volume", "children": [{"name": "测试min notifications volume能正常执行", "uid": "9e24795f9d2ea166", "parentUid": "11646edd82fe620b37c2b64152ced5c6", "status": "passed", "time": {"start": 1756790181763, "stop": 1756790202991, "duration": 21228}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "11646edd82fe620b37c2b64152ced5c6"}, {"name": "test_min_ring_volume", "children": [{"name": "测试min ring volume能正常执行", "uid": "8f9ad28725471f8", "parentUid": "d45b602df43d160bc186bdce526932e8", "status": "passed", "time": {"start": 1756790218933, "stop": 1756790239986, "duration": 21053}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d45b602df43d160bc186bdce526932e8"}, {"name": "test_minimum_volume", "children": [{"name": "测试minimum volume能正常执行", "uid": "ec6a759d2680a807", "parentUid": "25a58f43bfaf0da153ec687fea5dcbb5", "status": "passed", "time": {"start": 1756790256219, "stop": 1756790277776, "duration": 21557}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "25a58f43bfaf0da153ec687fea5dcbb5"}, {"name": "test_open_bluetooth", "children": [{"name": "测试open bluetooth", "uid": "92ceaaf9ada9fed9", "parentUid": "31e546db33350801c6eaef968b45b6aa", "status": "passed", "time": {"start": 1756790293889, "stop": 1756790317680, "duration": 23791}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "31e546db33350801c6eaef968b45b6aa"}, {"name": "test_open_bt", "children": [{"name": "测试open bt", "uid": "ac2a6bb20ec61f6", "parentUid": "176a0fdfd9c4144b8dfe3d66a049fda5", "status": "passed", "time": {"start": 1756790333899, "stop": 1756790356361, "duration": 22462}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "176a0fdfd9c4144b8dfe3d66a049fda5"}, {"name": "test_open_flashlight", "children": [{"name": "测试open flashlight", "uid": "cb22f338e3619aca", "parentUid": "649e9c0b88c706f3f0551679bed64e42", "status": "passed", "time": {"start": 1756790372405, "stop": 1756790396063, "duration": 23658}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "649e9c0b88c706f3f0551679bed64e42"}, {"name": "test_open_wifi", "children": [{"name": "测试open wifi", "uid": "d1ebb5532b030e39", "parentUid": "f9eadf40e8c6f09400b67a9959223d07", "status": "passed", "time": {"start": 1756790412045, "stop": 1756790433483, "duration": 21438}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f9eadf40e8c6f09400b67a9959223d07"}, {"name": "test_power_saving", "children": [{"name": "测试power saving能正常执行", "uid": "aa9e50a8deaf5d9f", "parentUid": "c06c413949d25e13cefc3d7381223a9b", "status": "passed", "time": {"start": 1756790449355, "stop": 1756790478237, "duration": 28882}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c06c413949d25e13cefc3d7381223a9b"}, {"name": "test_screen_record", "children": [{"name": "测试screen record能正常执行", "uid": "91ba7b91595a5e19", "parentUid": "b54fab3751e243d1b424e37d5d287ec3", "status": "failed", "time": {"start": 1756790494539, "stop": 1756790522944, "duration": 28405}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "72e7efc6f08f8afb", "parentUid": "b54fab3751e243d1b424e37d5d287ec3", "status": "passed", "time": {"start": 1756790539654, "stop": 1756790567839, "duration": 28185}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b54fab3751e243d1b424e37d5d287ec3"}, {"name": "test_set_a_timer_for_minutes", "children": [{"name": "测试set a timer for 10 minutes能正常执行", "uid": "141e911fa36d2127", "parentUid": "b51793332bcfdc1c2f251c5ba2c434e5", "status": "passed", "time": {"start": 1756790583987, "stop": 1756790613659, "duration": 29672}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b51793332bcfdc1c2f251c5ba2c434e5"}, {"name": "test_set_alarm_for_10_o_clock", "children": [{"name": "测试set alarm for 10 o'clock", "uid": "df5a2fb9f23a883d", "parentUid": "afeb5aed32d36d5eaa4b132ea5b382e0", "status": "passed", "time": {"start": 1756790629634, "stop": 1756790670381, "duration": 40747}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "afeb5aed32d36d5eaa4b132ea5b382e0"}, {"name": "test_set_alarm_volume", "children": [{"name": "测试set alarm volume 50", "uid": "c7b8e67bf5dda5ff", "parentUid": "1c45deff54cbb6d416ba43bacd27945b", "status": "passed", "time": {"start": 1756790686825, "stop": 1756790708486, "duration": 21661}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1c45deff54cbb6d416ba43bacd27945b"}, {"name": "test_set_an_alarm_at_am_tomorrow", "children": [{"name": "测试Set an alarm at 10 am tomorrow", "uid": "1ccb5dcab8a70e4f", "parentUid": "15251dccb77688b095b2448430ea7f0b", "status": "passed", "time": {"start": 1756790724218, "stop": 1756790784913, "duration": 60695}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "15251dccb77688b095b2448430ea7f0b"}, {"name": "test_set_battery_saver_setting", "children": [{"name": "测试set Battery Saver setting能正常执行", "uid": "44f7f2db4d16a214", "parentUid": "b6e60ab86df5fa2a1124b5d765cbfe9a", "status": "passed", "time": {"start": 1756790800703, "stop": 1756790828767, "duration": 28064}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b6e60ab86df5fa2a1124b5d765cbfe9a"}, {"name": "test_set_my_alarm_volume_to", "children": [{"name": "测试set my alarm volume to 50%", "uid": "d945566ecee6f0ed", "parentUid": "bba654dbbf79ef15853a19abfddc76e4", "status": "passed", "time": {"start": 1756790844483, "stop": 1756790865644, "duration": 21161}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bba654dbbf79ef15853a19abfddc76e4"}, {"name": "test_set_notifications_volume_to", "children": [{"name": "测试set notifications volume to 50能正常执行", "uid": "4d140110eec83794", "parentUid": "8941ad1b28523da85fde9e91215c0464", "status": "failed", "time": {"start": 1756790881821, "stop": 1756790903051, "duration": 21230}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8941ad1b28523da85fde9e91215c0464"}, {"name": "test_set_ringtone_volume_to", "children": [{"name": "测试set ringtone volume to 50能正常执行", "uid": "c42dabaff30bbc26", "parentUid": "9dd83889897a00276a8e889caae40a1f", "status": "passed", "time": {"start": 1756790919170, "stop": 1756790941043, "duration": 21873}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9dd83889897a00276a8e889caae40a1f"}, {"name": "test_set_screen_to_maximum_brightness", "children": [{"name": "测试set screen to maximum brightness能正常执行", "uid": "7b0676edc44ad6a8", "parentUid": "545283340cc920a78362ea8849374231", "status": "passed", "time": {"start": 1756790957327, "stop": 1756790979496, "duration": 22169}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "545283340cc920a78362ea8849374231"}, {"name": "test_set_the_alarm_at_9_o_clock_on_weekends", "children": [{"name": "测试set the alarm at 9 o'clock on weekends", "uid": "3d61c6b212aa3f73", "parentUid": "3921f68f918e595aa1edf4f965392437", "status": "passed", "time": {"start": 1756790995552, "stop": 1756791057688, "duration": 62136}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3921f68f918e595aa1edf4f965392437"}, {"name": "test_smart_charge", "children": [{"name": "测试smart charge能正常执行", "uid": "aaf7cd0d489d21b3", "parentUid": "38c8329119fb1c04106bbcd94b91ce11", "status": "failed", "time": {"start": 1756791074178, "stop": 1756791094255, "duration": 20077}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "38c8329119fb1c04106bbcd94b91ce11"}, {"name": "test_start_record", "children": [{"name": "测试start record能正常执行", "uid": "19661fd537284e83", "parentUid": "9018ab08ec79118ab3c7ac811af9f4fa", "status": "failed", "time": {"start": 1756791110519, "stop": 1756791138174, "duration": 27655}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9018ab08ec79118ab3c7ac811af9f4fa"}, {"name": "test_start_screen_recording", "children": [{"name": "测试start screen recording能正常执行", "uid": "3381659781da7319", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "failed", "time": {"start": 1756791154256, "stop": 1756791181812, "duration": 27556}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause screen recording能正常执行", "uid": "c36acb37010eceeb", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "failed", "time": {"start": 1756791198151, "stop": 1756791226019, "duration": 27868}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "continue  screen recording能正常执行", "uid": "67baaeb7d84dabd9", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "failed", "time": {"start": 1756791242857, "stop": 1756791271457, "duration": 28600}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "c5702f5fcaf230c0", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "passed", "time": {"start": 1756791287423, "stop": 1756791336659, "duration": 49236}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a083bce7f3fa2d36f16068ac034f4e62"}, {"name": "test_stop_recording", "children": [{"name": "stop  screen recording能正常执行", "uid": "eb168c41208d84e4", "parentUid": "4eb0eb9df164551aeca11800ab680c92", "status": "passed", "time": {"start": 1756791352832, "stop": 1756791403014, "duration": 50182}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4eb0eb9df164551aeca11800ab680c92"}, {"name": "test_switch_charging_modes", "children": [{"name": "测试switch charging modes能正常执行", "uid": "54c50d9dbaf270de", "parentUid": "43ff861df9a59eeef7ac3edd50740e02", "status": "passed", "time": {"start": 1756791419217, "stop": 1756791439998, "duration": 20781}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43ff861df9a59eeef7ac3edd50740e02"}, {"name": "test_switch_magic_voice_to_grace", "children": [{"name": "测试Switch Magic Voice to Grace能正常执行", "uid": "51ce257c78bf6fc9", "parentUid": "035194a70f8a76a4b76a28f704b8ff10", "status": "passed", "time": {"start": 1756791455998, "stop": 1756791477466, "duration": 21468}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "035194a70f8a76a4b76a28f704b8ff10"}, {"name": "test_switch_magic_voice_to_mango", "children": [{"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "6554ee4b5633e9ad", "parentUid": "661f1bc7755dcb73f23369637ff67460", "status": "passed", "time": {"start": 1756791493357, "stop": 1756791514506, "duration": 21149}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "661f1bc7755dcb73f23369637ff67460"}, {"name": "test_switch_to_barrage_notification", "children": [{"name": "测试Switch to Barrage Notification能正常执行", "uid": "d1e7988eef8ca89f", "parentUid": "71d9b05a5d33eaee0a7365eb0716032e", "status": "failed", "time": {"start": 1756791530803, "stop": 1756791551512, "duration": 20709}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "71d9b05a5d33eaee0a7365eb0716032e"}, {"name": "test_switch_to_default_mode", "children": [{"name": "测试switch to default mode能正常执行", "uid": "cbfbf9f515a15d56", "parentUid": "62ed32eba114769fb134a432054912e4", "status": "passed", "time": {"start": 1756791567663, "stop": 1756791595954, "duration": 28291}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "62ed32eba114769fb134a432054912e4"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "测试switch to equilibrium mode能正常执行", "uid": "650cd2ac0bb49e3c", "parentUid": "2d94b30b5f27ad8b3f8967a2a3fdebe3", "status": "passed", "time": {"start": 1756791612441, "stop": 1756791641034, "duration": 28593}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d94b30b5f27ad8b3f8967a2a3fdebe3"}, {"name": "test_switch_to_flash_notification", "children": [{"name": "测试switch to flash notification能正常执行", "uid": "b5227ebb2f9fc0ba", "parentUid": "561ddfab014b4de5926c248cf15d4c27", "status": "passed", "time": {"start": 1756791657518, "stop": 1756791678713, "duration": 21195}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "561ddfab014b4de5926c248cf15d4c27"}, {"name": "test_switch_to_hyper_charge", "children": [{"name": "测试Switch to Hyper Charge能正常执行", "uid": "1929379ff1f65509", "parentUid": "99e29771f7571a2fc146c069d7b2c125", "status": "passed", "time": {"start": 1756791695041, "stop": 1756791716309, "duration": 21268}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "99e29771f7571a2fc146c069d7b2c125"}, {"name": "test_switch_to_low_temp_charge", "children": [{"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "2187e2e4c8fe246e", "parentUid": "e6a1e2156d8852f07854b84613f04720", "status": "passed", "time": {"start": 1756791732887, "stop": 1756791753117, "duration": 20230}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e6a1e2156d8852f07854b84613f04720"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "测试switch to power saving mode能正常执行", "uid": "9112c0b7fc56b24a", "parentUid": "dd9e8f3389e8ba0ae9839c873338d760", "status": "passed", "time": {"start": 1756791769103, "stop": 1756791790537, "duration": 21434}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dd9e8f3389e8ba0ae9839c873338d760"}, {"name": "test_switch_to_smart_charge", "children": [{"name": "测试switch to smart charge能正常执行", "uid": "e3ab5f59f673c05a", "parentUid": "a22da6d101545dc9b0d8edfda3746b20", "status": "passed", "time": {"start": 1756791806885, "stop": 1756791828252, "duration": 21367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a22da6d101545dc9b0d8edfda3746b20"}, {"name": "test_switched_to_data_mode", "children": [{"name": "测试switched to data mode能正常执行", "uid": "d5db3da844ab4b1c", "parentUid": "2902f5b25b15010f9670892ff424d92c", "status": "passed", "time": {"start": 1756791844457, "stop": 1756791865995, "duration": 21538}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2902f5b25b15010f9670892ff424d92c"}, {"name": "test_take_a_photo", "children": [{"name": "测试take a photo能正常执行", "uid": "f49def4e0cf06e90", "parentUid": "07997292671f0c2fc7c4f6639faf371f", "status": "failed", "time": {"start": 1756791882153, "stop": 1756791929249, "duration": 47096}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "07997292671f0c2fc7c4f6639faf371f"}, {"name": "test_take_a_selfie", "children": [{"name": "测试take a selfie能正常执行", "uid": "cc9b50f6d943f117", "parentUid": "b76edbc7be133088ff6cc7dcf79d0688", "status": "passed", "time": {"start": 1756791948260, "stop": 1756791988016, "duration": 39756}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b76edbc7be133088ff6cc7dcf79d0688"}, {"name": "test_the_battery_of_the_mobile_phone_is_too_low", "children": [{"name": "测试the battery of the mobile phone is too low能正常执行", "uid": "bb1b20068527f996", "parentUid": "fd16a15b2bee6fa00e607a69966a816d", "status": "passed", "time": {"start": 1756792005920, "stop": 1756792034949, "duration": 29029}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fd16a15b2bee6fa00e607a69966a816d"}, {"name": "test_turn_down_alarm_clock_volume", "children": [{"name": "测试turn down alarm clock volume", "uid": "6a24dcf737533d80", "parentUid": "058fbd49b611700b8e525883b7a715c7", "status": "passed", "time": {"start": 1756792051436, "stop": 1756792072889, "duration": 21453}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "058fbd49b611700b8e525883b7a715c7"}, {"name": "test_turn_down_notifications_volume", "children": [{"name": "测试turn down notifications volume能正常执行", "uid": "925d1157a300a945", "parentUid": "653ec2baabc9caa267ed836aab8220ec", "status": "passed", "time": {"start": 1756792089275, "stop": 1756792132977, "duration": 43702}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "653ec2baabc9caa267ed836aab8220ec"}, {"name": "test_turn_down_ring_volume", "children": [{"name": "测试turn down ring volume能正常执行", "uid": "2f5eb11c955f547a", "parentUid": "550486b451765b696814b7b6743f77fe", "status": "passed", "time": {"start": 1756792149292, "stop": 1756792170828, "duration": 21536}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "550486b451765b696814b7b6743f77fe"}, {"name": "test_turn_down_the_brightness_to_the_min", "children": [{"name": "测试turn down the brightness to the min能正常执行", "uid": "4ce075fa73ad6e73", "parentUid": "4a960850c7bf239ac31ab236d66e878f", "status": "passed", "time": {"start": 1756792187133, "stop": 1756792209542, "duration": 22409}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a960850c7bf239ac31ab236d66e878f"}, {"name": "test_turn_off_adaptive_brightness", "children": [{"name": "测试turn off adaptive brightness能正常执行", "uid": "7f314554ae49e6a6", "parentUid": "3e70ee736f216554340491db70fd8257", "status": "passed", "time": {"start": 1756792225762, "stop": 1756792246393, "duration": 20631}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e70ee736f216554340491db70fd8257"}, {"name": "test_turn_off_auto_rotate_screen", "children": [{"name": "测试turn off auto rotate screen能正常执行", "uid": "ef24e91e2e3ccae4", "parentUid": "9029516e3ad0598210caeb84ca36028a", "status": "passed", "time": {"start": 1756792262714, "stop": 1756792283240, "duration": 20526}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9029516e3ad0598210caeb84ca36028a"}, {"name": "test_turn_off_flashlight", "children": [{"name": "测试turn off flashlight能正常执行", "uid": "426ae92a93768635", "parentUid": "990086735fb4bd184a8ac5a25a5f821c", "status": "passed", "time": {"start": 1756792299137, "stop": 1756792320454, "duration": 21317}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "990086735fb4bd184a8ac5a25a5f821c"}, {"name": "test_turn_off_light_theme", "children": [{"name": "测试turn off light theme能正常执行", "uid": "b0806a2c5a9fd7e0", "parentUid": "83d0ab198b941cca60896fe1448789bd", "status": "passed", "time": {"start": 1756792336826, "stop": 1756792357750, "duration": 20924}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83d0ab198b941cca60896fe1448789bd"}, {"name": "test_turn_off_nfc", "children": [{"name": "测试turn off nfc能正常执行", "uid": "7357d9f1eb02bb7f", "parentUid": "25370c24404047b47256854554ecf19e", "status": "passed", "time": {"start": 1756792373890, "stop": 1756792396566, "duration": 22676}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "25370c24404047b47256854554ecf19e"}, {"name": "test_turn_off_smart_reminder", "children": [{"name": "测试turn off smart reminder能正常执行", "uid": "364533e255c84cd3", "parentUid": "69969f8163b3af4121192a7c63c3fa49", "status": "passed", "time": {"start": 1756792412890, "stop": 1756792432626, "duration": 19736}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "69969f8163b3af4121192a7c63c3fa49"}, {"name": "test_turn_off_the_am_alarm", "children": [{"name": "测试turn off the 8 am alarm", "uid": "fc6e7cd50fd06462", "parentUid": "12bbe08f00547540096c4bd50cc2d900", "status": "passed", "time": {"start": 1756792449369, "stop": 1756792532427, "duration": 83058}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12bbe08f00547540096c4bd50cc2d900"}, {"name": "test_turn_on_adaptive_brightness", "children": [{"name": "测试turn on adaptive brightness能正常执行", "uid": "cdd1296da8bb57d1", "parentUid": "8074f6de3cc4d8e0ee673e8436e36098", "status": "passed", "time": {"start": 1756792548655, "stop": 1756792570365, "duration": 21710}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8074f6de3cc4d8e0ee673e8436e36098"}, {"name": "test_turn_on_auto_rotate_screen", "children": [{"name": "测试turn on auto rotate screen能正常执行", "uid": "96311abdd7feba55", "parentUid": "ae898b5cc3303c318290953f962dbbd3", "status": "passed", "time": {"start": 1756792586797, "stop": 1756792608581, "duration": 21784}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ae898b5cc3303c318290953f962dbbd3"}, {"name": "test_turn_on_bluetooth", "children": [{"name": "测试turn on bluetooth能正常执行", "uid": "a5cde4c741307bb4", "parentUid": "06858c4ec997c86c877ce6ab09e3bbaf", "status": "passed", "time": {"start": 1756792624715, "stop": 1756792647189, "duration": 22474}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "06858c4ec997c86c877ce6ab09e3bbaf"}, {"name": "test_turn_on_brightness_to_80", "children": [{"name": "测试turn on brightness to 80能正常执行", "uid": "bd42bab8bf767045", "parentUid": "2fb18876082f3ff4b34b7ccd72bb7b28", "status": "failed", "time": {"start": 1756792663531, "stop": 1756792685716, "duration": 22185}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2fb18876082f3ff4b34b7ccd72bb7b28"}, {"name": "test_turn_on_do_not_disturb_mode", "children": [{"name": "测试turn on do not disturb mode能正常执行", "uid": "586e1f358588d2e2", "parentUid": "869ae35c254b699074c4a890b2986c6d", "status": "passed", "time": {"start": 1756792702071, "stop": 1756792723712, "duration": 21641}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "869ae35c254b699074c4a890b2986c6d"}, {"name": "test_turn_on_light_theme", "children": [{"name": "测试turn on light theme能正常执行", "uid": "d11d43de5dd9a3bb", "parentUid": "9684df9c422f9cf3eef3d6f0ddd6607c", "status": "passed", "time": {"start": 1756792739974, "stop": 1756792762569, "duration": 22595}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on light theme能正常执行", "uid": "e88b1b31f0ce5f9f", "parentUid": "9684df9c422f9cf3eef3d6f0ddd6607c", "status": "passed", "time": {"start": 1756792779187, "stop": 1756792801680, "duration": 22493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9684df9c422f9cf3eef3d6f0ddd6607c"}, {"name": "test_turn_on_location_services", "children": [{"name": "测试turn on location services能正常执行", "uid": "f2c5fb23e17f8b84", "parentUid": "13b991141a19f0fe4b82a01990c6a851", "status": "passed", "time": {"start": 1756792818230, "stop": 1756792840011, "duration": 21781}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "13b991141a19f0fe4b82a01990c6a851"}, {"name": "test_turn_on_nfc", "children": [{"name": "测试turn on nfc能正常执行", "uid": "86cf3982a8e9d463", "parentUid": "ac9c5a7d76d32a3f0024f1bbc4260940", "status": "passed", "time": {"start": 1756792856063, "stop": 1756792879789, "duration": 23726}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ac9c5a7d76d32a3f0024f1bbc4260940"}, {"name": "test_turn_on_smart_reminder", "children": [{"name": "测试turn on smart reminder能正常执行", "uid": "99035abb7ee1be47", "parentUid": "2ea8c3d3e0404ec5eb56e144f27b3b76", "status": "passed", "time": {"start": 1756792895978, "stop": 1756792917689, "duration": 21711}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2ea8c3d3e0404ec5eb56e144f27b3b76"}, {"name": "test_turn_on_the_7am_alarm", "children": [{"name": "测试turn on the 7AM alarm", "uid": "2dd1629c4a57f5c0", "parentUid": "20bb02d2344b8a65effdb63f9de8d6cf", "status": "passed", "time": {"start": 1756792933689, "stop": 1756793014273, "duration": 80584}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "20bb02d2344b8a65effdb63f9de8d6cf"}, {"name": "test_turn_on_the_flashlight", "children": [{"name": "测试turn on the flashlight能正常执行", "uid": "2d667af6faa830a3", "parentUid": "c5e4b9b650952bb94f49d9ea64fb9376", "status": "passed", "time": {"start": 1756793030366, "stop": 1756793053496, "duration": 23130}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c5e4b9b650952bb94f49d9ea64fb9376"}, {"name": "test_turn_on_the_screen_record", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "67ec5be20359e244", "parentUid": "5cf049f3605e39b97e2902b74b81323d", "status": "failed", "time": {"start": 1756793069512, "stop": 1756793096641, "duration": 27129}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5cf049f3605e39b97e2902b74b81323d"}, {"name": "test_turn_on_wifi", "children": [{"name": "测试turn on wifi能正常执行", "uid": "96b5beef4eb3e5e1", "parentUid": "8f8981d5b6534e5c462a0d8cbafb4302", "status": "passed", "time": {"start": 1756793113203, "stop": 1756793136246, "duration": 23043}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8f8981d5b6534e5c462a0d8cbafb4302"}, {"name": "test_turn_up_alarm_clock_volume", "children": [{"name": "测试turn up alarm clock volume", "uid": "7713ec5d2e3b1392", "parentUid": "dcce4917ed7cc8e4416996e747de9c57", "status": "passed", "time": {"start": 1756793152117, "stop": 1756793176621, "duration": 24504}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dcce4917ed7cc8e4416996e747de9c57"}, {"name": "test_turn_up_notifications_volume", "children": [{"name": "测试turn up notifications volume能正常执行", "uid": "4193c1cad87a592d", "parentUid": "b0cf36b6141ae92300d37ba7f5cc28b4", "status": "passed", "time": {"start": 1756793192543, "stop": 1756793215554, "duration": 23011}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b0cf36b6141ae92300d37ba7f5cc28b4"}, {"name": "test_turn_up_ring_volume", "children": [{"name": "测试turn up ring volume能正常执行", "uid": "c404aeb3e1a0fb20", "parentUid": "d5f810cb9cfc767a77e9451e7e822fb9", "status": "passed", "time": {"start": 1756793231172, "stop": 1756793254107, "duration": 22935}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d5f810cb9cfc767a77e9451e7e822fb9"}, {"name": "test_turn_up_the_brightness_to_the_max", "children": [{"name": "测试turn up the brightness to the max能正常执行", "uid": "1234c38f4cea2cf4", "parentUid": "6357b07572538890d600f578a4c3c4aa", "status": "passed", "time": {"start": 1756793269897, "stop": 1756793292584, "duration": 22687}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6357b07572538890d600f578a4c3c4aa"}, {"name": "test_turn_up_the_volume_to_the_max", "children": [{"name": "测试turn up the volume to the max能正常执行", "uid": "4080df4128273d88", "parentUid": "2d41809a57eff7adaff72dd6fb331012", "status": "passed", "time": {"start": 1756793308757, "stop": 1756793331610, "duration": 22853}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d41809a57eff7adaff72dd6fb331012"}, {"name": "test_wake_me_up_at_am_tomorrow", "children": [{"name": "测试wake me up at 7:00 am tomorrow能正常执行", "uid": "95cd6432fa9350e1", "parentUid": "193cbbfa30db6a41aacdad7a864e09bc", "status": "passed", "time": {"start": 1756793347498, "stop": 1756793368013, "duration": 20515}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "193cbbfa30db6a41aacdad7a864e09bc"}, {"name": "test_where_is_the_carlcare_service_outlet", "children": [{"name": "测试where is the carlcare service outlet能正常执行", "uid": "e304f48b87cce60b", "parentUid": "a69c8f4ca42c7f7233299fb00d064c9f", "status": "passed", "time": {"start": 1756793384033, "stop": 1756793411163, "duration": 27130}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a69c8f4ca42c7f7233299fb00d064c9f"}], "uid": "93fc5475dc5c7ad817f2b1ab5b0ac7b9"}, {"name": "test_ask_screen", "children": [{"name": "contact", "children": [{"name": "test_add_all_the_numbers_to_lucy", "children": [{"name": "测试add all the numbers to lucy", "uid": "c898ef9c79312c73", "parentUid": "049f97d11088817f109d81731cab0600", "status": "passed", "time": {"start": 1756793415820, "stop": 1756793450100, "duration": 34280}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "049f97d11088817f109d81731cab0600"}, {"name": "test_add_the_lucy_s_number", "children": [{"name": "测试add the lucy‘s number", "uid": "819528b402a0bb32", "parentUid": "96f23ab7ecdddcf346741f76753e969b", "status": "failed", "time": {"start": 1756793455689, "stop": 1756793489180, "duration": 33491}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "96f23ab7ecdddcf346741f76753e969b"}, {"name": "test_add_the_lucy_s_number_in_this_picture", "children": [{"name": "测试add the lucy's number in this picture", "uid": "1be04dc69a1644cf", "parentUid": "8d2b500ec8b59f7f989759ce6418f319", "status": "passed", "time": {"start": 1756793494581, "stop": 1756793529819, "duration": 35238}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8d2b500ec8b59f7f989759ce6418f319"}, {"name": "test_add_the_mom_s_and_lucy_s_number", "children": [{"name": "测试add the mom's and lucy's number", "uid": "524d4e259de62074", "parentUid": "d68b8367360effce1837bc2d5eb5b8b4", "status": "failed", "time": {"start": 1756793534938, "stop": 1756793569768, "duration": 34830}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d68b8367360effce1837bc2d5eb5b8b4"}, {"name": "test_add_the_number_on_the_screen_to_contacts", "children": [{"name": "测试Add the number on the screen to contacts", "uid": "bfb22679d401104f", "parentUid": "81b0143472f193dcfe0895840469ffae", "status": "passed", "time": {"start": 1756793575004, "stop": 1756793609952, "duration": 34948}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "81b0143472f193dcfe0895840469ffae"}, {"name": "test_add_this_number", "children": [{"name": "测试add this number", "uid": "7176e6e82cfc1c02", "parentUid": "cadfb2b8b604468641947d0fd2c696c6", "status": "passed", "time": {"start": 1756793615063, "stop": 1756793651003, "duration": 35940}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cadfb2b8b604468641947d0fd2c696c6"}, {"name": "test_add_this_number_to_lucy", "children": [{"name": "测试add this number to lucy", "uid": "4dd93d1860a41a54", "parentUid": "4bc12057d97e4b01861b8534453b374f", "status": "passed", "time": {"start": 1756793656096, "stop": 1756793690972, "duration": 34876}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4bc12057d97e4b01861b8534453b374f"}, {"name": "test_add_this_number_to_tom", "children": [{"name": "测试add this number to tom", "uid": "406bdbd909a2c22", "parentUid": "e27471eab30418b7727b3a6a876f57ce", "status": "passed", "time": {"start": 1756793695993, "stop": 1756793730682, "duration": 34689}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e27471eab30418b7727b3a6a876f57ce"}, {"name": "test_dial_the_number_on_the_screen", "children": [{"name": "测试Dial the number on the screen", "uid": "13b13e7d7a5f8136", "parentUid": "6c76de280138c8db4571d94e9525728b", "status": "passed", "time": {"start": 1756793735925, "stop": 1756793770832, "duration": 34907}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6c76de280138c8db4571d94e9525728b"}, {"name": "test_save_the_number_on_the_screen_to_contact_lulu", "children": [{"name": "测试Save the number on the screen to contact <PERSON>", "uid": "4f7fe804b4821ade", "parentUid": "93cde33aea8165e962927bf900e1090e", "status": "passed", "time": {"start": 1756793775978, "stop": 1756793810005, "duration": 34027}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "93cde33aea8165e962927bf900e1090e"}], "uid": "abf678292b8b8a12436be0a25de41ba2"}, {"name": "image_analysis.test_scan_the_qr_code_in_the_image", "children": [{"name": "测试Scan the QR code in the image", "uid": "ef36759a2c339fd", "parentUid": "c5ca9301cd295927ecacd46ea26188b1", "status": "passed", "time": {"start": 1756793815234, "stop": 1756793850354, "duration": 35120}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "image_analysis.test_scan_the_qr_code_in_the_image"}, {"name": "math_calculation", "children": [{"name": "test_add_the_schedule_on_the_screen", "children": [{"name": "测试Add the schedule on the screen", "uid": "6d61f5a7db38fb00", "parentUid": "ff4b118718046e9646ea4fcd1fad4d2b", "status": "passed", "time": {"start": 1756793855717, "stop": 1756793917937, "duration": 62220}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ff4b118718046e9646ea4fcd1fad4d2b"}, {"name": "test_add_this_image_to_my_notes", "children": [{"name": "测试Add this image to my notes", "uid": "23b238f1c424e815", "parentUid": "c8dd47e0d678dff9e93534d8c22fe186", "status": "passed", "time": {"start": 1756793923383, "stop": 1756793958643, "duration": 35260}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c8dd47e0d678dff9e93534d8c22fe186"}, {"name": "test_organize_the_content_on_this_image_and_add_this_image_and_its_content_to_my_notes", "children": [{"name": "测试Organize the content on this image, and add this image and its content to my notes", "uid": "e9ce9b50d3cbc6f9", "parentUid": "fc4306e41d080ee6d21109429ef41e4b", "status": "passed", "time": {"start": 1756793963627, "stop": 1756793998997, "duration": 35370}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fc4306e41d080ee6d21109429ef41e4b"}, {"name": "test_organize_this_image_and_add_it_to_my_notes", "children": [{"name": "测试Organize this image and add it to my notes", "uid": "aa38e330a946abf5", "parentUid": "8190dad3f06a934d811b12744431d356", "status": "passed", "time": {"start": 1756794004165, "stop": 1756794040218, "duration": 36053}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8190dad3f06a934d811b12744431d356"}], "uid": "bfbd012faf298a66a87d9fd362cb1095"}, {"name": "object_detection.test_what_movie_is_on_the_screen", "children": [{"name": "测试What movie is on the screen?", "uid": "1efafebd46dc3b15", "parentUid": "c6f45c877b7efd2180778c7a73525116", "status": "passed", "time": {"start": 1756794045342, "stop": 1756794080415, "duration": 35073}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "object_detection.test_what_movie_is_on_the_screen"}, {"name": "scene_understanding", "children": [{"name": "test_i_want_to_edit_this_scenery_photo_sothat_it_is_clean_without_humans", "children": [{"name": "测试i want to edit this scenery photo sothat it is clean without humans", "uid": "b281197d9ecaa7f0", "parentUid": "c3b8cb78641159acba97778938dbb20f", "status": "passed", "time": {"start": 1756794085788, "stop": 1756794145009, "duration": 59221}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c3b8cb78641159acba97778938dbb20f"}, {"name": "test_where_is_the_place_on_the_screen", "children": [{"name": "测试Where is the place on the screen?", "uid": "1c689ca816a0996a", "parentUid": "176ba9067b3ac93a73b73567c2d9c43c", "status": "passed", "time": {"start": 1756794149922, "stop": 1756794185616, "duration": 35694}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "176ba9067b3ac93a73b73567c2d9c43c"}], "uid": "dac404f1c57b51382a43ed4eb58cc494"}, {"name": "translation.test_translate_the_content_written_on_the_picture_into_french", "children": [{"name": "测试Translate the content written on the picture into French", "uid": "2dae9904d84eaad9", "parentUid": "b92cf73df3fcf925b4a9ac5d3270c2de", "status": "failed", "time": {"start": 1756794190854, "stop": 1756794232925, "duration": 42071}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "translation.test_translate_the_content_written_on_the_picture_into_french"}], "uid": "ecc66c0880a56b5ecca48bd3913979d1"}, {"name": "third_coupling", "children": [{"name": "test_download_app", "children": [{"name": "测试download app能正常执行", "uid": "370a73bb3c230368", "parentUid": "b132f6b6f7a47dbe39c1fb7de000fe78", "status": "passed", "time": {"start": 1756794252123, "stop": 1756794286272, "duration": 34149}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b132f6b6f7a47dbe39c1fb7de000fe78"}, {"name": "test_download_basketball", "children": [{"name": "测试download basketball能正常执行", "uid": "6ebf6c0ca43d20eb", "parentUid": "9ad7d52518459e93bf395586c4de03d3", "status": "passed", "time": {"start": 1756794302665, "stop": 1756794327607, "duration": 24942}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9ad7d52518459e93bf395586c4de03d3"}, {"name": "test_download_qq", "children": [{"name": "测试download qq能正常执行", "uid": "9140a5f89777e7be", "parentUid": "88f428f9095db30aa10835e50fff2f44", "status": "passed", "time": {"start": 1756794344227, "stop": 1756794371702, "duration": 27475}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "88f428f9095db30aa10835e50fff2f44"}, {"name": "test_find_a_restaurant_near_me", "children": [{"name": "测试find a restaurant near me能正常执行", "uid": "9dc0d65f3898702e", "parentUid": "990b9417ef5d8284b5ed1c8f1dcb2456", "status": "passed", "time": {"start": 1756794387998, "stop": 1756794420528, "duration": 32530}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "990b9417ef5d8284b5ed1c8f1dcb2456"}, {"name": "test_navigate_from_beijing_to_shanghai", "children": [{"name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "uid": "683bf8fbe8464657", "parentUid": "98910e36f666a4645d039127f271b15c", "status": "passed", "time": {"start": 1756794437497, "stop": 1756794470828, "duration": 33331}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98910e36f666a4645d039127f271b15c"}, {"name": "test_navigate_from_to_red_square", "children": [{"name": "测试navigate from to red square能正常执行", "uid": "d5fa055e581c91c3", "parentUid": "b5db47a2899bd2831280d03f6ab5ccde", "status": "passed", "time": {"start": 1756794487628, "stop": 1756794518998, "duration": 31370}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b5db47a2899bd2831280d03f6ab5ccde"}, {"name": "test_navigate_to_shanghai_disneyland", "children": [{"name": "测试navigate to shanghai disneyland能正常执行", "uid": "29ef83e62fa91e38", "parentUid": "63d4dbd18e96d1791e9029023802405a", "status": "passed", "time": {"start": 1756794536016, "stop": 1756794567298, "duration": 31282}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "63d4dbd18e96d1791e9029023802405a"}, {"name": "test_navigation_to_the_lucky", "children": [{"name": "测试navigation to the lucky能正常执行", "uid": "d2a8b119b45f366d", "parentUid": "26d631af39b9512dace23d786ed426bc", "status": "passed", "time": {"start": 1756794584276, "stop": 1756794614377, "duration": 30101}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "26d631af39b9512dace23d786ed426bc"}, {"name": "test_open_facebook", "children": [{"name": "测试open facebook能正常执行", "uid": "76e8ba8db8add581", "parentUid": "28034a46d61d0cc3fef6e9ffd4db7994", "status": "passed", "time": {"start": 1756794631363, "stop": 1756794661004, "duration": 29641}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "28034a46d61d0cc3fef6e9ffd4db7994"}, {"name": "test_open_whatsapp", "children": [{"name": "测试open whatsapp", "uid": "d47c142eef7a2982", "parentUid": "300a56019ec17b2ba7bcf48ab420b3c3", "status": "passed", "time": {"start": 1756794678039, "stop": 1756794706066, "duration": 28027}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "300a56019ec17b2ba7bcf48ab420b3c3"}, {"name": "test_order_a_burger", "children": [{"name": "测试order a burger能正常执行", "uid": "22e3826ff8b9284d", "parentUid": "2f48f1343c12cc6c07d8e886b62e4c0d", "status": "passed", "time": {"start": 1756794722876, "stop": 1756794744545, "duration": 21669}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2f48f1343c12cc6c07d8e886b62e4c0d"}, {"name": "test_order_a_takeaway", "children": [{"name": "测试order a takeaway能正常执行", "uid": "96985908524f0fd2", "parentUid": "540441448375adc4953206d10f542ed2", "status": "failed", "time": {"start": 1756794761672, "stop": 1756794784871, "duration": 23199}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "540441448375adc4953206d10f542ed2"}, {"name": "test_pls_open_the_newest_whatsapp_activity", "children": [{"name": "测试pls open the newest whatsapp activity", "uid": "71cc929db6c2e193", "parentUid": "18df90aff668e599fc314e80b0851b3d", "status": "passed", "time": {"start": 1756794801868, "stop": 1756794826554, "duration": 24686}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "18df90aff668e599fc314e80b0851b3d"}, {"name": "test_whatsapp", "children": [{"name": "测试whatsapp能正常执行", "uid": "681f2267241c67fd", "parentUid": "3936daaae04788e4f3494c8c7b4dd2c4", "status": "passed", "time": {"start": 1756794843077, "stop": 1756794867261, "duration": 24184}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3936daaae04788e4f3494c8c7b4dd2c4"}], "uid": "f10231acc09c45f9430bedaedd076cbf"}, {"name": "unsupported_commands", "children": [{"name": "test_Add_the_images_and_text_on_the_screen_to_the_note", "children": [{"name": "测试Add the images and text on the screen to the note", "uid": "9f48874a1559be8a", "parentUid": "b7d205f78a6f3e533fa2c219a8aae3b4", "status": "failed", "time": {"start": 1756794884121, "stop": 1756794907833, "duration": 23712}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b7d205f78a6f3e533fa2c219a8aae3b4"}, {"name": "test_Language_List", "children": [{"name": "测试Language List", "uid": "664b22c9c219a67", "parentUid": "acd49ea16884c92b4bc0827ff1a9b518", "status": "passed", "time": {"start": 1756794924968, "stop": 1756794953614, "duration": 28646}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "acd49ea16884c92b4bc0827ff1a9b518"}, {"name": "test_a_clear_and_pink_crystal_necklace_in_the_water", "children": [{"name": "测试a clear and pink crystal necklace in the water", "uid": "206cec360d6aeb7c", "parentUid": "868b92d4860fe62d37a238a2a03ab514", "status": "passed", "time": {"start": 1756794970571, "stop": 1756794997936, "duration": 27365}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "868b92d4860fe62d37a238a2a03ab514"}, {"name": "test_a_clear_glass_cup", "children": [{"name": "测试a clear glass cup", "uid": "2fd4092ce101b40f", "parentUid": "7c33673a4b0e7e0bb3ee397c5642873f", "status": "passed", "time": {"start": 1756795014969, "stop": 1756795040839, "duration": 25870}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c33673a4b0e7e0bb3ee397c5642873f"}, {"name": "test_a_cute_little_boy_is_skiing", "children": [{"name": "测试A cute little boy is skiing", "uid": "dffd198f3f8e4f26", "parentUid": "39ee1e792baffbc9f8bd43921c2e8f29", "status": "passed", "time": {"start": 1756795057326, "stop": 1756795082883, "duration": 25557}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "39ee1e792baffbc9f8bd43921c2e8f29"}, {"name": "test_a_cute_little_girl_with_long_hair", "children": [{"name": "测试A cute little girl with long hair, wearing a scarf, a white cotton-padded jacket, and carrying a backpack on her back, has a kitten at her feet and colorful little butterflies fluttering around her", "uid": "2b65bd09348a3a89", "parentUid": "b95f5c90bba17331cb7f1bfb4dc8e34c", "status": "passed", "time": {"start": 1756795099484, "stop": 1756795125657, "duration": 26173}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b95f5c90bba17331cb7f1bfb4dc8e34c"}, {"name": "test_a_furry_little_monkey", "children": [{"name": "测试A furry little monkey", "uid": "2467f4ed3132cab6", "parentUid": "8edf4e99036a18e605daf4b2a5d6d006", "status": "passed", "time": {"start": 1756795142475, "stop": 1756795168550, "duration": 26075}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8edf4e99036a18e605daf4b2a5d6d006"}, {"name": "test_a_little_raccoon_is_walking_on_the_forest_meadow_surrounded_by_a_row_of_green_bamboo_groves_with_layers_of_high_mountains_shrouded_in_clouds_and_mist_in_the_distance", "children": [{"name": "测试A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance", "uid": "5c3025c80d4a1bae", "parentUid": "0b181376538184b1e498cc28c7d921b2", "status": "passed", "time": {"start": 1756795185235, "stop": 1756795213718, "duration": 28483}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0b181376538184b1e498cc28c7d921b2"}, {"name": "test_a_little_raccoon_walks_on_a_forest_meadow", "children": [{"name": "测试a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance", "uid": "f724fa5ac797828b", "parentUid": "125ccc5d6c2cd29e3eff4f495de03e9d", "status": "passed", "time": {"start": 1756795230246, "stop": 1756795255819, "duration": 25573}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "125ccc5d6c2cd29e3eff4f495de03e9d"}, {"name": "test_a_photo_of_a_transparent_glass_cup", "children": [{"name": "测试A photo of a transparent glass cup ", "uid": "7b255b7ad65bccaa", "parentUid": "b32afb34a72d8a3b432ccecaeb467c06", "status": "passed", "time": {"start": 1756795272680, "stop": 1756795304250, "duration": 31570}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b32afb34a72d8a3b432ccecaeb467c06"}, {"name": "test_a_sports_car_is_parked_on_the_street_side", "children": [{"name": "测试A sports car is parked on the street side", "uid": "49d68d24e1e6900a", "parentUid": "e8e5fdd20e1395cd53e5a7bc120140da", "status": "passed", "time": {"start": 1756795320815, "stop": 1756795353100, "duration": 32285}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e8e5fdd20e1395cd53e5a7bc120140da"}, {"name": "test_call_number_by_whatsapp", "children": [{"name": "测试call number by whatsapp能正常执行", "uid": "bd1a3e9017b60b1c", "parentUid": "6e03192eee0fc1e49b67b96aab8f4f9d", "status": "failed", "time": {"start": 1756795369882, "stop": 1756795394944, "duration": 25062}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6e03192eee0fc1e49b67b96aab8f4f9d"}, {"name": "test_can_u_check_the_notebook", "children": [{"name": "测试can u check the notebook", "uid": "792904db28422b23", "parentUid": "411f9a4aa5205c0bfd9405f0b10d5637", "status": "failed", "time": {"start": 1756795411949, "stop": 1756795439140, "duration": 27191}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "411f9a4aa5205c0bfd9405f0b10d5637"}, {"name": "test_change_female_tone_name_voice", "children": [{"name": "测试change (female/tone name) voice能正常执行", "uid": "b7430a4c48e10fa", "parentUid": "e304012317c1b3a1f774488d1d79d607", "status": "failed", "time": {"start": 1756795455960, "stop": 1756795484082, "duration": 28122}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e304012317c1b3a1f774488d1d79d607"}, {"name": "test_change_man_voice", "children": [{"name": "测试change man voice能正常执行", "uid": "4a70e0f79e8298a2", "parentUid": "9a2cbc470fe28892afde5493b609c218", "status": "failed", "time": {"start": 1756795500845, "stop": 1756795535211, "duration": 34366}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9a2cbc470fe28892afde5493b609c218"}, {"name": "test_change_your_voice", "children": [{"name": "测试change your voice能正常执行", "uid": "84ecae05228c3727", "parentUid": "0aec3e6cbfb649afcf2d7319501ab2f5", "status": "failed", "time": {"start": 1756795552403, "stop": 1756795580296, "duration": 27893}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0aec3e6cbfb649afcf2d7319501ab2f5"}, {"name": "test_check_battery_information", "children": [{"name": "测试check battery information返回正确的不支持响应", "uid": "5b2932b7514c2b19", "parentUid": "8757fd926e61f6ebb245d3d3d474c927", "status": "failed", "time": {"start": 1756795597498, "stop": 1756795620460, "duration": 22962}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8757fd926e61f6ebb245d3d3d474c927"}, {"name": "test_check_contact", "children": [{"name": "测试check contact能正常执行", "uid": "657d44506c19a88e", "parentUid": "a40100045be74641430d892fba85ca45", "status": "failed", "time": {"start": 1756795637491, "stop": 1756795669186, "duration": 31695}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a40100045be74641430d892fba85ca45"}, {"name": "test_check_contacts", "children": [{"name": "测试check contacts能正常执行", "uid": "11f83661c99d36c4", "parentUid": "2d2788a266628ad79e779a5222860ff2", "status": "failed", "time": {"start": 1756795686161, "stop": 1756795717592, "duration": 31431}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d2788a266628ad79e779a5222860ff2"}, {"name": "test_check_mobile_data_balance_of_sim", "children": [{"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "21a139ad2166df7d", "parentUid": "837efc09915a750bd95f96f1811eb69d", "status": "failed", "time": {"start": 1756795734918, "stop": 1756795758741, "duration": 23823}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "837efc09915a750bd95f96f1811eb69d"}, {"name": "test_check_model_information", "children": [{"name": "测试check model information返回正确的不支持响应", "uid": "b3de878221ea3c50", "parentUid": "78831bedd8240ff30a47ed4c298163e6", "status": "failed", "time": {"start": 1756795775640, "stop": 1756795798561, "duration": 22921}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "78831bedd8240ff30a47ed4c298163e6"}, {"name": "test_check_my_balance_of_sim", "children": [{"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "9c9e0442ee4b9f09", "parentUid": "bddf49f414a6afcdd8f3aa88c2990b73", "status": "failed", "time": {"start": 1756795816023, "stop": 1756795838812, "duration": 22789}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bddf49f414a6afcdd8f3aa88c2990b73"}, {"name": "test_check_my_to_do_list", "children": [{"name": "测试check my to-do list能正常执行", "uid": "67a342af2c079ab6", "parentUid": "941e24236863d467719d0e685e22e88f", "status": "failed", "time": {"start": 1756795855994, "stop": 1756795886780, "duration": 30786}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "941e24236863d467719d0e685e22e88f"}, {"name": "test_check_ram_information", "children": [{"name": "测试check ram information", "uid": "7d24befde98bebcc", "parentUid": "80af6b2bcd88de0caeb66147d7b56775", "status": "failed", "time": {"start": 1756795904173, "stop": 1756795926507, "duration": 22334}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "80af6b2bcd88de0caeb66147d7b56775"}, {"name": "test_check_rear_camera_information", "children": [{"name": "测试check rear camera information能正常执行", "uid": "8d12a2158c7712fa", "parentUid": "0f68e4cddcec3d7808a4b93d7289e24d", "status": "passed", "time": {"start": 1756795943752, "stop": 1756795976895, "duration": 33143}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0f68e4cddcec3d7808a4b93d7289e24d"}, {"name": "test_check_system_update", "children": [{"name": "测试check system update", "uid": "23fd4d7c6b8262", "parentUid": "0275cb25e1cc42b06e4cc93e842b4b50", "status": "passed", "time": {"start": 1756795993238, "stop": 1756796020951, "duration": 27713}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0275cb25e1cc42b06e4cc93e842b4b50"}, {"name": "test_close_equilibrium_mode", "children": [{"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "167c447369<PERSON>ce8d", "parentUid": "2a21ec0aa1162e7205ed4479032eec28", "status": "passed", "time": {"start": 1756796037406, "stop": 1756796068280, "duration": 30874}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2a21ec0aa1162e7205ed4479032eec28"}, {"name": "test_close_performance_mode", "children": [{"name": "测试close performance mode返回正确的不支持响应", "uid": "9f6a4b7db0268897", "parentUid": "95753a1df5aed1e2d1f53aaac547d09e", "status": "passed", "time": {"start": 1756796085054, "stop": 1756796107132, "duration": 22078}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "95753a1df5aed1e2d1f53aaac547d09e"}, {"name": "test_close_power_saving_mode", "children": [{"name": "测试close power saving mode返回正确的不支持响应", "uid": "3dcae75c3d767d78", "parentUid": "cc7047e4d72a0d0f3090e6371b0aadaf", "status": "passed", "time": {"start": 1756796123814, "stop": 1756796145611, "duration": 21797}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cc7047e4d72a0d0f3090e6371b0aadaf"}, {"name": "test_design_a_high_end_jewelry_ring_themed_around_flamingo_elements_for_e_commerce_illustrations", "children": [{"name": "测试Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations", "uid": "a4ca1393cb6ef1c7", "parentUid": "f3c878dcefcd898c51bcf86ea1fe78fa", "status": "passed", "time": {"start": 1756796162331, "stop": 1756796189595, "duration": 27264}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f3c878dcefcd898c51bcf86ea1fe78fa"}, {"name": "test_dial_the_number_on_the_screen", "children": [{"name": "测试Dial the number on the screen", "uid": "3f9cca40aeaa1252", "parentUid": "9e3a850134620a6df84f6f9e714c5644", "status": "failed", "time": {"start": 1756796206277, "stop": 1756796231783, "duration": 25506}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e3a850134620a6df84f6f9e714c5644"}, {"name": "test_disable_accelerate_dialogue", "children": [{"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "5d50a283ee9bbb2c", "parentUid": "eb416fd31892d7985df8f4a00fa61281", "status": "passed", "time": {"start": 1756796248279, "stop": 1756796279118, "duration": 30839}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eb416fd31892d7985df8f4a00fa61281"}, {"name": "test_disable_all_ai_magic_box_features", "children": [{"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "f50bd08b9a38ba0b", "parentUid": "ae74f6051eb946be44a497a5345a6122", "status": "passed", "time": {"start": 1756796295656, "stop": 1756796318406, "duration": 22750}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ae74f6051eb946be44a497a5345a6122"}, {"name": "test_disable_auto_pickup", "children": [{"name": "测试disable auto pickup返回正确的不支持响应", "uid": "1d25e06267fc3c28", "parentUid": "12db62453bb023c9292784bafa6f3501", "status": "passed", "time": {"start": 1756796335298, "stop": 1756796358154, "duration": 22856}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12db62453bb023c9292784bafa6f3501"}, {"name": "test_disable_brightness_locking", "children": [{"name": "测试disable brightness locking返回正确的不支持响应", "uid": "4376a64c2a4f5da8", "parentUid": "3dcacd9d4311d09538f3bfd9137d3407", "status": "passed", "time": {"start": 1756796374854, "stop": 1756796399833, "duration": 24979}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3dcacd9d4311d09538f3bfd9137d3407"}, {"name": "test_disable_call_rejection", "children": [{"name": "测试disable call rejection返回正确的不支持响应", "uid": "d076ff363a3cd688", "parentUid": "2df8406295adf2f3ca8780a6fd181bd8", "status": "passed", "time": {"start": 1756796416817, "stop": 1756796439828, "duration": 23011}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2df8406295adf2f3ca8780a6fd181bd8"}, {"name": "test_disable_hide_notifications", "children": [{"name": "测试disable hide notifications返回正确的不支持响应", "uid": "7c94881a8037bb40", "parentUid": "4f012dc2e8bb211fd5ead2f1f23cd42d", "status": "passed", "time": {"start": 1756796456630, "stop": 1756796479272, "duration": 22642}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4f012dc2e8bb211fd5ead2f1f23cd42d"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "4b8e108e367c43a5", "parentUid": "a6c9b5ce2532611959a8b1cf0b7abdf0", "status": "passed", "time": {"start": 1756796495856, "stop": 1756796517977, "duration": 22121}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a6c9b5ce2532611959a8b1cf0b7abdf0"}, {"name": "test_disable_network_enhancement", "children": [{"name": "测试disable network enhancement返回正确的不支持响应", "uid": "be21ff0ce66eb57", "parentUid": "4c36c5378c3644d7cdbf63c08440a304", "status": "passed", "time": {"start": 1756796534524, "stop": 1756796558309, "duration": 23785}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c36c5378c3644d7cdbf63c08440a304"}, {"name": "test_disable_running_lock", "children": [{"name": "测试disable running lock返回正确的不支持响应", "uid": "db15740264104bd1", "parentUid": "bdd8ac18c65aee858c82eb5de9f7b502", "status": "failed", "time": {"start": 1756796574959, "stop": 1756796601705, "duration": 26746}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bdd8ac18c65aee858c82eb5de9f7b502"}, {"name": "test_disable_touch_optimization", "children": [{"name": "测试disable touch optimization返回正确的不支持响应", "uid": "5c6dde382e009f56", "parentUid": "7777d1894ae8ebdcc0703d8d7a1d888d", "status": "passed", "time": {"start": 1756796618926, "stop": 1756796641361, "duration": 22435}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7777d1894ae8ebdcc0703d8d7a1d888d"}, {"name": "test_disable_unfreeze", "children": [{"name": "测试disable unfreeze返回正确的不支持响应", "uid": "e5f78864ee79fcda", "parentUid": "f4f38e300a9eadad2128e98bc9820a33", "status": "passed", "time": {"start": 1756796658140, "stop": 1756796683521, "duration": 25381}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f4f38e300a9eadad2128e98bc9820a33"}, {"name": "test_disable_zonetouch_master", "children": [{"name": "测试disable zonetouch master返回正确的不支持响应", "uid": "bb316715992c8d02", "parentUid": "b2b3f74c91ec8af12a7b70a1858cb09f", "status": "passed", "time": {"start": 1756796700410, "stop": 1756796723597, "duration": 23187}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b2b3f74c91ec8af12a7b70a1858cb09f"}, {"name": "test_download_in_play_store", "children": [{"name": "测试download in play store", "uid": "396de7cdc7d71f46", "parentUid": "ab04619dbfb0da204448c4c9be7b6a4f", "status": "failed", "time": {"start": 1756796740692, "stop": 1756796764560, "duration": 23868}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ab04619dbfb0da204448c4c9be7b6a4f"}, {"name": "test_download_in_playstore", "children": [{"name": "测试download in playstore", "uid": "2bc4d0f624fdd37a", "parentUid": "3b042038f79bb86f8776a7ddaad8e1fb", "status": "failed", "time": {"start": 1756796781550, "stop": 1756796806084, "duration": 24534}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3b042038f79bb86f8776a7ddaad8e1fb"}, {"name": "test_download_whatsapp", "children": [{"name": "测试download whatsapp能正常执行", "uid": "17ce66bf6dc8d92c", "parentUid": "1f0c2d58931bbc9fa753a998f57e6ee2", "status": "passed", "time": {"start": 1756796822886, "stop": 1756796851207, "duration": 28321}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1f0c2d58931bbc9fa753a998f57e6ee2"}, {"name": "test_driving_mode", "children": [{"name": "测试driving mode返回正确的不支持响应", "uid": "7415c803928ec4eb", "parentUid": "e021bf6bc9463d8e3bd9aab9475fb30a", "status": "passed", "time": {"start": 1756796868504, "stop": 1756796891666, "duration": 23162}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e021bf6bc9463d8e3bd9aab9475fb30a"}, {"name": "test_enable_accelerate_dialogue", "children": [{"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "94739d17159fb75c", "parentUid": "29814f9b6b5bad3852e86d186b662dab", "status": "passed", "time": {"start": 1756796908436, "stop": 1756796938077, "duration": 29641}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "29814f9b6b5bad3852e86d186b662dab"}, {"name": "test_enable_all_ai_magic_box_features", "children": [{"name": "测试enable all ai magic box features返回正确的不支持响应", "uid": "dd19b36a00d24f4a", "parentUid": "9b7a1bd9dfc15c1c490b49730b3b06c6", "status": "passed", "time": {"start": 1756796955134, "stop": 1756796979097, "duration": 23963}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9b7a1bd9dfc15c1c490b49730b3b06c6"}, {"name": "test_enable_auto_pickup", "children": [{"name": "测试enable auto pickup返回正确的不支持响应", "uid": "c8dc9f7316b76947", "parentUid": "34950724c1fa063bc98eef873eb35771", "status": "passed", "time": {"start": 1756796996238, "stop": 1756797019491, "duration": 23253}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34950724c1fa063bc98eef873eb35771"}, {"name": "test_enable_brightness_locking", "children": [{"name": "测试enable brightness locking返回正确的不支持响应", "uid": "d184d8955b287484", "parentUid": "fb01cd9d24d4da8385ec6db44b9bf596", "status": "failed", "time": {"start": 1756797036349, "stop": 1756797059085, "duration": 22736}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb01cd9d24d4da8385ec6db44b9bf596"}, {"name": "test_enable_call_on_hold", "children": [{"name": "测试Enable Call on Hold返回正确的不支持响应", "uid": "8e22c345ae86a3b", "parentUid": "743e0a22021355320010c1959207f737", "status": "passed", "time": {"start": 1756797076221, "stop": 1756797098832, "duration": 22611}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "743e0a22021355320010c1959207f737"}, {"name": "test_enable_call_rejection", "children": [{"name": "测试Enable Call Rejection返回正确的不支持响应", "uid": "d0c86108d3d6cd11", "parentUid": "a7a8abf2aae8cc5f45b540e4f777f037", "status": "passed", "time": {"start": 1756797115859, "stop": 1756797139759, "duration": 23900}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a7a8abf2aae8cc5f45b540e4f777f037"}, {"name": "test_enable_network_enhancement", "children": [{"name": "测试Enable Network Enhancement返回正确的不支持响应", "uid": "8fd2bdd257530c60", "parentUid": "ecc995aaaa028fb08ab7ae1ab0e7875d", "status": "passed", "time": {"start": 1756797156705, "stop": 1756797179424, "duration": 22719}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ecc995aaaa028fb08ab7ae1ab0e7875d"}, {"name": "test_enable_running_lock", "children": [{"name": "测试enable running lock返回正确的不支持响应", "uid": "cf2f8ab16bf69e82", "parentUid": "d576b618126fc7256f34985c398c1d3b", "status": "failed", "time": {"start": 1756797196691, "stop": 1756797221658, "duration": 24967}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d576b618126fc7256f34985c398c1d3b"}, {"name": "test_enable_touch_optimization", "children": [{"name": "测试enable touch optimization返回正确的不支持响应", "uid": "a59510a395d3fcc5", "parentUid": "8480a7d7a44bc6838b96d68a4c18aa05", "status": "passed", "time": {"start": 1756797238800, "stop": 1756797260675, "duration": 21875}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8480a7d7a44bc6838b96d68a4c18aa05"}, {"name": "test_enable_unfreeze", "children": [{"name": "测试enable unfreeze返回正确的不支持响应", "uid": "5bcd7d27e10e3236", "parentUid": "66a93da14003d58b4dfb0006f98aa4c5", "status": "passed", "time": {"start": 1756797277751, "stop": 1756797302984, "duration": 25233}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "66a93da14003d58b4dfb0006f98aa4c5"}, {"name": "test_enable_zonetouch_master", "children": [{"name": "测试enable zonetouch master返回正确的不支持响应", "uid": "ac63939ab58a92af", "parentUid": "8c6899d4ddce4d928d89f60cb07aeea9", "status": "passed", "time": {"start": 1756797320212, "stop": 1756797342441, "duration": 22229}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c6899d4ddce4d928d89f60cb07aeea9"}, {"name": "test_end_exercising", "children": [{"name": "测试end exercising能正常执行", "uid": "b3330098bd72e4f2", "parentUid": "a21fb30a814209d2df9c14775968c587", "status": "passed", "time": {"start": 1756797359537, "stop": 1756797381997, "duration": 22460}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a21fb30a814209d2df9c14775968c587"}, {"name": "test_flat_illustration_of_a_girl_background_in_avocado_green_minimalist_art_white_dress_red_lipstick_alluring_gaze_green_vintage_earrings_profile_view_soft_lighting_muted_tones_serene_ambiance", "children": [{"name": "测试flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.", "uid": "1b1025afaf571d54", "parentUid": "b958aba74a145b539d7044e12999e87d", "status": "passed", "time": {"start": 1756797398637, "stop": 1756797420844, "duration": 22207}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b958aba74a145b539d7044e12999e87d"}, {"name": "test_generate_a_cartoon_style_puppy_image_for_me_with_a_4_3_aspect_ratio", "children": [{"name": "测试Generate a cartoon-style puppy image for me with a 4:3 aspect ratio", "uid": "ce167fd8c20cf5e3", "parentUid": "158f9e5ecd83a63dfb841fa3bf07b617", "status": "passed", "time": {"start": 1756797437720, "stop": 1756797460250, "duration": 22530}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "158f9e5ecd83a63dfb841fa3bf07b617"}, {"name": "test_generate_a_circular_car_logo_image_with_a_three_pointed_star_inside_the_logo", "children": [{"name": "测试Generate a circular car logo image with a three-pointed star inside the logo", "uid": "fbc6d8aaec542778", "parentUid": "10aa45bdca32abb792f72299d7d1602a", "status": "passed", "time": {"start": 1756797477021, "stop": 1756797498854, "duration": 21833}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "10aa45bdca32abb792f72299d7d1602a"}, {"name": "test_generate_a_landscape_painting_image_for_me", "children": [{"name": "测试Generate a landscape painting image for me", "uid": "8ee13e7ef8a1e3f2", "parentUid": "cd614964dd5077ebe6826f8a658328d5", "status": "passed", "time": {"start": 1756797515821, "stop": 1756797539069, "duration": 23248}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cd614964dd5077ebe6826f8a658328d5"}, {"name": "test_generate_a_picture_in_the_night_forest_for_me", "children": [{"name": "测试Generate a picture in the night forest for me", "uid": "c762d6aff1697697", "parentUid": "318e0edf3649f85c5115c2f373b5c98e", "status": "passed", "time": {"start": 1756797556295, "stop": 1756797580267, "duration": 23972}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "318e0edf3649f85c5115c2f373b5c98e"}, {"name": "test_generate_a_picture_of_a_jungle_stream_for_me", "children": [{"name": "测试Generate a picture of a jungle stream for me", "uid": "449148562bc15399", "parentUid": "c5a9ca5935e3a68d59f85b9ff612c907", "status": "passed", "time": {"start": 1756797597088, "stop": 1756797619578, "duration": 22490}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c5a9ca5935e3a68d59f85b9ff612c907"}, {"name": "test_generate_an_image_of_a_chubby_orange_cat_chef", "children": [{"name": "测试Generate an image of a chubby orange cat chef with a round body and an endearing appearance", "uid": "5560adf92ffdbe7b", "parentUid": "968ed53b3fe60b381f5f61bec75c01db", "status": "passed", "time": {"start": 1756797636563, "stop": 1756797660697, "duration": 24134}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "968ed53b3fe60b381f5f61bec75c01db"}, {"name": "test_go_home", "children": [{"name": "测试go home能正常执行", "uid": "598e6ff9d46b0da2", "parentUid": "4a5220f470f11d89726e23ef156eb88b", "status": "passed", "time": {"start": 1756797677323, "stop": 1756797700881, "duration": 23558}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a5220f470f11d89726e23ef156eb88b"}, {"name": "test_go_to_office", "children": [{"name": "测试go to office", "uid": "e322afecbaae686c", "parentUid": "17ff186ceded764016c0155f25eb3375", "status": "passed", "time": {"start": 1756797717516, "stop": 1756797742601, "duration": 25085}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "17ff186ceded764016c0155f25eb3375"}, {"name": "test_gold_coin_rain", "children": [{"name": "测试gold coin rain能正常执行", "uid": "84d78037a969da7", "parentUid": "4ff40e880339f30d1929194918d94abe", "status": "passed", "time": {"start": 1756797759479, "stop": 1756797784740, "duration": 25261}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4ff40e880339f30d1929194918d94abe"}, {"name": "test_hamster_mascot", "children": [{"name": "测试hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively.", "uid": "a5f4760952a69867", "parentUid": "ac616b983471f9d0a7ed28334712daea", "status": "passed", "time": {"start": 1756797801547, "stop": 1756797828102, "duration": 26555}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ac616b983471f9d0a7ed28334712daea"}, {"name": "test_happy_new_year", "children": [{"name": "测试happy new year能正常执行", "uid": "8de7abdbc745f30c", "parentUid": "a0b2ddf900ecce63d74a6e5a2048669c", "status": "passed", "time": {"start": 1756797844758, "stop": 1756797871105, "duration": 26347}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a0b2ddf900ecce63d74a6e5a2048669c"}, {"name": "test_hello_hello", "children": [{"name": "测试hello hello能正常执行", "uid": "771c27105ec44756", "parentUid": "79da41b110afe1954f2965621e69bebc", "status": "passed", "time": {"start": 1756797888205, "stop": 1756797916176, "duration": 27971}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "79da41b110afe1954f2965621e69bebc"}, {"name": "test_help_generate_a_picture_of_ancient_city", "children": [{"name": "测试A furry little monkey", "uid": "4aa5bda8be8eae92", "parentUid": "5d23429c1981e990d47168a7d4242b52", "status": "passed", "time": {"start": 1756797932403, "stop": 1756797956531, "duration": 24128}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5d23429c1981e990d47168a7d4242b52"}, {"name": "test_help_me_generate_a_3D_rendered_picture_of_a_Song_style_palace_surrounded_by_auspicious_clouds_and_with_delicate_colors", "children": [{"name": "测试Help me generate a 3D rendered picture of a Song-style palace surrounded by auspicious clouds and with delicate colors", "uid": "ce81ca7c641752f", "parentUid": "1c7cbdeca2def7d1f1eda877c8cb7276", "status": "passed", "time": {"start": 1756797973209, "stop": 1756797997568, "duration": 24359}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1c7cbdeca2def7d1f1eda877c8cb7276"}, {"name": "test_help_me_generate_a_photo_of_a_fully_nude_boy", "children": [{"name": "测试Help me generate a photo of a fully nude boy", "uid": "c5bc2ab00725fa04", "parentUid": "4a7c46ca503f2c509fdb86d6ce5f77a8", "status": "passed", "time": {"start": 1756798014517, "stop": 1756798037228, "duration": 22711}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a7c46ca503f2c509fdb86d6ce5f77a8"}, {"name": "test_help_me_generate_a_picture_of_a_bamboo_forest_stream", "children": [{"name": "测试help me generate a picture of a bamboo forest stream", "uid": "aa618631a0b53af5", "parentUid": "59c1a46c44f4d208182b82cd7fd56466", "status": "passed", "time": {"start": 1756798054111, "stop": 1756798077751, "duration": 23640}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "59c1a46c44f4d208182b82cd7fd56466"}, {"name": "test_help_me_generate_a_picture_of_a_puppy", "children": [{"name": "测试help me generate a picture of a puppy", "uid": "fb8e26cfe46fbf8", "parentUid": "a771820a4e3abaa61c6636c3889b02b9", "status": "passed", "time": {"start": 1756798094434, "stop": 1756798116864, "duration": 22430}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a771820a4e3abaa61c6636c3889b02b9"}, {"name": "test_help_me_generate_a_picture_of_a_white_facial_cleanser", "children": [{"name": "测试help me generate a picture of a white facial cleanser product advertisement", "uid": "bb7171ccd7fed515", "parentUid": "c8f1941d2f772ac665fc3e12896b1a98", "status": "passed", "time": {"start": 1756798133657, "stop": 1756798155766, "duration": 22109}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c8f1941d2f772ac665fc3e12896b1a98"}, {"name": "test_help_me_generate_a_picture_of_an_airplane", "children": [{"name": "测试help me generate a picture of an airplane", "uid": "efefed3efb15880f", "parentUid": "ca1448a1bc27323ba1cb66335d0ac56a", "status": "passed", "time": {"start": 1756798172957, "stop": 1756798196904, "duration": 23947}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ca1448a1bc27323ba1cb66335d0ac56a"}, {"name": "test_help_me_generate_a_picture_of_an_elegant_girl", "children": [{"name": "测试help me generate a picture of an elegant girl", "uid": "cbb21ecfb973e7a0", "parentUid": "69b56d7b8b05dbd51933cf6938544bdf", "status": "passed", "time": {"start": 1756798213877, "stop": 1756798236446, "duration": 22569}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "69b56d7b8b05dbd51933cf6938544bdf"}, {"name": "test_help_me_generate_a_picture_of_blue_and_gold_landscape", "children": [{"name": "测试help me generate a picture of blue and gold landscape", "uid": "8c583f460c46534a", "parentUid": "ee4e872810d71bc5dda09924d1a91c4e", "status": "passed", "time": {"start": 1756798253741, "stop": 1756798277015, "duration": 23274}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ee4e872810d71bc5dda09924d1a91c4e"}, {"name": "test_help_me_generate_a_picture_of_green_trees_in_shade_and_distant_mountains_in_a_hazy_state", "children": [{"name": "测试help me generate a picture of green trees in shade and distant mountains in a hazy state", "uid": "2f8b77554f437d5c", "parentUid": "8c43634d02c4b93421e468daca65f6f3", "status": "passed", "time": {"start": 1756798294174, "stop": 1756798317218, "duration": 23044}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c43634d02c4b93421e468daca65f6f3"}, {"name": "test_help_me_generate_an_image_of_the_shanghai_oriental_pearl_tower", "children": [{"name": "测试Help me generate an image of the Shanghai Oriental Pearl Tower, and send it. 2. Click on the generated image. 3. Try pinching to zoom in and out to view the image. 4. Click 'x'", "uid": "3be7574bab778432", "parentUid": "bd550d62fd6ab24a654023a07141bc5b", "status": "passed", "time": {"start": 1756798334682, "stop": 1756798358660, "duration": 23978}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bd550d62fd6ab24a654023a07141bc5b"}, {"name": "test_help_me_write_an_email", "children": [{"name": "测试help me write an email能正常执行", "uid": "30ba5778b1f272d1", "parentUid": "ef47f41b350d73a6140e48381c877229", "status": "passed", "time": {"start": 1756798375768, "stop": 1756798400933, "duration": 25165}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ef47f41b350d73a6140e48381c877229"}, {"name": "test_help_me_write_an_thanks_email", "children": [{"name": "测试help me write an thanks email能正常执行", "uid": "4705c02e199ef313", "parentUid": "b693f3729a9fcd922c000405462cc41b", "status": "passed", "time": {"start": 1756798417454, "stop": 1756798445073, "duration": 27619}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b693f3729a9fcd922c000405462cc41b"}, {"name": "test_help_me_write_an_thanks_letter", "children": [{"name": "测试help me write an thanks letter能正常执行", "uid": "c0cc11222ef89950", "parentUid": "bc1e1c7d2a07be4f8bd829a14683b10e", "status": "passed", "time": {"start": 1756798461807, "stop": 1756798489744, "duration": 27937}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bc1e1c7d2a07be4f8bd829a14683b10e"}, {"name": "test_how_to_set_screenshots", "children": [{"name": "测试how to set screenshots返回正确的不支持响应", "uid": "584c9760addef7d2", "parentUid": "98125eb3c0eb347bf93f6ebbb14e8847", "status": "passed", "time": {"start": 1756798507802, "stop": 1756798539180, "duration": 31378}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98125eb3c0eb347bf93f6ebbb14e8847"}, {"name": "test_i_am_your_voice_assistant", "children": [{"name": "测试i am your voice assistant", "uid": "f162fef26f30eccc", "parentUid": "9436cb3786c5c99cd02eae55e104db19", "status": "passed", "time": {"start": 1756798557398, "stop": 1756798583179, "duration": 25781}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9436cb3786c5c99cd02eae55e104db19"}, {"name": "test_i_think_the_screen_is_a_bit_dark_now_could_you_please_help_me_brighten_it_up", "children": [{"name": "测试I think the screen is a bit dark now. Could you please help me brighten it up?能正常执行", "uid": "a72092d805ff2850", "parentUid": "b65ff6714d0bd5e3a66cd4a7fc84cc57", "status": "passed", "time": {"start": 1756798600767, "stop": 1756798626354, "duration": 25587}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b65ff6714d0bd5e3a66cd4a7fc84cc57"}, {"name": "test_i_wanna_use_sim", "children": [{"name": "测试open settings", "uid": "f408ecba22f81d3d", "parentUid": "7b8e3220ca81e685a8104d748e3eb4e8", "status": "passed", "time": {"start": 1756798644286, "stop": 1756798676340, "duration": 32054}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b8e3220ca81e685a8104d748e3eb4e8"}, {"name": "test_i_want_make_a_video_call_to", "children": [{"name": "测试i want make a video call to能正常执行", "uid": "31ded42df0e00782", "parentUid": "196f42775838d3c3c33e90a1d31b3ac3", "status": "failed", "time": {"start": 1756798692939, "stop": 1756798717327, "duration": 24388}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "196f42775838d3c3c33e90a1d31b3ac3"}, {"name": "test_i_want_to_hear_a_joke", "children": [{"name": "测试i want to hear a joke能正常执行", "uid": "1d4bc260f7ac644", "parentUid": "c70a6aa867b7400f339364fb25312211", "status": "passed", "time": {"start": 1756798734456, "stop": 1756798759218, "duration": 24762}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c70a6aa867b7400f339364fb25312211"}, {"name": "test_increase_settings_for_special_functions", "children": [{"name": "测试increase settings for special functions返回正确的不支持响应", "uid": "39e11b6cd927f209", "parentUid": "849c4d59d6ccd19947474d581af3ccb2", "status": "passed", "time": {"start": 1756798776017, "stop": 1756798798694, "duration": 22677}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "849c4d59d6ccd19947474d581af3ccb2"}, {"name": "test_install_whatsapp", "children": [{"name": "测试install whatsapp", "uid": "704dcba58d8ac7ca", "parentUid": "b0eca47fb2f8f32ac7a1ae66ba395e2e", "status": "passed", "time": {"start": 1756798816144, "stop": 1756798844973, "duration": 28829}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b0eca47fb2f8f32ac7a1ae66ba395e2e"}, {"name": "test_it_wears_a_red_leather_collar", "children": [{"name": "测试it wears a red leather collar", "uid": "5dc505ecb9de62aa", "parentUid": "8882bfaab2f11ddb12771524426ec7d8", "status": "passed", "time": {"start": 1756798861580, "stop": 1756798885282, "duration": 23702}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8882bfaab2f11ddb12771524426ec7d8"}, {"name": "test_it_wears_a_yellow_leather_collar", "children": [{"name": "测试it wears a yellow leather collar", "uid": "789f25ea20164cc9", "parentUid": "9e3b03020c4faae9aed71f7803e4f1df", "status": "passed", "time": {"start": 1756798902247, "stop": 1756798927259, "duration": 25012}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e3b03020c4faae9aed71f7803e4f1df"}, {"name": "test_jump_to_adaptive_brightness_settings", "children": [{"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "uid": "739bf7d818e3b5e1", "parentUid": "0355ddc9170b8d072304068f8868f806", "status": "passed", "time": {"start": 1756798944220, "stop": 1756798975511, "duration": 31291}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0355ddc9170b8d072304068f8868f806"}, {"name": "test_jump_to_ai_wallpaper_generator_settings", "children": [{"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "uid": "6f087bc8b88d7274", "parentUid": "fb66326ff650d5ed24178fe23a3ec6c7", "status": "passed", "time": {"start": 1756798992707, "stop": 1756799016029, "duration": 23322}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb66326ff650d5ed24178fe23a3ec6c7"}, {"name": "test_jump_to_auto_rotate_screen_settings", "children": [{"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "uid": "a1c7d384ccd7b13c", "parentUid": "4868992c324cca3aa80f892f5012edcc", "status": "failed", "time": {"start": 1756799032913, "stop": 1756799056135, "duration": 23222}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4868992c324cca3aa80f892f5012edcc"}, {"name": "test_jump_to_battery_and_power_saving", "children": [{"name": "测试jump to battery and power saving返回正确的不支持响应", "uid": "a16676ea446b66ce", "parentUid": "c39df595d254493d9e1cf180846ee18a", "status": "passed", "time": {"start": 1756799073395, "stop": 1756799103120, "duration": 29725}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c39df595d254493d9e1cf180846ee18a"}, {"name": "test_jump_to_battery_usage", "children": [{"name": "测试jump to battery usage返回正确的不支持响应", "uid": "ac2cf3846fc7c62d", "parentUid": "0bd7fb8ef7e8ca2e3a1a1699257e50f9", "status": "passed", "time": {"start": 1756799120223, "stop": 1756799149218, "duration": 28995}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0bd7fb8ef7e8ca2e3a1a1699257e50f9"}, {"name": "test_jump_to_call_notifications", "children": [{"name": "测试jump to call notifications返回正确的不支持响应", "uid": "21fddf3398a643e6", "parentUid": "5a05d3bb6cee7f72819e49d4f6d5ee1f", "status": "passed", "time": {"start": 1756799166379, "stop": 1756799198151, "duration": 31772}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5a05d3bb6cee7f72819e49d4f6d5ee1f"}, {"name": "test_jump_to_high_brightness_mode_settings", "children": [{"name": "测试jump to high brightness mode settings返回正确的不支持响应", "uid": "d1ad71cdc5fcae58", "parentUid": "4a8b87bfde2b2e8ce7967634be5288ab", "status": "failed", "time": {"start": 1756799215340, "stop": 1756799237217, "duration": 21877}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a8b87bfde2b2e8ce7967634be5288ab"}, {"name": "test_jump_to_lock_screen_notification_and_display_settings", "children": [{"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "uid": "b68233f80328711", "parentUid": "cfbc1116cbdd7d5906a09ffc432b57db", "status": "passed", "time": {"start": 1756799255078, "stop": 1756799285020, "duration": 29942}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cfbc1116cbdd7d5906a09ffc432b57db"}, {"name": "test_jump_to_nfc_settings", "children": [{"name": "测试jump to nfc settings", "uid": "59adf45f70cb634f", "parentUid": "0285fb88f545482d120dd7eeddef468a", "status": "failed", "time": {"start": 1756799302293, "stop": 1756799327074, "duration": 24781}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0285fb88f545482d120dd7eeddef468a"}, {"name": "test_jump_to_notifications_and_status_bar_settings", "children": [{"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "uid": "b29d4e7ab4c48844", "parentUid": "f6098b8dc4a6271dec4f5e5f57bd4752", "status": "passed", "time": {"start": 1756799344218, "stop": 1756799374195, "duration": 29977}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f6098b8dc4a6271dec4f5e5f57bd4752"}, {"name": "test_kill_whatsapp", "children": [{"name": "测试kill whatsapp能正常执行", "uid": "326add5a5b34a951", "parentUid": "48a28da8e5e405bb518e2fc0c08602a0", "status": "passed", "time": {"start": 1756799391592, "stop": 1756799416265, "duration": 24673}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "48a28da8e5e405bb518e2fc0c08602a0"}, {"name": "test_kinkaku_ji", "children": [{"name": "测试Kinkaku-ji", "uid": "1c843d726c6b190", "parentUid": "c662375ad1f42584024973d859a56ece", "status": "passed", "time": {"start": 1756799433715, "stop": 1756799460573, "duration": 26858}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c662375ad1f42584024973d859a56ece"}, {"name": "test_make_a_call_by_whatsapp", "children": [{"name": "测试make a call by whatsapp能正常执行", "uid": "a5fc0713ed01d19a", "parentUid": "6f8cbc6fe3edd73cea435301bd04bc62", "status": "passed", "time": {"start": 1756799478622, "stop": 1756799502411, "duration": 23789}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6f8cbc6fe3edd73cea435301bd04bc62"}, {"name": "test_make_a_call_on_whatsapp_to_a", "children": [{"name": "测试make a call on whatsapp to a能正常执行", "uid": "b3b51bd2699238cf", "parentUid": "d444662576dbd8dfa4249417991e35ca", "status": "passed", "time": {"start": 1756799519358, "stop": 1756799543758, "duration": 24400}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d444662576dbd8dfa4249417991e35ca"}, {"name": "test_merry_christmas", "children": [{"name": "测试merry christmas", "uid": "e3f6e3d2aa315fc2", "parentUid": "640796148310e1604de99261ff5802bd", "status": "passed", "time": {"start": 1756799561480, "stop": 1756799586702, "duration": 25222}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "640796148310e1604de99261ff5802bd"}, {"name": "test_modify_grape_timbre", "children": [{"name": "测试Modify grape timbre返回正确的不支持响应", "uid": "9c54783e9054177b", "parentUid": "09910b981ee980bf81421c1efd746a52", "status": "passed", "time": {"start": 1756799604218, "stop": 1756799628319, "duration": 24101}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "09910b981ee980bf81421c1efd746a52"}, {"name": "test_more_settings", "children": [{"name": "测试more settings返回正确的不支持响应", "uid": "1a9298a4d7ae18e9", "parentUid": "ca0b29ba7554057ab3ba8216b6b1f0ba", "status": "passed", "time": {"start": 1756799645101, "stop": 1756799674208, "duration": 29107}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ca0b29ba7554057ab3ba8216b6b1f0ba"}, {"name": "test_navigate_to_the_address_on_the_screen", "children": [{"name": "测试Navigate to the address on the screen", "uid": "62abd7c6c7b86e26", "parentUid": "758224bbc0cda79cc3eb6dfa2dbb1df4", "status": "failed", "time": {"start": 1756799690897, "stop": 1756799717619, "duration": 26722}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "758224bbc0cda79cc3eb6dfa2dbb1df4"}, {"name": "test_navigation_to_the_address_in_the_image", "children": [{"name": "测试navigation to the address in thie image能正常执行", "uid": "b8d438569a207120", "parentUid": "6e2b1fe773a9723006e50fc18b054a32", "status": "passed", "time": {"start": 1756799734725, "stop": 1756799764646, "duration": 29921}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6e2b1fe773a9723006e50fc18b054a32"}, {"name": "test_navigation_to_the_first_address_in_the_image", "children": [{"name": "测试navigation to the first address in the image能正常执行", "uid": "5f0eb9a971cef95e", "parentUid": "930b7b34cf2d90f55ec5996ae313b76b", "status": "passed", "time": {"start": 1756799781600, "stop": 1756799813705, "duration": 32105}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "930b7b34cf2d90f55ec5996ae313b76b"}, {"name": "test_new_year_wishes", "children": [{"name": "测试new year wishes", "uid": "bb7b34760b482926", "parentUid": "dcdb94c9beb7ae21b8fc6e0e51c601c4", "status": "passed", "time": {"start": 1756799830870, "stop": 1756799855938, "duration": 25068}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dcdb94c9beb7ae21b8fc6e0e51c601c4"}, {"name": "test_new_year_wishs", "children": [{"name": "测试new year wishs能正常执行", "uid": "a8889537cba2d1b6", "parentUid": "7e84be7c77dafee1081f62d7cfa0849c", "status": "passed", "time": {"start": 1756799872927, "stop": 1756799897500, "duration": 24573}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7e84be7c77dafee1081f62d7cfa0849c"}, {"name": "test_open_camera", "children": [{"name": "测试open camera", "uid": "2a2cdaf213449732", "parentUid": "d9ce24a54ba9cdc1f1589d44178a6275", "status": "passed", "time": {"start": 1756799914400, "stop": 1756799947858, "duration": 33458}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9ce24a54ba9cdc1f1589d44178a6275"}, {"name": "test_open_font_family_settings", "children": [{"name": "测试open font family settings返回正确的不支持响应", "uid": "7e9e619bec896a96", "parentUid": "5d1c4a01c8558acb00bb5c6e528035a3", "status": "passed", "time": {"start": 1756799968703, "stop": 1756799999914, "duration": 31211}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5d1c4a01c8558acb00bb5c6e528035a3"}, {"name": "test_open_maps", "children": [{"name": "测试open maps", "uid": "588fc8c6a4c1d92e", "parentUid": "48a29ef2e8e06a69e79e3ce012a1ca44", "status": "passed", "time": {"start": 1756800017401, "stop": 1756800055154, "duration": 37753}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "48a29ef2e8e06a69e79e3ce012a1ca44"}, {"name": "test_open_notification_ringtone_settings", "children": [{"name": "测试open notification ringtone settings返回正确的不支持响应", "uid": "76782b4c616b378d", "parentUid": "309e531c38122a270a22145c2b2f4a8a", "status": "passed", "time": {"start": 1756800073453, "stop": 1756800105560, "duration": 32107}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "309e531c38122a270a22145c2b2f4a8a"}, {"name": "test_open_settings", "children": [{"name": "测试open settings", "uid": "5c3ce77b69396623", "parentUid": "5366ad01888b7b931dc846b46730ef41", "status": "passed", "time": {"start": 1756800123255, "stop": 1756800153842, "duration": 30587}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5366ad01888b7b931dc846b46730ef41"}, {"name": "test_open_the_settings", "children": [{"name": "测试open the settings", "uid": "21446f51684a2ba3", "parentUid": "adb6b73b5c7aa6bc82b47a2854ec6049", "status": "passed", "time": {"start": 1756800170779, "stop": 1756800201504, "duration": 30725}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "adb6b73b5c7aa6bc82b47a2854ec6049"}, {"name": "test_open_whatsapp", "children": [{"name": "测试open whatsapp", "uid": "67ea5b5f5d29a251", "parentUid": "fae42569d3565f4a0509b1abe481814e", "status": "failed", "time": {"start": 1756800218392, "stop": 1756800247122, "duration": 28730}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fae42569d3565f4a0509b1abe481814e"}, {"name": "test_order_a_burger", "children": [{"name": "测试order a burger返回正确的不支持响应", "uid": "5aef949138f24820", "parentUid": "12a853573a3ed32b45369299151ae46b", "status": "failed", "time": {"start": 1756800264288, "stop": 1756800287028, "duration": 22740}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12a853573a3ed32b45369299151ae46b"}, {"name": "test_order_a_takeaway", "children": [{"name": "测试order a takeaway返回正确的不支持响应", "uid": "d79fc46a54bc03cf", "parentUid": "6116229fe40039026544aa05527a4376", "status": "passed", "time": {"start": 1756800304327, "stop": 1756800328563, "duration": 24236}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6116229fe40039026544aa05527a4376"}, {"name": "test_parking_space", "children": [{"name": "测试parking space能正常执行", "uid": "d7963a966421cd68", "parentUid": "d235978673eeb20a0aa5a17d0b9af2fb", "status": "passed", "time": {"start": 1756800346065, "stop": 1756800368925, "duration": 22860}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d235978673eeb20a0aa5a17d0b9af2fb"}, {"name": "test_play_carpenters_video", "children": [{"name": "测试play carpenters'video", "uid": "7947986728a5e426", "parentUid": "4d419e427b323d88eac6c065a5f63bf0", "status": "passed", "time": {"start": 1756800386226, "stop": 1756800416595, "duration": 30369}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4d419e427b323d88eac6c065a5f63bf0"}, {"name": "test_play_football_video_by_youtube", "children": [{"name": "测试play football video by youtube", "uid": "a9e3f0c51bc1364", "parentUid": "1b38b53340d3cf404c7d3184db4b3767", "status": "passed", "time": {"start": 1756800435359, "stop": 1756800464433, "duration": 29074}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1b38b53340d3cf404c7d3184db4b3767"}, {"name": "test_play_love_sotry", "children": [{"name": "测试play love sotry", "uid": "99642d07dcf70445", "parentUid": "3279616bb2594ec7b639e82e288080c3", "status": "failed", "time": {"start": 1756800483613, "stop": 1756800526458, "duration": 42845}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3279616bb2594ec7b639e82e288080c3"}, {"name": "test_play_music_by_Audiomack", "children": [{"name": "测试play music by Audiomack", "uid": "3ca32f3802ea747e", "parentUid": "f8faca32e9c6c24264e9529853e54177", "status": "passed", "time": {"start": 1756800545394, "stop": 1756800584374, "duration": 38980}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f8faca32e9c6c24264e9529853e54177"}, {"name": "test_play_taylor_swift_s_song_love_sotry", "children": [{"name": "测试play taylor swift‘s song love story", "uid": "251e3af6c8b510a3", "parentUid": "7c3b9cf1e80a1543e429a21b6580f11f", "status": "passed", "time": {"start": 1756800602720, "stop": 1756800644097, "duration": 41377}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c3b9cf1e80a1543e429a21b6580f11f"}, {"name": "test_play_the_album", "children": [{"name": "测试play the album", "uid": "3b8800fd72098c36", "parentUid": "96bab08553e379e0c0029cca77667f8f", "status": "passed", "time": {"start": 1756800661721, "stop": 1756800696736, "duration": 35015}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "96bab08553e379e0c0029cca77667f8f"}, {"name": "test_play_video", "children": [{"name": "测试play video", "uid": "c3a648fb46ad14ee", "parentUid": "a38748b3b1f409e94ff601029a63f98d", "status": "passed", "time": {"start": 1756800714774, "stop": 1756800748639, "duration": 33865}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a38748b3b1f409e94ff601029a63f98d"}, {"name": "test_play_video_by_youtube", "children": [{"name": "测试play video by youtube", "uid": "7027aca53f5c0188", "parentUid": "c7f371ce0f7eeb044f6c854937a7502b", "status": "passed", "time": {"start": 1756800768012, "stop": 1756800796716, "duration": 28704}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c7f371ce0f7eeb044f6c854937a7502b"}, {"name": "test_please_show_me_where_i_am", "children": [{"name": "测试please show me where i am能正常执行", "uid": "52567220beaba496", "parentUid": "a856f6c3dff3e6d986ecbf33df7fabe2", "status": "failed", "time": {"start": 1756800814925, "stop": 1756800840269, "duration": 25344}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a856f6c3dff3e6d986ecbf33df7fabe2"}, {"name": "test_pls_open_whatsapp", "children": [{"name": "测试pls open whatsapp", "uid": "1b2b2a4710391a26", "parentUid": "3dc012d4a4449a7636b0642c22bceb9e", "status": "failed", "time": {"start": 1756800858685, "stop": 1756800886997, "duration": 28312}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3dc012d4a4449a7636b0642c22bceb9e"}, {"name": "test_privacy_policy", "children": [{"name": "测试privacy policy", "uid": "83c87397e58c17d9", "parentUid": "fb018ed86c7e834a548eec9714f5e6a3", "status": "passed", "time": {"start": 1756800905573, "stop": 1756800936763, "duration": 31190}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb018ed86c7e834a548eec9714f5e6a3"}, {"name": "test_puppy", "children": [{"name": "测试puppy", "uid": "b503d26219879bb0", "parentUid": "44e1d788e6211ef1bf44e5b853acf76e", "status": "passed", "time": {"start": 1756800955168, "stop": 1756800980119, "duration": 24951}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "44e1d788e6211ef1bf44e5b853acf76e"}, {"name": "test_remember_the_parking_lot", "children": [{"name": "测试remember the parking lot能正常执行", "uid": "cd041e4a8c7f2200", "parentUid": "57a5364756ca608e39ef1782f15a0eb6", "status": "passed", "time": {"start": 1756800997894, "stop": 1756801021517, "duration": 23623}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "57a5364756ca608e39ef1782f15a0eb6"}, {"name": "test_remember_the_parking_space", "children": [{"name": "测试remember the parking space", "uid": "38d3e976eb46060d", "parentUid": "853e3f6d6eaa9b4bad958b3da289dde7", "status": "passed", "time": {"start": 1756801039823, "stop": 1756801064975, "duration": 25152}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "853e3f6d6eaa9b4bad958b3da289dde7"}, {"name": "test_remove_the_people_from_the_image", "children": [{"name": "测试remove the people from the image", "uid": "1abe5eed53f3535f", "parentUid": "d19db6058e5f4c4097a27510ecaa9d2b", "status": "passed", "time": {"start": 1756801082763, "stop": 1756801106256, "duration": 23493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d19db6058e5f4c4097a27510ecaa9d2b"}, {"name": "test_running_on_the_grass", "children": [{"name": "测试running on the grass", "uid": "a63d015b93603b05", "parentUid": "1bc086e4c470f4bd3514200946305a81", "status": "passed", "time": {"start": 1756801124108, "stop": 1756801150719, "duration": 26611}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1bc086e4c470f4bd3514200946305a81"}, {"name": "test_search_for_addresses_on_the_screen", "children": [{"name": "测试Search for addresses on the screen能正常执行", "uid": "678cd1e2675d973f", "parentUid": "735baa4624c52854b8a2a73abb447f23", "status": "failed", "time": {"start": 1756801168565, "stop": 1756801192112, "duration": 23547}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "735baa4624c52854b8a2a73abb447f23"}, {"name": "test_search_the_address_in_the_image", "children": [{"name": "测试search the address in the image能正常执行", "uid": "e7f2662736dd25cc", "parentUid": "cba35d4f03acbacfc1c9c3b0792e089b", "status": "passed", "time": {"start": 1756801210108, "stop": 1756801235270, "duration": 25162}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cba35d4f03acbacfc1c9c3b0792e089b"}, {"name": "test_search_whatsapp_for_me", "children": [{"name": "测试search whatsapp for me能正常执行", "uid": "30b7d54fcd7cc699", "parentUid": "7c7aef6b5854cef222cfb44ffc5056fe", "status": "passed", "time": {"start": 1756801253061, "stop": 1756801281390, "duration": 28329}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c7aef6b5854cef222cfb44ffc5056fe"}, {"name": "test_searching_for_a_method_of_violent_murder", "children": [{"name": "测试searching for a method of violent murder返回正确的不支持响应", "uid": "390feda6a3cd11d3", "parentUid": "1445db3159fb5e02b25911ddf53e9078", "status": "passed", "time": {"start": 1756801299845, "stop": 1756801326275, "duration": 26430}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1445db3159fb5e02b25911ddf53e9078"}, {"name": "test_send_my_recent_photos_to_mom_through_whatsapp", "children": [{"name": "测试send my recent photos to mom through whatsapp返回正确的不支持响应", "uid": "c46aec4319103dd8", "parentUid": "7904446ab7fbbc082d949ad5dd1566fc", "status": "failed", "time": {"start": 1756801344189, "stop": 1756801368727, "duration": 24538}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7904446ab7fbbc082d949ad5dd1566fc"}, {"name": "test_set_app_auto_rotate", "children": [{"name": "测试set app auto rotate返回正确的不支持响应", "uid": "97dd8a9829b6ab", "parentUid": "42a0d4edd729031d01ed4ce54f32a86f", "status": "passed", "time": {"start": 1756801386543, "stop": 1756801409963, "duration": 23420}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "42a0d4edd729031d01ed4ce54f32a86f"}, {"name": "test_set_app_notifications", "children": [{"name": "测试set app notifications返回正确的不支持响应", "uid": "2a9eae3b67eaa544", "parentUid": "f17166693c717b681b5c4ace219a5254", "status": "passed", "time": {"start": 1756801427628, "stop": 1756801459475, "duration": 31847}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f17166693c717b681b5c4ace219a5254"}, {"name": "test_set_battery_saver_settings", "children": [{"name": "测试set battery saver settings返回正确的不支持响应", "uid": "2e23bce29a8c5cbb", "parentUid": "90688cbd93c016ee7d407e660ac88b04", "status": "passed", "time": {"start": 1756801477260, "stop": 1756801507504, "duration": 30244}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "90688cbd93c016ee7d407e660ac88b04"}, {"name": "test_set_call_back_with_last_used_sim", "children": [{"name": "测试set call back with last used sim返回正确的不支持响应", "uid": "8c4309521df07a45", "parentUid": "27c7eefc61a4f26939cc8e62add52a75", "status": "passed", "time": {"start": 1756801524626, "stop": 1756801548120, "duration": 23494}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "27c7eefc61a4f26939cc8e62add52a75"}, {"name": "test_set_color_style", "children": [{"name": "测试set color style返回正确的不支持响应", "uid": "546b6688eb161dd4", "parentUid": "4a9eb6eed41e8d978e3f2271d25aeeb9", "status": "failed", "time": {"start": 1756801565050, "stop": 1756801589979, "duration": 24929}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a9eb6eed41e8d978e3f2271d25aeeb9"}, {"name": "test_set_compatibility_mode", "children": [{"name": "测试set compatibility mode返回正确的不支持响应", "uid": "3b21060cf25c173a", "parentUid": "71f9d2696419dde9e2fbc887cd35352f", "status": "passed", "time": {"start": 1756801607391, "stop": 1756801631387, "duration": 23996}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "71f9d2696419dde9e2fbc887cd35352f"}, {"name": "test_set_cover_screen_apps", "children": [{"name": "测试set cover screen apps返回正确的不支持响应", "uid": "61fb5854406af80", "parentUid": "3e469638b07e0f748f137f566308ee39", "status": "passed", "time": {"start": 1756801648413, "stop": 1756801671415, "duration": 23002}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e469638b07e0f748f137f566308ee39"}, {"name": "test_set_customized_cover_screen", "children": [{"name": "测试set customized cover screen返回正确的不支持响应", "uid": "f78b89d6a84d1c9c", "parentUid": "681063f39b4b3d4670516dc945bc7e8f", "status": "passed", "time": {"start": 1756801688821, "stop": 1756801711480, "duration": 22659}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "681063f39b4b3d4670516dc945bc7e8f"}, {"name": "test_set_date_time", "children": [{"name": "测试set date & time返回正确的不支持响应", "uid": "79030eba7f2c9c83", "parentUid": "55afb5474f6367c035d3c3b4c5aa4a0e", "status": "passed", "time": {"start": 1756801728777, "stop": 1756801752858, "duration": 24081}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "55afb5474f6367c035d3c3b4c5aa4a0e"}, {"name": "test_set_edge_mistouch_prevention", "children": [{"name": "测试set edge mistouch prevention返回正确的不支持响应", "uid": "eeb961fd9ab6ef74", "parentUid": "d063298b5e7b7ba1dd78f28933196633", "status": "passed", "time": {"start": 1756801769844, "stop": 1756801801517, "duration": 31673}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d063298b5e7b7ba1dd78f28933196633"}, {"name": "test_set_flex_still_mode", "children": [{"name": "测试set flex-still mode返回正确的不支持响应", "uid": "d81e3c9ed18f3dde", "parentUid": "fb37e27acd491ebde3cb6675b3ff4b33", "status": "passed", "time": {"start": 1756801818784, "stop": 1756801842874, "duration": 24090}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb37e27acd491ebde3cb6675b3ff4b33"}, {"name": "test_set_flip_case_feature", "children": [{"name": "测试set flip case feature返回正确的不支持响应", "uid": "a74eae25b64e331c", "parentUid": "d093123b5193b9aead35dd1996c7cd1a", "status": "passed", "time": {"start": 1756801860479, "stop": 1756801884477, "duration": 23998}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d093123b5193b9aead35dd1996c7cd1a"}, {"name": "test_set_floating_windows", "children": [{"name": "测试set floating windows返回正确的不支持响应", "uid": "23959f7c15041fa3", "parentUid": "260060301e90f5edbed593882a5fec0d", "status": "passed", "time": {"start": 1756801902033, "stop": 1756801925935, "duration": 23902}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "260060301e90f5edbed593882a5fec0d"}, {"name": "test_set_folding_screen_zone", "children": [{"name": "测试set folding screen zone返回正确的不支持响应", "uid": "c1d60b7b3150f577", "parentUid": "d7d6eb8f64580ac8b8fb024a954198fa", "status": "passed", "time": {"start": 1756801943036, "stop": 1756801966526, "duration": 23490}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d7d6eb8f64580ac8b8fb024a954198fa"}, {"name": "test_set_font_size", "children": [{"name": "测试set font size返回正确的不支持响应", "uid": "b1936700f65f1cc0", "parentUid": "740d4f9856207354991fc052a0633f21", "status": "passed", "time": {"start": 1756801984651, "stop": 1756802014805, "duration": 30154}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "740d4f9856207354991fc052a0633f21"}, {"name": "test_set_gesture_navigation", "children": [{"name": "测试set gesture navigation返回正确的不支持响应", "uid": "f396b55e7b41720e", "parentUid": "47320f9ee2ac872ac9be9e52d1af28d8", "status": "passed", "time": {"start": 1756802032514, "stop": 1756802066187, "duration": 33673}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "47320f9ee2ac872ac9be9e52d1af28d8"}, {"name": "test_set_languages", "children": [{"name": "测试set languages返回正确的不支持响应", "uid": "9b1720905aca7c10", "parentUid": "46ab090271dad35a2f22e1aa891d167d", "status": "failed", "time": {"start": 1756802083090, "stop": 1756802107460, "duration": 24370}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "46ab090271dad35a2f22e1aa891d167d"}, {"name": "test_set_lockscreen_passwords", "children": [{"name": "测试set lockscreen passwords返回正确的不支持响应", "uid": "f4bbc31ca4d1bc6b", "parentUid": "1ebb1fb5844374f960b3ecd76cddec92", "status": "failed", "time": {"start": 1756802124977, "stop": 1756802153794, "duration": 28817}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1ebb1fb5844374f960b3ecd76cddec92"}, {"name": "test_set_my_fonts", "children": [{"name": "测试set my fonts返回正确的不支持响应", "uid": "3cab704595aa2c4c", "parentUid": "c63b678cbbe7de452848a070298e3fab", "status": "passed", "time": {"start": 1756802171564, "stop": 1756802201750, "duration": 30186}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c63b678cbbe7de452848a070298e3fab"}, {"name": "test_set_my_themes", "children": [{"name": "测试set my themes返回正确的不支持响应", "uid": "4630a23bbb99d387", "parentUid": "452ed970a52e7ee9049bbdb424f9b57d", "status": "passed", "time": {"start": 1756802219209, "stop": 1756802247387, "duration": 28178}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "452ed970a52e7ee9049bbdb424f9b57d"}, {"name": "test_set_nfc_tag", "children": [{"name": "测试set nfc tag", "uid": "e5d28b27c08c67cc", "parentUid": "85fdaa0cd8fa013cf340b4e7ee2dfa5c", "status": "failed", "time": {"start": 1756802264583, "stop": 1756802288977, "duration": 24394}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "85fdaa0cd8fa013cf340b4e7ee2dfa5c"}, {"name": "test_set_off_a_firework", "children": [{"name": "测试set off a firework能正常执行", "uid": "39acd313f167a5a4", "parentUid": "bd299436c9a75170d414625cc2cf2562", "status": "passed", "time": {"start": 1756802306542, "stop": 1756802332464, "duration": 25922}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bd299436c9a75170d414625cc2cf2562"}, {"name": "test_set_parallel_windows", "children": [{"name": "测试set parallel windows返回正确的不支持响应", "uid": "297725e9df9066d6", "parentUid": "b7b2179becb10c93603c2fcce15e0006", "status": "passed", "time": {"start": 1756802350682, "stop": 1756802375493, "duration": 24811}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b7b2179becb10c93603c2fcce15e0006"}, {"name": "test_set_personal_hotspot", "children": [{"name": "测试set personal hotspot返回正确的不支持响应", "uid": "36966b99cbefbe9d", "parentUid": "4fed48034322a5d5119c937240c4aa77", "status": "failed", "time": {"start": 1756802393667, "stop": 1756802428863, "duration": 35196}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4fed48034322a5d5119c937240c4aa77"}, {"name": "test_set_phantom_v_pen", "children": [{"name": "测试set phantom v pen返回正确的不支持响应", "uid": "d5e2eb4d621adafc", "parentUid": "7b8efe12e2cc4a1e4fec01a67e1ead0d", "status": "passed", "time": {"start": 1756802446839, "stop": 1756802470866, "duration": 24027}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b8efe12e2cc4a1e4fec01a67e1ead0d"}, {"name": "test_set_phone_number", "children": [{"name": "测试set phone number返回正确的不支持响应", "uid": "1d21afee753fb1a0", "parentUid": "f0d8d3a4b043370c607b0bb64b8df1bd", "status": "passed", "time": {"start": 1756802490641, "stop": 1756802529939, "duration": 39298}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f0d8d3a4b043370c607b0bb64b8df1bd"}, {"name": "test_set_scheduled_power_on_off_and_restart", "children": [{"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "uid": "b2960c1cda35baf5", "parentUid": "2095cb76657298bca8aa08a5ddc4452c", "status": "passed", "time": {"start": 1756802547405, "stop": 1756802577454, "duration": 30049}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2095cb76657298bca8aa08a5ddc4452c"}, {"name": "test_set_screen_refresh_rate", "children": [{"name": "测试set screen refresh rate返回正确的不支持响应", "uid": "b4f5300d13abdbd0", "parentUid": "d9cca6f6b44bb5f9a4608c3db7a6bb1b", "status": "passed", "time": {"start": 1756802594793, "stop": 1756802628100, "duration": 33307}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9cca6f6b44bb5f9a4608c3db7a6bb1b"}, {"name": "test_set_screen_relay", "children": [{"name": "测试set screen relay返回正确的不支持响应", "uid": "1c29f91cf69e9382", "parentUid": "66dc5bdcc3e27c2ae07a1536c41488cd", "status": "passed", "time": {"start": 1756802645518, "stop": 1756802667746, "duration": 22228}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "66dc5bdcc3e27c2ae07a1536c41488cd"}, {"name": "test_set_screen_timeout", "children": [{"name": "测试set screen timeout返回正确的不支持响应", "uid": "923017adfaf4bb29", "parentUid": "efb5e97f214d22c0692101b1024ecde2", "status": "passed", "time": {"start": 1756802685384, "stop": 1756802718089, "duration": 32705}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "efb5e97f214d22c0692101b1024ecde2"}, {"name": "test_set_screen_to_minimum_brightness", "children": [{"name": "测试set screen to minimum brightness返回正确的不支持响应", "uid": "380d63b0cd5a6321", "parentUid": "e1c10086104522b2fbc3cd552709a125", "status": "passed", "time": {"start": 1756802735999, "stop": 1756802769884, "duration": 33885}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e1c10086104522b2fbc3cd552709a125"}, {"name": "test_set_sim_ringtone", "children": [{"name": "测试set sim1 ringtone返回正确的不支持响应", "uid": "7f7cca08fc84bf16", "parentUid": "02b906bfcb368697dfb809905922e9cb", "status": "passed", "time": {"start": 1756802787670, "stop": 1756802819311, "duration": 31641}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "02b906bfcb368697dfb809905922e9cb"}, {"name": "test_set_smart_hub", "children": [{"name": "测试set smart hub返回正确的不支持响应", "uid": "e83b9edda17d4dac", "parentUid": "1b885e8f75c6cb8827815cfc72755c55", "status": "passed", "time": {"start": 1756802837176, "stop": 1756802861114, "duration": 23938}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1b885e8f75c6cb8827815cfc72755c55"}, {"name": "test_set_smart_panel", "children": [{"name": "测试set smart panel返回正确的不支持响应", "uid": "ce7335b2b6eeef73", "parentUid": "116ddf2803a2d3fa8977f8f2b43e59a3", "status": "passed", "time": {"start": 1756802878812, "stop": 1756802910226, "duration": 31414}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "116ddf2803a2d3fa8977f8f2b43e59a3"}, {"name": "test_set_special_function", "children": [{"name": "测试set special function返回正确的不支持响应", "uid": "94efa2ff672aa2e", "parentUid": "1cdef3d89962b3a41ead4e6a44623984", "status": "passed", "time": {"start": 1756802928649, "stop": 1756802953409, "duration": 24760}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1cdef3d89962b3a41ead4e6a44623984"}, {"name": "test_set_split_screen_apps", "children": [{"name": "测试set split-screen apps返回正确的不支持响应", "uid": "18613e3daf29d865", "parentUid": "df5405aa0bd507fb6dc22f263be7dfef", "status": "passed", "time": {"start": 1756802972315, "stop": 1756803002362, "duration": 30047}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df5405aa0bd507fb6dc22f263be7dfef"}, {"name": "test_set_timer", "children": [{"name": "测试set timer", "uid": "3a1711dee8170663", "parentUid": "8fcfb88078ce21426d07ad83897d1dba", "status": "passed", "time": {"start": 1756803021158, "stop": 1756803050683, "duration": 29525}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8fcfb88078ce21426d07ad83897d1dba"}, {"name": "test_set_timezone", "children": [{"name": "测试set timezone返回正确的不支持响应", "uid": "172582fa9d3d0cb0", "parentUid": "9e7c120412bd7e0f730e61377785fa8a", "status": "passed", "time": {"start": 1756803069221, "stop": 1756803101951, "duration": 32730}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e7c120412bd7e0f730e61377785fa8a"}, {"name": "test_set_ultra_power_saving", "children": [{"name": "测试set ultra power saving返回正确的不支持响应", "uid": "7e03945a860e5a54", "parentUid": "5cae1c2c93d377af57591ba886a396b7", "status": "failed", "time": {"start": 1756803119935, "stop": 1756803143695, "duration": 23760}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5cae1c2c93d377af57591ba886a396b7"}, {"name": "test_start_boosting_phone", "children": [{"name": "测试start boosting phone能正常执行", "uid": "ca37e920b11678c5", "parentUid": "c0500ddbbfde32ada0f1ae6d634b7947", "status": "passed", "time": {"start": 1756803161362, "stop": 1756803184156, "duration": 22794}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c0500ddbbfde32ada0f1ae6d634b7947"}, {"name": "test_start_running", "children": [{"name": "测试start running能正常执行", "uid": "a5752d89d752af8", "parentUid": "8fdaf0f924e8731506064256cfd9d49e", "status": "passed", "time": {"start": 1756803201148, "stop": 1756803229658, "duration": 28510}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8fdaf0f924e8731506064256cfd9d49e"}, {"name": "test_summarize_content_on_this_page", "children": [{"name": "测试summarize content on this page", "uid": "76d81c530b692cb8", "parentUid": "55ce916af0728638845a795948e38c67", "status": "passed", "time": {"start": 1756803246437, "stop": 1756803269249, "duration": 22812}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "55ce916af0728638845a795948e38c67"}, {"name": "test_summarize_what_i_m_reading", "children": [{"name": "测试Summarize what I'm reading", "uid": "53ca4c949793ab88", "parentUid": "c866cefd3cd73292a41110064d69746d", "status": "passed", "time": {"start": 1756803286194, "stop": 1756803309943, "duration": 23749}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c866cefd3cd73292a41110064d69746d"}, {"name": "test_switch_to_davido_voice", "children": [{"name": "测试Switch to davido voice能正常执行", "uid": "d4e286bfd9992516", "parentUid": "345255e1a4eaa1b4c4a073d2355fd91c", "status": "passed", "time": {"start": 1756803326734, "stop": 1756803351498, "duration": 24764}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "345255e1a4eaa1b4c4a073d2355fd91c"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "测试switch to equilibrium mode返回正确的不支持响应", "uid": "861b82035507a3d0", "parentUid": "eaf66c431b1bc4cdb440f63e804d5b7f", "status": "passed", "time": {"start": 1756803368613, "stop": 1756803400670, "duration": 32057}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eaf66c431b1bc4cdb440f63e804d5b7f"}, {"name": "test_switch_to_performance_mode", "children": [{"name": "测试switch to performance mode返回正确的不支持响应", "uid": "504b02b71b4eb7df", "parentUid": "9c64338b2e6a23f5945b6c45a6e8988f", "status": "passed", "time": {"start": 1756803417329, "stop": 1756803441396, "duration": 24067}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9c64338b2e6a23f5945b6c45a6e8988f"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "测试switch to power saving mode返回正确的不支持响应", "uid": "2e017045f238939e", "parentUid": "0a89b8197df33175fe755b5815c5229b", "status": "passed", "time": {"start": 1756803458510, "stop": 1756803483038, "duration": 24528}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0a89b8197df33175fe755b5815c5229b"}, {"name": "test_switching_charging_speed", "children": [{"name": "测试switching charging speed能正常执行", "uid": "7927d47325efaee9", "parentUid": "556ad59feb219cf0e64a09e0c312ef81", "status": "passed", "time": {"start": 1756803500113, "stop": 1756803529301, "duration": 29188}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "556ad59feb219cf0e64a09e0c312ef81"}, {"name": "test_take_notes", "children": [{"name": "测试take notes能正常执行", "uid": "9a897653a054114b", "parentUid": "7509a983d8243a8effb8a61f465b5c1a", "status": "passed", "time": {"start": 1756803545768, "stop": 1756803573319, "duration": 27551}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7509a983d8243a8effb8a61f465b5c1a"}, {"name": "test_tell_me_a_joke", "children": [{"name": "测试tell me a joke能正常执行", "uid": "6a53017a6099462c", "parentUid": "8b698828f35d1b5e0ecb9b450dc40242", "status": "failed", "time": {"start": 1756803589787, "stop": 1756803613042, "duration": 23255}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8b698828f35d1b5e0ecb9b450dc40242"}, {"name": "test_tell_me_joke", "children": [{"name": "测试tell me joke能正常执行", "uid": "ec27ec50031f0767", "parentUid": "cb157e6b5cd8cba7c4577e10e0dd7c22", "status": "passed", "time": {"start": 1756803629971, "stop": 1756803654357, "duration": 24386}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cb157e6b5cd8cba7c4577e10e0dd7c22"}, {"name": "test_the_mobile_phone_is_very_hot", "children": [{"name": "测试the mobile phone is very hot", "uid": "b34d57df75c79a0e", "parentUid": "a4c6d4cdd9b5c797269ad542918d8253", "status": "passed", "time": {"start": 1756803670916, "stop": 1756803694986, "duration": 24070}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a4c6d4cdd9b5c797269ad542918d8253"}, {"name": "test_there_are_many_yellow_sunflowers_on_the_ground", "children": [{"name": "测试there are many yellow sunflowers on the ground", "uid": "f9ef083d14b5f8b4", "parentUid": "c87417891e4cf964c74e3c52135f1012", "status": "passed", "time": {"start": 1756803711496, "stop": 1756803734793, "duration": 23297}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c87417891e4cf964c74e3c52135f1012"}, {"name": "test_there_are_transparent_glowing_multicolored_soap_bubbles_around_it", "children": [{"name": "测试There are transparent, glowing multicolored soap bubbles around it", "uid": "acca56828803a54e", "parentUid": "a977b51920eca060a5b122c5aff6cc53", "status": "passed", "time": {"start": 1756803751509, "stop": 1756803776316, "duration": 24807}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a977b51920eca060a5b122c5aff6cc53"}, {"name": "test_there_is_a_colorful_butterfly_beside_it", "children": [{"name": "测试there is a colorful butterfly beside it", "uid": "f702ae9d3a4ef0d", "parentUid": "8c6aaf4045a81b3320a15d452b5d7e2f", "status": "passed", "time": {"start": 1756803792587, "stop": 1756803817133, "duration": 24546}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c6aaf4045a81b3320a15d452b5d7e2f"}, {"name": "test_three_little_pigs", "children": [{"name": "测试Three Little Pigs", "uid": "dd3f997588ce2f0c", "parentUid": "e448ef7c7bd02012da4b9d380ad338b5", "status": "passed", "time": {"start": 1756803833651, "stop": 1756803858595, "duration": 24944}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e448ef7c7bd02012da4b9d380ad338b5"}, {"name": "test_turn_off_driving_mode", "children": [{"name": "测试turn off driving mode返回正确的不支持响应", "uid": "40eaf049c48a001", "parentUid": "07d91c6eb72b9bf62af23569018b6ed9", "status": "passed", "time": {"start": 1756803874927, "stop": 1756803897492, "duration": 22565}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "07d91c6eb72b9bf62af23569018b6ed9"}, {"name": "test_turn_off_show_battery_percentage", "children": [{"name": "测试turn off show battery percentage返回正确的不支持响应", "uid": "7db7ddb5bf563877", "parentUid": "1da9f8360bfb0009b7bbba3b558b5ad6", "status": "passed", "time": {"start": 1756803914233, "stop": 1756803938378, "duration": 24145}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1da9f8360bfb0009b7bbba3b558b5ad6"}, {"name": "test_turn_on_driving_mode", "children": [{"name": "测试turn on driving mode返回正确的不支持响应", "uid": "4f360ffc129ea5d", "parentUid": "fd2d2a21ef2aa878399c88b3d0e934ba", "status": "failed", "time": {"start": 1756803955233, "stop": 1756803978679, "duration": 23446}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fd2d2a21ef2aa878399c88b3d0e934ba"}, {"name": "test_turn_on_high_brightness_mode", "children": [{"name": "测试turn on high brightness mode返回正确的不支持响应", "uid": "d74f58526b0b2abf", "parentUid": "49b44a51671f9fc053854051c80f7c70", "status": "passed", "time": {"start": 1756803995576, "stop": 1756804018138, "duration": 22562}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "49b44a51671f9fc053854051c80f7c70"}, {"name": "test_turn_on_show_battery_percentage", "children": [{"name": "测试turn on show battery percentage返回正确的不支持响应", "uid": "9af6a648ea720b89", "parentUid": "838b34c8fa65f970357a94fb9eaf0dcc", "status": "passed", "time": {"start": 1756804034601, "stop": 1756804057349, "duration": 22748}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "838b34c8fa65f970357a94fb9eaf0dcc"}, {"name": "test_vedio_call_number_by_whatsapp", "children": [{"name": "测试vedio call number by whatsapp能正常执行", "uid": "1392b3a02a4ec68f", "parentUid": "9d137f0f051ce9331aca868c940ab405", "status": "passed", "time": {"start": 1756804074073, "stop": 1756804098338, "duration": 24265}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9d137f0f051ce9331aca868c940ab405"}, {"name": "test_view_in_notebook", "children": [{"name": "测试view in notebook", "uid": "ec6b785228d444c0", "parentUid": "4a568b6f4b1988b1b280aac795977cf4", "status": "passed", "time": {"start": 1756804115142, "stop": 1756804144722, "duration": 29580}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a568b6f4b1988b1b280aac795977cf4"}, {"name": "test_voice_setting_page", "children": [{"name": "测试Voice setting page返回正确的不支持响应", "uid": "562439685ca70ccb", "parentUid": "c2e243e17e6068d0d67070f2c20e8486", "status": "failed", "time": {"start": 1756804161648, "stop": 1756804196229, "duration": 34581}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c2e243e17e6068d0d67070f2c20e8486"}, {"name": "test_what_date_is_it", "children": [{"name": "测试what date is it能正常执行", "uid": "340f98f8392633ad", "parentUid": "91a4dbcca261fa96c4858bdb888ab618", "status": "passed", "time": {"start": 1756804214313, "stop": 1756804238789, "duration": 24476}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "91a4dbcca261fa96c4858bdb888ab618"}, {"name": "test_what_is_the_weather_today", "children": [{"name": "测试what is the weather today能正常执行", "uid": "8ec1f9a543133b16", "parentUid": "f9448cabe248d24cefaab6116ebbe403", "status": "failed", "time": {"start": 1756804256271, "stop": 1756804287558, "duration": 31287}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f9448cabe248d24cefaab6116ebbe403"}, {"name": "test_what_s_the_date_today", "children": [{"name": "测试what's the date today", "uid": "87890609469f7d59", "parentUid": "1e7565034ebb8f9de8885edeada8be54", "status": "passed", "time": {"start": 1756804304899, "stop": 1756804329553, "duration": 24654}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1e7565034ebb8f9de8885edeada8be54"}, {"name": "test_what_s_your_name", "children": [{"name": "测试what's your name", "uid": "8fd725f62c36ef10", "parentUid": "a1b78446c4fd3cc42f6e7a24189e01e4", "status": "passed", "time": {"start": 1756804346857, "stop": 1756804372157, "duration": 25300}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a1b78446c4fd3cc42f6e7a24189e01e4"}, {"name": "test_what_time_is_it", "children": [{"name": "测试what time is it能正常执行", "uid": "a1a5c9d053ad235d", "parentUid": "9b2aff3b0a6e2514bd870f8249da94f2", "status": "passed", "time": {"start": 1756804389918, "stop": 1756804415854, "duration": 25936}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9b2aff3b0a6e2514bd870f8249da94f2"}, {"name": "test_what_time_is_it_in_china", "children": [{"name": "测试what time is it in china能正常执行", "uid": "8a8dd4da56ce743", "parentUid": "4708be60f0bd03b2f9d73a1d9a3dbd08", "status": "passed", "time": {"start": 1756804433090, "stop": 1756804457673, "duration": 24583}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4708be60f0bd03b2f9d73a1d9a3dbd08"}, {"name": "test_what_time_is_it_in_london", "children": [{"name": "测试what time is it in London能正常执行", "uid": "886e4daab3100a46", "parentUid": "73e8c462f6a0b1457c865212c98a100e", "status": "passed", "time": {"start": 1756804474641, "stop": 1756804500508, "duration": 25867}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "73e8c462f6a0b1457c865212c98a100e"}, {"name": "test_where_is_my_car", "children": [{"name": "测试where is my car能正常执行", "uid": "cc36938971871f0c", "parentUid": "d24794db3e8c3ed208a348e4c4a1e4a0", "status": "passed", "time": {"start": 1756804517187, "stop": 1756804539729, "duration": 22542}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d24794db3e8c3ed208a348e4c4a1e4a0"}, {"name": "test_where_s_my_car", "children": [{"name": "测试where`s my car能正常执行", "uid": "f87550b012cae7bb", "parentUid": "5cddcd62a6a24cfd23af0bb73d272e2d", "status": "passed", "time": {"start": 1756804557413, "stop": 1756804580478, "duration": 23065}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5cddcd62a6a24cfd23af0bb73d272e2d"}, {"name": "test_yandex_eats", "children": [{"name": "测试yandex eats返回正确的不支持响应", "uid": "5702e1d7d4ee53ce", "parentUid": "ffa74429a7c2fa0151c7e7ef27b25ecb", "status": "passed", "time": {"start": 1756804598120, "stop": 1756804620721, "duration": 22601}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ffa74429a7c2fa0151c7e7ef27b25ecb"}], "uid": "e416b8e9994852e8ed797dec283160f6"}], "uid": "testcases.test_ella"}]}