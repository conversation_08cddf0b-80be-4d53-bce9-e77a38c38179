{"name": "测试what date is it能正常执行", "status": "passed", "description": "what date is it", "steps": [{"name": "执行命令: what date is it", "status": "passed", "steps": [{"name": "执行命令: what date is it", "status": "passed", "start": 1756804214313, "stop": 1756804238223}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "433b00d0-8835-4af6-bbc3-af49508a3956-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "41c99e51-d18b-44fb-a822-a5c8d647ed0b-attachment.png", "type": "image/png"}], "start": 1756804238223, "stop": 1756804238480}], "start": 1756804214313, "stop": 1756804238481}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756804238481, "stop": 1756804238482}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4ebb5c1d-989a-401d-8aeb-5a0370cb7db7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a4dce19c-f2bb-4d4f-a056-5c1d69ab147c-attachment.png", "type": "image/png"}], "start": 1756804238482, "stop": 1756804238789}], "attachments": [{"name": "stdout", "source": "3addfa1f-d61a-4c78-93e3-14152d2dd8bc-attachment.txt", "type": "text/plain"}], "start": 1756804214313, "stop": 1756804238789, "uuid": "20bc9ade-2f8c-4cd7-92a6-459192bf00e0", "historyId": "44e27936b56f219f63af671a8fd7f5fb", "testCaseId": "44e27936b56f219f63af671a8fd7f5fb", "fullName": "testcases.test_ella.unsupported_commands.test_what_date_is_it.TestEllaWhatDateIsIt#test_what_date_is_it", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_what_date_is_it"}, {"name": "subSuite", "value": "TestEllaWhatDateIsIt"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_what_date_is_it"}]}