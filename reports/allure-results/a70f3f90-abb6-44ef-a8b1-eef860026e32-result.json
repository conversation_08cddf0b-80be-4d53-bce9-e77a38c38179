{"name": "测试turn off the 7AM alarm", "status": "passed", "description": "测试turn off the 7AM alarm指令", "steps": [{"name": "执行命令: delete all the alarms", "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "status": "passed", "start": 1756784300586, "stop": 1756784323489}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7a84d38d-695d-4776-b90d-7c355735533c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cb7b84f5-8cc3-4b40-96a5-0fc1b7e0793a-attachment.png", "type": "image/png"}], "start": 1756784323489, "stop": 1756784323702}], "start": 1756784300586, "stop": 1756784323702}, {"name": "执行命令: set an alarm at 7 am", "status": "passed", "steps": [{"name": "执行命令: set an alarm at 7 am", "status": "passed", "start": 1756784323703, "stop": 1756784346143}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "27240dbe-5a4e-4338-9da5-e9c85701b9cb-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5a253d6c-13c6-4121-829e-fa6fb60ec42b-attachment.png", "type": "image/png"}], "start": 1756784346143, "stop": 1756784346339}], "start": 1756784323703, "stop": 1756784346339}, {"name": "执行命令: turn off the 7AM alarm", "status": "passed", "steps": [{"name": "执行命令: turn off the 7AM alarm", "status": "passed", "start": 1756784346339, "stop": 1756784367477}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "24eb7cd5-e2b5-49a3-b08a-2198b3247806-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5ed4d8ac-1ad4-4ec2-8d6e-b5f1b8b8feb8-attachment.png", "type": "image/png"}], "start": 1756784367477, "stop": 1756784367706}], "start": 1756784346339, "stop": 1756784367706}, {"name": "执行命令: get all the alarms", "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "status": "passed", "start": 1756784367706, "stop": 1756784388434}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fc25961a-e6cf-4ea1-9d48-cc7cfd97dae7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "046d60f5-993a-4662-900c-268453e024fa-attachment.png", "type": "image/png"}], "start": 1756784388434, "stop": 1756784388632}], "start": 1756784367706, "stop": 1756784388633}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756784388633, "stop": 1756784388635}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2629535e-855e-4bcd-8c94-fabcb7c74c7d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2c7963c1-1f64-46c1-a840-f54799eafdc7-attachment.png", "type": "image/png"}], "start": 1756784388635, "stop": 1756784388848}], "attachments": [{"name": "stdout", "source": "1855e355-2ce4-47cc-9700-9d7ab151cb9f-attachment.txt", "type": "text/plain"}], "start": 1756784300586, "stop": 1756784388849, "uuid": "284397f8-a037-4d70-85de-049b996e49b7", "historyId": "e56b7788214bc4e18231f16dfd713954", "testCaseId": "e56b7788214bc4e18231f16dfd713954", "fullName": "testcases.test_ella.component_coupling.test_turn_off_the_7_am_alarm.TestEllaOpenClock#test_turn_off_the_am_alarm", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_turn_off_the_7_am_alarm"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_turn_off_the_7_am_alarm"}]}