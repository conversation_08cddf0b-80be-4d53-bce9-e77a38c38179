{"name": "测试close equilibrium mode返回正确的不支持响应", "status": "passed", "description": "验证close equilibrium mode指令返回预期的不支持响应", "steps": [{"name": "执行命令: close equilibrium mode", "status": "passed", "steps": [{"name": "执行命令: close equilibrium mode", "status": "passed", "start": 1756796037406, "stop": 1756796067859}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d465f333-10d8-4757-b844-eea18925c985-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5c8489bf-e0d0-4d19-8088-bb62ef228103-attachment.png", "type": "image/png"}], "start": 1756796067859, "stop": 1756796068051}], "start": 1756796037406, "stop": 1756796068051}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756796068051, "stop": 1756796068054}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4d3d838a-e786-4114-83ef-1ffeb4883cd7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "bdcc3ca1-462a-4438-820f-2c593954e9ab-attachment.png", "type": "image/png"}], "start": 1756796068054, "stop": 1756796068280}], "attachments": [{"name": "stdout", "source": "f9e20528-e4a7-40bd-b844-e5dbe3c12818-attachment.txt", "type": "text/plain"}], "start": 1756796037406, "stop": 1756796068280, "uuid": "706ea026-7dba-473d-818a-3334a5c6b606", "historyId": "dc901cadfe1de0042de7c0f7461a804e", "testCaseId": "dc901cadfe1de0042de7c0f7461a804e", "fullName": "testcases.test_ella.unsupported_commands.test_close_equilibrium_mode.TestEllaCloseEquilibriumMode#test_close_equilibrium_mode", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_close_equilibrium_mode"}, {"name": "subSuite", "value": "TestEllaCloseEquilibriumMode"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_close_equilibrium_mode"}]}