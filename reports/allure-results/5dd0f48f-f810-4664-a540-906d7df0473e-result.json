{"name": "测试how to say i love you in french能正常执行", "status": "passed", "description": "how to say i love you in french", "steps": [{"name": "执行命令: how to say i love you in french", "status": "passed", "steps": [{"name": "执行命令: how to say i love you in french", "status": "passed", "start": 1756785529738, "stop": 1756785554547}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2077b2f3-35ef-4875-9400-2d115bd29c1c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9119fe38-6ad8-45f2-ae76-a1330557467a-attachment.png", "type": "image/png"}], "start": 1756785554547, "stop": 1756785554783}], "start": 1756785529738, "stop": 1756785554784}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756785554784, "stop": 1756785554785}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f94133cb-58da-4bd7-a3b4-f1cb7feb1648-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "46032194-7ede-40c8-83fe-e46c7388761a-attachment.png", "type": "image/png"}], "start": 1756785554785, "stop": 1756785554992}], "attachments": [{"name": "stdout", "source": "0eb99acf-e33c-44b8-852a-1b6c90a15159-attachment.txt", "type": "text/plain"}], "start": 1756785529738, "stop": 1756785554992, "uuid": "e5fc14ee-0566-421e-9c88-d087e3835ade", "historyId": "78de5607a6208f59723ba6cf4fcf09c4", "testCaseId": "78de5607a6208f59723ba6cf4fcf09c4", "fullName": "testcases.test_ella.dialogue.test_how_to_say_i_love_you_in_french.TestEllaHowSayILoveYouFrench#test_how_to_say_i_love_you_in_french", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_to_say_i_love_you_in_french"}, {"name": "subSuite", "value": "TestEllaHowSayILoveYouFrench"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_to_say_i_love_you_in_french"}]}