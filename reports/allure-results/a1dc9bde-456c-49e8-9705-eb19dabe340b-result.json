{"name": "测试set alarm for 10 o'clock", "status": "passed", "description": "测试set alarm for 10 o'clock指令", "steps": [{"name": "执行命令: delete all the alarms", "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "status": "passed", "start": 1756790629634, "stop": 1756790650008}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b6b5bc9d-1fe5-4765-88ca-857c9469d002-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2e8803ce-0450-4464-8921-10c5e52a57bd-attachment.png", "type": "image/png"}], "start": 1756790650008, "stop": 1756790650217}], "start": 1756790629634, "stop": 1756790650217}, {"name": "执行命令: set alarm for 10 o'clock", "status": "passed", "steps": [{"name": "执行命令: set alarm for 10 o'clock", "status": "passed", "start": 1756790650218, "stop": 1756790669946}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c65c34b4-76b7-47a8-8744-42023bc24c5e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7a3e0402-232a-4563-9566-0d39af762914-attachment.png", "type": "image/png"}], "start": 1756790669946, "stop": 1756790670153}], "start": 1756790650217, "stop": 1756790670153}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790670153, "stop": 1756790670154}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "da48e643-5eda-4515-a627-ec6cc93a7de1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9e81ffba-b62d-4962-a915-9cee737fa441-attachment.png", "type": "image/png"}], "start": 1756790670154, "stop": 1756790670380}], "attachments": [{"name": "stdout", "source": "0f372050-7f0a-4bf1-98de-7a12781889f8-attachment.txt", "type": "text/plain"}], "start": 1756790629634, "stop": 1756790670381, "uuid": "11b36299-c06c-4674-9356-83c7e9c06ad0", "historyId": "5b1e68388004fe021690469c3d83b485", "testCaseId": "5b1e68388004fe021690469c3d83b485", "fullName": "testcases.test_ella.system_coupling.test_set_alarm_for_10_o_clock.TestEllaOpenClock#test_set_alarm_for_10_o_clock", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_alarm_for_10_o_clock"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_alarm_for_10_o_clock"}]}