{"name": "测试min brightness能正常执行", "status": "passed", "description": "min brightness", "steps": [{"name": "执行命令: min brightness", "status": "passed", "steps": [{"name": "执行命令: min brightness", "status": "passed", "start": 1756790143952, "stop": 1756790165225}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "835f9607-5930-41b4-8426-25279dbd48bd-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "82f84b12-496d-4a2b-91a8-aaab12010633-attachment.png", "type": "image/png"}], "start": 1756790165225, "stop": 1756790165409}], "start": 1756790143952, "stop": 1756790165409}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790165409, "stop": 1756790165410}, {"name": "验证应用已打开", "status": "passed", "start": 1756790165410, "stop": 1756790165410}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a68f63a8-4a10-4287-a1f2-02d3a2633bb5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a829975a-e068-4a00-a2af-73d117457e37-attachment.png", "type": "image/png"}], "start": 1756790165410, "stop": 1756790165627}], "attachments": [{"name": "stdout", "source": "02e85a26-c184-4d6f-83d7-0a813f64dda8-attachment.txt", "type": "text/plain"}], "start": 1756790143951, "stop": 1756790165628, "uuid": "397b268a-4e72-405b-bf1e-608c6b72d1b9", "historyId": "f27eb1a59c1d5c5c845852e05e6a17d9", "testCaseId": "f27eb1a59c1d5c5c845852e05e6a17d9", "fullName": "testcases.test_ella.system_coupling.test_min_brightness.TestEllaMinBrightness#test_min_brightness", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_min_brightness"}, {"name": "subSuite", "value": "Test<PERSON>lla<PERSON>inBrightness"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_min_brightness"}]}