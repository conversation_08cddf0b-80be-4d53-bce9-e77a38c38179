{"name": "测试play taylor swift‘s song love story", "status": "passed", "description": "测试play taylor swift‘s song love story指令", "steps": [{"name": "执行命令: play taylor swift‘s song love story", "status": "passed", "steps": [{"name": "执行命令: play taylor swift‘s song love story", "status": "passed", "start": 1756800602720, "stop": 1756800643617}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "011e479e-6b84-468d-b3c3-265b16ceff80-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4d39de20-da3b-444b-8455-1646b79cce55-attachment.png", "type": "image/png"}], "start": 1756800643617, "stop": 1756800643865}], "start": 1756800602720, "stop": 1756800643866}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756800643866, "stop": 1756800643868}, {"name": "验证visha已打开", "status": "passed", "start": 1756800643868, "stop": 1756800643868}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "196546c1-bb36-4b6b-ac17-280dd742e607-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9cb65df5-6319-4a6f-a64b-9ab7b8d86460-attachment.png", "type": "image/png"}], "start": 1756800643868, "stop": 1756800644097}], "attachments": [{"name": "stdout", "source": "681626d6-07ff-4f25-84e1-8aaad32d2917-attachment.txt", "type": "text/plain"}], "start": 1756800602720, "stop": 1756800644097, "uuid": "3d59b5da-749c-4870-a22b-4f3af07edcfd", "historyId": "b12ee4e4a5d7e51ba0abe166b6c90352", "testCaseId": "b12ee4e4a5d7e51ba0abe166b6c90352", "fullName": "testcases.test_ella.unsupported_commands.test_play_taylor_swift_s_song_love_sotry.TestEllaOpenPlayPoliticalNews#test_play_taylor_swift_s_song_love_sotry", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_taylor_swift_s_song_love_sotry"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_taylor_swift_s_song_love_sotry"}]}