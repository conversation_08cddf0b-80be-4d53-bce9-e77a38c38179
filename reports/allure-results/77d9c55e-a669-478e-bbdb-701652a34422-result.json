{"name": "测试set timer", "status": "passed", "description": "测试set timer指令", "steps": [{"name": "执行命令: set timer", "status": "passed", "steps": [{"name": "执行命令: set timer", "status": "passed", "start": 1756803021158, "stop": 1756803050270}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f108b6ec-9578-4db1-9806-1836b6cf61a8-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "de6c5da3-291a-42f4-99d0-c80c69369a99-attachment.png", "type": "image/png"}], "start": 1756803050270, "stop": 1756803050492}], "start": 1756803021158, "stop": 1756803050493}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756803050493, "stop": 1756803050494}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d8eb8019-28db-4c8d-9ad9-f12a1c20d28b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fb2a6fe0-9ac3-4895-a612-21ebeb597590-attachment.png", "type": "image/png"}], "start": 1756803050494, "stop": 1756803050682}], "attachments": [{"name": "stdout", "source": "e0ecf499-9104-41a6-a08f-792d77cce0c1-attachment.txt", "type": "text/plain"}], "start": 1756803021158, "stop": 1756803050683, "uuid": "020bcd63-dd83-4090-8a7c-bd128038ef03", "historyId": "9445ebf64ec65c769332fec8dd505332", "testCaseId": "9445ebf64ec65c769332fec8dd505332", "fullName": "testcases.test_ella.unsupported_commands.test_set_timer.TestEllaOpenPlayPoliticalNews#test_set_timer", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_timer"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_timer"}]}