{"name": "测试jump to high brightness mode settings返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['jump to high brightness mode settings', 'Which feature should I turn on?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_jump_to_high_brightness_mode_settings.TestEllaJumpHighBrightnessModeSettings object at 0x0000018BC655AE30>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6671B40>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_jump_to_high_brightness_mode_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['jump to high brightness mode settings', 'Which feature should I turn on?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_jump_to_high_brightness_mode_settings.py:34: AssertionError"}, "description": "验证jump to high brightness mode settings指令返回预期的不支持响应", "steps": [{"name": "执行命令: jump to high brightness mode settings", "status": "passed", "steps": [{"name": "执行命令: jump to high brightness mode settings", "status": "passed", "start": 1756799215340, "stop": 1756799236972}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1c571593-1f52-4ecd-9a25-def5b5e97b0b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "be0ae67d-b546-44b7-ab5a-1e8980ef2814-attachment.png", "type": "image/png"}], "start": 1756799236973, "stop": 1756799237212}], "start": 1756799215340, "stop": 1756799237213}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['jump to high brightness mode settings', 'Which feature should I turn on?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_jump_to_high_brightness_mode_settings.py\", line 34, in test_jump_to_high_brightness_mode_settings\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756799237213, "stop": 1756799237216}], "attachments": [{"name": "stdout", "source": "af874fae-b7d4-4979-9d1b-a0c34c6bb23e-attachment.txt", "type": "text/plain"}], "start": 1756799215340, "stop": 1756799237217, "uuid": "58e147dd-20cd-4494-8c42-cbc5f3b962a4", "historyId": "48a2a80bfed06f0c82b99a0aaa26e252", "testCaseId": "48a2a80bfed06f0c82b99a0aaa26e252", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_high_brightness_mode_settings.TestEllaJumpHighBrightnessModeSettings#test_jump_to_high_brightness_mode_settings", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_high_brightness_mode_settings"}, {"name": "subSuite", "value": "TestEllaJumpHighBrightnessModeSettings"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_high_brightness_mode_settings"}]}