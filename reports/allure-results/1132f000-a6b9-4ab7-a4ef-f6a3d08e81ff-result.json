{"name": "测试navigation to the first address in the image能正常执行", "status": "passed", "description": "navigation to the first address in the image", "steps": [{"name": "执行命令: navigation to the first address in the image", "status": "passed", "steps": [{"name": "执行命令: navigation to the first address in the image", "status": "passed", "start": 1756799781600, "stop": 1756799813285}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5f302464-4dd2-4602-8770-60add8dae9fb-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d6517638-6d29-41cd-9993-ef6ed4bdae90-attachment.png", "type": "image/png"}], "start": 1756799813285, "stop": 1756799813494}], "start": 1756799781600, "stop": 1756799813495}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756799813495, "stop": 1756799813496}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "73ef8531-a65f-4236-9bd1-e3813bb0abeb-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "bbc12042-8dc7-4e8b-8650-6cad948c4c97-attachment.png", "type": "image/png"}], "start": 1756799813496, "stop": 1756799813705}], "attachments": [{"name": "stdout", "source": "3639dc1e-c41c-49a1-8efc-87fdfa951679-attachment.txt", "type": "text/plain"}], "start": 1756799781600, "stop": 1756799813705, "uuid": "f2846090-5535-4c84-a6b9-06591b5a9b6d", "historyId": "9c84b087eb7d9fde94ed5bb5370b275b", "testCaseId": "9c84b087eb7d9fde94ed5bb5370b275b", "fullName": "testcases.test_ella.unsupported_commands.test_navigation_to_the_first_address_in_the_image.TestEllaNavigationFirstAddressImage#test_navigation_to_the_first_address_in_the_image", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_navigation_to_the_first_address_in_the_image"}, {"name": "subSuite", "value": "TestEllaNavigationFirstAddressImage"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_navigation_to_the_first_address_in_the_image"}]}