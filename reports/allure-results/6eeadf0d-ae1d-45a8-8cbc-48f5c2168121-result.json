{"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "status": "passed", "description": "验证jump to notifications and status bar settings指令返回预期的不支持响应", "steps": [{"name": "执行命令: jump to notifications and status bar settings", "status": "passed", "steps": [{"name": "执行命令: jump to notifications and status bar settings", "status": "passed", "start": 1756799344218, "stop": 1756799373631}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7eb5d6f9-df83-4645-b0e9-4f326d30d54e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "12462cf5-560c-4fd2-b3e2-8bd606e65aa6-attachment.png", "type": "image/png"}], "start": 1756799373631, "stop": 1756799373898}], "start": 1756799344218, "stop": 1756799373899}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756799373899, "stop": 1756799373901}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e79fbe30-d0c7-449f-b9b0-b844cf52b4e1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "019768d4-9c0c-4946-8b75-5fba60800eb3-attachment.png", "type": "image/png"}], "start": 1756799373901, "stop": 1756799374193}], "attachments": [{"name": "stdout", "source": "9d3bd296-57f3-41db-86f4-399a5745b955-attachment.txt", "type": "text/plain"}], "start": 1756799344218, "stop": 1756799374195, "uuid": "06e63028-aefd-4a35-ae6d-17e3ffccc837", "historyId": "d8bd499fa9e4e04741c5c255fac9036d", "testCaseId": "d8bd499fa9e4e04741c5c255fac9036d", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_notifications_and_status_bar_settings.TestEllaJumpNotificationsStatusBarSettings#test_jump_to_notifications_and_status_bar_settings", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_notifications_and_status_bar_settings"}, {"name": "subSuite", "value": "TestEllaJumpNotificationsStatusBarSettings"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_notifications_and_status_bar_settings"}]}