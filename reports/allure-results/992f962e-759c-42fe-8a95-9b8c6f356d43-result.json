{"name": "测试how's the weather today in shanghai能正常执行", "status": "passed", "description": "how's the weather today in shanghai", "steps": [{"name": "执行命令: how's the weather today in shanghai", "status": "passed", "steps": [{"name": "执行命令: how's the weather today in shanghai", "status": "passed", "start": 1756785443641, "stop": 1756785473283}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "cd2859b3-bd36-41b8-aeb4-20e26be0c2f9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "dc869026-613a-40ea-baaa-b634223cad78-attachment.png", "type": "image/png"}], "start": 1756785473283, "stop": 1756785473526}], "start": 1756785443641, "stop": 1756785473527}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756785473527, "stop": 1756785473529}, {"name": "使用优化后的天气数据验证范围", "status": "passed", "attachments": [{"name": "天气数据验证通过", "source": "faae50c0-5e74-4e4b-8fb8-e8641caf4bcf-attachment.txt", "type": "text/plain"}, {"name": "温度验证", "source": "f4883d70-fed3-4e83-95b9-59e4804820fa-attachment.txt", "type": "text/plain"}], "start": 1756785473529, "stop": 1756785473536}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8af671fc-85c2-456b-a540-032be49cebae-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a6d4d6e3-2d5d-479a-9580-6af22e3749c9-attachment.png", "type": "image/png"}], "start": 1756785473536, "stop": 1756785473744}], "attachments": [{"name": "stdout", "source": "a086285e-1588-4010-8ae4-8150c214f592-attachment.txt", "type": "text/plain"}], "start": 1756785443641, "stop": 1756785473745, "uuid": "95addf2b-3659-4fee-9791-eb4e3ced58fe", "historyId": "bd4d204a449f3a4013b03af9a9101446", "testCaseId": "bd4d204a449f3a4013b03af9a9101446", "fullName": "testcases.test_ella.dialogue.test_how_s_the_weather_today_in_shanghai.TestEllaWhatSWeatherLikeShanghaiToday#test_what_s_the_weather_like_in_shanghai_today", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_s_the_weather_today_in_shanghai"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherLikeShanghaiToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_s_the_weather_today_in_shanghai"}]}