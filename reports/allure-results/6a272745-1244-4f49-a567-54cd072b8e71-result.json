{"name": "测试countdown 5 min能正常执行", "status": "passed", "description": "countdown 5 min", "steps": [{"name": "执行命令:  countdown 5 min", "status": "passed", "steps": [{"name": "执行命令: countdown 5 min", "status": "passed", "start": 1756789227819, "stop": 1756789259082}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4e6747a4-0c3b-41cc-8afb-577da5a7ceb9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3b943176-fcd2-4a81-89b0-37766e992581-attachment.png", "type": "image/png"}], "start": 1756789259082, "stop": 1756789259304}], "start": 1756789227819, "stop": 1756789259305}, {"name": "执行命令: stop the countdown", "status": "passed", "steps": [{"name": "执行命令: stop the countdown", "status": "passed", "start": 1756789259305, "stop": 1756789282287}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a124938f-57ce-41d1-9f9a-556a595b0525-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6774eec3-b0fa-4180-a4b5-4e32e37877a8-attachment.png", "type": "image/png"}], "start": 1756789282288, "stop": 1756789282503}], "start": 1756789259305, "stop": 1756789282504}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756789282504, "stop": 1756789282504}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b82465fd-ce2c-48c3-8211-0ba0944d3982-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "12efa637-d587-4679-8b80-4c2d3f57fafd-attachment.png", "type": "image/png"}], "start": 1756789282505, "stop": 1756789282715}], "attachments": [{"name": "stdout", "source": "a0189e2d-7923-455e-8952-54a5f526b4f8-attachment.txt", "type": "text/plain"}], "start": 1756789227818, "stop": 1756789282715, "uuid": "4856ecc0-2adb-4fd3-9eb4-2472115323af", "historyId": "ffd7dc86cbeda13ca78bbca09f06422a", "testCaseId": "ffd7dc86cbeda13ca78bbca09f06422a", "fullName": "testcases.test_ella.system_coupling.test_countdown_min.TestEllaCountdownMin#test_countdown_min", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_countdown_min"}, {"name": "subSuite", "value": "TestEllaCountdownMin"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_countdown_min"}]}