{"name": "测试open font family settings返回正确的不支持响应", "status": "passed", "description": "验证open font family settings指令返回预期的不支持响应", "steps": [{"name": "执行命令: open font family settings", "status": "passed", "steps": [{"name": "执行命令: open font family settings", "status": "passed", "start": 1756799968703, "stop": 1756799999415}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8dab99c9-0432-452b-89b7-82430c08947d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c8b4b5f6-51de-4233-8fe8-a8e48a838d46-attachment.png", "type": "image/png"}], "start": 1756799999415, "stop": 1756799999666}], "start": 1756799968703, "stop": 1756799999666}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756799999666, "stop": 1756799999668}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a4f50bae-20ae-42be-940c-fef925bcb401-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fe9a3682-6ce8-4674-9290-40d4d386c182-attachment.png", "type": "image/png"}], "start": 1756799999668, "stop": 1756799999913}], "attachments": [{"name": "stdout", "source": "a58c4288-b2fb-48d8-a1df-f5644b690aff-attachment.txt", "type": "text/plain"}], "start": 1756799968703, "stop": 1756799999914, "uuid": "7e58c52c-b543-436c-afb0-1aabd5514e97", "historyId": "fd79b0d35f1f4639521f70b269d3aadc", "testCaseId": "fd79b0d35f1f4639521f70b269d3aadc", "fullName": "testcases.test_ella.unsupported_commands.test_open_font_family_settings.TestEllaOpenSettings#test_open_font_family_settings", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_open_font_family_settings"}, {"name": "subSuite", "value": "TestEllaOpenSettings"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_open_font_family_settings"}]}