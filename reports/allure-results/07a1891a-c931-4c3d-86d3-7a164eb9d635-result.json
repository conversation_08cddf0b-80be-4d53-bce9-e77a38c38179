{"name": "测试max ring volume能正常执行", "status": "passed", "description": "max ring volume", "steps": [{"name": "执行命令: max ring volume", "status": "passed", "steps": [{"name": "执行命令: max ring volume", "status": "passed", "start": 1756789977223, "stop": 1756789999804}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "62a2b725-2a3f-40e1-b2db-88b445668654-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "53021c21-f7dc-48b1-98fc-6f6168b8e456-attachment.png", "type": "image/png"}], "start": 1756789999804, "stop": 1756790000006}], "start": 1756789977223, "stop": 1756790000007}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790000007, "stop": 1756790000009}, {"name": "验证应用已打开", "status": "passed", "start": 1756790000009, "stop": 1756790000009}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d0a01c2a-c5e9-4953-879a-43c4297388c6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e5a90339-8672-4012-9c01-853cdeb1eee1-attachment.png", "type": "image/png"}], "start": 1756790000009, "stop": 1756790000215}], "attachments": [{"name": "stdout", "source": "052edf8c-b50b-48ff-8920-ecb9f3b6282e-attachment.txt", "type": "text/plain"}], "start": 1756789977223, "stop": 1756790000216, "uuid": "0f181376-c5b2-4494-aedd-826138be388e", "historyId": "a54454fcb441a45b0e29dcbbf21679aa", "testCaseId": "a54454fcb441a45b0e29dcbbf21679aa", "fullName": "testcases.test_ella.system_coupling.test_max_ring_volume.TestEllaMaxRingVolume#test_max_ring_volume", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_max_ring_volume"}, {"name": "subSuite", "value": "TestEllaMaxRingVolume"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_max_ring_volume"}]}