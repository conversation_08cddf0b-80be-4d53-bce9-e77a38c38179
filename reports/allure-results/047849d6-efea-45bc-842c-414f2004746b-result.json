{"name": "测试help me generate a picture of a bamboo forest stream", "status": "passed", "description": "测试help me generate a picture of a bamboo forest stream指令", "steps": [{"name": "执行命令: help me generate a picture of a bamboo forest stream", "status": "passed", "steps": [{"name": "执行命令: help me generate a picture of a bamboo forest stream", "status": "passed", "start": 1756798054111, "stop": 1756798077318}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b500b90d-61a2-4292-8195-77fa47e6d65a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0a5819db-657a-4f3c-ace1-6882ee802aaa-attachment.png", "type": "image/png"}], "start": 1756798077318, "stop": 1756798077506}], "start": 1756798054111, "stop": 1756798077506}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756798077506, "stop": 1756798077508}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fd944927-424f-4ff4-9b50-271013a2eaa3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "433dfd89-96f2-42d9-9af4-176b8050a4a3-attachment.png", "type": "image/png"}], "start": 1756798077508, "stop": 1756798077750}], "attachments": [{"name": "stdout", "source": "334e1e0d-e71b-4a9f-a0a2-43e64ef96f39-attachment.txt", "type": "text/plain"}], "start": 1756798054111, "stop": 1756798077751, "uuid": "ab91d4d7-3742-400e-b937-017ec073a570", "historyId": "9e84af065ec0018044fd43f37b4c2179", "testCaseId": "9e84af065ec0018044fd43f37b4c2179", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_a_bamboo_forest_stream.TestEllaOpenPlayPoliticalNews#test_help_me_generate_a_picture_of_a_bamboo_forest_stream", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_generate_a_picture_of_a_bamboo_forest_stream"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_a_bamboo_forest_stream"}]}