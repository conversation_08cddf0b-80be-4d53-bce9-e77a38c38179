{"name": "测试listen to fm能正常执行", "status": "passed", "description": "listen to fm", "steps": [{"name": "执行命令: listen to fm", "status": "passed", "steps": [{"name": "执行命令: listen to fm", "status": "passed", "start": 1756785810730, "stop": 1756785832696}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "522fff02-86fe-4ba1-bf77-28c11526eefe-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6e2e95a1-eb7f-4049-b894-69cd2b9f0da6-attachment.png", "type": "image/png"}], "start": 1756785832696, "stop": 1756785832901}], "start": 1756785810730, "stop": 1756785832902}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756785832902, "stop": 1756785832904}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a646b78d-2592-4b2b-8de4-51b429b92d09-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "64f1746f-552c-4a32-9061-c2a6ac7a4348-attachment.png", "type": "image/png"}], "start": 1756785832904, "stop": 1756785833128}], "attachments": [{"name": "stdout", "source": "aec2833c-d91c-4ae8-93dc-8e6dd35305d9-attachment.txt", "type": "text/plain"}], "start": 1756785810730, "stop": 1756785833128, "uuid": "11cb516d-37a1-4b34-964a-7d2082eff682", "historyId": "a6002c930e7d5977f441f6060af6308d", "testCaseId": "a6002c930e7d5977f441f6060af6308d", "fullName": "testcases.test_ella.dialogue.test_listen_to_fm.TestEllaHelloHello#test_listen_to_fm", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_listen_to_fm"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_listen_to_fm"}]}