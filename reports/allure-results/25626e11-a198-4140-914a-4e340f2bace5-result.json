{"name": "测试play music by VLC", "status": "passed", "description": "测试play music by VLC指令", "steps": [{"name": "执行命令: play music by VLC", "status": "passed", "steps": [{"name": "执行命令: play music by VLC", "status": "passed", "start": 1756786117164, "stop": 1756786141180}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3f771835-256f-4940-b379-f137d2b42c62-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "da48f2e4-e779-4a92-bfef-eea9bcd68f66-attachment.png", "type": "image/png"}], "start": 1756786141181, "stop": 1756786141379}], "start": 1756786117164, "stop": 1756786141379}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756786141379, "stop": 1756786141380}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "48a3c68b-8635-41b2-bf5e-2ca4c0035888-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c56b7e5b-fc8b-4453-91a0-5c7dffa91ef4-attachment.png", "type": "image/png"}], "start": 1756786141380, "stop": 1756786141616}], "attachments": [{"name": "stdout", "source": "b7d164c2-b827-4b77-b481-ce914ac2fa36-attachment.txt", "type": "text/plain"}], "start": 1756786117164, "stop": 1756786141617, "uuid": "fd674473-bce2-4934-bec9-90cbaf0627c8", "historyId": "aac47e3ff4229b41a579f7c9ec938afb", "testCaseId": "aac47e3ff4229b41a579f7c9ec938afb", "fullName": "testcases.test_ella.dialogue.test_play_music_by_VLC.TestEllaOpenPlayPoliticalNews#test_play_music_by_VLC", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_music_by_VLC"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_music_by_VLC"}]}