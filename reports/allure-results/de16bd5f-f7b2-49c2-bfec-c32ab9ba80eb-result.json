{"name": "测试enable zonetouch master返回正确的不支持响应", "status": "passed", "description": "验证enable zonetouch master指令返回预期的不支持响应", "steps": [{"name": "执行命令: enable zonetouch master", "status": "passed", "steps": [{"name": "执行命令: enable zonetouch master", "status": "passed", "start": 1756797320212, "stop": 1756797341945}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a33a1eed-631b-413b-998c-492494abaf56-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5d91da4b-e869-4eb9-a2d5-89ccc6770c8e-attachment.png", "type": "image/png"}], "start": 1756797341945, "stop": 1756797342200}], "start": 1756797320212, "stop": 1756797342201}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756797342201, "stop": 1756797342202}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c0bd6c05-5cbd-4cfc-bfe6-a26f147032fb-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f3bef8e2-20ce-48b6-8b50-a9042890ffa3-attachment.png", "type": "image/png"}], "start": 1756797342202, "stop": 1756797342441}], "attachments": [{"name": "stdout", "source": "25e5b349-3309-4259-ac74-b419f2b07da1-attachment.txt", "type": "text/plain"}], "start": 1756797320212, "stop": 1756797342441, "uuid": "ff8caac5-39ce-46a4-99cd-82cd993b1466", "historyId": "7a670647c2336e6a5a5d07824fe89da6", "testCaseId": "7a670647c2336e6a5a5d07824fe89da6", "fullName": "testcases.test_ella.unsupported_commands.test_enable_zonetouch_master.TestEllaEnableZonetouchMaster#test_enable_zonetouch_master", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_zonetouch_master"}, {"name": "subSuite", "value": "TestEllaEnableZonetouchMaster"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_zonetouch_master"}]}