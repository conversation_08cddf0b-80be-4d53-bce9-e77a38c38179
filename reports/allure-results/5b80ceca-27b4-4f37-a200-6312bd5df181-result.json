{"name": "测试measure blood oxygen", "status": "passed", "description": "测试measure blood oxygen指令", "steps": [{"name": "执行命令: measure blood oxygen", "status": "passed", "steps": [{"name": "执行命令: measure blood oxygen", "status": "passed", "start": 1756785886173, "stop": 1756785907475}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7b6f7f52-5e36-4567-93c4-9d494c5bf001-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e9a4ef3e-0728-4ed1-9950-982cbaab87c0-attachment.png", "type": "image/png"}], "start": 1756785907475, "stop": 1756785907674}], "start": 1756785886173, "stop": 1756785907674}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756785907675, "stop": 1756785907676}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5c04893f-9fe0-4e68-a29b-4db9ba922111-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6122b771-108e-4a3a-af83-c2d2373e5ca4-attachment.png", "type": "image/png"}], "start": 1756785907676, "stop": 1756785907891}], "attachments": [{"name": "stdout", "source": "*************-46d0-bee0-ec530ef04b13-attachment.txt", "type": "text/plain"}], "start": 1756785886173, "stop": 1756785907891, "uuid": "12bd6b02-62a8-4559-9525-b6392dc8e3bf", "historyId": "7ec382c77bede0ad015817770cd9e1eb", "testCaseId": "7ec382c77bede0ad015817770cd9e1eb", "fullName": "testcases.test_ella.dialogue.test_measure_blood_oxygen.TestEllaOpenPlayPoliticalNews#test_measure_blood_oxygen", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_measure_blood_oxygen"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_measure_blood_oxygen"}]}