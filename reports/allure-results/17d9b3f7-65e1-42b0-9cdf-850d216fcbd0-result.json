{"name": "测试Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations", "status": "passed", "description": "测试Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations指令", "steps": [{"name": "执行命令: Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations", "status": "passed", "steps": [{"name": "执行命令: Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations", "status": "passed", "start": 1756796162331, "stop": 1756796189069}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1b7d6a2a-9b03-4787-992a-8d1c0f3880ed-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f4d7fc80-fcab-468a-8547-e6e59ebcbd09-attachment.png", "type": "image/png"}], "start": 1756796189069, "stop": 1756796189358}], "start": 1756796162331, "stop": 1756796189358}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756796189358, "stop": 1756796189360}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8a4001e5-b9c8-4039-82db-e1308aa6d266-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "caf29853-9da0-453b-a73f-286c4d64dfe8-attachment.png", "type": "image/png"}], "start": 1756796189360, "stop": 1756796189594}], "attachments": [{"name": "stdout", "source": "096da45c-ebc4-49ab-8b49-f8aa1d360bc5-attachment.txt", "type": "text/plain"}], "start": 1756796162331, "stop": 1756796189595, "uuid": "c143a24b-486f-41d5-9230-3eccd9ace43e", "historyId": "a416a85ec867e3b2cfd6e23150d72859", "testCaseId": "a416a85ec867e3b2cfd6e23150d72859", "fullName": "testcases.test_ella.unsupported_commands.test_design_a_high_end_jewelry_ring_themed_around_flamingo_elements_for_e_commerce_illustrations.TestEllaOpenPlayPoliticalNews#test_design_a_high_end_jewelry_ring_themed_around_flamingo_elements_for_e_commerce_illustrations", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_design_a_high_end_jewelry_ring_themed_around_flamingo_elements_for_e_commerce_illustrations"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_design_a_high_end_jewelry_ring_themed_around_flamingo_elements_for_e_commerce_illustrations"}]}