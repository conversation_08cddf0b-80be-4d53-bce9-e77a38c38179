{"name": "测试max brightness能正常执行", "status": "passed", "description": "max brightness", "steps": [{"name": "执行命令: max brightness", "status": "passed", "steps": [{"name": "执行命令: max brightness", "status": "passed", "start": 1756789901320, "stop": 1756789923356}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6cc83be1-c276-49b1-8ea4-3750fdd06b11-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c7d2d081-ee9c-4365-ba9a-9706501a5dcd-attachment.png", "type": "image/png"}], "start": 1756789923356, "stop": 1756789923557}], "start": 1756789901320, "stop": 1756789923557}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756789923557, "stop": 1756789923558}, {"name": "验证应用已打开", "status": "passed", "start": 1756789923559, "stop": 1756789923559}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "427de667-ca3f-44c1-aa33-9350cdc7eb5c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "59622d76-2490-471c-8ffb-eeb9aaa2997a-attachment.png", "type": "image/png"}], "start": 1756789923559, "stop": 1756789923779}], "attachments": [{"name": "stdout", "source": "5468815f-475c-4a98-8d3f-f76050cb66db-attachment.txt", "type": "text/plain"}], "start": 1756789901320, "stop": 1756789923780, "uuid": "d0f91da5-a1d2-4dcd-81c4-7d9650aee9da", "historyId": "4b3ad3bdf0873599e48d8f20d70246c9", "testCaseId": "4b3ad3bdf0873599e48d8f20d70246c9", "fullName": "testcases.test_ella.system_coupling.test_max_brightness.TestEllaMaxBrightness#test_max_brightness", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_max_brightness"}, {"name": "subSuite", "value": "TestEllaMaxBrightness"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_max_brightness"}]}