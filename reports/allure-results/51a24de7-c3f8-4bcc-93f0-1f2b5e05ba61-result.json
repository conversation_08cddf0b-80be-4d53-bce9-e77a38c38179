{"name": "测试set a timer for 10 minutes能正常执行", "status": "passed", "description": "set a timer for 10 minutes", "steps": [{"name": "执行命令: set a timer for 10 minutes", "status": "passed", "steps": [{"name": "执行命令: set a timer for 10 minutes", "status": "passed", "start": 1756790583987, "stop": 1756790613250}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "310a390e-0a3a-4c96-9c57-3c05846eccae-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a0f0cafa-a8d6-4564-b397-d6bb594b4dd2-attachment.png", "type": "image/png"}], "start": 1756790613250, "stop": 1756790613462}], "start": 1756790583987, "stop": 1756790613462}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790613463, "stop": 1756790613464}, {"name": "验证应用已打开", "status": "passed", "start": 1756790613464, "stop": 1756790613464}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6ff1de26-0efb-4d2c-bec7-107c7d076abf-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a2be7fa5-2138-458b-841c-7ec1d314b6e5-attachment.png", "type": "image/png"}], "start": 1756790613464, "stop": 1756790613659}], "attachments": [{"name": "stdout", "source": "0193b434-30d9-46ce-a8eb-82b00ef0b8db-attachment.txt", "type": "text/plain"}], "start": 1756790583987, "stop": 1756790613659, "uuid": "fe5c2f6a-44bc-4029-87f4-48b964447695", "historyId": "f46a1c12d07d5949dbcf4b31314824ec", "testCaseId": "f46a1c12d07d5949dbcf4b31314824ec", "fullName": "testcases.test_ella.system_coupling.test_set_a_timer_for_minutes.TestEllaSetTimerMinutes#test_set_a_timer_for_minutes", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_a_timer_for_minutes"}, {"name": "subSuite", "value": "TestEllaSetTimerMinutes"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_a_timer_for_minutes"}]}