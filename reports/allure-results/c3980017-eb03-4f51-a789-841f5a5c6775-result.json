{"name": "测试what time is it in London能正常执行", "status": "passed", "description": "what time is it in London", "steps": [{"name": "执行命令: what time is it in London", "status": "passed", "steps": [{"name": "执行命令: what time is it in London", "status": "passed", "start": 1756804474641, "stop": 1756804499970}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6357de1f-4701-48b3-8fa3-03295879c9e8-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4f8a7ca2-a181-4b85-aec6-269ff899e650-attachment.png", "type": "image/png"}], "start": 1756804499970, "stop": 1756804500271}], "start": 1756804474641, "stop": 1756804500272}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756804500272, "stop": 1756804500273}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e186a269-48a9-4c17-9f94-7c00589aab4f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b8065092-0b34-4138-b6be-d01aca9e8c01-attachment.png", "type": "image/png"}], "start": 1756804500273, "stop": 1756804500507}], "attachments": [{"name": "stdout", "source": "14b3beae-e403-4591-9ac0-dd57d9cb5c69-attachment.txt", "type": "text/plain"}], "start": 1756804474641, "stop": 1756804500508, "uuid": "4a7c0ee3-079b-464e-ae25-87c7f4bb4248", "historyId": "a084b34b62992b8f17deb64af6e57e39", "testCaseId": "a084b34b62992b8f17deb64af6e57e39", "fullName": "testcases.test_ella.unsupported_commands.test_what_time_is_it_in_london.TestEllaWhatTimeIsItLondon#test_what_time_is_it_in_london", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_what_time_is_it_in_london"}, {"name": "subSuite", "value": "TestEllaWhatTimeIsItLondon"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_what_time_is_it_in_london"}]}