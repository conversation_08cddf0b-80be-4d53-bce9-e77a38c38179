{"name": "测试jump to battery usage返回正确的不支持响应", "status": "passed", "description": "验证jump to battery usage指令返回预期的不支持响应", "steps": [{"name": "执行命令: jump to battery usage", "status": "passed", "steps": [{"name": "执行命令: jump to battery usage", "status": "passed", "start": 1756799120223, "stop": 1756799148691}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7a0cb625-54cd-488e-a77e-049bc3d01f7a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "454a5572-6466-4d38-885b-e62a72d7b7e8-attachment.png", "type": "image/png"}], "start": 1756799148691, "stop": 1756799148965}], "start": 1756799120223, "stop": 1756799148966}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756799148966, "stop": 1756799148967}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "34f56157-a5f1-40cb-8a9a-515b59447da0-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cae35755-0358-45bb-98f7-50b364e39198-attachment.png", "type": "image/png"}], "start": 1756799148968, "stop": 1756799149217}], "attachments": [{"name": "stdout", "source": "8b4de5b9-d6f0-4090-b15e-174ec8fde73d-attachment.txt", "type": "text/plain"}], "start": 1756799120223, "stop": 1756799149218, "uuid": "6e281a7c-da44-4a40-8854-3699eec97078", "historyId": "4276e587385154206726240ad06acd24", "testCaseId": "4276e587385154206726240ad06acd24", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_battery_usage.TestEllaJumpBatteryUsage#test_jump_to_battery_usage", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_battery_usage"}, {"name": "subSuite", "value": "TestEllaJumpBatteryUsage"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_battery_usage"}]}