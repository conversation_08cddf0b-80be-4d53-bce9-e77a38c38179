{"name": "测试turn down notifications volume能正常执行", "status": "passed", "description": "turn down notifications volume", "steps": [{"name": "执行命令: turn down notifications volume", "status": "passed", "steps": [{"name": "执行命令: set notifications volume to 50", "status": "passed", "start": 1756792089275, "stop": 1756792110080}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "51562236-f3d7-46ac-a749-74a75c50ab16-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "14e7b142-2cef-4d72-b9f3-52f24f47709f-attachment.png", "type": "image/png"}], "start": 1756792110080, "stop": 1756792110296}], "start": 1756792089275, "stop": 1756792110296}, {"name": "执行命令: turn down notifications volume", "status": "passed", "steps": [{"name": "执行命令: turn down notifications volume", "status": "passed", "start": 1756792110296, "stop": 1756792132560}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6e8ff441-9bd0-4506-9a4e-b06c19a79424-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d0dbb116-bbb7-4be9-b009-7e17a17acc2f-attachment.png", "type": "image/png"}], "start": 1756792132560, "stop": 1756792132778}], "start": 1756792110296, "stop": 1756792132778}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756792132778, "stop": 1756792132779}, {"name": "验证应用已打开", "status": "passed", "start": 1756792132779, "stop": 1756792132779}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f1881a9d-4f8c-49d8-b1d5-b852c2616321-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "93dac260-c146-4b03-af5f-96f073c74d0b-attachment.png", "type": "image/png"}], "start": 1756792132779, "stop": 1756792132976}], "attachments": [{"name": "stdout", "source": "05f1a902-f01f-48f0-a510-bf02a19907ec-attachment.txt", "type": "text/plain"}], "start": 1756792089275, "stop": 1756792132977, "uuid": "*************-45f2-ad46-fd075605fc78", "historyId": "8373737839693413a37e35b627a0f5de", "testCaseId": "8373737839693413a37e35b627a0f5de", "fullName": "testcases.test_ella.system_coupling.test_turn_down_notifications_volume.TestEllaTurnDownNotificationsVolume#test_turn_down_notifications_volume", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_down_notifications_volume"}, {"name": "subSuite", "value": "TestEllaTurnDownNotificationsVolume"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_down_notifications_volume"}]}