{"name": "测试unset alarms能正常执行", "status": "passed", "description": "unset alarms", "steps": [{"name": "执行命令:  Set an alarm at 10 am tomorrow", "status": "passed", "steps": [{"name": "执行命令: Set an alarm at 10 am tomorrow", "status": "passed", "start": 1756787273934, "stop": 1756787296475}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1346278e-0633-4179-9c5c-162f1910aac7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "03c65660-6883-4b6e-b819-c5542b796fcb-attachment.png", "type": "image/png"}], "start": 1756787296475, "stop": 1756787296776}], "start": 1756787273934, "stop": 1756787296777}, {"name": "执行命令: unset alarms", "status": "passed", "steps": [{"name": "执行命令: unset alarms", "status": "passed", "start": 1756787296777, "stop": 1756787318643}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "af0da6bf-e898-43d4-bf8d-d675472944a8-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "72f7c05b-399f-48ba-9ac7-f08a3e788897-attachment.png", "type": "image/png"}], "start": 1756787318643, "stop": 1756787318854}], "start": 1756787296777, "stop": 1756787318855}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756787318855, "stop": 1756787318858}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6c279673-b4c4-48bb-a472-b11ad9710e05-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "032b3935-e95c-4a0f-8ca9-3fd9050e7840-attachment.png", "type": "image/png"}], "start": 1756787318858, "stop": 1756787319086}], "attachments": [{"name": "stdout", "source": "ccf9a21f-b094-40ed-b634-cfa778b657cb-attachment.txt", "type": "text/plain"}], "start": 1756787273934, "stop": 1756787319086, "uuid": "0060cc9a-a1cb-4cad-814f-d57bf5448ce7", "historyId": "b9fc05e613fdd4d145434e9cf6378c4b", "testCaseId": "b9fc05e613fdd4d145434e9cf6378c4b", "fullName": "testcases.test_ella.dialogue.test_unset_alarms.TestEllaHowIsWeatherToday#test_unset_alarms", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_unset_alarms"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_unset_alarms"}]}