{"name": "测试play love sotry", "status": "failed", "statusDetails": {"message": "AssertionError: visha: 初始=False, 最终=False, 响应='['play love sotry', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.google.android.youtube页面内容] Shorts | Subscriptions | Library']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_play_love_sotry.TestEllaOpenPlayPoliticalNews object at 0x0000018BC659B7C0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC952C790>\n\n    @allure.title(\"测试play love sotry\")\n    @allure.description(\"测试play love sotry指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_play_love_sotry(self, ella_app):\n        \"\"\"测试play love sotry命令\"\"\"\n        command = \"play love sotry\"\n        # expected_text = [\"I'm glad to open Youtube Music for you\", 'You need to select music to play.']\n        expected_text = [\"Done\"]\n        app_name = 'visha'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n    \n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证{app_name}已打开\"):\n>           assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: visha: 初始=False, 最终=False, 响应='['play love sotry', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.google.android.youtube页面内容] Shorts | Subscriptions | Library']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_play_love_sotry.py:36: AssertionError"}, "description": "测试play love sotry指令", "steps": [{"name": "执行命令: play love sotry", "status": "passed", "steps": [{"name": "执行命令: play love sotry", "status": "passed", "start": 1756800483613, "stop": 1756800526201}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "34db21e5-2001-42e6-9768-97019f11ccb9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a8e91763-15c7-4252-b393-1bffa1ae224c-attachment.png", "type": "image/png"}], "start": 1756800526201, "stop": 1756800526453}], "start": 1756800483613, "stop": 1756800526453}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756800526453, "stop": 1756800526457}, {"name": "验证visha已打开", "status": "failed", "statusDetails": {"message": "AssertionError: visha: 初始=False, 最终=False, 响应='['play love sotry', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.google.android.youtube页面内容] Shorts | Subscriptions | Library']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_play_love_sotry.py\", line 36, in test_play_love_sotry\n    assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1756800526457, "stop": 1756800526457}], "attachments": [{"name": "stdout", "source": "9a8ca327-a326-42f8-9d8c-eebbbd536044-attachment.txt", "type": "text/plain"}], "start": 1756800483613, "stop": 1756800526458, "uuid": "9183207d-0166-4f06-9012-24cd3ce49815", "historyId": "a8ceb9cec2faa4662cde95140569091b", "testCaseId": "a8ceb9cec2faa4662cde95140569091b", "fullName": "testcases.test_ella.unsupported_commands.test_play_love_sotry.TestEllaOpenPlayPoliticalNews#test_play_love_sotry", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_love_sotry"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_love_sotry"}]}