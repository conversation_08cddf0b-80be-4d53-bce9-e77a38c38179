{"name": "测试tell me a joke能正常执行", "status": "passed", "description": "tell me a joke", "steps": [{"name": "执行命令: tell me a joke", "status": "passed", "steps": [{"name": "执行命令: tell me a joke", "status": "passed", "start": 1756787233084, "stop": 1756787257016}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "873d28cb-a53c-47c1-a954-51c02d0cfef2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "175196cb-3c45-47b4-bf0e-6a8750f5362c-attachment.png", "type": "image/png"}], "start": 1756787257016, "stop": 1756787257234}], "start": 1756787233084, "stop": 1756787257235}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756787257235, "stop": 1756787257236}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c5f06bc2-f76e-4f9d-9bf4-96bb3ae31acf-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c7d756a0-774d-42a2-8144-a361634633fa-attachment.png", "type": "image/png"}], "start": 1756787257236, "stop": 1756787257460}], "attachments": [{"name": "stdout", "source": "7ee8de02-d7cc-44fd-a701-9e32a3749f85-attachment.txt", "type": "text/plain"}], "start": 1756787233084, "stop": 1756787257461, "uuid": "28f99f57-462f-45e4-aa4f-eb40a5e47299", "historyId": "3dae350db69abded432b3e7f5f8463c8", "testCaseId": "3dae350db69abded432b3e7f5f8463c8", "fullName": "testcases.test_ella.dialogue.test_tell_me_a_joke.TestEllaTellMeJoke#test_tell_me_a_joke", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_tell_me_a_joke"}, {"name": "subSuite", "value": "TestEllaTellMeJoke"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_tell_me_a_joke"}]}