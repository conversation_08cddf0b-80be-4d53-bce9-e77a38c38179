{"name": "测试switch to power saving mode返回正确的不支持响应", "status": "passed", "description": "验证switch to power saving mode指令返回预期的不支持响应", "steps": [{"name": "执行命令: switch to power saving mode", "status": "passed", "steps": [{"name": "执行命令: switch to power saving mode", "status": "passed", "start": 1756803458510, "stop": 1756803482414}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f673826d-226e-48a8-a4e1-899d4d32d284-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6c1a7da8-2d9e-40fe-a3c6-9a85a1bd1491-attachment.png", "type": "image/png"}], "start": 1756803482414, "stop": 1756803482766}], "start": 1756803458510, "stop": 1756803482766}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756803482766, "stop": 1756803482768}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "beb35694-2226-483c-a7e9-9d63f2d7d144-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b24e29d8-acf6-4856-8a05-8651fae20d03-attachment.png", "type": "image/png"}], "start": 1756803482768, "stop": 1756803483037}], "attachments": [{"name": "stdout", "source": "cf2bf12d-5b21-499f-a83e-4e06c9235aa2-attachment.txt", "type": "text/plain"}], "start": 1756803458510, "stop": 1756803483038, "uuid": "b43f21d8-67a7-4a32-a15a-59163e50a713", "historyId": "a0efcebc4cee6024e690bd290b4f3fbb", "testCaseId": "a0efcebc4cee6024e690bd290b4f3fbb", "fullName": "testcases.test_ella.unsupported_commands.test_switch_to_power_saving_mode.TestEllaSwitchPowerSavingMode#test_switch_to_power_saving_mode", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_switch_to_power_saving_mode"}, {"name": "subSuite", "value": "TestEllaSwitchPowerSavingMode"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_switch_to_power_saving_mode"}]}