{"name": "测试disable hide notifications返回正确的不支持响应", "status": "passed", "description": "验证disable hide notifications指令返回预期的不支持响应", "steps": [{"name": "执行命令: disable hide notifications", "status": "passed", "steps": [{"name": "执行命令: disable hide notifications", "status": "passed", "start": 1756796456630, "stop": 1756796478840}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c9fb6787-9c33-4178-9b69-7fa919c6b63c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b8ed922e-67f1-46ad-8678-923c9388c008-attachment.png", "type": "image/png"}], "start": 1756796478840, "stop": 1756796479082}], "start": 1756796456630, "stop": 1756796479082}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756796479082, "stop": 1756796479083}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "bf405e5b-9453-41e7-af20-c66106d747a3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d85a5793-e24b-4a66-9f67-792730f5d864-attachment.png", "type": "image/png"}], "start": 1756796479083, "stop": 1756796479272}], "attachments": [{"name": "stdout", "source": "51905577-65dc-4105-8a65-df53ac4cadc2-attachment.txt", "type": "text/plain"}], "start": 1756796456630, "stop": 1756796479272, "uuid": "d0e589f9-376b-4c45-83a4-746f5f537ec1", "historyId": "3eec462c8da39eaf95742ed9ab45b7c7", "testCaseId": "3eec462c8da39eaf95742ed9ab45b7c7", "fullName": "testcases.test_ella.unsupported_commands.test_disable_hide_notifications.TestEllaDisableHideNotifications#test_disable_hide_notifications", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_hide_notifications"}, {"name": "subSuite", "value": "TestEllaDisableHideNotifications"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_hide_notifications"}]}