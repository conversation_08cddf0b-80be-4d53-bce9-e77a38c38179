{"name": "测试open settings", "status": "passed", "description": "测试open settings指令", "steps": [{"name": "执行命令: open settings", "status": "passed", "steps": [{"name": "执行命令: open settings", "status": "passed", "start": 1756798644286, "stop": 1756798675882}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e7898b30-b81b-4afe-b3bc-46f83191454b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7416bd8c-808c-445d-9822-dc65e264768f-attachment.png", "type": "image/png"}], "start": 1756798675883, "stop": 1756798676119}], "start": 1756798644286, "stop": 1756798676119}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756798676119, "stop": 1756798676121}, {"name": "验证settings已打开", "status": "passed", "start": 1756798676121, "stop": 1756798676122}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c730972a-61a9-4db5-b376-4a6529ea8d99-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "55204670-3fe4-4352-b16d-e56a46a43aee-attachment.png", "type": "image/png"}], "start": 1756798676122, "stop": 1756798676340}], "attachments": [{"name": "stdout", "source": "e6f9af73-f9c2-4cb4-9950-5fa32ae45340-attachment.txt", "type": "text/plain"}], "start": 1756798644286, "stop": 1756798676340, "uuid": "a1ca648f-88ee-4f95-a6c7-c41974703616", "historyId": "e1ef5de48cb99781fc16bc01be62dca2", "testCaseId": "e1ef5de48cb99781fc16bc01be62dca2", "fullName": "testcases.test_ella.unsupported_commands.test_i_wanna_use_sim.TestEllaOpenPlayPoliticalNews#test_i_wanna_use_sim", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_i_wanna_use_sim"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_i_wanna_use_sim"}]}