{"name": "open clock", "status": "passed", "description": "使用open clock命令，验证响应包含Done且实际打开clock命令", "steps": [{"name": "执行命令: open clock", "status": "passed", "steps": [{"name": "执行命令: open clock", "status": "passed", "start": 1756783288217, "stop": 1756783322091}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dd67b12b-fea7-4c9f-803e-baefb2778e61-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d6aad2fc-f2e7-42aa-b001-7cbf1f4dc3c2-attachment.png", "type": "image/png"}], "start": 1756783322091, "stop": 1756783322305}], "start": 1756783288217, "stop": 1756783322305}, {"name": "验证响应包含Done", "status": "passed", "start": 1756783322305, "stop": 1756783322306}, {"name": "验证clock已打开", "status": "passed", "start": 1756783322306, "stop": 1756783322306}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ef63d9c4-60e1-40ca-a806-349d98982b14-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "952d5e01-fc19-4659-97e7-0ec6e8578fec-attachment.png", "type": "image/png"}], "start": 1756783322306, "stop": 1756783322502}], "attachments": [{"name": "stdout", "source": "d795c1a2-504d-4447-9c34-5759955414a2-attachment.txt", "type": "text/plain"}], "start": 1756783288217, "stop": 1756783322503, "uuid": "571122eb-dbb8-4163-880b-500208094de0", "historyId": "169e5b613c0fec2cebd053175998bf17", "testCaseId": "169e5b613c0fec2cebd053175998bf17", "fullName": "testcases.test_ella.component_coupling.test_open_clock.TestEllaCommandConcise#test_open_clock", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "open clock"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_clock"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_clock"}]}