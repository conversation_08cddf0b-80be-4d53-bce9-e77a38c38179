{"name": "stop  screen recording能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 文件不存在！\nassert False", "trace": "self = <testcases.test_ella.system_coupling.test_end_screen_recording.TestEllaTurnScreenRecord object at 0x0000018BC613A1A0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC3E54D60>\n\n    @allure.title(f\"stop  screen recording能正常执行\")\n    @allure.description(f\"stop screen recording\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_end_screen_recording(self, ella_app):\n        command = \"end screen recording\"\n        expected_text = ['Screen recording finished']\n        f\"\"\"{command}\"\"\"\n    \n        with allure.step(f\"执行命令: turn on the screen record\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, 'turn on the screen record', verify_status=False, verify_files=True  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=True  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证已关闭\"):\n            assert not final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n    \n        with allure.step(f\"验证文件存在\"):\n>           assert files_status, f\"文件不存在！\"\nE           AssertionError: 文件不存在！\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_end_screen_recording.py:41: AssertionError"}, "description": "stop screen recording", "steps": [{"name": "执行命令: turn on the screen record", "status": "passed", "steps": [{"name": "执行命令: turn on the screen record", "status": "passed", "start": 1756789486343, "stop": 1756789515432}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ae0a3de7-ecc4-4005-9936-f038c47a4931-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ee3502e5-9d77-49c8-a452-dcd5560feac5-attachment.png", "type": "image/png"}], "start": 1756789515432, "stop": 1756789515660}], "start": 1756789486343, "stop": 1756789515660}, {"name": "执行命令: end screen recording", "status": "passed", "steps": [{"name": "执行命令: end screen recording", "status": "passed", "start": 1756789515660, "stop": 1756789544708}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d23ce8ee-d5c5-46f1-861a-78325cfb0f97-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "360e4815-8257-4917-9c01-bf86da08817f-attachment.png", "type": "image/png"}], "start": 1756789544708, "stop": 1756789544931}], "start": 1756789515660, "stop": 1756789544931}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756789544931, "stop": 1756789544933}, {"name": "验证已关闭", "status": "passed", "start": 1756789544933, "stop": 1756789544933}, {"name": "验证文件存在", "status": "failed", "statusDetails": {"message": "AssertionError: 文件不存在！\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\system_coupling\\test_end_screen_recording.py\", line 41, in test_end_screen_recording\n    assert files_status, f\"文件不存在！\"\n"}, "start": 1756789544933, "stop": 1756789544933}], "attachments": [{"name": "stdout", "source": "47310b54-34d9-4eb5-9401-eddfcf29f073-attachment.txt", "type": "text/plain"}], "start": 1756789486343, "stop": 1756789544934, "uuid": "ff2e5a38-dc87-44e9-b67e-6553960c78f9", "historyId": "b07852caec1a6673427b80552f64be85", "testCaseId": "b07852caec1a6673427b80552f64be85", "fullName": "testcases.test_ella.system_coupling.test_end_screen_recording.TestEllaTurnScreenRecord#test_end_screen_recording", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_end_screen_recording"}, {"name": "subSuite", "value": "TestEllaTurnScreenRecord"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_end_screen_recording"}]}