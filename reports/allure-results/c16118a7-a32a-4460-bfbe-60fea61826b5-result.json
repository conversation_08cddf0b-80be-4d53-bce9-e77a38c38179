{"name": "测试play carpenters'video", "status": "passed", "description": "测试play carpenters'video指令", "steps": [{"name": "执行命令: play carpenters'video", "status": "passed", "steps": [{"name": "执行命令: play carpenters'video", "status": "passed", "start": 1756800386226, "stop": 1756800416030}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ee5da677-bcea-4741-a046-936b69b0c154-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "21ab96e3-1417-4ae4-b0fd-382fc8159dd1-attachment.png", "type": "image/png"}], "start": 1756800416030, "stop": 1756800416292}], "start": 1756800386226, "stop": 1756800416293}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756800416293, "stop": 1756800416294}, {"name": "验证youtube已打开", "status": "passed", "start": 1756800416294, "stop": 1756800416294}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c4788b65-6101-44d5-8107-947ae4f4a353-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b6c9f2ca-2de5-47dc-b92e-787c0344f057-attachment.png", "type": "image/png"}], "start": 1756800416294, "stop": 1756800416593}], "attachments": [{"name": "stdout", "source": "f47b2cd6-5021-4fa0-ba1b-c60193da8a44-attachment.txt", "type": "text/plain"}], "start": 1756800386226, "stop": 1756800416595, "uuid": "10399989-700f-452e-bedb-9c8584e269cd", "historyId": "36066a5bbb1f962a6ad5842baefe4ff3", "testCaseId": "36066a5bbb1f962a6ad5842baefe4ff3", "fullName": "testcases.test_ella.unsupported_commands.test_play_carpenters_video.TestEllaOpenPlayPoliticalNews#test_play_carpenters_video", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_carpenters_video"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_carpenters_video"}]}