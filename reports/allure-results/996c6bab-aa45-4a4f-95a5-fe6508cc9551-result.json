{"name": "测试turn off the 8 am alarm", "status": "passed", "description": "测试turn off the 8 am alarm指令", "steps": [{"name": "执行命令: delete all the alarms", "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "status": "passed", "start": 1756784404787, "stop": 1756784425455}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ed45afc1-35ad-4537-991f-a13a6151511c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6b45a0ab-3be4-4623-af08-b9a64b25a336-attachment.png", "type": "image/png"}], "start": 1756784425455, "stop": 1756784425676}], "start": 1756784404787, "stop": 1756784425676}, {"name": "执行命令: set an alarm at 8 am", "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "status": "passed", "start": 1756784425676, "stop": 1756784446769}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7342d751-9997-4e5f-a7e3-d67b058c2574-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0a619644-57a2-464f-8901-7a67c05678ba-attachment.png", "type": "image/png"}], "start": 1756784446769, "stop": 1756784446987}], "start": 1756784425676, "stop": 1756784446988}, {"name": "执行命令: turn off the 8 am alarm", "status": "passed", "steps": [{"name": "执行命令: turn off the 8 am alarm", "status": "passed", "start": 1756784446988, "stop": 1756784468487}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "bab9242e-68f7-46b4-90c1-6a6ba83afc38-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1d171bcf-48d6-41ea-87ce-c51544587541-attachment.png", "type": "image/png"}], "start": 1756784468487, "stop": 1756784468727}], "start": 1756784446988, "stop": 1756784468727}, {"name": "执行命令: get all the alarms", "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "status": "passed", "start": 1756784468727, "stop": 1756784489487}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e24e357e-7416-437f-a2fb-abd027ca3f6f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "782a5912-68b3-4ff9-840d-e2f85abf0731-attachment.png", "type": "image/png"}], "start": 1756784489487, "stop": 1756784489681}], "start": 1756784468727, "stop": 1756784489681}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756784489681, "stop": 1756784489683}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "249ff02f-f20e-4e6f-9e30-2e1fcc262eae-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "092eb317-bda3-4e8d-a8be-e83d1cb7e126-attachment.png", "type": "image/png"}], "start": 1756784489683, "stop": 1756784489893}], "attachments": [{"name": "stdout", "source": "85b77102-5fa8-4ae8-b2ab-7d7c01ae10a7-attachment.txt", "type": "text/plain"}], "start": 1756784404787, "stop": 1756784489893, "uuid": "dffcc774-66db-42cd-b321-8535ccada031", "historyId": "098126ed77f375b3e0f5370b3ec7d0b7", "testCaseId": "098126ed77f375b3e0f5370b3ec7d0b7", "fullName": "testcases.test_ella.component_coupling.test_turn_off_the_8_am_alarm.TestEllaOpenClock#test_turn_off_the_8_am_alarm", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_turn_off_the_8_am_alarm"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_turn_off_the_8_am_alarm"}]}