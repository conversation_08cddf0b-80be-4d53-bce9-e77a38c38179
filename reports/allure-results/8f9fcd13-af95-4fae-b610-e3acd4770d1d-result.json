{"name": "测试increase screen brightness能正常执行", "status": "passed", "description": "increase screen brightness", "steps": [{"name": "执行命令: increase screen brightness", "status": "passed", "steps": [{"name": "执行命令: increase screen brightness", "status": "passed", "start": 1756789675901, "stop": 1756789697760}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6e400584-f35b-4ae7-9153-46a496e9df83-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "70bf029c-c429-4856-aded-6cf2a88c15c0-attachment.png", "type": "image/png"}], "start": 1756789697760, "stop": 1756789697977}], "start": 1756789675901, "stop": 1756789697977}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756789697977, "stop": 1756789697978}, {"name": "验证应用已打开", "status": "passed", "start": 1756789697978, "stop": 1756789697978}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "24a5a320-5d5e-453e-b04d-4b4a341ae26f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a2c5ceff-2b13-421b-9b53-f2e62b545e62-attachment.png", "type": "image/png"}], "start": 1756789697978, "stop": 1756789698157}], "attachments": [{"name": "stdout", "source": "5f8ffa72-8bbc-4c0a-9de6-95e48c3f1c83-attachment.txt", "type": "text/plain"}], "start": 1756789675900, "stop": 1756789698158, "uuid": "d2ee788a-2fd4-4886-a5fe-4c8b525f6301", "historyId": "fcfaafeb2b49ed6f468b8db263c64a18", "testCaseId": "fcfaafeb2b49ed6f468b8db263c64a18", "fullName": "testcases.test_ella.system_coupling.test_increase_screen_brightness.TestEllaIncreaseScreenBrightness#test_increase_screen_brightness", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_increase_screen_brightness"}, {"name": "subSuite", "value": "TestEllaIncreaseScreenBrightness"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_increase_screen_brightness"}]}