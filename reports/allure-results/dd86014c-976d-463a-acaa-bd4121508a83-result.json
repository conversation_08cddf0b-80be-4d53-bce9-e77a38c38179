{"name": "测试record audio for 5 seconds能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only']，实际响应: '['record audio for 5 seconds', 'Screen recording started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.component_coupling.test_record_audio_for_seconds.TestEllaRecordAudioSeconds object at 0x0000018BC4DFD7B0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC69777F0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_record_audio_for_seconds(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=True\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only']，实际响应: '['record audio for 5 seconds', 'Screen recording started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\component_coupling\\test_record_audio_for_seconds.py:34: AssertionError"}, "description": "record audio for 5 seconds", "steps": [{"name": "执行命令: record audio for 5 seconds", "status": "passed", "steps": [{"name": "执行命令: record audio for 5 seconds", "status": "passed", "start": 1756784066621, "stop": 1756784087574}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1bcdb38a-cfb3-444f-9412-fbfb6f9dc0de-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "40836212-446b-4565-8412-f271ebe655da-attachment.png", "type": "image/png"}], "start": 1756784087574, "stop": 1756784087811}], "start": 1756784066621, "stop": 1756784087812}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only']，实际响应: '['record audio for 5 seconds', 'Screen recording started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\component_coupling\\test_record_audio_for_seconds.py\", line 34, in test_record_audio_for_seconds\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756784087812, "stop": 1756784087815}], "attachments": [{"name": "stdout", "source": "f24b4938-2c5a-45a8-b587-cecc6df00e66-attachment.txt", "type": "text/plain"}], "start": 1756784066621, "stop": 1756784087815, "uuid": "ba6f6b1e-4426-45e6-8af0-080955f04f63", "historyId": "17788b061637289a04732fe5840218ee", "testCaseId": "17788b061637289a04732fe5840218ee", "fullName": "testcases.test_ella.component_coupling.test_record_audio_for_seconds.TestEllaRecordAudioSeconds#test_record_audio_for_seconds", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_record_audio_for_seconds"}, {"name": "subSuite", "value": "TestEllaRecordAudioSeconds"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_record_audio_for_seconds"}]}