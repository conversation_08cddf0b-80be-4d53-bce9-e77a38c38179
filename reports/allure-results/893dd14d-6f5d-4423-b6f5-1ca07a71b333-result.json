{"name": "测试turn on high brightness mode返回正确的不支持响应", "status": "passed", "description": "验证turn on high brightness mode指令返回预期的不支持响应", "steps": [{"name": "执行命令: turn on high brightness mode", "status": "passed", "steps": [{"name": "执行命令: turn on high brightness mode", "status": "passed", "start": 1756803995577, "stop": 1756804017697}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dc3932b5-6ec3-4ba5-84bc-100f63e2a433-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "58910fff-7d53-4234-bbd2-563a116626e9-attachment.png", "type": "image/png"}], "start": 1756804017697, "stop": 1756804017923}], "start": 1756803995577, "stop": 1756804017924}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756804017924, "stop": 1756804017925}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8d66d125-29e5-45c9-b6f2-59aa6b38ebea-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9aa9b84b-4ab3-4cfe-822f-c9172acf198d-attachment.png", "type": "image/png"}], "start": 1756804017925, "stop": 1756804018138}], "attachments": [{"name": "stdout", "source": "e4eedaa4-9eb9-4029-938e-4e1a4363faff-attachment.txt", "type": "text/plain"}], "start": 1756803995576, "stop": 1756804018138, "uuid": "8f393d0f-f765-4405-9843-80e6fa7df6c2", "historyId": "599b7a465f619c38a4638073f59c38c0", "testCaseId": "599b7a465f619c38a4638073f59c38c0", "fullName": "testcases.test_ella.unsupported_commands.test_turn_on_high_brightness_mode.TestEllaTurnHighBrightnessMode#test_turn_on_high_brightness_mode", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_on_high_brightness_mode"}, {"name": "subSuite", "value": "TestEllaTurnHighBrightnessMode"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_on_high_brightness_mode"}]}