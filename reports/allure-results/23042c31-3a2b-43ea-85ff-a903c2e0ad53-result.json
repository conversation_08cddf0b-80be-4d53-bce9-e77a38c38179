{"name": "测试check model information返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Multiple settings options found']，实际响应: '['check model information', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_check_model_information.TestEllaCheckModelInformation object at 0x0000018BC6431A80>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC70A4FD0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_check_model_information(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Multiple settings options found']，实际响应: '['check model information', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_check_model_information.py:34: AssertionError"}, "description": "验证check model information指令返回预期的不支持响应", "steps": [{"name": "执行命令: check model information", "status": "passed", "steps": [{"name": "执行命令: check model information", "status": "passed", "start": 1756795775641, "stop": 1756795798341}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1eca29a9-1fb9-4164-a062-b298b3131a67-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "43f4269a-b535-41fe-921b-787c06cee9ff-attachment.png", "type": "image/png"}], "start": 1756795798341, "stop": 1756795798558}], "start": 1756795775641, "stop": 1756795798558}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Multiple settings options found']，实际响应: '['check model information', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_check_model_information.py\", line 34, in test_check_model_information\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756795798558, "stop": 1756795798560}], "attachments": [{"name": "stdout", "source": "860c8933-7018-49bf-9db9-b3087a1687f0-attachment.txt", "type": "text/plain"}], "start": 1756795775640, "stop": 1756795798561, "uuid": "d9f520df-097e-4bbd-8f10-d2c9efe38b32", "historyId": "49ae31ee7fe8baa7f1604fd83d56bb68", "testCaseId": "49ae31ee7fe8baa7f1604fd83d56bb68", "fullName": "testcases.test_ella.unsupported_commands.test_check_model_information.TestEllaCheckModelInformation#test_check_model_information", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_model_information"}, {"name": "subSuite", "value": "TestEllaCheckModelInformation"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_model_information"}]}