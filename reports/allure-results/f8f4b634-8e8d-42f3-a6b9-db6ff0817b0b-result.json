{"name": "测试Generate a picture of a jungle stream for me", "status": "passed", "description": "测试Generate a picture of a jungle stream for me指令", "steps": [{"name": "执行命令: Generate a picture of a jungle stream for me", "status": "passed", "steps": [{"name": "执行命令: Generate a picture of a jungle stream for me", "status": "passed", "start": 1756797597088, "stop": 1756797619091}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "355cb88e-ed86-432a-9a2c-81ce2ea49c04-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "bbe4a458-7596-4823-9061-9d1cf65994c5-attachment.png", "type": "image/png"}], "start": 1756797619091, "stop": 1756797619332}], "start": 1756797597088, "stop": 1756797619332}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756797619332, "stop": 1756797619333}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "66fed47c-f322-401e-87cd-500f4f5ad6e3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "63197f2a-7da2-446c-959a-5187b0c3eff8-attachment.png", "type": "image/png"}], "start": 1756797619333, "stop": 1756797619577}], "attachments": [{"name": "stdout", "source": "3bd42d3b-a806-424d-8ece-916c998af219-attachment.txt", "type": "text/plain"}], "start": 1756797597088, "stop": 1756797619578, "uuid": "ce77941c-5492-4ca0-810d-909b1585a1c4", "historyId": "700a4f81d53d76c265778c230c99dd8c", "testCaseId": "700a4f81d53d76c265778c230c99dd8c", "fullName": "testcases.test_ella.unsupported_commands.test_generate_a_picture_of_a_jungle_stream_for_me.TestEllaOpenPlayPoliticalNews#test_generate_a_picture_of_a_jungle_stream_for_me", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_generate_a_picture_of_a_jungle_stream_for_me"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_generate_a_picture_of_a_jungle_stream_for_me"}]}