{"uid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "name": "timeline", "children": [{"name": "shcybuchangyi1-pc", "children": [{"name": "33032-MainThread", "children": [{"name": "测试turn off smart reminder能正常执行", "uid": "364533e255c84cd3", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792412890, "stop": 1756792432626, "duration": 19736}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn down the brightness to the min能正常执行", "uid": "4ce075fa73ad6e73", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792187133, "stop": 1756792209542, "duration": 22409}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试long screenshot能正常执行", "uid": "c01f7948bc85ea61", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789788707, "stop": 1756789811813, "duration": 23106}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on the 7AM alarm", "uid": "2dd1629c4a57f5c0", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792933689, "stop": 1756793014273, "duration": 80584}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play rock music", "uid": "21a57458a1be039e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783932607, "stop": 1756783969015, "duration": 36408}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a takeaway能正常执行", "uid": "96985908524f0fd2", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756794761672, "stop": 1756794784871, "duration": 23199}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试make a call能正常执行", "uid": "af7d50665a230f0c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785848657, "stop": 1756785870248, "duration": 21591}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试previous music能正常执行", "uid": "56e85e9a688e148", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756784030802, "stop": 1756784050900, "duration": 20098}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试send my recent photos to mom through whatsapp能正常执行", "uid": "4b0ca3a6abd6b339", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786716846, "stop": 1756786742170, "duration": 25324}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试go home能正常执行", "uid": "598e6ff9d46b0da2", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797677323, "stop": 1756797700881, "duration": 23558}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试make a call by whatsapp能正常执行", "uid": "a5fc0713ed01d19a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799478622, "stop": 1756799502411, "duration": 23789}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pls open whatsapp", "uid": "1b2b2a4710391a26", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756800858685, "stop": 1756800886997, "duration": 28312}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play video", "uid": "c3a648fb46ad14ee", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756800714774, "stop": 1756800748639, "duration": 33865}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试merry christmas", "uid": "e3f6e3d2aa315fc2", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799561480, "stop": 1756799586702, "duration": 25222}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set off a firework能正常执行", "uid": "39acd313f167a5a4", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756802306542, "stop": 1756802332464, "duration": 25922}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试next song能正常执行", "uid": "9e71aa520d7f5088", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786001256, "stop": 1756786024035, "duration": 22779}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on the flashlight能正常执行", "uid": "2d667af6faa830a3", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793030366, "stop": 1756793053496, "duration": 23130}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试there is a colorful butterfly beside it", "uid": "f702ae9d3a4ef0d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803792587, "stop": 1756803817133, "duration": 24546}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Language List", "uid": "664b22c9c219a67", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794924968, "stop": 1756794953614, "duration": 28646}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试video call mom through whatsapp能正常执行", "uid": "bfa07e72898e462", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756787335445, "stop": 1756787365973, "duration": 30528}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn up alarm clock volume", "uid": "7713ec5d2e3b1392", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793152117, "stop": 1756793176621, "duration": 24504}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令", "uid": "a884ae420d086a2e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783548048, "stop": 1756783578842, "duration": 30794}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn up the volume to the max能正常执行", "uid": "4080df4128273d88", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793308757, "stop": 1756793331610, "duration": 22853}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试puppy", "uid": "b503d26219879bb0", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756800955168, "stop": 1756800980119, "duration": 24951}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试max notifications volume能正常执行", "uid": "b6fbaed39b0b8cf6", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756789939434, "stop": 1756789960948, "duration": 21514}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close bluetooth能正常执行", "uid": "d3ab8f9da337262c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789149504, "stop": 1756789172911, "duration": 23407}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试boost phone能正常执行", "uid": "e06a6c9ea846f9ba", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756788961194, "stop": 1756788994079, "duration": 32885}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "21a139ad2166df7d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756795734918, "stop": 1756795758741, "duration": 23823}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Enable Network Enhancement返回正确的不支持响应", "uid": "8fd2bdd257530c60", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797156705, "stop": 1756797179424, "duration": 22719}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试remember the parking lot能正常执行", "uid": "cd041e4a8c7f2200", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756800997894, "stop": 1756801021517, "duration": 23623}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试minimum volume能正常执行", "uid": "ec6a759d2680a807", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790256219, "stop": 1756790277776, "duration": 21557}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn up the brightness to the max能正常执行", "uid": "1234c38f4cea2cf4", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793269897, "stop": 1756793292584, "duration": 22687}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on show battery percentage返回正确的不支持响应", "uid": "9af6a648ea720b89", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756804034601, "stop": 1756804057349, "duration": 22748}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试A sports car is parked on the street side", "uid": "49d68d24e1e6900a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756795320815, "stop": 1756795353100, "duration": 32285}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Enable Call Rejection返回正确的不支持响应", "uid": "d0c86108d3d6cd11", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797115859, "stop": 1756797139759, "duration": 23900}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen refresh rate返回正确的不支持响应", "uid": "b4f5300d13abdbd0", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756802594793, "stop": 1756802628100, "duration": 33307}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause music能正常执行", "uid": "19ae44f5cafb8a3a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786079262, "stop": 1756786101475, "duration": 22213}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play political news", "uid": "1316579bfb181bf1", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786415721, "stop": 1756786442462, "duration": 26741}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable call on hold返回正确的不支持响应", "uid": "e1176be1fa1f77b9", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783076500, "stop": 1756783097619, "duration": 21119}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试A furry little monkey", "uid": "2467f4ed3132cab6", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756795142475, "stop": 1756795168550, "duration": 26075}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试display the route go company", "uid": "8f4486399f2f43b", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783113791, "stop": 1756783140848, "duration": 27057}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set timezone返回正确的不支持响应", "uid": "172582fa9d3d0cb0", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803069221, "stop": 1756803101951, "duration": 32730}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off the 7AM alarm", "uid": "fa36a673ebbfe035", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756784300586, "stop": 1756784388849, "duration": 88263}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "94739d17159fb75c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796908436, "stop": 1756796938077, "duration": 29641}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what's the wheather today?能正常执行", "uid": "14dca835eaa3e362", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756787636254, "stop": 1756787659044, "duration": 22790}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check ram information", "uid": "7d24befde98bebcc", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756795904173, "stop": 1756795926507, "duration": 22334}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试i want to listen to fm能正常执行", "uid": "d7bf4937d6857b76", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785612556, "stop": 1756785634414, "duration": 21858}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on high brightness mode返回正确的不支持响应", "uid": "d74f58526b0b2abf", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803995576, "stop": 1756804018138, "duration": 22562}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to battery usage返回正确的不支持响应", "uid": "ac2cf3846fc7c62d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799120223, "stop": 1756799149218, "duration": 28995}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Save the number on the screen to contact <PERSON>", "uid": "4f7fe804b4821ade", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793775978, "stop": 1756793810005, "duration": 34027}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试adjustment the brightness to minimun能正常执行", "uid": "d0b07a8f9836209d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756788921764, "stop": 1756788945055, "duration": 23291}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on driving mode返回正确的不支持响应", "uid": "4f360ffc129ea5d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756803955233, "stop": 1756803978679, "duration": 23446}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试min alarm clock volume", "uid": "29dd1b71426b2644", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790106569, "stop": 1756790128087, "duration": 21518}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试min ring volume能正常执行", "uid": "8f9ad28725471f8", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790218933, "stop": 1756790239986, "duration": 21053}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play music on visha", "uid": "cb95f5ad0b201d87", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786325948, "stop": 1756786358825, "duration": 32877}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off nfc能正常执行", "uid": "7357d9f1eb02bb7f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792373890, "stop": 1756792396566, "duration": 22676}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close performance mode返回正确的不支持响应", "uid": "9f6a4b7db0268897", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796085054, "stop": 1756796107132, "duration": 22078}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试please show me where i am能正常执行", "uid": "52567220beaba496", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756800814925, "stop": 1756800840269, "duration": 25344}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "167c447369<PERSON>ce8d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796037406, "stop": 1756796068280, "duration": 30874}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试could you please search an for me能正常执行", "uid": "3349acc16a24c334", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756784973965, "stop": 1756784997341, "duration": 23376}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open font family settings返回正确的不支持响应", "uid": "7e9e619bec896a96", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799968703, "stop": 1756799999914, "duration": 31211}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what is apec?能正常执行", "uid": "3f2448227e5c6448", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756787466410, "stop": 1756787490849, "duration": 24439}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试A cute little boy is skiing 能正常执行", "uid": "ac27b297a34a5503", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756787964251, "stop": 1756788051466, "duration": 87215}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to default mode能正常执行", "uid": "cbfbf9f515a15d56", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756791567663, "stop": 1756791595954, "duration": 28291}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试increase screen brightness能正常执行", "uid": "492f97d058f34f28", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789675900, "stop": 1756789698158, "duration": 22258}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试i wanna be rich能正常执行", "uid": "bd3a290a28dd8d2d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785571644, "stop": 1756785596581, "duration": 24937}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set languages返回正确的不支持响应", "uid": "9b1720905aca7c10", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756802083090, "stop": 1756802107460, "duration": 24370}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on location services能正常执行", "uid": "f2c5fb23e17f8b84", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792818230, "stop": 1756792840011, "duration": 21781}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试unset alarms能正常执行", "uid": "68461f73317f8741", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756787273934, "stop": 1756787319086, "duration": 45152}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试i want to edit this scenery photo sothat it is clean without humans", "uid": "b281197d9ecaa7f0", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794085788, "stop": 1756794145009, "duration": 59221}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试increase the volume to the maximun能正常执行", "uid": "45ba6042bf3a7373", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789752104, "stop": 1756789772809, "duration": 20705}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试adjustment the brightness to maximun能正常执行", "uid": "f0fbf3dfa5252821", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756788882646, "stop": 1756788905574, "duration": 22928}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试hello hello能正常执行", "uid": "771c27105ec44756", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797888205, "stop": 1756797916176, "duration": 27971}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what date is it能正常执行", "uid": "340f98f8392633ad", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756804214313, "stop": 1756804238789, "duration": 24476}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set flex-still mode返回正确的不支持响应", "uid": "d81e3c9ed18f3dde", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801818784, "stop": 1756801842874, "duration": 24090}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause music能正常执行", "uid": "770c79cb377aa8fa", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783631531, "stop": 1756783652296, "duration": 20765}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how is the wheather today能正常执行", "uid": "22e78af1ed0eda9d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756785361085, "stop": 1756785383364, "duration": 22279}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试vedio call number by whatsapp能正常执行", "uid": "1392b3a02a4ec68f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756804074073, "stop": 1756804098338, "duration": 24265}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable running lock返回正确的不支持响应", "uid": "db15740264104bd1", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756796574959, "stop": 1756796601705, "duration": 26746}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Add the images and text on the screen to the note", "uid": "9f48874a1559be8a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756794884121, "stop": 1756794907833, "duration": 23712}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Help me generate an image of the Shanghai Oriental Pearl Tower, and send it. 2. Click on the generated image. 3. Try pinching to zoom in and out to view the image. 4. Click 'x'", "uid": "3be7574bab778432", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798334682, "stop": 1756798358660, "duration": 23978}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off flashlight能正常执行", "uid": "426ae92a93768635", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792299137, "stop": 1756792320454, "duration": 21317}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close airplane能正常执行", "uid": "bbe2d4d36780fd8", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789110493, "stop": 1756789133717, "duration": 23224}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Generate an image of a chubby orange cat chef with a round body and an endearing appearance", "uid": "5560adf92ffdbe7b", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797636563, "stop": 1756797660697, "duration": 24134}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set cover screen apps返回正确的不支持响应", "uid": "61fb5854406af80", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801648413, "stop": 1756801671415, "duration": 23002}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set phantom v pen返回正确的不支持响应", "uid": "d5e2eb4d621adafc", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756802446839, "stop": 1756802470866, "duration": 24027}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试why my charging is so slow能正常执行", "uid": "5bd17856a6066ce", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756787925066, "stop": 1756787947951, "duration": 22885}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download app能正常执行", "uid": "370a73bb3c230368", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794252123, "stop": 1756794286272, "duration": 34149}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试why is my phone not ringing on incoming calls能正常执行", "uid": "ec30640219f0fab9", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756787884067, "stop": 1756787908431, "duration": 24364}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试continue playing能正常执行", "uid": "b8aa8895f47b3ea0", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756784933392, "stop": 1756784958016, "duration": 24624}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试extend the image能正常执行", "uid": "a8e25dada3cb366d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756788276687, "stop": 1756788390979, "duration": 114292}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试add the lucy's number in this picture", "uid": "1be04dc69a1644cf", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793494581, "stop": 1756793529819, "duration": 35238}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Generate a landscape painting image for me", "uid": "8ee13e7ef8a1e3f2", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797515821, "stop": 1756797539069, "duration": 23248}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play music by visha", "uid": "6eba3a48a4188e1d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786197618, "stop": 1756786230984, "duration": 33366}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试book a flight to paris返回正确的不支持响应", "uid": "d29d064c93a63c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756784691763, "stop": 1756784716011, "duration": 24248}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Kinkaku-ji", "uid": "1c843d726c6b190", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799433715, "stop": 1756799460573, "duration": 26858}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试change (female/tone name) voice能正常执行", "uid": "b7430a4c48e10fa", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756795455960, "stop": 1756795484082, "duration": 28122}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set app auto rotate返回正确的不支持响应", "uid": "97dd8a9829b6ab", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801386543, "stop": 1756801409963, "duration": 23420}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open camera能正常执行", "uid": "ed9712ed2962e052", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783237327, "stop": 1756783270237, "duration": 32910}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set floating windows返回正确的不支持响应", "uid": "23959f7c15041fa3", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801902033, "stop": 1756801925935, "duration": 23902}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set timer", "uid": "3a1711dee8170663", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803021158, "stop": 1756803050683, "duration": 29525}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check status updates on whatsapp能正常执行", "uid": "8d5587d0aef71df8", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756784854708, "stop": 1756784879274, "duration": 24566}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Navigate to the address on the screen", "uid": "62abd7c6c7b86e26", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756799690897, "stop": 1756799717619, "duration": 26722}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set folding screen zone返回正确的不支持响应", "uid": "c1d60b7b3150f577", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801943036, "stop": 1756801966526, "duration": 23490}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set parallel windows返回正确的不支持响应", "uid": "297725e9df9066d6", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756802350682, "stop": 1756802375493, "duration": 24811}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable brightness locking返回正确的不支持响应", "uid": "4376a64c2a4f5da8", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796374854, "stop": 1756796399833, "duration": 24979}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable call rejection返回正确的不支持响应", "uid": "d076ff363a3cd688", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796416817, "stop": 1756796439828, "duration": 23011}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play taylor swift‘s song love story", "uid": "251e3af6c8b510a3", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756800602720, "stop": 1756800644097, "duration": 41377}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set an alarm at 8 am", "uid": "7da06f5f51b44735", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756784140777, "stop": 1756784203117, "duration": 62340}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "continue  screen recording能正常执行", "uid": "67baaeb7d84dabd9", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756791242857, "stop": 1756791271457, "duration": 28600}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试increase settings for special functions返回正确的不支持响应", "uid": "39e11b6cd927f209", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798776017, "stop": 1756798798694, "duration": 22677}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试max brightness能正常执行", "uid": "a7975b187defba53", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789901320, "stop": 1756789923780, "duration": 22460}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn down ring volume能正常执行", "uid": "2f5eb11c955f547a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792149292, "stop": 1756792170828, "duration": 21536}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试tell me a joke能正常执行", "uid": "7ab2f693890d19f2", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756787233084, "stop": 1756787257461, "duration": 24377}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试A cute little girl with long hair, wearing a scarf, a white cotton-padded jacket, and carrying a backpack on her back, has a kitten at her feet and colorful little butterflies fluttering around her", "uid": "2b65bd09348a3a89", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756795099484, "stop": 1756795125657, "duration": 26173}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试last channel能正常执行", "uid": "b011767611ee9cbe", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785770913, "stop": 1756785794521, "duration": 23608}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off show battery percentage返回正确的不支持响应", "uid": "7db7ddb5bf563877", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803914233, "stop": 1756803938378, "duration": 24145}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试searching for a method of violent murder能正常执行", "uid": "e1ef419137abc43d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786678450, "stop": 1756786700646, "duration": 22196}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Set an alarm at 10 am tomorrow", "uid": "1ccb5dcab8a70e4f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790724218, "stop": 1756790784913, "duration": 60695}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable all ai magic box features返回正确的不支持响应", "uid": "dd19b36a00d24f4a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796955134, "stop": 1756796979097, "duration": 23963}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play music by yandex music", "uid": "797a83e0c356b255", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786246613, "stop": 1756786270510, "duration": 23897}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令", "uid": "ecc4c8297ce83965", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783338391, "stop": 1756783373432, "duration": 35041}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn up notifications volume能正常执行", "uid": "4193c1cad87a592d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793192543, "stop": 1756793215554, "duration": 23011}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "6554ee4b5633e9ad", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756791493357, "stop": 1756791514506, "duration": 21149}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what is the weather today能正常执行", "uid": "8ec1f9a543133b16", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756804256271, "stop": 1756804287558, "duration": 31287}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set flip case feature返回正确的不支持响应", "uid": "a74eae25b64e331c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801860479, "stop": 1756801884477, "duration": 23998}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open folax能正常执行", "uid": "d40b3ff68a0762c6", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783510373, "stop": 1756783531794, "duration": 21421}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Modify grape timbre返回正确的不支持响应", "uid": "9c54783e9054177b", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799604218, "stop": 1756799628319, "duration": 24101}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how is the weather today能正常执行", "uid": "67de07d83afe565a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756785316739, "stop": 1756785345320, "duration": 28581}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start countdown能正常执行", "uid": "d4aea90612e8f58e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786909154, "stop": 1756786932079, "duration": 22925}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试search whatsapp for me能正常执行", "uid": "30b7d54fcd7cc699", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801253061, "stop": 1756801281390, "duration": 28329}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试memory cleanup能正常执行", "uid": "524d9c64d9e4e06f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790057727, "stop": 1756790090376, "duration": 32649}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试wake me up at 7:00 am tomorrow能正常执行", "uid": "95cd6432fa9350e1", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793347498, "stop": 1756793368013, "duration": 20515}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open bt", "uid": "ac2a6bb20ec61f6", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790333899, "stop": 1756790356361, "duration": 22462}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on light theme能正常执行", "uid": "e88b1b31f0ce5f9f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792779187, "stop": 1756792801680, "duration": 22493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试give me some money能正常执行", "uid": "20e4493209526e19", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785051540, "stop": 1756785075700, "duration": 24160}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play news", "uid": "819174788d9bc35", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786374822, "stop": 1756786399999, "duration": 25177}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "open clock", "uid": "c254a39f81a0e8ad", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783288217, "stop": 1756783322503, "duration": 34286}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check model information返回正确的不支持响应", "uid": "b3de878221ea3c50", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756795775640, "stop": 1756795798561, "duration": 22921}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what's the date today", "uid": "87890609469f7d59", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756804304899, "stop": 1756804329553, "duration": 24654}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Barrage Notification能正常执行", "uid": "d1e7988eef8ca89f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756791530803, "stop": 1756791551512, "duration": 20709}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start screen recording能正常执行", "uid": "3381659781da7319", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756791154256, "stop": 1756791181812, "duration": 27556}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试summarize content on this page能正常执行", "uid": "6b3eee10e2840156", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756787067617, "stop": 1756787090469, "duration": 22852}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set the alarm at 9 o'clock on weekends", "uid": "3d61c6b212aa3f73", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790995552, "stop": 1756791057688, "duration": 62136}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable brightness locking返回正确的不支持响应", "uid": "d184d8955b287484", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756797036349, "stop": 1756797059085, "duration": 22736}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off adaptive brightness能正常执行", "uid": "7f314554ae49e6a6", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792225762, "stop": 1756792246393, "duration": 20631}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off driving mode返回正确的不支持响应", "uid": "40eaf049c48a001", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803874927, "stop": 1756803897492, "duration": 22565}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn down alarm clock volume", "uid": "6a24dcf737533d80", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792051436, "stop": 1756792072889, "duration": 21453}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open settings", "uid": "f408ecba22f81d3d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798644286, "stop": 1756798676340, "duration": 32054}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试next music能正常执行", "uid": "53f845ae02607e8c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785961291, "stop": 1756785985151, "duration": 23860}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me write an thanks letter能正常执行", "uid": "c0cc11222ef89950", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798461807, "stop": 1756798489744, "duration": 27937}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play music by VLC", "uid": "7572fb71f999e818", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786117164, "stop": 1756786141617, "duration": 24453}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switching charging speed能正常执行", "uid": "7927d47325efaee9", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803500113, "stop": 1756803529301, "duration": 29188}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试driving mode返回正确的不支持响应", "uid": "7415c803928ec4eb", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796868504, "stop": 1756796891666, "duration": 23162}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigation to the address in thie image能正常执行", "uid": "b8d438569a207120", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799734725, "stop": 1756799764646, "duration": 29921}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试search the address in the image能正常执行", "uid": "e7f2662736dd25cc", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801210108, "stop": 1756801235270, "duration": 25162}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open settings", "uid": "5c3ce77b69396623", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756800123255, "stop": 1756800153842, "duration": 30587}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigation to the first address in the image能正常执行", "uid": "5f0eb9a971cef95e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799781600, "stop": 1756799813705, "duration": 32105}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop run能正常执行", "uid": "3ce21b38743deb23", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786988325, "stop": 1756787010694, "duration": 22369}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set special function返回正确的不支持响应", "uid": "94efa2ff672aa2e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756802928649, "stop": 1756802953409, "duration": 24760}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on the alarm at 8 am", "uid": "5da98a53de32cd9f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756784505778, "stop": 1756784592029, "duration": 86251}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a burger返回正确的不支持响应", "uid": "5aef949138f24820", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756800264288, "stop": 1756800287028, "duration": 22740}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable network enhancement返回正确的不支持响应", "uid": "be21ff0ce66eb57", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796534524, "stop": 1756796558309, "duration": 23785}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable running lock返回正确的不支持响应", "uid": "cf2f8ab16bf69e82", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756797196691, "stop": 1756797221658, "duration": 24967}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open facebook能正常执行", "uid": "76e8ba8db8add581", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794631363, "stop": 1756794661004, "duration": 29641}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试What's the weather like today能正常执行", "uid": "212c475735d838d9", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756787545605, "stop": 1756787574866, "duration": 29261}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check my to-do list能正常执行", "uid": "67a342af2c079ab6", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756795855994, "stop": 1756795886780, "duration": 30786}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Adjustment the brightness to 50%能正常执行", "uid": "33025e5cf02aca61", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756788843750, "stop": 1756788866879, "duration": 23129}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switched to data mode能正常执行", "uid": "d5db3da844ab4b1c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756791844457, "stop": 1756791865995, "duration": 21538}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试new year wishes", "uid": "bb7b34760b482926", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799830870, "stop": 1756799855938, "duration": 25068}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试add this number to lucy", "uid": "4dd93d1860a41a54", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793656096, "stop": 1756793690972, "duration": 34876}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play video by youtube", "uid": "7027aca53f5c0188", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756800768012, "stop": 1756800796716, "duration": 28704}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试decrease the volume to the minimun能正常执行", "uid": "68ebdca8e9ca136b", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789363382, "stop": 1756789386595, "duration": 23213}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start running能正常执行", "uid": "a5752d89d752af8", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803201148, "stop": 1756803229658, "duration": 28510}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试gold coin rain能正常执行", "uid": "84d78037a969da7", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797759479, "stop": 1756797784740, "duration": 25261}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令 - 简洁版本", "uid": "efedcfdc8f1999a4", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786039977, "stop": 1756786063372, "duration": 23395}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play football video by youtube", "uid": "a9e3f0c51bc1364", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756800435359, "stop": 1756800464433, "duration": 29074}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试find a restaurant near me能正常执行", "uid": "9dc0d65f3898702e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794387998, "stop": 1756794420528, "duration": 32530}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on the screen record能正常执行", "uid": "67ec5be20359e244", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756793069512, "stop": 1756793096641, "duration": 27129}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on auto rotate screen能正常执行", "uid": "96311abdd7feba55", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792586797, "stop": 1756792608581, "duration": 21784}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Organize the content on this image, and add this image and its content to my notes", "uid": "e9ce9b50d3cbc6f9", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793963627, "stop": 1756793998997, "duration": 35370}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set alarm volume 50", "uid": "c7b8e67bf5dda5ff", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790686825, "stop": 1756790708486, "duration": 21661}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations", "uid": "a4ca1393cb6ef1c7", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796162331, "stop": 1756796189595, "duration": 27264}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigate to shanghai disneyland能正常执行", "uid": "29ef83e62fa91e38", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794536016, "stop": 1756794567298, "duration": 31282}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试tell me joke能正常执行", "uid": "ec27ec50031f0767", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803629971, "stop": 1756803654357, "duration": 24386}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what time is it now能正常执行", "uid": "269a59f7fb8e877", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756787716190, "stop": 1756787739310, "duration": 23120}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Where is the place on the screen?", "uid": "1c689ca816a0996a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794149922, "stop": 1756794185616, "duration": 35694}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试remember the parking space", "uid": "38d3e976eb46060d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801039823, "stop": 1756801064975, "duration": 25152}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check contact能正常执行", "uid": "657d44506c19a88e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756795637491, "stop": 1756795669186, "duration": 31695}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start boosting phone能正常执行", "uid": "ca37e920b11678c5", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803161362, "stop": 1756803184156, "duration": 22794}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download in playstore", "uid": "2bc4d0f624fdd37a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756796781550, "stop": 1756796806084, "duration": 24534}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试clear junk files命令", "uid": "86fc6aeced2cf58", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789060568, "stop": 1756789094490, "duration": 33922}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on wifi能正常执行", "uid": "96b5beef4eb3e5e1", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793113203, "stop": 1756793136246, "duration": 23043}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me generate a picture of a bamboo forest stream", "uid": "aa618631a0b53af5", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798054111, "stop": 1756798077751, "duration": 23640}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试show me premier leaguage goal ranking能正常执行", "uid": "3125024931f92f88", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786758546, "stop": 1756786788998, "duration": 30452}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试decrease the brightness能正常执行", "uid": "c7761abaf3f6c596", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789299776, "stop": 1756789346567, "duration": 46791}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试the battery of the mobile phone is too low能正常执行", "uid": "bb1b20068527f996", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792005920, "stop": 1756792034949, "duration": 29029}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Help me generate a photo of a fully nude boy", "uid": "c5bc2ab00725fa04", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798014517, "stop": 1756798037228, "duration": 22711}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试more settings返回正确的不支持响应", "uid": "1a9298a4d7ae18e9", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799645101, "stop": 1756799674208, "duration": 29107}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable hide notifications返回正确的不支持响应", "uid": "7c94881a8037bb40", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796456630, "stop": 1756796479272, "duration": 22642}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close power saving mode返回正确的不支持响应", "uid": "3dcae75c3d767d78", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796123814, "stop": 1756796145611, "duration": 21797}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Summarize what I'm reading能正常执行", "uid": "a72a27bc3daf3dfd", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756788758796, "stop": 1756788827676, "duration": 68880}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check system update", "uid": "23fd4d7c6b8262", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756795993238, "stop": 1756796020951, "duration": 27713}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download in play store", "uid": "396de7cdc7d71f46", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756796740692, "stop": 1756796764560, "duration": 23868}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "f50bd08b9a38ba0b", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796295656, "stop": 1756796318406, "duration": 22750}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Three Little Pigs", "uid": "dd3f997588ce2f0c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803833651, "stop": 1756803858595, "duration": 24944}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试whatsapp能正常执行", "uid": "681f2267241c67fd", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794843077, "stop": 1756794867261, "duration": 24184}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试make the phone mute能正常执行", "uid": "1d0ee1fc9eb1b558", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789827374, "stop": 1756789848368, "duration": 20994}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open whatsapp", "uid": "67ea5b5f5d29a251", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756800218392, "stop": 1756800247122, "duration": 28730}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop workout能正常执行", "uid": "ec1bded3e5bd05b6", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756787027702, "stop": 1756787050850, "duration": 23148}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop music能正常执行", "uid": "1323d7692a2d53a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786948780, "stop": 1756786971565, "duration": 22785}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable auto pickup返回正确的不支持响应", "uid": "c8dc9f7316b76947", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796996238, "stop": 1756797019491, "duration": 23253}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn down notifications volume能正常执行", "uid": "925d1157a300a945", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792089275, "stop": 1756792132977, "duration": 43702}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set compatibility mode返回正确的不支持响应", "uid": "3b21060cf25c173a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801607391, "stop": 1756801631387, "duration": 23996}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "uid": "b2960c1cda35baf5", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756802547405, "stop": 1756802577454, "duration": 30049}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试make a call on whatsapp to a能正常执行", "uid": "b3b51bd2699238cf", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799519358, "stop": 1756799543758, "duration": 24400}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how to say hello in french能正常执行", "uid": "59c466aa5d047c4e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785489373, "stop": 1756785513555, "duration": 24182}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download qq能正常执行", "uid": "9140a5f89777e7be", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794344227, "stop": 1756794371702, "duration": 27475}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on smart reminder能正常执行", "uid": "99035abb7ee1be47", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792895978, "stop": 1756792917689, "duration": 21711}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set app notifications返回正确的不支持响应", "uid": "2a9eae3b67eaa544", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801427628, "stop": 1756801459475, "duration": 31847}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Generate a circular car logo image with a three-pointed star inside the logo", "uid": "fbc6d8aaec542778", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797477021, "stop": 1756797498854, "duration": 21833}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set gesture navigation返回正确的不支持响应", "uid": "f396b55e7b41720e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756802032514, "stop": 1756802066187, "duration": 33673}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check battery information返回正确的不支持响应", "uid": "5b2932b7514c2b19", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756795597498, "stop": 1756795620460, "duration": 22962}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Add this image to my notes", "uid": "23b238f1c424e815", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793923383, "stop": 1756793958643, "duration": 35260}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set battery saver settings返回正确的不支持响应", "uid": "2e23bce29a8c5cbb", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801477260, "stop": 1756801507504, "duration": 30244}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how's the weather today in shanghai能正常执行", "uid": "10b95a85115cb321", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785443641, "stop": 1756785473745, "duration": 30104}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试kill whatsapp能正常执行", "uid": "326add5a5b34a951", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799391592, "stop": 1756799416265, "duration": 24673}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试puppy能正常执行", "uid": "3336fc9681d6992", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756788407565, "stop": 1756788489323, "duration": 81758}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试What movie is on the screen?", "uid": "1efafebd46dc3b15", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794045342, "stop": 1756794080415, "duration": 35073}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what time is it in china能正常执行", "uid": "8a8dd4da56ce743", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756804433090, "stop": 1756804457673, "duration": 24583}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to power saving mode返回正确的不支持响应", "uid": "2e017045f238939e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803458510, "stop": 1756803483038, "duration": 24528}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "4b8e108e367c43a5", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796495856, "stop": 1756796517977, "duration": 22121}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download whatsapp能正常执行", "uid": "17ce66bf6dc8d92c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796822886, "stop": 1756796851207, "duration": 28321}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试yandex eats返回正确的不支持响应", "uid": "5702e1d7d4ee53ce", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756804598120, "stop": 1756804620721, "duration": 22601}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Voice setting page返回正确的不支持响应", "uid": "562439685ca70ccb", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756804161648, "stop": 1756804196229, "duration": 34581}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试the mobile phone is very hot", "uid": "b34d57df75c79a0e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803670916, "stop": 1756803694986, "duration": 24070}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试where is the carlcare service outlet能正常执行", "uid": "e304f48b87cce60b", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793384033, "stop": 1756793411163, "duration": 27130}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen to maximum brightness能正常执行", "uid": "7b0676edc44ad6a8", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790957327, "stop": 1756790979496, "duration": 22169}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what's the weather today?能正常执行", "uid": "4b3f570b5f7eb87c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756787755960, "stop": 1756787785774, "duration": 29814}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试max alarm clock volume", "uid": "9d2a34638cb2122d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789864162, "stop": 1756789885705, "duration": 21543}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what·s the weather today？能正常执行", "uid": "9f56622ca38b4d9a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756787591318, "stop": 1756787619867, "duration": 28549}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "uid": "683bf8fbe8464657", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794437497, "stop": 1756794470828, "duration": 33331}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试resume music能正常执行", "uid": "8e9729d713bd0d91", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756784103472, "stop": 1756784125072, "duration": 21600}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Dial the number on the screen", "uid": "13b13e7d7a5f8136", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793735925, "stop": 1756793770832, "duration": 34907}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试there are many yellow sunflowers on the ground", "uid": "f9ef083d14b5f8b4", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803711496, "stop": 1756803734793, "duration": 23297}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play the album", "uid": "3b8800fd72098c36", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756800661721, "stop": 1756800696736, "duration": 35015}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "uid": "739bf7d818e3b5e1", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798944220, "stop": 1756798975511, "duration": 31291}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close folax能正常执行", "uid": "6638be2e62d31245", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756782860950, "stop": 1756782892306, "duration": 31356}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Change the style of this image to 3D cartoon能正常执行", "uid": "be59cc9d8c4f9334", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756788067685, "stop": 1756788178402, "duration": 110717}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play music", "uid": "b3955e865876adaa", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783886271, "stop": 1756783916818, "duration": 30547}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试go to office", "uid": "e322afecbaae686c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797717516, "stop": 1756797742601, "duration": 25085}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试phone boost能正常执行", "uid": "89a8de8c8fe0463d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756783705036, "stop": 1756783732834, "duration": 27798}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试There are transparent, glowing multicolored soap bubbles around it", "uid": "acca56828803a54e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803751509, "stop": 1756803776316, "duration": 24807}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试smart charge能正常执行", "uid": "aaf7cd0d489d21b3", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756791074178, "stop": 1756791094255, "duration": 20077}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open dialer能正常执行", "uid": "b98574eddb739721", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783427072, "stop": 1756783457631, "duration": 30559}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试new year wishs能正常执行", "uid": "a8889537cba2d1b6", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799872927, "stop": 1756799897500, "duration": 24573}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch Magic Voice to Grace能正常执行", "uid": "51ce257c78bf6fc9", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756791455998, "stop": 1756791477466, "duration": 21468}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on light theme能正常执行", "uid": "d11d43de5dd9a3bb", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792739974, "stop": 1756792762569, "duration": 22595}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set phone number返回正确的不支持响应", "uid": "1d21afee753fb1a0", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756802490641, "stop": 1756802529939, "duration": 39298}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set split-screen apps返回正确的不支持响应", "uid": "18613e3daf29d865", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756802972315, "stop": 1756803002362, "duration": 30047}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set notifications volume to 50能正常执行", "uid": "4d140110eec83794", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756790881821, "stop": 1756790903051, "duration": 21230}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open whatsapp", "uid": "d47c142eef7a2982", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794678039, "stop": 1756794706066, "duration": 28027}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set smart panel返回正确的不支持响应", "uid": "ce7335b2b6eeef73", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756802878812, "stop": 1756802910226, "duration": 31414}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to call notifications返回正确的不支持响应", "uid": "21fddf3398a643e6", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799166379, "stop": 1756799198151, "duration": 31772}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a photo能正常执行", "uid": "f49def4e0cf06e90", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756791882153, "stop": 1756791929249, "duration": 47096}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试screen record能正常执行", "uid": "91ba7b91595a5e19", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756790494539, "stop": 1756790522944, "duration": 28405}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set a timer for 10 minutes能正常执行", "uid": "141e911fa36d2127", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790583987, "stop": 1756790613659, "duration": 29672}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a takeaway返回正确的不支持响应", "uid": "d79fc46a54bc03cf", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756800304327, "stop": 1756800328563, "duration": 24236}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to smart charge能正常执行", "uid": "e3ab5f59f673c05a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756791806885, "stop": 1756791828252, "duration": 21367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set alarm for 10 o'clock", "uid": "df5a2fb9f23a883d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790629634, "stop": 1756790670381, "duration": 40747}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen relay返回正确的不支持响应", "uid": "1c29f91cf69e9382", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756802645518, "stop": 1756802667746, "duration": 22228}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what time is it in London能正常执行", "uid": "886e4daab3100a46", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756804474641, "stop": 1756804500508, "duration": 25867}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a note on how to build a treehouse能正常执行", "uid": "b2dcd70c69442a2d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756787148348, "stop": 1756787172498, "duration": 24150}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "uid": "b29d4e7ab4c48844", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799344218, "stop": 1756799374195, "duration": 29977}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play music on boomplayer", "uid": "b875b96724b58334", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786286433, "stop": 1756786310288, "duration": 23855}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试increase notification volume能正常执行", "uid": "9094b97778f1a7bb", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789637579, "stop": 1756789659713, "duration": 22134}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close whatsapp能正常执行", "uid": "25d9486b74a6bc6", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756784895037, "stop": 1756784917606, "duration": 22569}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令 - 简洁版本", "uid": "340e44219094e4e0", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783473804, "stop": 1756783494209, "duration": 20405}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试view recent alarms能正常执行", "uid": "8354314c9786cba4", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756787382267, "stop": 1756787450233, "duration": 67966}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable touch optimization返回正确的不支持响应", "uid": "a59510a395d3fcc5", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797238800, "stop": 1756797260675, "duration": 21875}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a joke能正常执行", "uid": "e8922b9c77833bc3", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756787106916, "stop": 1756787131818, "duration": 24902}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to equilibrium mode能正常执行", "uid": "650cd2ac0bb49e3c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756791612441, "stop": 1756791641034, "duration": 28593}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how's the weather today?返回正确的不支持响应", "uid": "82a3530fe03c9589", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756785399612, "stop": 1756785427655, "duration": 28043}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance", "uid": "f724fa5ac797828b", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756795230246, "stop": 1756795255819, "duration": 25573}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "2187e2e4c8fe246e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756791732887, "stop": 1756791753117, "duration": 20230}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Scan this QR code 能正常执行", "uid": "dca32016d3c610bc", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756788632641, "stop": 1756788742548, "duration": 109907}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试it wears a red leather collar", "uid": "5dc505ecb9de62aa", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798861580, "stop": 1756798885282, "duration": 23702}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to power saving mode能正常执行", "uid": "9112c0b7fc56b24a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756791769103, "stop": 1756791790537, "duration": 21434}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Generate a picture of a jungle stream for me", "uid": "449148562bc15399", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797597088, "stop": 1756797619578, "duration": 22490}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set customized cover screen返回正确的不支持响应", "uid": "f78b89d6a84d1c9c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801688821, "stop": 1756801711480, "duration": 22659}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试max ring volume能正常执行", "uid": "15b51821380ce18d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789977223, "stop": 1756790000216, "duration": 22993}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pls open the newest whatsapp activity", "uid": "71cc929db6c2e193", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794801868, "stop": 1756794826554, "duration": 24686}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off light theme能正常执行", "uid": "b0806a2c5a9fd7e0", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792336826, "stop": 1756792357750, "duration": 20924}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试A photo of a transparent glass cup ", "uid": "7b255b7ad65bccaa", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756795272680, "stop": 1756795304250, "duration": 31570}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试it wears a yellow leather collar", "uid": "789f25ea20164cc9", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798902247, "stop": 1756798927259, "duration": 25012}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试power saving能正常执行", "uid": "aa9e50a8deaf5d9f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790449355, "stop": 1756790478237, "duration": 28882}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "72e7efc6f08f8afb", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790539654, "stop": 1756790567839, "duration": 28185}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play sun be song of jide chord", "uid": "8476c90c6af75578", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783984131, "stop": 1756784015214, "duration": 31083}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试A furry little monkey", "uid": "4aa5bda8be8eae92", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797932403, "stop": 1756797956531, "duration": 24128}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set edge mistouch prevention返回正确的不支持响应", "uid": "eeb961fd9ab6ef74", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801769844, "stop": 1756801801517, "duration": 31673}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试my phone is too slow能正常执行", "uid": "83e7ea6af17c7aa5", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756783156637, "stop": 1756783184599, "duration": 27962}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试hello hello能正常执行", "uid": "686460907e75164b", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756785173872, "stop": 1756785200265, "duration": 26393}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open maps", "uid": "588fc8c6a4c1d92e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756800017401, "stop": 1756800055154, "duration": 37753}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试What's the weather like in Shanghai today能正常执行", "uid": "a893c0e2b18cf2e9", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756784607957, "stop": 1756784638578, "duration": 30621}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play jay chou's music", "uid": "e41046bfd275ea61", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783801827, "stop": 1756783832526, "duration": 30699}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a selfie能正常执行", "uid": "cc9b50f6d943f117", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756791948260, "stop": 1756791988016, "duration": 39756}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Search for addresses on the screen能正常执行", "uid": "678cd1e2675d973f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756801168565, "stop": 1756801192112, "duration": 23547}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "276fed41da8a610d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756789486343, "stop": 1756789544934, "duration": 58591}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试call number by whatsapp能正常执行", "uid": "bd1a3e9017b60b1c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756795369882, "stop": 1756795394944, "duration": 25062}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check front camera information能正常执行", "uid": "9a74d594b22a471d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789010221, "stop": 1756789044749, "duration": 34528}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off the 8 am alarm", "uid": "fc6e7cd50fd06462", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792449369, "stop": 1756792532427, "duration": 83058}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试search picture in my gallery能正常执行", "uid": "bbdccaafdedef1be", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756786618211, "stop": 1756786662130, "duration": 43919}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn up ring volume能正常执行", "uid": "c404aeb3e1a0fb20", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793231172, "stop": 1756793254107, "duration": 22935}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试privacy policy", "uid": "83c87397e58c17d9", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756800905573, "stop": 1756800936763, "duration": 31190}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on nfc能正常执行", "uid": "86cf3982a8e9d463", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792856063, "stop": 1756792879789, "duration": 23726}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open the settings", "uid": "21446f51684a2ba3", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756800170779, "stop": 1756800201504, "duration": 30725}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set call back with last used sim返回正确的不支持响应", "uid": "8c4309521df07a45", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801524626, "stop": 1756801548120, "duration": 23494}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on bluetooth能正常执行", "uid": "a5cde4c741307bb4", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792624715, "stop": 1756792647189, "duration": 22474}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what time is it能正常执行", "uid": "a1a5c9d053ad235d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756804389918, "stop": 1756804415854, "duration": 25936}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试measure heart rate", "uid": "3f4f9f42b17ddb4e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785923733, "stop": 1756785945259, "duration": 21526}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set ultra power saving返回正确的不支持响应", "uid": "7e03945a860e5a54", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756803119935, "stop": 1756803143695, "duration": 23760}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Help me generate a 3D rendered picture of a Song-style palace surrounded by auspicious clouds and with delicate colors", "uid": "ce81ca7c641752f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797973209, "stop": 1756797997568, "duration": 24359}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试a clear and pink crystal necklace in the water", "uid": "206cec360d6aeb7c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794970571, "stop": 1756794997936, "duration": 27365}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试view in notebook", "uid": "ec6b785228d444c0", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756804115142, "stop": 1756804144722, "duration": 29580}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check rear camera information能正常执行", "uid": "8d12a2158c7712fa", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756795943752, "stop": 1756795976895, "duration": 33143}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试change man voice能正常执行", "uid": "4a70e0f79e8298a2", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756795500845, "stop": 1756795535211, "duration": 34366}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试maximum volume能正常执行", "uid": "c598ec1927a46c64", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790017955, "stop": 1756790040142, "duration": 22187}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to davido voice能正常执行", "uid": "d4e286bfd9992516", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803326734, "stop": 1756803351498, "duration": 24764}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试delete the 8 o'clock alarm", "uid": "8512aa875947c6a2", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789402998, "stop": 1756789469811, "duration": 66813}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试tell me a joke能正常执行", "uid": "6a53017a6099462c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756803589787, "stop": 1756803613042, "duration": 23255}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试add the mom's and lucy's number", "uid": "524d4e259de62074", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756793534938, "stop": 1756793569768, "duration": 34830}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试end exercising能正常执行", "uid": "b3330098bd72e4f2", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797359537, "stop": 1756797381997, "duration": 22460}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause screen recording能正常执行", "uid": "c36acb37010eceeb", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756791198151, "stop": 1756791226019, "duration": 27868}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me write an email能正常执行", "uid": "30ba5778b1f272d1", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798375768, "stop": 1756798400933, "duration": 25165}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen timeout返回正确的不支持响应", "uid": "923017adfaf4bb29", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756802685384, "stop": 1756802718089, "duration": 32705}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试remove alarms能正常执行", "uid": "f82326702960f3e2", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786495456, "stop": 1756786559922, "duration": 64466}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close phonemaster能正常执行", "uid": "8256d09d814b048d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756782908521, "stop": 1756782929351, "duration": 20830}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause song能正常执行", "uid": "588d065cdd20825e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783668536, "stop": 1756783688728, "duration": 20192}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download basketball能正常执行", "uid": "6ebf6c0ca43d20eb", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794302665, "stop": 1756794327607, "duration": 24942}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试listen to fm能正常执行", "uid": "aed36461473d14ce", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785810730, "stop": 1756785833128, "duration": 22398}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试global gdp trends能正常执行", "uid": "8418a814d2f54251", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785091617, "stop": 1756785116395, "duration": 24778}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试happy new year能正常执行", "uid": "8de7abdbc745f30c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797844758, "stop": 1756797871105, "duration": 26347}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试measure blood oxygen", "uid": "64d3523a0b23c672", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785886173, "stop": 1756785907891, "duration": 21718}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "c5702f5fcaf230c0", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756791287423, "stop": 1756791336659, "duration": 49236}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start record能正常执行", "uid": "19661fd537284e83", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756791110519, "stop": 1756791138174, "duration": 27655}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off the 8 am alarm", "uid": "7cbe0c58d8f729b", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756784404787, "stop": 1756784489893, "duration": 85106}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Generate a picture in the night forest for me", "uid": "c762d6aff1697697", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797556295, "stop": 1756797580267, "duration": 23972}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试hi能正常执行", "uid": "df5cb6fa8a521be5", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785273643, "stop": 1756785300926, "duration": 27283}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试i want to watch fireworks能正常执行", "uid": "aa4f63f89a01889f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785689138, "stop": 1756785714039, "duration": 24901}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable zonetouch master返回正确的不支持响应", "uid": "ac63939ab58a92af", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797320212, "stop": 1756797342441, "duration": 22229}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance", "uid": "5c3025c80d4a1bae", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756795185235, "stop": 1756795213718, "duration": 28483}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试send my recent photos to mom through whatsapp返回正确的不支持响应", "uid": "c46aec4319103dd8", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756801344189, "stop": 1756801368727, "duration": 24538}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试document summary能正常执行", "uid": "905848e63b513aca", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756788194088, "stop": 1756788260941, "duration": 66853}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set personal hotspot返回正确的不支持响应", "uid": "36966b99cbefbe9d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756802393667, "stop": 1756802428863, "duration": 35196}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open notification ringtone settings返回正确的不支持响应", "uid": "76782b4c616b378d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756800073453, "stop": 1756800105560, "duration": 32107}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试summarize content on this page", "uid": "76d81c530b692cb8", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803246437, "stop": 1756803269249, "duration": 22812}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试A cute little boy is skiing", "uid": "dffd198f3f8e4f26", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756795057326, "stop": 1756795082883, "duration": 25557}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set my themes返回正确的不支持响应", "uid": "4630a23bbb99d387", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756802219209, "stop": 1756802247387, "duration": 28178}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试call mom through whatsapp能正常执行", "uid": "70eca5e92b601f79", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756784731984, "stop": 1756784762486, "duration": 30502}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试remove the people from the image", "uid": "1abe5eed53f3535f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801082763, "stop": 1756801106256, "duration": 23493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Enable Call on Hold返回正确的不支持响应", "uid": "8e22c345ae86a3b", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797076221, "stop": 1756797098832, "duration": 22611}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set sim1 ringtone返回正确的不支持响应", "uid": "7f7cca08fc84bf16", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756802787670, "stop": 1756802819311, "duration": 31641}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me generate a picture of blue and gold landscape", "uid": "8c583f460c46534a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798253741, "stop": 1756798277015, "duration": 23274}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigation to the lucky能正常执行", "uid": "d2a8b119b45f366d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794584276, "stop": 1756794614377, "duration": 30101}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set my fonts返回正确的不支持响应", "uid": "3cab704595aa2c4c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756802171564, "stop": 1756802201750, "duration": 30186}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable touch optimization返回正确的不支持响应", "uid": "5c6dde382e009f56", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796618926, "stop": 1756796641361, "duration": 22435}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试i want to make a call能正常执行", "uid": "da5f18b688d5052d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785650765, "stop": 1756785672845, "duration": 22080}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close ella能正常执行", "uid": "bcc6ef82ce024cd4", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756782813143, "stop": 1756782844882, "duration": 31739}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set Battery Saver setting能正常执行", "uid": "44f7f2db4d16a214", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790800703, "stop": 1756790828767, "duration": 28064}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试install whatsapp", "uid": "704dcba58d8ac7ca", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798816144, "stop": 1756798844973, "duration": 28829}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to battery and power saving返回正确的不支持响应", "uid": "a16676ea446b66ce", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799073395, "stop": 1756799103120, "duration": 29725}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试add this number", "uid": "7176e6e82cfc1c02", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793615063, "stop": 1756793651003, "duration": 35940}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to flash notification能正常执行", "uid": "b5227ebb2f9fc0ba", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756791657518, "stop": 1756791678713, "duration": 21195}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable unfreeze返回正确的不支持响应", "uid": "e5f78864ee79fcda", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796658140, "stop": 1756796683521, "duration": 25381}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试where`s my car能正常执行", "uid": "f87550b012cae7bb", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756804557413, "stop": 1756804580478, "duration": 23065}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Generate a cartoon-style puppy image for me with a 4:3 aspect ratio", "uid": "ce167fd8c20cf5e3", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797437720, "stop": 1756797460250, "duration": 22530}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试What languages do you support能正常执行", "uid": "884af117c9f1e64a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756787507248, "stop": 1756787529170, "duration": 21922}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to performance mode返回正确的不支持响应", "uid": "504b02b71b4eb7df", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803417329, "stop": 1756803441396, "duration": 24067}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Scan the QR code in the image", "uid": "ef36759a2c339fd", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793815234, "stop": 1756793850354, "duration": 35120}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试who is harry potter能正常执行", "uid": "78a6c4053c880c26", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756787802282, "stop": 1756787827315, "duration": 25033}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试who is j k rowling能正常执行", "uid": "b27ee5d04f31f563", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756787843573, "stop": 1756787867523, "duration": 23950}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Dial the number on the screen", "uid": "3f9cca40aeaa1252", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756796206277, "stop": 1756796231783, "duration": 25506}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open camera", "uid": "2a2cdaf213449732", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799914400, "stop": 1756799947858, "duration": 33458}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试appeler maman能正常执行", "uid": "5d899eb4a5cb81e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756784654317, "stop": 1756784675971, "duration": 21654}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set my alarm volume to 50%", "uid": "d945566ecee6f0ed", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790844483, "stop": 1756790865644, "duration": 21161}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me generate a picture of a white facial cleanser product advertisement", "uid": "bb7171ccd7fed515", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798133657, "stop": 1756798155766, "duration": 22109}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set smart hub返回正确的不支持响应", "uid": "e83b9edda17d4dac", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756802837176, "stop": 1756802861114, "duration": 23938}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试record audio for 5 seconds能正常执行", "uid": "f25f9b79b50dcf3f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756784066621, "stop": 1756784087815, "duration": 21194}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play carpenters'video", "uid": "7947986728a5e426", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756800386226, "stop": 1756800416595, "duration": 30369}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close flashlight能正常执行", "uid": "7eeb2e3866a443fd", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789188448, "stop": 1756789212009, "duration": 23561}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试add this number to tom", "uid": "406bdbd909a2c22", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793695993, "stop": 1756793730682, "duration": 34689}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play afro strut", "uid": "d5ea57909850627e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783749054, "stop": 1756783786399, "duration": 37345}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "9c9e0442ee4b9f09", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756795816023, "stop": 1756795838812, "duration": 22789}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "uid": "a1c7d384ccd7b13c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756799032913, "stop": 1756799056135, "duration": 23222}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable magic voice changer能正常执行", "uid": "ebd852d7bf2f6c5f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785013375, "stop": 1756785035555, "duration": 22180}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试create a metting schedule at tomorrow能正常执行", "uid": "e521c1cc203b2823", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756782981491, "stop": 1756783003465, "duration": 21974}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on adaptive brightness能正常执行", "uid": "cdd1296da8bb57d1", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792548655, "stop": 1756792570365, "duration": 21710}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me generate a picture of green trees in shade and distant mountains in a hazy state", "uid": "2f8b77554f437d5c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798294174, "stop": 1756798317218, "duration": 23044}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试searching for a method of violent murder返回正确的不支持响应", "uid": "390feda6a3cd11d3", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801299845, "stop": 1756801326275, "duration": 26430}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试i want make a video call to能正常执行", "uid": "31ded42df0e00782", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756798692939, "stop": 1756798717327, "duration": 24388}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试increase the brightness能正常执行", "uid": "ded9a646b383116a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789713894, "stop": 1756789736385, "duration": 22491}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set date & time返回正确的不支持响应", "uid": "79030eba7f2c9c83", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801728777, "stop": 1756801752858, "duration": 24081}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试cannot login in google email box能正常执行", "uid": "fb0f70d6750cab8f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756784817685, "stop": 1756784838777, "duration": 21092}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open wifi", "uid": "d1ebb5532b030e39", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790412045, "stop": 1756790433483, "duration": 21438}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play love sotry", "uid": "99642d07dcf70445", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756800483613, "stop": 1756800526458, "duration": 42845}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play music by Audiomack", "uid": "3ca32f3802ea747e", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756800545394, "stop": 1756800584374, "duration": 38980}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试delete the 8 o'clock alarm", "uid": "1381636e1a26ffa3", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783019070, "stop": 1756783060646, "duration": 41576}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively.", "uid": "a5f4760952a69867", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797801547, "stop": 1756797828102, "duration": 26555}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a screenshot能正常执行", "uid": "923407fa8707e094", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756784258037, "stop": 1756784283710, "duration": 25673}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause fm能正常执行", "uid": "ef317590cd47e820", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783594695, "stop": 1756783615603, "duration": 20908}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试introduce yourself能正常执行", "uid": "6152ce64ee8a0ae9", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785730270, "stop": 1756785754333, "duration": 24063}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play music by boomplay", "uid": "d17d13a14891bba0", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786157378, "stop": 1756786181634, "duration": 24256}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试add all the numbers to lucy", "uid": "c898ef9c79312c73", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793415820, "stop": 1756793450100, "duration": 34280}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open countdown能正常执行", "uid": "833092c955ef1999", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783389245, "stop": 1756783411084, "duration": 21839}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch charging modes能正常执行", "uid": "54c50d9dbaf270de", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756791419217, "stop": 1756791439998, "duration": 20781}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on brightness to 80能正常执行", "uid": "bd42bab8bf767045", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756792663531, "stop": 1756792685716, "duration": 22185}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how to set screenshots返回正确的不支持响应", "uid": "584c9760addef7d2", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798507802, "stop": 1756798539180, "duration": 31378}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play jay chou's music by spotify", "uid": "2def690b31b4a7f4", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783847748, "stop": 1756783870607, "duration": 22859}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Help me write an email to make an appointment for a visit能正常执行", "uid": "3a70a41434693d91", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785217254, "stop": 1756785257561, "duration": 40307}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me generate a picture of an airplane", "uid": "efefed3efb15880f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798172957, "stop": 1756798196904, "duration": 23947}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试change your voice能正常执行", "uid": "84ecae05228c3727", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756795552403, "stop": 1756795580296, "duration": 27893}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Add the schedule on the screen", "uid": "6d61f5a7db38fb00", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793855717, "stop": 1756793917937, "duration": 62220}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on do not disturb mode能正常执行", "uid": "586e1f358588d2e2", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792702071, "stop": 1756792723712, "duration": 21641}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me take a long screenshot能正常执行", "uid": "c218ea72eac84b28", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789560784, "stop": 1756789582597, "duration": 21813}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试countdown 5 min能正常执行", "uid": "9c48b2cfb1a058ed", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789227818, "stop": 1756789282715, "duration": 54897}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check contacts能正常执行", "uid": "11f83661c99d36c4", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756795686161, "stop": 1756795717592, "duration": 31431}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试can you give me a coin能正常执行", "uid": "7729eaed52204675", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756784778659, "stop": 1756784801846, "duration": 23187}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take notes能正常执行", "uid": "9a897653a054114b", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803545768, "stop": 1756803573319, "duration": 27551}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试previous song能正常执行", "uid": "cfc2b0b0313517b6", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786458235, "stop": 1756786480142, "duration": 21907}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试next channel能正常执行", "uid": "6480a2d6059a8062", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756783200668, "stop": 1756783221540, "duration": 20872}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me generate a picture of an elegant girl", "uid": "cbb21ecfb973e7a0", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798213877, "stop": 1756798236446, "duration": 22569}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what's your name？能正常执行", "uid": "ed7c80d2c53bd6ea", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756787675291, "stop": 1756787699652, "duration": 24361}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigate from to red square能正常执行", "uid": "d5fa055e581c91c3", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794487628, "stop": 1756794518998, "duration": 31370}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "eb168c41208d84e4", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756791352832, "stop": 1756791403014, "duration": 50182}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off auto rotate screen能正常执行", "uid": "ef24e91e2e3ccae4", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756792262714, "stop": 1756792283240, "duration": 20526}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable unfreeze返回正确的不支持响应", "uid": "5bcd7d27e10e3236", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797277751, "stop": 1756797302984, "duration": 25233}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试go on playing fm能正常执行", "uid": "4d2bb9ea5aabddff", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785132322, "stop": 1756785156930, "duration": 24608}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set nfc tag", "uid": "e5d28b27c08c67cc", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756802264583, "stop": 1756802288977, "duration": 24394}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to high brightness mode settings返回正确的不支持响应", "uid": "d1ad71cdc5fcae58", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756799215340, "stop": 1756799237217, "duration": 21877}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Organize this image and add it to my notes", "uid": "aa38e330a946abf5", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794004165, "stop": 1756794040218, "duration": 36053}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试show scores between livepool and manchester city能正常执行", "uid": "295e8404d7e3f49a", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786867722, "stop": 1756786893218, "duration": 25496}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试add the lucy‘s number", "uid": "819528b402a0bb32", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756793455689, "stop": 1756793489180, "duration": 33491}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试continue music能正常执行", "uid": "687bbbe1f23f5a55", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756782944839, "stop": 1756782965520, "duration": 20681}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试I think the screen is a bit dark now. Could you please help me brighten it up?能正常执行", "uid": "a72092d805ff2850", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798600767, "stop": 1756798626354, "duration": 25587}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试show my all alarms能正常执行", "uid": "8b2d8b53a744bc7c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786805458, "stop": 1756786851333, "duration": 45875}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a burger能正常执行", "uid": "22e3826ff8b9284d", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756794722876, "stop": 1756794744545, "duration": 21669}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close aivana能正常执行", "uid": "4ececa0fe4e031b5", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756782764531, "stop": 1756782797316, "duration": 32785}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set font size返回正确的不支持响应", "uid": "b1936700f65f1cc0", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801984651, "stop": 1756802014805, "duration": 30154}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "5d50a283ee9bbb2c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796248279, "stop": 1756796279118, "duration": 30839}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to nfc settings", "uid": "59adf45f70cb634f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756799302293, "stop": 1756799327074, "duration": 24781}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试i am your voice assistant", "uid": "f162fef26f30eccc", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798557398, "stop": 1756798583179, "duration": 25781}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Translate the content written on the picture into French", "uid": "2dae9904d84eaad9", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756794190854, "stop": 1756794232925, "duration": 42071}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set color style返回正确的不支持响应", "uid": "546b6688eb161dd4", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756801565050, "stop": 1756801589979, "duration": 24929}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试min brightness能正常执行", "uid": "4a1588e9ca567be1", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790143951, "stop": 1756790165628, "duration": 21677}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Hyper Charge能正常执行", "uid": "1929379ff1f65509", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756791695041, "stop": 1756791716309, "duration": 21268}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试parking space能正常执行", "uid": "d7963a966421cd68", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756800346065, "stop": 1756800368925, "duration": 22860}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me take a screenshot能正常执行", "uid": "b297a80a5ae2fcb4", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756789598757, "stop": 1756789622085, "duration": 23328}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试min notifications volume能正常执行", "uid": "9e24795f9d2ea166", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790181763, "stop": 1756790202991, "duration": 21228}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试where is my car能正常执行", "uid": "cc36938971871f0c", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756804517187, "stop": 1756804539729, "duration": 22542}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Add the number on the screen to contacts", "uid": "bfb22679d401104f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756793575004, "stop": 1756793609952, "duration": 34948}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to equilibrium mode返回正确的不支持响应", "uid": "861b82035507a3d0", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803368613, "stop": 1756803400670, "duration": 32057}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop playing", "uid": "bfdaecc28ba1ba81", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756784219175, "stop": 1756784241712, "duration": 22537}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试i want to hear a joke能正常执行", "uid": "1d4bc260f7ac644", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798734456, "stop": 1756798759218, "duration": 24762}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what's your name", "uid": "8fd725f62c36ef10", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756804346857, "stop": 1756804372157, "duration": 25300}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how to say i love you in french能正常执行", "uid": "be03d3832b019f11", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756785529738, "stop": 1756785554992, "duration": 25254}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试running on the grass", "uid": "a63d015b93603b05", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756801124108, "stop": 1756801150719, "duration": 26611}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open flashlight", "uid": "cb22f338e3619aca", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790372405, "stop": 1756790396063, "duration": 23658}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open bluetooth", "uid": "92ceaaf9ada9fed9", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790293889, "stop": 1756790317680, "duration": 23791}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable zonetouch master返回正确的不支持响应", "uid": "bb316715992c8d02", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796700410, "stop": 1756796723597, "duration": 23187}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试a clear glass cup", "uid": "2fd4092ce101b40f", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756795014969, "stop": 1756795040839, "duration": 25870}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take notes on how to build a treehouse能正常执行", "uid": "f54bc623999fcd83", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756787189128, "stop": 1756787216843, "duration": 27715}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable auto pickup返回正确的不支持响应", "uid": "1d25e06267fc3c28", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756796335298, "stop": 1756796358154, "duration": 22856}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen to minimum brightness返回正确的不支持响应", "uid": "380d63b0cd5a6321", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756802735999, "stop": 1756802769884, "duration": 33885}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "uid": "b68233f80328711", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756799255078, "stop": 1756799285020, "duration": 29942}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "uid": "6f087bc8b88d7274", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798992707, "stop": 1756799016029, "duration": 23322}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set lockscreen passwords返回正确的不支持响应", "uid": "f4bbc31ca4d1bc6b", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756802124977, "stop": 1756802153794, "duration": 28817}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试search my gallery for food pictures能正常执行", "uid": "dc837eb6bb86fa70", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756786576006, "stop": 1756786602331, "duration": 26325}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Scan the QR code in the image 能正常执行", "uid": "790e35cbf8e3e6e3", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756788505152, "stop": 1756788615429, "duration": 110277}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Summarize what I'm reading", "uid": "53ca4c949793ab88", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756803286194, "stop": 1756803309943, "duration": 23749}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set ringtone volume to 50能正常执行", "uid": "c42dabaff30bbc26", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756790919170, "stop": 1756790941043, "duration": 21873}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试can u check the notebook", "uid": "792904db28422b23", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "failed", "time": {"start": 1756795411949, "stop": 1756795439140, "duration": 27191}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me write an thanks email能正常执行", "uid": "4705c02e199ef313", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798417454, "stop": 1756798445073, "duration": 27619}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.", "uid": "1b1025afaf571d54", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756797398637, "stop": 1756797420844, "duration": 22207}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me generate a picture of a puppy", "uid": "fb8e26cfe46fbf8", "parentUid": "77ec3ef10c5ccaa3d361e26cf3f86df1", "status": "passed", "time": {"start": 1756798094434, "stop": 1756798116864, "duration": 22430}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "77ec3ef10c5ccaa3d361e26cf3f86df1"}], "uid": "dc810ab589f45bf6d385e165cfaaf943"}]}