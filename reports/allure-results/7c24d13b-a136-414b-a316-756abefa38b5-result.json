{"name": "测试how is the wheather today能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the wheather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.dialogue.test_how_is_the_wheather_today.TestEllaHowIsWheatherToday object at 0x0000018BC4CD9450>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6B648B0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_how_is_the_wheather_today(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the wheather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_how_is_the_wheather_today.py:36: AssertionError"}, "description": "how is the wheather today", "steps": [{"name": "执行命令: how is the wheather today", "status": "passed", "steps": [{"name": "执行命令: how is the wheather today", "status": "passed", "start": 1756785361086, "stop": 1756785383136}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e7ccae26-d2e9-4df0-b238-f669d81bef93-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8ef4df4e-ed8d-4a41-8232-7479061b67fa-attachment.png", "type": "image/png"}], "start": 1756785383136, "stop": 1756785383358}], "start": 1756785361086, "stop": 1756785383359}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the wheather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\dialogue\\test_how_is_the_wheather_today.py\", line 36, in test_how_is_the_wheather_today\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756785383359, "stop": 1756785383362}], "attachments": [{"name": "stdout", "source": "9edcb8c8-c944-48ee-aa69-d76ade68c348-attachment.txt", "type": "text/plain"}], "start": 1756785361085, "stop": 1756785383364, "uuid": "7e519b32-c0a9-4efb-a422-0be6ddec4b4f", "historyId": "f7282303534c1c8599c3343608e6f453", "testCaseId": "f7282303534c1c8599c3343608e6f453", "fullName": "testcases.test_ella.dialogue.test_how_is_the_wheather_today.TestEllaHowIsWheatherToday#test_how_is_the_wheather_today", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_is_the_wheather_today"}, {"name": "subSuite", "value": "TestEllaHowIsWheatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_is_the_wheather_today"}]}