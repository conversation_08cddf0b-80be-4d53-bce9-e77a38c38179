{"name": "测试the battery of the mobile phone is too low能正常执行", "status": "passed", "description": "the battery of the mobile phone is too low", "steps": [{"name": "执行命令: the battery of the mobile phone is too low", "status": "passed", "steps": [{"name": "执行命令: the battery of the mobile phone is too low", "status": "passed", "start": 1756792005920, "stop": 1756792034484}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e1903c01-bea3-4d70-8af1-41669b6041db-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "bdfb5aaf-dffd-4c52-b581-f9cf33f819db-attachment.png", "type": "image/png"}], "start": 1756792034484, "stop": 1756792034741}], "start": 1756792005920, "stop": 1756792034741}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756792034741, "stop": 1756792034742}, {"name": "验证PhoneMaster应用已打开", "status": "passed", "start": 1756792034742, "stop": 1756792034742}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3cf47657-84d6-48e9-a1fa-1aac0479fdce-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4c22f4c3-ee13-4749-9eb1-3dd06caa06ce-attachment.png", "type": "image/png"}], "start": 1756792034743, "stop": 1756792034948}], "attachments": [{"name": "stdout", "source": "c3159a6d-96f6-4fe2-b160-062b70a915fc-attachment.txt", "type": "text/plain"}], "start": 1756792005920, "stop": 1756792034949, "uuid": "d052e238-caf8-4fef-9efc-77019c8e3d77", "historyId": "2ecac23eb3f511651fafc6ba6a3725f2", "testCaseId": "2ecac23eb3f511651fafc6ba6a3725f2", "fullName": "testcases.test_ella.system_coupling.test_the_battery_of_the_mobile_phone_is_too_low.TestEllaBatteryMobilePhoneIsTooLow#test_the_battery_of_the_mobile_phone_is_too_low", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_the_battery_of_the_mobile_phone_is_too_low"}, {"name": "subSuite", "value": "TestEllaBatteryMobilePhoneIsTooLow"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_the_battery_of_the_mobile_phone_is_too_low"}]}