{"name": "测试set timezone返回正确的不支持响应", "status": "passed", "description": "验证set timezone指令返回预期的不支持响应", "steps": [{"name": "执行命令: set timezone", "status": "passed", "steps": [{"name": "执行命令: set timezone", "status": "passed", "start": 1756803069221, "stop": 1756803101399}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "76d37d38-5a6e-437c-8f34-441d5ca6f20a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6e9367bc-92ef-4a46-95c3-60eacc68a0f4-attachment.png", "type": "image/png"}], "start": 1756803101399, "stop": 1756803101678}], "start": 1756803069221, "stop": 1756803101678}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756803101678, "stop": 1756803101682}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "17381c71-0099-4d1f-a051-33912bb38d96-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9caaeb6e-da20-422a-b94f-2783075174c5-attachment.png", "type": "image/png"}], "start": 1756803101682, "stop": 1756803101950}], "attachments": [{"name": "stdout", "source": "8abf3131-4d25-4de2-90b7-d428ffc43dbf-attachment.txt", "type": "text/plain"}], "start": 1756803069221, "stop": 1756803101951, "uuid": "ddb4071f-3d5c-4e51-80f4-ea5404ba0143", "historyId": "dbd7a7f96e1740fa05f50ed6fa7becfb", "testCaseId": "dbd7a7f96e1740fa05f50ed6fa7becfb", "fullName": "testcases.test_ella.unsupported_commands.test_set_timezone.TestEllaSetTimezone#test_set_timezone", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_timezone"}, {"name": "subSuite", "value": "TestEllaSetTimezone"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_timezone"}]}