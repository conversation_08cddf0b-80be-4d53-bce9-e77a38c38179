{"name": "测试switch to equilibrium mode返回正确的不支持响应", "status": "passed", "description": "验证switch to equilibrium mode指令返回预期的不支持响应", "steps": [{"name": "执行命令: switch to equilibrium mode", "status": "passed", "steps": [{"name": "执行命令: switch to equilibrium mode", "status": "passed", "start": 1756803368613, "stop": 1756803400239}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ca86f4d6-a926-49d2-9de9-8dda0a274a38-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "655e00ef-3edb-4152-8c2e-11c6e5cf81c4-attachment.png", "type": "image/png"}], "start": 1756803400239, "stop": 1756803400451}], "start": 1756803368613, "stop": 1756803400452}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756803400452, "stop": 1756803400454}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "febb1fe1-6648-4f6b-b1c9-9a51c164f4b4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c2e518e6-583e-4b0e-8fb4-6965c38d7095-attachment.png", "type": "image/png"}], "start": 1756803400454, "stop": 1756803400670}], "attachments": [{"name": "stdout", "source": "567df8e5-9567-4d8e-94f8-c84310526b65-attachment.txt", "type": "text/plain"}], "start": 1756803368613, "stop": 1756803400670, "uuid": "4750c3a8-5235-4ff4-999f-430d0324e966", "historyId": "8b6d374ba70006ef7591c8e0bf72bb00", "testCaseId": "8b6d374ba70006ef7591c8e0bf72bb00", "fullName": "testcases.test_ella.unsupported_commands.test_switch_to_equilibrium_mode.TestEllaSwitchEquilibriumMode#test_switch_to_equilibrium_mode", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_switch_to_equilibrium_mode"}, {"name": "subSuite", "value": "TestEllaSwitchEquilibriumMode"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_switch_to_equilibrium_mode"}]}