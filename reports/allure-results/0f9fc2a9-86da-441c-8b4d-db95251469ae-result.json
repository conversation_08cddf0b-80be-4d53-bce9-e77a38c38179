{"name": "测试pause song能正常执行", "status": "passed", "description": "pause song", "steps": [{"name": "执行命令: pause song", "status": "passed", "steps": [{"name": "执行命令: pause song", "status": "passed", "start": 1756783668536, "stop": 1756783688276}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "27002941-9242-4c18-b33c-60306386d4e1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "01fd8a73-f768-4452-acd9-47e22806d68e-attachment.png", "type": "image/png"}], "start": 1756783688276, "stop": 1756783688498}], "start": 1756783668536, "stop": 1756783688498}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756783688499, "stop": 1756783688500}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "33ec831d-bc48-462a-a402-c76d08de424c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "41b92573-68f7-4c01-84cf-6683d134a8cc-attachment.png", "type": "image/png"}], "start": 1756783688500, "stop": 1756783688727}], "attachments": [{"name": "stdout", "source": "6063a6fa-cfd0-43cc-9978-d0b0ec6b6c46-attachment.txt", "type": "text/plain"}], "start": 1756783668536, "stop": 1756783688728, "uuid": "230ba141-2f0d-4ac2-a32c-8b09899fe696", "historyId": "f09a8375806e200073a99f1cbabdc35c", "testCaseId": "f09a8375806e200073a99f1cbabdc35c", "fullName": "testcases.test_ella.component_coupling.test_pause_song.TestEllaPauseSong#test_pause_song", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_pause_song"}, {"name": "subSuite", "value": "TestEllaPauseSong"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_pause_song"}]}