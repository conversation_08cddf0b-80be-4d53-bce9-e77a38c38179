{"name": "测试where is my car能正常执行", "status": "passed", "description": "where is my car", "steps": [{"name": "执行命令: where is my car", "status": "passed", "steps": [{"name": "执行命令: where is my car", "status": "passed", "start": 1756804517187, "stop": 1756804539183}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b97324d4-60dd-4a35-90c7-49dd056f9eb4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2020d495-188a-41f9-83b2-de7c91eb036f-attachment.png", "type": "image/png"}], "start": 1756804539183, "stop": 1756804539478}], "start": 1756804517187, "stop": 1756804539479}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756804539479, "stop": 1756804539483}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d830f0d8-8b8f-408c-9656-4464b4da0968-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6661499f-9ede-4a30-b6cb-6128dfe92efa-attachment.png", "type": "image/png"}], "start": 1756804539483, "stop": 1756804539729}], "attachments": [{"name": "stdout", "source": "fe9d2e7f-5c22-47e1-8363-aeb18d8af648-attachment.txt", "type": "text/plain"}], "start": 1756804517187, "stop": 1756804539729, "uuid": "7e9d2655-9485-4c16-a489-77954495a86f", "historyId": "79ed3b173757e627534e24ad9289f338", "testCaseId": "79ed3b173757e627534e24ad9289f338", "fullName": "testcases.test_ella.unsupported_commands.test_where_is_my_car.TestEllaWhereIsMyCar#test_where_is_my_car", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_where_is_my_car"}, {"name": "subSuite", "value": "TestEllaWhereIsMyCar"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_where_is_my_car"}]}