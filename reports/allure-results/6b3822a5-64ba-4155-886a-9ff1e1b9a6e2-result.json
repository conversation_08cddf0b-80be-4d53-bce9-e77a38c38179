{"name": "测试turn on brightness to 80能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=48, 最终=88, 响应='['turn on brightness to 80', 'Brightness is at 80% now.', '', '', '', '', '', '', '', '', '']'\nassert 88 == 171", "trace": "self = <testcases.test_ella.system_coupling.test_turn_on_brightness_to_80.TestEllaTurnBrightness object at 0x0000018BC6235A50>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC7CCB220>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_turn_on_brightness_to(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证应用已打开\"):\n>           assert  final_status==171, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=48, 最终=88, 响应='['turn on brightness to 80', 'Brightness is at 80% now.', '', '', '', '', '', '', '', '', '']'\nE           assert 88 == 171\n\ntestcases\\test_ella\\system_coupling\\test_turn_on_brightness_to_80.py:36: AssertionError"}, "description": "turn on brightness to 80", "steps": [{"name": "执行命令: turn on brightness to 80", "status": "passed", "steps": [{"name": "执行命令: turn on brightness to 80", "status": "passed", "start": 1756792663531, "stop": 1756792685511}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "340705c4-1505-405f-9560-0cc0c73f0f80-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "99d6edf9-ef6a-45ad-9745-1e351f1133b7-attachment.png", "type": "image/png"}], "start": 1756792685511, "stop": 1756792685713}], "start": 1756792663531, "stop": 1756792685715}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756792685715, "stop": 1756792685716}, {"name": "验证应用已打开", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=48, 最终=88, 响应='['turn on brightness to 80', 'Brightness is at 80% now.', '', '', '', '', '', '', '', '', '']'\nassert 88 == 171\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\system_coupling\\test_turn_on_brightness_to_80.py\", line 36, in test_turn_on_brightness_to\n    assert  final_status==171, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1756792685716, "stop": 1756792685716}], "attachments": [{"name": "stdout", "source": "3f9cd20c-d9c2-4955-b458-cf82d30574da-attachment.txt", "type": "text/plain"}], "start": 1756792663531, "stop": 1756792685716, "uuid": "5ab02f9c-355d-4273-a21d-fac53240de5f", "historyId": "8971377f4371d1ea3384cde4ed276db1", "testCaseId": "8971377f4371d1ea3384cde4ed276db1", "fullName": "testcases.test_ella.system_coupling.test_turn_on_brightness_to_80.TestEllaTurnBrightness#test_turn_on_brightness_to", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_brightness_to_80"}, {"name": "subSuite", "value": "TestEllaTurnBrightness"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_brightness_to_80"}]}