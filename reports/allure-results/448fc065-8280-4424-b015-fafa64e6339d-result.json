{"name": "测试i want to watch fireworks能正常执行", "status": "passed", "description": "i want to watch fireworks", "steps": [{"name": "执行命令: i want to watch fireworks", "status": "passed", "steps": [{"name": "执行命令: i want to watch fireworks", "status": "passed", "start": 1756785689138, "stop": 1756785713583}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4dfa905c-7a07-48f2-8ea8-badf67faf6a9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "389bb332-721a-4f14-9214-1f400e361fcd-attachment.png", "type": "image/png"}], "start": 1756785713583, "stop": 1756785713837}], "start": 1756785689138, "stop": 1756785713837}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756785713837, "stop": 1756785713838}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8b7ba926-ccde-4dc6-9f39-32ccec2aa9b8-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "12cdc422-af21-4358-8bbf-9eac820f79c3-attachment.png", "type": "image/png"}], "start": 1756785713838, "stop": 1756785714039}], "attachments": [{"name": "stdout", "source": "82fee3d8-21c0-45e9-8bcd-ceec4ea3eead-attachment.txt", "type": "text/plain"}], "start": 1756785689138, "stop": 1756785714039, "uuid": "1eeae9b8-05f6-48b0-8285-7d5d8bdd9aea", "historyId": "4ae696581fe41611547bc10ddba4f526", "testCaseId": "4ae696581fe41611547bc10ddba4f526", "fullName": "testcases.test_ella.dialogue.test_i_want_to_watch_fireworks.TestEllaIWantWatchFireworks#test_i_want_to_watch_fireworks", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_i_want_to_watch_fireworks"}, {"name": "subSuite", "value": "TestEllaIWantWatchFireworks"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_i_want_to_watch_fireworks"}]}