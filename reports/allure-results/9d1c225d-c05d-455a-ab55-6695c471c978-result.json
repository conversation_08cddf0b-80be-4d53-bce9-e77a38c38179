{"name": "测试turn on the flashlight能正常执行", "status": "passed", "description": "turn on the flashlight", "steps": [{"name": "执行命令: turn on the flashlight", "status": "passed", "steps": [{"name": "执行命令: turn on the flashlight", "status": "passed", "start": 1756793030366, "stop": 1756793053105}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5d4afe2c-ae81-44c2-8a83-c085b1ed582a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b114ab6d-2688-4c06-a6f5-c7f31c74db1b-attachment.png", "type": "image/png"}], "start": 1756793053105, "stop": 1756793053310}], "start": 1756793030366, "stop": 1756793053310}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756793053310, "stop": 1756793053311}, {"name": "验证应用已打开", "status": "passed", "start": 1756793053311, "stop": 1756793053311}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2cdc6ebb-cb4e-474a-b121-fb187ab00921-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "51af495b-050a-4091-9cb4-d176bd7811e3-attachment.png", "type": "image/png"}], "start": 1756793053311, "stop": 1756793053496}], "attachments": [{"name": "stdout", "source": "b310b9e5-d65f-4715-80a3-190af876d53b-attachment.txt", "type": "text/plain"}], "start": 1756793030366, "stop": 1756793053496, "uuid": "41c57760-4a47-4cee-b2d4-21e948761c96", "historyId": "8c5a5747e91f2cb0412111d5027bb7ec", "testCaseId": "8c5a5747e91f2cb0412111d5027bb7ec", "fullName": "testcases.test_ella.system_coupling.test_turn_on_the_flashlight.TestEllaTurnFlashlight#test_turn_on_the_flashlight", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_the_flashlight"}, {"name": "subSuite", "value": "TestEllaTurnFlashlight"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_the_flashlight"}]}