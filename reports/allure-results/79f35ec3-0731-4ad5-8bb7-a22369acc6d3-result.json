{"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "status": "passed", "description": "switch magic voice to Mango", "steps": [{"name": "执行命令: switch magic voice to Mango", "status": "passed", "steps": [{"name": "执行命令: switch magic voice to Mango", "status": "passed", "start": 1756791493357, "stop": 1756791513994}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "79499cd2-a8e6-4e59-b886-23fd4dff0449-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "398876ba-4759-4515-a33c-c9d43f67108a-attachment.png", "type": "image/png"}], "start": 1756791513994, "stop": 1756791514277}], "start": 1756791493357, "stop": 1756791514278}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1756791514278, "stop": 1756791514280}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ef8d9e10-43f9-4776-820f-81675eb31860-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c42ac03a-74cd-4698-a32e-b99189fb902e-attachment.png", "type": "image/png"}], "start": 1756791514280, "stop": 1756791514506}], "attachments": [{"name": "stdout", "source": "*************-43f1-b85d-efc1ec0d9182-attachment.txt", "type": "text/plain"}], "start": 1756791493357, "stop": 1756791514506, "uuid": "1028c5da-f562-48f3-9600-50d85011728e", "historyId": "9420fd606a04614c6f09bf36f2873f93", "testCaseId": "9420fd606a04614c6f09bf36f2873f93", "fullName": "testcases.test_ella.system_coupling.test_switch_magic_voice_to_mango.TestEllaSwitchMagicVoiceToMango#test_switch_magic_voice_to_mango", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_magic_voice_to_mango"}, {"name": "subSuite", "value": "TestEllaSwitchMagicVoiceToMango"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_magic_voice_to_mango"}]}