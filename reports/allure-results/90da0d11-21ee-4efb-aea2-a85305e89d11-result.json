{"name": "测试start boosting phone能正常执行", "status": "passed", "description": "start boosting phone", "steps": [{"name": "执行命令: start boosting phone", "status": "passed", "steps": [{"name": "执行命令: start boosting phone", "status": "passed", "start": 1756803161362, "stop": 1756803183581}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "cdec937e-8889-49c6-93b0-8444aa668b55-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2def93f1-951f-43a6-ad7a-eb6064cff546-attachment.png", "type": "image/png"}], "start": 1756803183581, "stop": 1756803183882}], "start": 1756803161362, "stop": 1756803183882}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756803183882, "stop": 1756803183884}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e2f2b053-c435-46bc-bbfe-a786fd5e6ae7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7dbcb83e-e666-47a8-a388-86cfeefd119e-attachment.png", "type": "image/png"}], "start": 1756803183884, "stop": 1756803184156}], "attachments": [{"name": "stdout", "source": "9c1d3835-4005-4df1-a339-ece7b7134fc1-attachment.txt", "type": "text/plain"}], "start": 1756803161362, "stop": 1756803184156, "uuid": "cb46958c-4518-48c0-8478-0a98e19f129d", "historyId": "bda3c1f82ca5c246958b5bf50db09a67", "testCaseId": "bda3c1f82ca5c246958b5bf50db09a67", "fullName": "testcases.test_ella.unsupported_commands.test_start_boosting_phone.TestEllaStartBoostingPhone#test_start_boosting_phone", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_start_boosting_phone"}, {"name": "subSuite", "value": "TestEllaStartBoostingPhone"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_start_boosting_phone"}]}