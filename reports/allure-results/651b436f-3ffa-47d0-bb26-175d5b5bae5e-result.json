{"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "status": "passed", "description": "验证jump to adaptive brightness settings指令返回预期的不支持响应", "steps": [{"name": "执行命令: jump to adaptive brightness settings", "status": "passed", "steps": [{"name": "执行命令: jump to adaptive brightness settings", "status": "passed", "start": 1756798944220, "stop": 1756798975025}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6f250e6a-82dc-4163-a813-1ec6326cb456-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a57e24ec-2509-4235-a6f4-1444acc0c333-attachment.png", "type": "image/png"}], "start": 1756798975025, "stop": 1756798975288}], "start": 1756798944220, "stop": 1756798975288}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756798975288, "stop": 1756798975290}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "63e952ff-1e6e-4bb9-8c7d-ae25ce4c5ae9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c9f34128-52e2-4a9a-a048-358b6c172d5e-attachment.png", "type": "image/png"}], "start": 1756798975290, "stop": 1756798975510}], "attachments": [{"name": "stdout", "source": "26d2afe6-d47a-4aed-bb59-18a2013365b7-attachment.txt", "type": "text/plain"}], "start": 1756798944220, "stop": 1756798975511, "uuid": "aa3bea32-5ebd-4ea1-b53a-2d57a487cce7", "historyId": "c796c03cca51cea23bdc87f3f9d6fa95", "testCaseId": "c796c03cca51cea23bdc87f3f9d6fa95", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_adaptive_brightness_settings.TestEllaJumpAdaptiveBrightnessSettings#test_jump_to_adaptive_brightness_settings", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_adaptive_brightness_settings"}, {"name": "subSuite", "value": "TestEllaJumpAdaptiveBrightnessSettings"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_adaptive_brightness_settings"}]}