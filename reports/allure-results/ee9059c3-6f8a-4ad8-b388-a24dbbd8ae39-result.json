{"name": "测试running on the grass", "status": "passed", "description": "测试running on the grass指令", "steps": [{"name": "执行命令: running on the grass", "status": "passed", "steps": [{"name": "执行命令: running on the grass", "status": "passed", "start": 1756801124108, "stop": 1756801150170}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0485265b-9cea-4d6f-9e6e-7e199a538351-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "64839734-2d58-4556-a05f-9b5c738b814d-attachment.png", "type": "image/png"}], "start": 1756801150170, "stop": 1756801150479}], "start": 1756801124108, "stop": 1756801150479}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756801150480, "stop": 1756801150484}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "eca214c7-c659-4241-86fd-4c9059dc775e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ea17e027-1f88-4908-a026-5f5722788fa8-attachment.png", "type": "image/png"}], "start": 1756801150484, "stop": 1756801150718}], "attachments": [{"name": "stdout", "source": "9285f812-db26-4b60-89ec-f05bd1bea4c1-attachment.txt", "type": "text/plain"}], "start": 1756801124108, "stop": 1756801150719, "uuid": "27dbd2e7-9fc1-4357-a0de-58d363e12bc3", "historyId": "5eaa5a3015ce02f75c4c021fdbd2f78d", "testCaseId": "5eaa5a3015ce02f75c4c021fdbd2f78d", "fullName": "testcases.test_ella.unsupported_commands.test_running_on_the_grass.TestEllaOpenPlayPoliticalNews#test_running_on_the_grass", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_running_on_the_grass"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_running_on_the_grass"}]}