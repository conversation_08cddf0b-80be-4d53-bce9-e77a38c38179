{"name": "测试turn on bluetooth能正常执行", "status": "passed", "description": "turn on bluetooth", "steps": [{"name": "执行命令: turn on bluetooth", "status": "passed", "steps": [{"name": "执行命令: turn on bluetooth", "status": "passed", "start": 1756792624715, "stop": 1756792646767}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3645e7ef-4450-4e15-8c52-0091e48c0f07-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ce4e7b59-da6f-46ea-a85e-75f1fe0cd48e-attachment.png", "type": "image/png"}], "start": 1756792646767, "stop": 1756792646987}], "start": 1756792624715, "stop": 1756792646987}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756792646987, "stop": 1756792646988}, {"name": "验证应用已打开", "status": "passed", "start": 1756792646988, "stop": 1756792646988}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e9c90d19-ce35-4664-914b-6453d03fa004-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fd59d4a7-8e17-4461-baad-b36daa820aac-attachment.png", "type": "image/png"}], "start": 1756792646988, "stop": 1756792647188}], "attachments": [{"name": "stdout", "source": "0bb7a8f1-68ad-4ecb-8be5-980b7ecc54ca-attachment.txt", "type": "text/plain"}], "start": 1756792624715, "stop": 1756792647189, "uuid": "139850fa-d343-47c0-878c-fd4b8a284e85", "historyId": "aff947fee562ec2636c3ce68a270b88d", "testCaseId": "aff947fee562ec2636c3ce68a270b88d", "fullName": "testcases.test_ella.system_coupling.test_turn_on_bluetooth.TestEllaTurnBluetooth#test_turn_on_bluetooth", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_bluetooth"}, {"name": "subSuite", "value": "TestEllaTurnBluetooth"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_bluetooth"}]}