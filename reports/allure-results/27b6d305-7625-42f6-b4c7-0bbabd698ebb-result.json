{"name": "测试go to office", "status": "passed", "description": "测试go to office指令", "steps": [{"name": "执行命令: go to office", "status": "passed", "steps": [{"name": "执行命令: go to office", "status": "passed", "start": 1756797717516, "stop": 1756797742188}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e5ee2716-8d28-4e08-b988-20bec0e389e7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0d25efdf-dded-42db-9661-12b86963546e-attachment.png", "type": "image/png"}], "start": 1756797742188, "stop": 1756797742389}], "start": 1756797717516, "stop": 1756797742389}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756797742389, "stop": 1756797742390}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2775da69-d56f-46cf-aedc-2ecc90077ac4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "91806204-def7-4c78-8d4d-07ac7e69acec-attachment.png", "type": "image/png"}], "start": 1756797742390, "stop": 1756797742600}], "attachments": [{"name": "stdout", "source": "c3ee7bfc-296b-475f-92bc-c1aa6fa5059e-attachment.txt", "type": "text/plain"}], "start": 1756797717516, "stop": 1756797742601, "uuid": "5d6bcd39-8bcf-4b25-9dcd-6a8b3c3982f0", "historyId": "02dda06829926b51f170419357629e86", "testCaseId": "02dda06829926b51f170419357629e86", "fullName": "testcases.test_ella.unsupported_commands.test_go_to_office.TestEllaOpenPlayPoliticalNews#test_go_to_office", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_go_to_office"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_go_to_office"}]}