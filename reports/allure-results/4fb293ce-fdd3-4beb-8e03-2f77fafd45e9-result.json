{"name": "测试take notes能正常执行", "status": "passed", "description": "take notes", "steps": [{"name": "执行命令: take notes", "status": "passed", "steps": [{"name": "执行命令: take notes", "status": "passed", "start": 1756803545768, "stop": 1756803572765}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "91e26b1e-de63-449c-bd4f-2a1f3b205673-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b8dafbba-3c18-4e4b-96ac-9a5d04842440-attachment.png", "type": "image/png"}], "start": 1756803572765, "stop": 1756803573021}], "start": 1756803545768, "stop": 1756803573021}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756803573021, "stop": 1756803573023}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "689824be-fb12-4597-b72c-52f538baf2a2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9e2a6786-8d6b-4f16-854f-268a2e4665ad-attachment.png", "type": "image/png"}], "start": 1756803573023, "stop": 1756803573318}], "attachments": [{"name": "stdout", "source": "deba8dc3-af53-452b-85e9-c191b23cc51b-attachment.txt", "type": "text/plain"}], "start": 1756803545768, "stop": 1756803573319, "uuid": "3dd0674a-be6a-42f6-befd-97a164fa91ce", "historyId": "fcda536a017e05b2edb24a2e80ce1ec0", "testCaseId": "fcda536a017e05b2edb24a2e80ce1ec0", "fullName": "testcases.test_ella.unsupported_commands.test_take_notes.TestEllaTakeNotes#test_take_notes", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_take_notes"}, {"name": "subSuite", "value": "TestEllaTakeNotes"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_take_notes"}]}