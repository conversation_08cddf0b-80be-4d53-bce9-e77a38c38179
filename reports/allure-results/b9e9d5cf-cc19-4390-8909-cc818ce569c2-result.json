{"name": "测试add all the numbers to lucy", "status": "passed", "description": "测试Ask Screen功能: add all the numbers to lucy", "steps": [{"name": "准备测试数据", "status": "passed", "start": 1756793415820, "stop": 1756793423786}, {"name": "执行Ask Screen命令: add all the numbers to lucy", "status": "passed", "steps": [{"name": "执行浮窗命令: add all the numbers to lucy", "status": "passed", "steps": [{"name": "执行命令: add all the numbers to lucy", "status": "passed", "start": 1756793423786, "stop": 1756793432993}, {"name": "等待并获取AI响应", "status": "passed", "start": 1756793432993, "stop": 1756793448694}, {"name": "验证响应内容", "status": "passed", "attachments": [{"name": "关键词验证结果", "source": "ae6e4a7d-aebf-43b0-9e72-67e866df8faf-attachment.txt", "type": "text/plain"}], "start": 1756793448694, "stop": 1756793448697}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "浮窗测试总结", "source": "bbddc3c8-3283-4b6a-ac6d-a3aadbf95047-attachment.txt", "type": "text/plain"}, {"name": "AI响应内容", "source": "b3811328-fce9-4e7f-8707-390803e25067-attachment.txt", "type": "text/plain"}], "start": 1756793448697, "stop": 1756793448699}], "start": 1756793423786, "stop": 1756793448699}, {"name": "截图记录测试完成状态", "status": "passed", "attachments": [{"name": "floating_test_completed", "source": "074da760-e0ad-4071-abf5-44a8fd91c3a4-attachment.png", "type": "image/png"}], "start": 1756793448699, "stop": 1756793448892}], "start": 1756793423786, "stop": 1756793450096}, {"name": "验证测试结果", "status": "passed", "start": 1756793450096, "stop": 1756793450098}], "attachments": [{"name": "stdout", "source": "4eb6bf94-a226-4e34-ae25-1bb5e080e38c-attachment.txt", "type": "text/plain"}], "start": 1756793415820, "stop": 1756793450100, "uuid": "51215b1b-a0e0-41a1-a2f5-acf0707d7322", "historyId": "6727d4a9ee2f10fdd00778048be14e2a", "testCaseId": "6727d4a9ee2f10fdd00778048be14e2a", "fullName": "testcases.test_ella.test_ask_screen.contact.test_add_all_the_numbers_to_lucy.TestAskScreenAddAllNumbersLucy#test_add_all_the_numbers_to_lucy", "labels": [{"name": "epic", "value": "Ella浮窗测试"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ask Screen功能"}, {"name": "story", "value": "联系人相关"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.test_ask_screen.contact"}, {"name": "suite", "value": "test_add_all_the_numbers_to_lucy"}, {"name": "subSuite", "value": "TestAskScreenAddAllNumbersLucy"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_ask_screen.contact.test_add_all_the_numbers_to_lucy"}]}