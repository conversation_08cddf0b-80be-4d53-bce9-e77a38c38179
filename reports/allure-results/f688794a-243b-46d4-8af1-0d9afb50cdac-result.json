{"name": "测试turn on light theme能正常执行", "status": "passed", "description": "turn on light theme", "steps": [{"name": "执行命令: turn on light theme", "status": "passed", "steps": [{"name": "执行命令: turn on light theme", "status": "passed", "start": 1756792739974, "stop": 1756792762139}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "58009de4-bd24-4cb5-b9f4-e195f3c45919-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "06244254-7bb5-4e1f-bc41-5fcadf04f292-attachment.png", "type": "image/png"}], "start": 1756792762139, "stop": 1756792762358}], "start": 1756792739974, "stop": 1756792762358}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756792762358, "stop": 1756792762359}, {"name": "验证应用已打开", "status": "passed", "start": 1756792762359, "stop": 1756792762359}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "344b83d1-12ba-48f0-b21d-9af2660de310-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f4dd7862-f9ae-44cd-9c75-5530d9461bf8-attachment.png", "type": "image/png"}], "start": 1756792762359, "stop": 1756792762568}], "attachments": [{"name": "stdout", "source": "56db6c4f-674f-4ecf-acbd-86d454c49ef2-attachment.txt", "type": "text/plain"}], "start": 1756792739974, "stop": 1756792762569, "uuid": "cef7902b-676d-468c-a392-c8d9d41d2e01", "historyId": "16f38913a9d7e0ffc4c5ac51d5acf5c7", "testCaseId": "16f38913a9d7e0ffc4c5ac51d5acf5c7", "fullName": "testcases.test_ella.system_coupling.test_turn_on_light_theme.TestEllaTurnLightTheme#test_turn_on_light_theme", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_light_theme"}, {"name": "subSuite", "value": "TestEllaTurnLightTheme"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_light_theme"}]}