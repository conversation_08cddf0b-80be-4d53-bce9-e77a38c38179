{"name": "测试flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.", "status": "passed", "description": "测试flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.指令", "steps": [{"name": "执行命令: flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.", "status": "passed", "steps": [{"name": "执行命令: flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.", "status": "passed", "start": 1756797398637, "stop": 1756797420390}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "63cad66b-df8d-470e-875f-a06d447a5b6b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "25260841-52ac-441d-a2d7-c681424dd515-attachment.png", "type": "image/png"}], "start": 1756797420390, "stop": 1756797420619}], "start": 1756797398637, "stop": 1756797420619}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756797420619, "stop": 1756797420620}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3813ecd5-21d7-4a3b-b11b-83c74bef1df7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d9bad463-5823-4b7d-9a7f-25ad34ce1a17-attachment.png", "type": "image/png"}], "start": 1756797420620, "stop": 1756797420843}], "attachments": [{"name": "stdout", "source": "a2488227-b132-4cb1-9a00-ca2627ce1a99-attachment.txt", "type": "text/plain"}], "start": 1756797398637, "stop": 1756797420844, "uuid": "3845445a-e5af-41c2-9749-59bddeedf36b", "historyId": "eb6abc860fad339739076abacb13ac83", "testCaseId": "eb6abc860fad339739076abacb13ac83", "fullName": "testcases.test_ella.unsupported_commands.test_flat_illustration_of_a_girl_background_in_avocado_green_minimalist_art_white_dress_red_lipstick_alluring_gaze_green_vintage_earrings_profile_view_soft_lighting_muted_tones_serene_ambiance.TestEllaOpenPlayPoliticalNews#test_flat_illustration_of_a_girl_background_in_avocado_green_minimalist_art_white_dress_red_lipstick_alluring_gaze_green_vintage_earrings_profile_view_soft_lighting_muted_tones_serene_ambiance", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_flat_illustration_of_a_girl_background_in_avocado_green_minimalist_art_white_dress_red_lipstick_alluring_gaze_green_vintage_earrings_profile_view_soft_lighting_muted_tones_serene_ambiance"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_flat_illustration_of_a_girl_background_in_avocado_green_minimalist_art_white_dress_red_lipstick_alluring_gaze_green_vintage_earrings_profile_view_soft_lighting_muted_tones_serene_ambiance"}]}