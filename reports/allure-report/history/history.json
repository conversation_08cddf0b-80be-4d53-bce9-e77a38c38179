{"7c50481bc992b9ff109abbeeeece073a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "21a139ad2166df7d", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'SIM 2 not detected.']，实际响应: '['check mobile data balance of sim2', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756795734918, "stop": 1756795758741, "duration": 23823}}]}, "8a87a1d0f534afc4770901f3d1dfa316": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f2c5fb23e17f8b84", "status": "passed", "time": {"start": 1756792818230, "stop": 1756792840011, "duration": 21781}}]}, "1f9da5f1f2977bbbae31e0d3334eff82": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1234c38f4cea2cf4", "status": "passed", "time": {"start": 1756793269897, "stop": 1756793292584, "duration": 22687}}]}, "4dcdc98a8f38f748728aec71f673e025": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a8e25dada3cb366d", "status": "passed", "time": {"start": 1756788276687, "stop": 1756788390979, "duration": 114292}}]}, "c50847e2010bac3c5a9bb7ff0b690fb6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e7f2662736dd25cc", "status": "passed", "time": {"start": 1756801210108, "stop": 1756801235270, "duration": 25162}}]}, "8e367b8da758818b9a0fe21deca7ec48": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1d21afee753fb1a0", "status": "passed", "time": {"start": 1756802490641, "stop": 1756802529939, "duration": 39298}}]}, "70c9bd8c4aab57e96eb06acb93ca2223": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "30ba5778b1f272d1", "status": "passed", "time": {"start": 1756798375768, "stop": 1756798400933, "duration": 25165}}]}, "57c053de6acd628d4b4cd1230b702a40": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9f6a4b7db0268897", "status": "passed", "time": {"start": 1756796085054, "stop": 1756796107132, "duration": 22078}}]}, "1a5cbbb97cbe59e003ae71750a8d910f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b27ee5d04f31f563", "status": "passed", "time": {"start": 1756787843573, "stop": 1756787867523, "duration": 23950}}]}, "1d8131ec3deb65d953f2f16c259f261b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9e71aa520d7f5088", "status": "passed", "time": {"start": 1756786001256, "stop": 1756786024035, "duration": 22779}}]}, "de0ed312f350c708e7a00bb74aeaac0f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7da06f5f51b44735", "status": "passed", "time": {"start": 1756784140777, "stop": 1756784203117, "duration": 62340}}]}, "36066a5bbb1f962a6ad5842baefe4ff3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7947986728a5e426", "status": "passed", "time": {"start": 1756800386226, "stop": 1756800416595, "duration": 30369}}]}, "fc477656b55eea3a3906a5bdcaa93554": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "71cc929db6c2e193", "status": "passed", "time": {"start": 1756794801868, "stop": 1756794826554, "duration": 24686}}]}, "3eec462c8da39eaf95742ed9ab45b7c7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7c94881a8037bb40", "status": "passed", "time": {"start": 1756796456630, "stop": 1756796479272, "duration": 22642}}]}, "a2c60aff2518d86c9e5686c943130f40": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "acca56828803a54e", "status": "passed", "time": {"start": 1756803751509, "stop": 1756803776316, "duration": 24807}}]}, "f27eb1a59c1d5c5c845852e05e6a17d9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4a1588e9ca567be1", "status": "passed", "time": {"start": 1756790143951, "stop": 1756790165628, "duration": 21677}}]}, "d45827de1782723ad9f8cd9d38f067dc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8c4309521df07a45", "status": "passed", "time": {"start": 1756801524626, "stop": 1756801548120, "duration": 23494}}]}, "c5debd3414c81b3cc4349236d4020867": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f162fef26f30eccc", "status": "passed", "time": {"start": 1756798557398, "stop": 1756798583179, "duration": 25781}}]}, "da0740a44317121ce1879d022e95ae23": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ac27b297a34a5503", "status": "passed", "time": {"start": 1756787964251, "stop": 1756788051466, "duration": 87215}}]}, "19cc9ff22947964a912a8f57a87a3c68": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3349acc16a24c334", "status": "passed", "time": {"start": 1756784973965, "stop": 1756784997341, "duration": 23376}}]}, "1bf9bd9c91ab7da6f818ff587cfff7da": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1316579bfb181bf1", "status": "passed", "time": {"start": 1756786415721, "stop": 1756786442462, "duration": 26741}}]}, "b68db9e7b007fe641926babf537afa6c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c7761abaf3f6c596", "status": "passed", "time": {"start": 1756789299776, "stop": 1756789346567, "duration": 46791}}]}, "e8f9ac327bc5f166a4c4a14509f365bf": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "364533e255c84cd3", "status": "passed", "time": {"start": 1756792412890, "stop": 1756792432626, "duration": 19736}}]}, "3bd2ea64304a19b2c3694e3a1975c668": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c5bc2ab00725fa04", "status": "passed", "time": {"start": 1756798014517, "stop": 1756798037228, "duration": 22711}}]}, "ac6792be4ebb553a5655a2c44aca218c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "212c475735d838d9", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['the high is forecast', '℃']，实际响应: '[\"What's the weather like today\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756787545605, "stop": 1756787574866, "duration": 29261}}]}, "6d49aaf4a5e11b32b961aa0aa3dbbf6e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1a9298a4d7ae18e9", "status": "passed", "time": {"start": 1756799645101, "stop": 1756799674208, "duration": 29107}}]}, "6c59bb8b6ba250c58da738ab8237ed3c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "797a83e0c356b255", "status": "passed", "time": {"start": 1756786246613, "stop": 1756786270510, "duration": 23897}}]}, "b236a18e19a6aa2218c85b80afef2746": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "53f845ae02607e8c", "status": "passed", "time": {"start": 1756785961291, "stop": 1756785985151, "duration": 23860}}]}, "13916c423237bf06a74b0b109aad0d66": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "4d140110eec83794", "status": "failed", "statusDetails": "AssertionError: 初始=0, 最终=0, 响应='['set notifications volume to 50', 'Notification volume has been set to 50.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert 0 == 7", "time": {"start": 1756790881821, "stop": 1756790903051, "duration": 21230}}]}, "7ec382c77bede0ad015817770cd9e1eb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "64d3523a0b23c672", "status": "passed", "time": {"start": 1756785886173, "stop": 1756785907891, "duration": 21718}}]}, "5dff8ffa0041df33df80919398086e48": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9112c0b7fc56b24a", "status": "passed", "time": {"start": 1756791769103, "stop": 1756791790537, "duration": 21434}}]}, "3d11cdc9d7b9f1e332cba29afd4889a7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1be04dc69a1644cf", "status": "passed", "time": {"start": 1756793494581, "stop": 1756793529819, "duration": 35238}}]}, "f622c7c4831272dc58cb99e6af8d9943": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "683bf8fbe8464657", "status": "passed", "time": {"start": 1756794437497, "stop": 1756794470828, "duration": 33331}}]}, "5fc780d1e7f790011f0e4a521e125a16": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ce7335b2b6eeef73", "status": "passed", "time": {"start": 1756802878812, "stop": 1756802910226, "duration": 31414}}]}, "7327f3fdeeac58d0323250ec95196667": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bfb22679d401104f", "status": "passed", "time": {"start": 1756793575004, "stop": 1756793609952, "duration": 34948}}]}, "e2beda2a0bda4155b33d47f14bdcb9ed": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "546b6688eb161dd4", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['set color style', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "time": {"start": 1756801565050, "stop": 1756801589979, "duration": 24929}}]}, "468e62202269d66d1ea3c0ae6e2a0e21": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f724fa5ac797828b", "status": "passed", "time": {"start": 1756795230246, "stop": 1756795255819, "duration": 25573}}]}, "2b244b852ff1236f560ec792596ae556": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5aef949138f24820", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['I need to download Yandex Eats to continue']，实际响应: '['order a burger', \"O<PERSON>, out of my reach, ask me again after I've learned it\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756800264288, "stop": 1756800287028, "duration": 22740}}]}, "9e84af065ec0018044fd43f37b4c2179": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "aa618631a0b53af5", "status": "passed", "time": {"start": 1756798054111, "stop": 1756798077751, "duration": 23640}}]}, "148d3ba280bfe2b41b8464beec5f6763": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b3955e865876adaa", "status": "passed", "time": {"start": 1756783886271, "stop": 1756783916818, "duration": 30547}}]}, "de524bccf252aabc016822f1a65de7f4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3f4f9f42b17ddb4e", "status": "passed", "time": {"start": 1756785923733, "stop": 1756785945259, "duration": 21526}}]}, "fe7a0f349fbd2027990e72ab5a6650f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4193c1cad87a592d", "status": "passed", "time": {"start": 1756793192543, "stop": 1756793215554, "duration": 23011}}]}, "0fa1017773031b1388876c73f0e0e653": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7713ec5d2e3b1392", "status": "passed", "time": {"start": 1756793152117, "stop": 1756793176621, "duration": 24504}}]}, "fe3d09fe0bad56e7804ef2f5ea49d283": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c218ea72eac84b28", "status": "passed", "time": {"start": 1756789560784, "stop": 1756789582597, "duration": 21813}}]}, "04cef4934fe29e90ca0248af7b395794": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "51ce257c78bf6fc9", "status": "passed", "time": {"start": 1756791455998, "stop": 1756791477466, "duration": 21468}}]}, "acca7d0b06a28ad8e6ccfe3c35828ce1": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "4b3f570b5f7eb87c", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '[\"what's the weather today?\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756787755960, "stop": 1756787785774, "duration": 29814}}]}, "223077c4b7372197db11e72856c8b7e6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1c843d726c6b190", "status": "passed", "time": {"start": 1756799433715, "stop": 1756799460573, "duration": 26858}}]}, "274ccc19fc9f00ce49f6545c8286db01": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "524d4e259de62074", "status": "failed", "statusDetails": "Failed: 响应中未找到任何期望关键词: ['following number is recognized', 'save it as a contact']", "time": {"start": 1756793534938, "stop": 1756793569768, "duration": 34830}}]}, "6bdbaaabff6497c5d3be4727f1a7cd8d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "923407fa8707e094", "status": "passed", "time": {"start": 1756784258037, "stop": 1756784283710, "duration": 25673}}]}, "693bce6b8bfdefa98827246122355bf1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ce167fd8c20cf5e3", "status": "passed", "time": {"start": 1756797437720, "stop": 1756797460250, "duration": 22530}}]}, "762b1ab748e39965c1484eb7fe38bfe4": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "89a8de8c8fe0463d", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore <PERSON>wi<PERSON> down to view earlier chats 11:28 am <PERSON>, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh How do campfires repel insects at night? Extract key points from document <PERSON> After Linsanity Fame DeepSeek-R1 Feel free to ask me any questions…\", '[com.transsion.phonemaster页面] 未获取到文本内容']'\nassert False", "time": {"start": 1756783705036, "stop": 1756783732834, "duration": 27798}}]}, "71970769dc57394634693dcd9db391b9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7176e6e82cfc1c02", "status": "passed", "time": {"start": 1756793615063, "stop": 1756793651003, "duration": 35940}}]}, "d6d97ebce763bf8ead601650bfb2383c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ebd852d7bf2f6c5f", "status": "passed", "time": {"start": 1756785013375, "stop": 1756785035555, "duration": 22180}}]}, "2b4b7555520a6b757239820871731d81": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "588fc8c6a4c1d92e", "status": "passed", "time": {"start": 1756800017401, "stop": 1756800055154, "duration": 37753}}]}, "4aad505422b0d8c87c499477cd89ff5d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "68ebdca8e9ca136b", "status": "passed", "time": {"start": 1756789363382, "stop": 1756789386595, "duration": 23213}}]}, "e8c3c7bb72cf538a9e89a7b790c5e689": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7927d47325efaee9", "status": "passed", "time": {"start": 1756803500113, "stop": 1756803529301, "duration": 29188}}]}, "6c46a38570672e3c21f37ef82690d639": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cc9b50f6d943f117", "status": "passed", "time": {"start": 1756791948260, "stop": 1756791988016, "duration": 39756}}]}, "79b68fd9ac84793c5f55250aad03649a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d1e7988eef8ca89f", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['Switch to Barrage Notification', 'This feature is temporarily not supported.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756791530803, "stop": 1756791551512, "duration": 20709}}]}, "ff945a5d436679bddd13261b231955ec": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "23959f7c15041fa3", "status": "passed", "time": {"start": 1756801902033, "stop": 1756801925935, "duration": 23902}}]}, "ad18e983dce31052b87b7404f3b347ce": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "33025e5cf02aca61", "status": "failed", "statusDetails": "AssertionError: 初始=255, 最终=22, 响应='['Adjustment the brightness to 50%', 'Brightness is at 50% now.', '', '', '', '', '', '', '', '', '']'\nassert 22 == 75", "time": {"start": 1756788843750, "stop": 1756788866879, "duration": 23129}}]}, "05cfb3e6f186373be53bb1a7166ac69f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c3a648fb46ad14ee", "status": "passed", "time": {"start": 1756800714774, "stop": 1756800748639, "duration": 33865}}]}, "83e3a1b41834e87017b680efd7c16b92": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "295e8404d7e3f49a", "status": "passed", "time": {"start": 1756786867722, "stop": 1756786893218, "duration": 25496}}]}, "50e811b93ce50e5ac8364a9fea07e234": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cbb21ecfb973e7a0", "status": "passed", "time": {"start": 1756798213877, "stop": 1756798236446, "duration": 22569}}]}, "32ba476d46e86e963aa12ced39981955": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bbe2d4d36780fd8", "status": "passed", "time": {"start": 1756789110493, "stop": 1756789133717, "duration": 23224}}]}, "b07852caec1a6673427b80552f64be85": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "276fed41da8a610d", "status": "failed", "statusDetails": "AssertionError: 文件不存在！\nassert False", "time": {"start": 1756789486343, "stop": 1756789544934, "duration": 58591}}]}, "af89bd1d18cd6a175678a8fe1f43ee33": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5c3ce77b69396623", "status": "passed", "time": {"start": 1756800123255, "stop": 1756800153842, "duration": 30587}}]}, "0c59a39b6298b394468a10532e127070": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8512aa875947c6a2", "status": "passed", "time": {"start": 1756789402998, "stop": 1756789469811, "duration": 66813}}]}, "039e454ca4c329751543f1bfbb5e008e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "29ef83e62fa91e38", "status": "passed", "time": {"start": 1756794536016, "stop": 1756794567298, "duration": 31282}}]}, "d8a3659601151a79f3b71ba4e47cafee": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "4f360ffc129ea5d", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['turn on driving mode', \"This one stumped me, but I'll keep improving. Let's try again differently!\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756803955233, "stop": 1756803978679, "duration": 23446}}]}, "f2f6762c5ec83e110ace25b47e3112d5": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "82a3530fe03c9589", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['℃', 'today']，实际响应: '[\"how's the weather today?\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756785399612, "stop": 1756785427655, "duration": 28043}}]}, "f9558282973df5c72bd1c57fb0e19984": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "664b22c9c219a67", "status": "passed", "time": {"start": 1756794924968, "stop": 1756794953614, "duration": 28646}}]}, "eb142151b4b5ba4125a1a866dc2b58ef": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "297725e9df9066d6", "status": "passed", "time": {"start": 1756802350682, "stop": 1756802375493, "duration": 24811}}]}, "3238a485ed8e76dc2866c0b0bcd4930e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c7b8e67bf5dda5ff", "status": "passed", "time": {"start": 1756790686825, "stop": 1756790708486, "duration": 21661}}]}, "ff8706df57207971727cf6e1326d4a26": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "76782b4c616b378d", "status": "passed", "time": {"start": 1756800073453, "stop": 1756800105560, "duration": 32107}}]}, "95e68fb1d20b8d7ff190c67b0bbc2ee8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ec1bded3e5bd05b6", "status": "passed", "time": {"start": 1756787027702, "stop": 1756787050850, "duration": 23148}}]}, "503ff57584874e8387e6b367bfa70c8c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4b8e108e367c43a5", "status": "passed", "time": {"start": 1756796495856, "stop": 1756796517977, "duration": 22121}}]}, "a19924fb0a564cf26596907610c0f678": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6152ce64ee8a0ae9", "status": "passed", "time": {"start": 1756785730270, "stop": 1756785754333, "duration": 24063}}]}, "c190fe929896ea57ed1e33f8bc5bf113": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "426ae92a93768635", "status": "passed", "time": {"start": 1756792299137, "stop": 1756792320454, "duration": 21317}}]}, "db313838c77140c89e69785e101f25d7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9c54783e9054177b", "status": "passed", "time": {"start": 1756799604218, "stop": 1756799628319, "duration": 24101}}]}, "fcfaafeb2b49ed6f468b8db263c64a18": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "492f97d058f34f28", "status": "passed", "time": {"start": 1756789675900, "stop": 1756789698158, "duration": 22258}}]}, "7af47ccbdaf69e5292e05041f822c0c7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ef24e91e2e3ccae4", "status": "passed", "time": {"start": 1756792262714, "stop": 1756792283240, "duration": 20526}}]}, "70c4e0c16f5cb3629f87876768739a8d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "84ecae05228c3727", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['OK, the voice is switched. You can also select other voices.']，实际响应: '['change your voice', 'The following images are generated for you.', '', '', '', '', '', '', '', '', '']'\nassert False", "time": {"start": 1756795552403, "stop": 1756795580296, "duration": 27893}}]}, "c7b8111fa78410a413dfc969cfe6f0e1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "21446f51684a2ba3", "status": "passed", "time": {"start": 1756800170779, "stop": 1756800201504, "duration": 30725}}]}, "8b2d3084bb429ea5def5db416bbf10a7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6638be2e62d31245", "status": "passed", "time": {"start": 1756782860950, "stop": 1756782892306, "duration": 31356}}]}, "9da64d3434f91a12d693ed9c71b62e87": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d40b3ff68a0762c6", "status": "passed", "time": {"start": 1756783510373, "stop": 1756783531794, "duration": 21421}}]}, "f4da532f5d62abff197a05947efc027a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "da5f18b688d5052d", "status": "passed", "time": {"start": 1756785650765, "stop": 1756785672845, "duration": 22080}}]}, "fcda536a017e05b2edb24a2e80ce1ec0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9a897653a054114b", "status": "passed", "time": {"start": 1756803545768, "stop": 1756803573319, "duration": 27551}}]}, "d29b58e8e7ac85336b7dc255831fd5ba": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d17d13a14891bba0", "status": "passed", "time": {"start": 1756786157378, "stop": 1756786181634, "duration": 24256}}]}, "f805fddc06490f3d2a31cfcdf9ab576b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "aa38e330a946abf5", "status": "passed", "time": {"start": 1756794004165, "stop": 1756794040218, "duration": 36053}}]}, "bd4f9d0c0f70cf6b24bb9923810b25c1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "38d3e976eb46060d", "status": "passed", "time": {"start": 1756801039823, "stop": 1756801064975, "duration": 25152}}]}, "733cc57b9e666f7c16017a85f41c410d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "92ceaaf9ada9fed9", "status": "passed", "time": {"start": 1756790293889, "stop": 1756790317680, "duration": 23791}}]}, "f1bf796cd6804ca9b19a3e3f949a04ba": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6f087bc8b88d7274", "status": "passed", "time": {"start": 1756798992707, "stop": 1756799016029, "duration": 23322}}]}, "00495562396e9306113e5f37378ac991": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5560adf92ffdbe7b", "status": "passed", "time": {"start": 1756797636563, "stop": 1756797660697, "duration": 24134}}]}, "c359e36718cf3ac8fd335c333a6470cf": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "62abd7c6c7b86e26", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach']，实际响应: '['Navigate to the address on the screen', 'Where are you going?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756799690897, "stop": 1756799717619, "duration": 26722}}]}, "71c64c122f83e6fd138db517bbda4aef": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7e03945a860e5a54", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['set ultra power saving', \"Sorry, I couldn't locate the setting option(s) for ultra power saving.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756803119935, "stop": 1756803143695, "duration": 23760}}]}, "49ae31ee7fe8baa7f1604fd83d56bb68": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b3de878221ea3c50", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Multiple settings options found']，实际响应: '['check model information', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756795775640, "stop": 1756795798561, "duration": 22921}}]}, "0e5513d569c7270e4332e484218ae36b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "94739d17159fb75c", "status": "passed", "time": {"start": 1756796908436, "stop": 1756796938077, "duration": 29641}}]}, "c33807b5ffc8a9f3d1a58e446637a66b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fc6e7cd50fd06462", "status": "passed", "time": {"start": 1756792449369, "stop": 1756792532427, "duration": 83058}}]}, "fee3033814a8b17ff8c8abe6bbcdc839": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "819174788d9bc35", "status": "passed", "time": {"start": 1756786374822, "stop": 1756786399999, "duration": 25177}}]}, "c796c03cca51cea23bdc87f3f9d6fa95": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "739bf7d818e3b5e1", "status": "passed", "time": {"start": 1756798944220, "stop": 1756798975511, "duration": 31291}}]}, "e14cd5a605f26d24de7f5f63d4667c68": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f396b55e7b41720e", "status": "passed", "time": {"start": 1756802032514, "stop": 1756802066187, "duration": 33673}}]}, "4b3ad3bdf0873599e48d8f20d70246c9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a7975b187defba53", "status": "passed", "time": {"start": 1756789901320, "stop": 1756789923780, "duration": 22460}}]}, "cb21afc40e4aec32b847936756c8ba6e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2def690b31b4a7f4", "status": "passed", "time": {"start": 1756783847748, "stop": 1756783870607, "duration": 22859}}]}, "cf3df9e3e259f083301aa2ec640729fe": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "340e44219094e4e0", "status": "passed", "time": {"start": 1756783473804, "stop": 1756783494209, "duration": 20405}}]}, "dbd7a7f96e1740fa05f50ed6fa7becfb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "172582fa9d3d0cb0", "status": "passed", "time": {"start": 1756803069221, "stop": 1756803101951, "duration": 32730}}]}, "4bda544c08fa4bc5494c7dda01d4cc77": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7db7ddb5bf563877", "status": "passed", "time": {"start": 1756803914233, "stop": 1756803938378, "duration": 24145}}]}, "eb6abc860fad339739076abacb13ac83": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1b1025afaf571d54", "status": "passed", "time": {"start": 1756797398637, "stop": 1756797420844, "duration": 22207}}]}, "217ee9f7b3be9f625903076716d45106": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d7963a966421cd68", "status": "passed", "time": {"start": 1756800346065, "stop": 1756800368925, "duration": 22860}}]}, "8279fa7d1223eafd72f332d022439d6e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "819528b402a0bb32", "status": "failed", "statusDetails": "Failed: 响应中未找到任何期望关键词: ['following number is recognized', 'save it as a contact']", "time": {"start": 1756793455689, "stop": 1756793489180, "duration": 33491}}]}, "0a0a3640b2ba4adce516043bd9362070": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "83e7ea6af17c7aa5", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore <PERSON>wi<PERSON> down to view earlier chats 11:19 am <PERSON>, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh USA Collapses in AmeriCup Semis Design a fun coding game Send my recent photo to mom on WhatsApp DeepSeek-R1 Feel free to ask me any questions…\", '[com.transsion.phonemaster页面] 未获取到文本内容']'\nassert False", "time": {"start": 1756783156637, "stop": 1756783184599, "duration": 27962}}]}, "87f3dc53ab72c729262e053c16a3dbcb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "687bbbe1f23f5a55", "status": "passed", "time": {"start": 1756782944839, "stop": 1756782965520, "duration": 20681}}]}, "540cff5d6d552c22ec37f66efd17315f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "21fddf3398a643e6", "status": "passed", "time": {"start": 1756799166379, "stop": 1756799198151, "duration": 31772}}]}, "35c27c2a65b1189393c74d916f6ae378": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "13b13e7d7a5f8136", "status": "passed", "time": {"start": 1756793735925, "stop": 1756793770832, "duration": 34907}}]}, "d4ded95517fa8a5af49f09554cc49725": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dd19b36a00d24f4a", "status": "passed", "time": {"start": 1756796955134, "stop": 1756796979097, "duration": 23963}}]}, "b9fc05e613fdd4d145434e9cf6378c4b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "68461f73317f8741", "status": "passed", "time": {"start": 1756787273934, "stop": 1756787319086, "duration": 45152}}]}, "6075008522e5d0ae1667c4ac4be759eb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "21a57458a1be039e", "status": "passed", "time": {"start": 1756783932607, "stop": 1756783969015, "duration": 36408}}]}, "400a9b197316d3b1e59fe33ed78a836a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "792904db28422b23", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['can u check the notebook', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756795411949, "stop": 1756795439140, "duration": 27191}}]}, "3215de286c6ddd59d6e52a44f2a9967d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7415c803928ec4eb", "status": "passed", "time": {"start": 1756796868504, "stop": 1756796891666, "duration": 23162}}]}, "0e7fd56ff1d5c85e0ce1830d9899313d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b875b96724b58334", "status": "passed", "time": {"start": 1756786286433, "stop": 1756786310288, "duration": 23855}}]}, "657acdf17dda1a11abf6946763f6ed52": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8fd2bdd257530c60", "status": "passed", "time": {"start": 1756797156705, "stop": 1756797179424, "duration": 22719}}]}, "104cf8a7ef102b6850b6d14f4cb14052": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f78b89d6a84d1c9c", "status": "passed", "time": {"start": 1756801688821, "stop": 1756801711480, "duration": 22659}}]}, "a8ceb9cec2faa4662cde95140569091b": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "99642d07dcf70445", "status": "failed", "statusDetails": "AssertionError: visha: 初始=False, 最终=False, 响应='['play love sotry', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.google.android.youtube页面内容] Shorts | Subscriptions | Library']'\nassert False", "time": {"start": 1756800483613, "stop": 1756800526458, "duration": 42845}}]}, "1bc9389e45f0f75c30d3dfb39134948d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b4f5300d13abdbd0", "status": "passed", "time": {"start": 1756802594793, "stop": 1756802628100, "duration": 33307}}]}, "ebd2c9b6cd7c07b69e348328a207e18b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7f314554ae49e6a6", "status": "passed", "time": {"start": 1756792225762, "stop": 1756792246393, "duration": 20631}}]}, "c9c4c38d0ca341040a41178716623909": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1d0ee1fc9eb1b558", "status": "passed", "time": {"start": 1756789827374, "stop": 1756789848368, "duration": 20994}}]}, "123d65cc114fff79e459e14acfbcd445": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cdd1296da8bb57d1", "status": "passed", "time": {"start": 1756792548655, "stop": 1756792570365, "duration": 21710}}]}, "92c8c8e017b096314ffde2f610a6791e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "44f7f2db4d16a214", "status": "passed", "time": {"start": 1756790800703, "stop": 1756790828767, "duration": 28064}}]}, "0231bab2d0366f671b068e02756c1a71": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4f7fe804b4821ade", "status": "passed", "time": {"start": 1756793775978, "stop": 1756793810005, "duration": 34027}}]}, "c046a1c6e6cc8effa10641e329b1cfad": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "94efa2ff672aa2e", "status": "passed", "time": {"start": 1756802928649, "stop": 1756802953409, "duration": 24760}}]}, "459c099a876d1129ddcb7cb28663b756": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b297a80a5ae2fcb4", "status": "passed", "time": {"start": 1756789598757, "stop": 1756789622085, "duration": 23328}}]}, "6f2c4144233271771cdd01a5c48ea3ca": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6ebf6c0ca43d20eb", "status": "passed", "time": {"start": 1756794302665, "stop": 1756794327607, "duration": 24942}}]}, "70b49b2a6719030c7c51e642fdaec270": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d4aea90612e8f58e", "status": "passed", "time": {"start": 1756786909154, "stop": 1756786932079, "duration": 22925}}]}, "4f84a6588e41dde581e4eef4fccd6344": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e304f48b87cce60b", "status": "passed", "time": {"start": 1756793384033, "stop": 1756793411163, "duration": 27130}}]}, "d60fcab377d9b5093e0f03ecf20f5d10": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4d2bb9ea5aabddff", "status": "passed", "time": {"start": 1756785132322, "stop": 1756785156930, "duration": 24608}}]}, "6980dbcce9a72cd9dea6dee04c6891de": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4aa5bda8be8eae92", "status": "passed", "time": {"start": 1756797932403, "stop": 1756797956531, "duration": 24128}}]}, "7acb737855a3a3110ed556a3e5fe1256": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "19661fd537284e83", "status": "failed", "statusDetails": "AssertionError: 初始=False, 最终=False, 响应='['start record', 'Screen recording started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756791110519, "stop": 1756791138174, "duration": 27655}}]}, "99709ca7d9951f6f7049b49ea81d0cd1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d1ebb5532b030e39", "status": "passed", "time": {"start": 1756790412045, "stop": 1756790433483, "duration": 21438}}]}, "44720253edec52ccf0868b33f1938265": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dd3f997588ce2f0c", "status": "passed", "time": {"start": 1756803833651, "stop": 1756803858595, "duration": 24944}}]}, "b89775573784e6ef95769309baebeae4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9140a5f89777e7be", "status": "passed", "time": {"start": 1756794344227, "stop": 1756794371702, "duration": 27475}}]}, "876e77318cece5d1079b726f0c97bc45": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "25d9486b74a6bc6", "status": "passed", "time": {"start": 1756784895037, "stop": 1756784917606, "duration": 22569}}]}, "3333dd58fd9312a504ae6bc6edf830af": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5bd17856a6066ce", "status": "passed", "time": {"start": 1756787925066, "stop": 1756787947951, "duration": 22885}}]}, "b5e1711cce3102fc710ff74e18bf9129": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "18613e3daf29d865", "status": "passed", "time": {"start": 1756802972315, "stop": 1756803002362, "duration": 30047}}]}, "19df0c79ab8c9ce909771e1a9d21fed3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ec6b785228d444c0", "status": "passed", "time": {"start": 1756804115142, "stop": 1756804144722, "duration": 29580}}]}, "b6eee20d1fad16a048ce8490d7189be4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "49d68d24e1e6900a", "status": "passed", "time": {"start": 1756795320815, "stop": 1756795353100, "duration": 32285}}]}, "7b90ee3ed7bdd2837a37215aac61cfd9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c404aeb3e1a0fb20", "status": "passed", "time": {"start": 1756793231172, "stop": 1756793254107, "duration": 22935}}]}, "18377608d977aa5cb5e2fc6b03b9ad05": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "326add5a5b34a951", "status": "passed", "time": {"start": 1756799391592, "stop": 1756799416265, "duration": 24673}}]}, "3a27fef360a79f638f96f0461df262da": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a74eae25b64e331c", "status": "passed", "time": {"start": 1756801860479, "stop": 1756801884477, "duration": 23998}}]}, "2428ad915810150c12838b88ee13f49c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "af7d50665a230f0c", "status": "passed", "time": {"start": 1756785848657, "stop": 1756785870248, "duration": 21591}}]}, "b8a3cad490db8fccbe1d65628faf6743": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a8889537cba2d1b6", "status": "passed", "time": {"start": 1756799872927, "stop": 1756799897500, "duration": 24573}}]}, "563b9c2c8d2e1d68901eeac733e12913": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "45ba6042bf3a7373", "status": "passed", "time": {"start": 1756789752104, "stop": 1756789772809, "duration": 20705}}]}, "d4f6d299fdb0e6fd724a5069e0b36670": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4dd93d1860a41a54", "status": "passed", "time": {"start": 1756793656096, "stop": 1756793690972, "duration": 34876}}]}, "9511be8e6426d5078713c6e78f3b02e3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ac2a6bb20ec61f6", "status": "passed", "time": {"start": 1756790333899, "stop": 1756790356361, "duration": 22462}}]}, "fc75b92fb4a100575b2c948dd6c5a008": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a59510a395d3fcc5", "status": "passed", "time": {"start": 1756797238800, "stop": 1756797260675, "duration": 21875}}]}, "ae86b8d534909e1e7c8c7adb4ee39e5c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e88b1b31f0ce5f9f", "status": "passed", "time": {"start": 1756792779187, "stop": 1756792801680, "duration": 22493}}]}, "fa47cb0b4427dd62fb8f91c9e5e15ace": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a1a5c9d053ad235d", "status": "passed", "time": {"start": 1756804389918, "stop": 1756804415854, "duration": 25936}}]}, "57acf2797af332487c1fdb9a53a30e4f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c8dc9f7316b76947", "status": "passed", "time": {"start": 1756796996238, "stop": 1756797019491, "duration": 23253}}]}, "b7bfa1ba094155307273abc83c43a0d5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "19ae44f5cafb8a3a", "status": "passed", "time": {"start": 1756786079262, "stop": 1756786101475, "duration": 22213}}]}, "a416a85ec867e3b2cfd6e23150d72859": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a4ca1393cb6ef1c7", "status": "passed", "time": {"start": 1756796162331, "stop": 1756796189595, "duration": 27264}}]}, "c0d6ce0b7c5e41242c01a6e0c0186608": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ed7c80d2c53bd6ea", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含[\"I'm <PERSON>\", 'assistant']，实际响应: '[\"what's your name？\", '', '', '', \"My name is <PERSON>. I'm here to help!\", 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', \"Dialogue Explore <PERSON>wipe down to view earlier chats Send my recent photo to mom on WhatsApp Real Madrid, Athletic Club Lead La Liga How's the weather today? what's your name？ My name is <PERSON>. I'm here to help! Generated by AI, for reference only Do you have a last name? Are you a real person? <PERSON> is a helpful AI. DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "time": {"start": 1756787675291, "stop": 1756787699652, "duration": 24361}}]}, "643a7bbfbf5c5eedbae7ae814fbc8b52": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e3ab5f59f673c05a", "status": "passed", "time": {"start": 1756791806885, "stop": 1756791828252, "duration": 21367}}]}, "3dae350db69abded432b3e7f5f8463c8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7ab2f693890d19f2", "status": "passed", "time": {"start": 1756787233084, "stop": 1756787257461, "duration": 24377}}]}, "44c9275711c93730c8d2cb2a7374b3cd": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "3f9cca40aeaa1252", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Who would you like to call']，实际响应: '['', '', '', '', '', '', 'Intelligent Customer Service', '1. Please check whether phone is connected for all number or specific number, if only for specific number, maybe blacklist related.\\n\\n2. Check whether SIM card signal is good or not , try other places with better network.\\n\\n3. Check whether it is because the SIM card not have sufficient balance or not installed properly,try another SIM card to verify.\\n\\n4. The phone is in Airplane mode, please turn off Airplane mode.\\n\\n5. Call barring is set for the phone, please cancel the call barring for the phone.\\n\\n6. The PIN code of SIM card is locked, please use the PUK code of SIM card to unlock the PIN code (the PUK code can be obtained by providing the carrier with the SIM card service code).\\n\\n7. SIM card loose, clean and reassemble the SIM card or try another SIM card with sufficient balance.\\n\\n8. The phone serial number (IMEI number. is lost, On dial screen, enter \"#06#\" to view the phone serial number (IMEI number), If the serial number is lost, bring the phone to a local service center.\\n\\n9. The surrounding environment interferences, weak signal, or signal blind spots, try other place with better signal.\\n\\n10. Phone firmware is faulty, please bring the phone to a service center to fix the software, or download the corresponding firmware from official website and update the phone with it.\\n\\n11. Try to reset factory settings, but remember to back up important data.\\n\\n12. Visit the nearest Carlcare for further check.', '', '', '', 'Dialogue', 'Dialogue Explore Swipe down to view earlier chats Intelligent Customer Service DeepSeek-R1 Feel free to ask me any questions…']'\nassert False", "time": {"start": 1756796206277, "stop": 1756796231783, "duration": 25506}}]}, "92e2909ea81e82011e43342b4fc06c3b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "79030eba7f2c9c83", "status": "passed", "time": {"start": 1756801728777, "stop": 1756801752858, "duration": 24081}}]}, "772728b3468560788490a3673352724d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f54bc623999fcd83", "status": "passed", "time": {"start": 1756787189128, "stop": 1756787216843, "duration": 27715}}]}, "4933b925ec694ecfcd17b3423ac28184": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "76e8ba8db8add581", "status": "passed", "time": {"start": 1756794631363, "stop": 1756794661004, "duration": 29641}}]}, "3d685d9ca6a0d7795be3c96921595318": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "be21ff0ce66eb57", "status": "passed", "time": {"start": 1756796534524, "stop": 1756796558309, "duration": 23785}}]}, "28f9087c186df37701fba71f366c084e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "95cd6432fa9350e1", "status": "passed", "time": {"start": 1756793347498, "stop": 1756793368013, "duration": 20515}}]}, "bb51fd67dac102de95e755be72996bd1": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "db15740264104bd1", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['disable running lock', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "time": {"start": 1756796574959, "stop": 1756796601705, "duration": 26746}}]}, "9458f45d3c37d9141658da9964a470f5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2a9eae3b67eaa544", "status": "passed", "time": {"start": 1756801427628, "stop": 1756801459475, "duration": 31847}}]}, "7d8296483e3dfc0902b42ccfe6759e59": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e5d28b27c08c67cc", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['set nfc tag', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "time": {"start": 1756802264583, "stop": 1756802288977, "duration": 24394}}]}, "51b4b46de04a8c1e37077a9f688cb490": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "67ea5b5f5d29a251", "status": "failed", "statusDetails": "AssertionError: whatsapp: 初始=False, 最终=False, 响应='['open whatsapp', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[android页面内容] Dual App | Enable dual app, you can use dual WhatsApp simultaneously.']'\nassert False", "time": {"start": 1756800218392, "stop": 1756800247122, "duration": 28730}}]}, "286e9cba8578d73b1f445f9d6d3a7d2e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2467f4ed3132cab6", "status": "passed", "time": {"start": 1756795142475, "stop": 1756795168550, "duration": 26075}}]}, "629e60713979f085f1805341fb36622f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1efafebd46dc3b15", "status": "passed", "time": {"start": 1756794045342, "stop": 1756794080415, "duration": 35073}}]}, "4fd50eb7a7e49fc09f612442a33e3010": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "bd1a3e9017b60b1c", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['call number by whatsapp', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756795369882, "stop": 1756795394944, "duration": 25062}}]}, "3e1e4da6344de7cdf40fa1d59c43dcc3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6b3eee10e2840156", "status": "passed", "time": {"start": 1756787067617, "stop": 1756787090469, "duration": 22852}}]}, "59f89eb284ef9300c926c5e2f1d3fd26": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7b0676edc44ad6a8", "status": "passed", "time": {"start": 1756790957327, "stop": 1756790979496, "duration": 22169}}]}, "02dda06829926b51f170419357629e86": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e322afecbaae686c", "status": "passed", "time": {"start": 1756797717516, "stop": 1756797742601, "duration": 25085}}]}, "4072ba85d37e03a8ef0a5dd9d0741631": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8f9ad28725471f8", "status": "passed", "time": {"start": 1756790218933, "stop": 1756790239986, "duration": 21053}}]}, "18415b75388fbfdac9a7e4232373c000": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "df5cb6fa8a521be5", "status": "passed", "time": {"start": 1756785273643, "stop": 1756785300926, "duration": 27283}}]}, "3bafa6d8eb5b49bc5b77f1784275285e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "eb168c41208d84e4", "status": "passed", "time": {"start": 1756791352832, "stop": 1756791403014, "duration": 50182}}]}, "51c103053dd0c5596d9f4d9f178c3d8d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5c3025c80d4a1bae", "status": "passed", "time": {"start": 1756795185235, "stop": 1756795213718, "duration": 28483}}]}, "178c119ddfd51f19a22377df428e3fc1": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "4a70e0f79e8298a2", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['OK, the voice is switched. You can also select other voices.']，实际响应: '['change man voice', '', '', '', '', '', '', '', '', '', '', 'Dialogue', 'Dialogue Explore Swipe down to view earlier chats Close WhatsApp Convert image to Word document change man voice The following images are generated for you. Generated by AI, for reference only Exit AI Image Generator DeepSeek-R1 Describe the image you want to generate']'\nassert False", "time": {"start": 1756795500845, "stop": 1756795535211, "duration": 34366}}]}, "a0efcebc4cee6024e690bd290b4f3fbb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2e017045f238939e", "status": "passed", "time": {"start": 1756803458510, "stop": 1756803483038, "duration": 24528}}]}, "12eb3852c333145c5906579f2346c37a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3a70a41434693d91", "status": "passed", "time": {"start": 1756785217254, "stop": 1756785257561, "duration": 40307}}]}, "3d6cfc87445d1bd76dceee439d00b3d6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d7bf4937d6857b76", "status": "passed", "time": {"start": 1756785612556, "stop": 1756785634414, "duration": 21858}}]}, "75c9edd252211f9e74fd8c1a2faeefd1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "72e7efc6f08f8afb", "status": "passed", "time": {"start": 1756790539654, "stop": 1756790567839, "duration": 28185}}]}, "44e27936b56f219f63af671a8fd7f5fb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "340f98f8392633ad", "status": "passed", "time": {"start": 1756804214313, "stop": 1756804238789, "duration": 24476}}]}, "4ae696581fe41611547bc10ddba4f526": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "aa4f63f89a01889f", "status": "passed", "time": {"start": 1756785689138, "stop": 1756785714039, "duration": 24901}}]}, "20bd2e7c8179ca1901bc63c9a02b9ee1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8de7abdbc745f30c", "status": "passed", "time": {"start": 1756797844758, "stop": 1756797871105, "duration": 26347}}]}, "5c75e7ecaa2fe55a9b9666aae0ca1b5a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a5752d89d752af8", "status": "passed", "time": {"start": 1756803201148, "stop": 1756803229658, "duration": 28510}}]}, "8bcc4c0c2b314e79a7177168f7d787b8": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9c9e0442ee4b9f09", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The carrier for SIM 1 is not supported']，实际响应: '['check my balance of sim1', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756795816023, "stop": 1756795838812, "duration": 22789}}]}, "5697ea2b6f1cefef94d5b32e213e05a7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dca32016d3c610bc", "status": "passed", "time": {"start": 1756788632641, "stop": 1756788742548, "duration": 109907}}]}, "c121335a9bce64dd570aaf5b221b21df": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1abe5eed53f3535f", "status": "passed", "time": {"start": 1756801082763, "stop": 1756801106256, "duration": 23493}}]}, "56a09613cdb882018377e1c2c4e78472": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "78a6c4053c880c26", "status": "passed", "time": {"start": 1756787802282, "stop": 1756787827315, "duration": 25033}}]}, "9445ebf64ec65c769332fec8dd505332": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3a1711dee8170663", "status": "passed", "time": {"start": 1756803021158, "stop": 1756803050683, "duration": 29525}}]}, "bd4d204a449f3a4013b03af9a9101446": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "10b95a85115cb321", "status": "passed", "time": {"start": 1756785443641, "stop": 1756785473745, "duration": 30104}}]}, "a306f4c2244f596f1ab838aa7a80dd45": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dffd198f3f8e4f26", "status": "passed", "time": {"start": 1756795057326, "stop": 1756795082883, "duration": 25557}}]}, "e8f03971277a71512b5ebaad612bc964": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d5fa055e581c91c3", "status": "passed", "time": {"start": 1756794487628, "stop": 1756794518998, "duration": 31370}}]}, "75c56947a7b1061f7ec858fb20919b50": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d2a8b119b45f366d", "status": "passed", "time": {"start": 1756794584276, "stop": 1756794614377, "duration": 30101}}]}, "c45251d74b80bdc9105dde9b444ef1c7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "23fd4d7c6b8262", "status": "passed", "time": {"start": 1756795993238, "stop": 1756796020951, "duration": 27713}}]}, "88a6294df5f2ab484e181b1f196ff253": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "59adf45f70cb634f", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['jump to nfc settings', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "time": {"start": 1756799302293, "stop": 1756799327074, "duration": 24781}}]}, "a4af452e0448ec3c1ecc9afcc30459be": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "54c50d9dbaf270de", "status": "passed", "time": {"start": 1756791419217, "stop": 1756791439998, "duration": 20781}}]}, "0eb27e9cfa9ed24b7ee5e6be6e495cb7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "56e85e9a688e148", "status": "passed", "time": {"start": 1756784030802, "stop": 1756784050900, "duration": 20098}}]}, "5f09c86ae320138eb73de73a25f73607": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2f8b77554f437d5c", "status": "passed", "time": {"start": 1756798294174, "stop": 1756798317218, "duration": 23044}}]}, "61e923bb9b35a687b231b0c27b5ec620": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e83b9edda17d4dac", "status": "passed", "time": {"start": 1756802837176, "stop": 1756802861114, "duration": 23938}}]}, "9c301cfc137fb94f119957b5f74291ec": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "562439685ca70ccb", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['sorry']，实际响应: '['Voice setting page', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Accessibility | Downloaded apps | Ella | Hi Translate | ScrollCaptureAccessibilityService | Select to Speak | Hear selected text | Smart Translate | TalkBack | Speak items on screen | Display | Text and Display | Extra dim | Dim screen beyond your phone’s minimum brightness | Magnification | Zoom in on the screen | Interaction controls | Accessibility Menu']'\nassert False", "time": {"start": 1756804161648, "stop": 1756804196229, "duration": 34581}}]}, "91f19453d2c7e6d363b99298706ba47d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1c689ca816a0996a", "status": "passed", "time": {"start": 1756794149922, "stop": 1756794185616, "duration": 35694}}]}, "caccad19499f4bc8494953ac84d8d23c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "be59cc9d8c4f9334", "status": "passed", "time": {"start": 1756788067685, "stop": 1756788178402, "duration": 110717}}]}, "929b39cceeb7a01f602573951f5fef39": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8fd725f62c36ef10", "status": "passed", "time": {"start": 1756804346857, "stop": 1756804372157, "duration": 25300}}]}, "e865942f74e70950eccebd8243dd6035": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bfa07e72898e462", "status": "passed", "time": {"start": 1756787335445, "stop": 1756787365973, "duration": 30528}}]}, "776dca6a65836abb5a943543ee2b9f12": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f87550b012cae7bb", "status": "passed", "time": {"start": 1756804557413, "stop": 1756804580478, "duration": 23065}}]}, "e78aa9affdc78502b8ad3b712ecf28b9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ded9a646b383116a", "status": "passed", "time": {"start": 1756789713894, "stop": 1756789736385, "duration": 22491}}]}, "ffb0a39af30beaa699329479ec564117": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d5ea57909850627e", "status": "passed", "time": {"start": 1756783749054, "stop": 1756783786399, "duration": 37345}}]}, "356db57bafcf61266d2f62fd1b8ab4e2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2b65bd09348a3a89", "status": "passed", "time": {"start": 1756795099484, "stop": 1756795125657, "duration": 26173}}]}, "0c44c94f08feed70addcec44e96bda5a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "884af117c9f1e64a", "status": "passed", "time": {"start": 1756787507248, "stop": 1756787529170, "duration": 21922}}]}, "9c84b087eb7d9fde94ed5bb5370b275b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5f0eb9a971cef95e", "status": "passed", "time": {"start": 1756799781600, "stop": 1756799813705, "duration": 32105}}]}, "613ef0933e4be87696bbedd56b4f0052": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5b2932b7514c2b19", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['check battery information', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756795597498, "stop": 1756795620460, "duration": 22962}}]}, "ffd7dc86cbeda13ca78bbca09f06422a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9c48b2cfb1a058ed", "status": "passed", "time": {"start": 1756789227818, "stop": 1756789282715, "duration": 54897}}]}, "17788b061637289a04732fe5840218ee": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f25f9b79b50dcf3f", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only']，实际响应: '['record audio for 5 seconds', 'Screen recording started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756784066621, "stop": 1756784087815, "duration": 21194}}]}, "1ca8d9c300d55584cdcf637ede08bdba": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d076ff363a3cd688", "status": "passed", "time": {"start": 1756796416817, "stop": 1756796439828, "duration": 23011}}]}, "1d15cba90ae0426fa12e3218f1c542a6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6480a2d6059a8062", "status": "passed", "time": {"start": 1756783200668, "stop": 1756783221540, "duration": 20872}}]}, "0b659537bc9c9b47c2c23f702fadd56b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5c6dde382e009f56", "status": "passed", "time": {"start": 1756796618926, "stop": 1756796641361, "duration": 22435}}]}, "eadc304b3069d4918c06805d847a62d7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8c583f460c46534a", "status": "passed", "time": {"start": 1756798253741, "stop": 1756798277015, "duration": 23274}}]}, "b7e448432379b6f8a430f1cbdb3ee3fb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4b0ca3a6abd6b339", "status": "passed", "time": {"start": 1756786716846, "stop": 1756786742170, "duration": 25324}}]}, "0c4bd81bf0dbac094265e3ac47550bbd": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3f2448227e5c6448", "status": "passed", "time": {"start": 1756787466410, "stop": 1756787490849, "duration": 24439}}]}, "b4e75f584d82368436f820de28f92cfd": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e41046bfd275ea61", "status": "passed", "time": {"start": 1756783801827, "stop": 1756783832526, "duration": 30699}}]}, "753ba105235625e906d023bd3aaa0821": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2187e2e4c8fe246e", "status": "passed", "time": {"start": 1756791732887, "stop": 1756791753117, "duration": 20230}}]}, "ab2195315637668cad08b0606ef7ff17": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a1c7d384ccd7b13c", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Multiple settings options found']，实际响应: '['jump to auto rotate screen settings', 'Which feature should I turn on?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756799032913, "stop": 1756799056135, "duration": 23222}}]}, "794f685415bbcd702feae0b55a4dd537": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1323d7692a2d53a", "status": "passed", "time": {"start": 1756786948780, "stop": 1756786971565, "duration": 22785}}]}, "7b1fce8b7d3ff59dca969b439bb82f75": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d29d064c93a63c", "status": "passed", "time": {"start": 1756784691763, "stop": 1756784716011, "duration": 24248}}]}, "48a2a80bfed06f0c82b99a0aaa26e252": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d1ad71cdc5fcae58", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['jump to high brightness mode settings', 'Which feature should I turn on?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756799215340, "stop": 1756799237217, "duration": 21877}}]}, "b5cd1ef67868d05c6583ed1669e6e522": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b281197d9ecaa7f0", "status": "passed", "time": {"start": 1756794085788, "stop": 1756794145009, "duration": 59221}}]}, "f09a8375806e200073a99f1cbabdc35c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "588d065cdd20825e", "status": "passed", "time": {"start": 1756783668536, "stop": 1756783688728, "duration": 20192}}]}, "1ab60ce22774c460e557aaa3b3f9120a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c36acb37010eceeb", "status": "failed", "statusDetails": "AssertionError: 初始=False, 最终=False, 响应='['pause screen recording', 'Screen recording on hold.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756791198151, "stop": 1756791226019, "duration": 27868}}]}, "aecf9a6f67cd29766190cbcc133448d2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3b21060cf25c173a", "status": "passed", "time": {"start": 1756801607391, "stop": 1756801631387, "duration": 23996}}]}, "ffd9b4e8a27f1469e05050bd5989e500": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1929379ff1f65509", "status": "passed", "time": {"start": 1756791695041, "stop": 1756791716309, "duration": 21268}}]}, "9420fd606a04614c6f09bf36f2873f93": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6554ee4b5633e9ad", "status": "passed", "time": {"start": 1756791493357, "stop": 1756791514506, "duration": 21149}}]}, "1147a84f37b71eb5b15008169cadcc53": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8b2d8b53a744bc7c", "status": "passed", "time": {"start": 1756786805458, "stop": 1756786851333, "duration": 45875}}]}, "e9039096aff36ba6e4fae58e40eb8539": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3ca32f3802ea747e", "status": "passed", "time": {"start": 1756800545394, "stop": 1756800584374, "duration": 38980}}]}, "57c5370b1a13de534ed16f0ce2ee85b9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6a24dcf737533d80", "status": "passed", "time": {"start": 1756792051436, "stop": 1756792072889, "duration": 21453}}]}, "c052c8813edd9c2261dc1bcc29786fe9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "76d81c530b692cb8", "status": "passed", "time": {"start": 1756803246437, "stop": 1756803269249, "duration": 22812}}]}, "bfddb3863bb9971cace5dae92df6977d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c0cc11222ef89950", "status": "passed", "time": {"start": 1756798461807, "stop": 1756798489744, "duration": 27937}}]}, "e56b7788214bc4e18231f16dfd713954": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fa36a673ebbfe035", "status": "passed", "time": {"start": 1756784300586, "stop": 1756784388849, "duration": 88263}}]}, "154a720f41d8f5a908552e8c7cf8e781": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "61fb5854406af80", "status": "passed", "time": {"start": 1756801648413, "stop": 1756801671415, "duration": 23002}}]}, "afa6af304cfb25a990764680de5fa777": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5bcd7d27e10e3236", "status": "passed", "time": {"start": 1756797277751, "stop": 1756797302984, "duration": 25233}}]}, "fcabf101e08450542157f8740eeec9a7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d47c142eef7a2982", "status": "passed", "time": {"start": 1756794678039, "stop": 1756794706066, "duration": 28027}}]}, "d094a0b21c0bd532e6db707dcbab5564": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bb316715992c8d02", "status": "passed", "time": {"start": 1756796700410, "stop": 1756796723597, "duration": 23187}}]}, "6ecc7e0fc961d0d4e7e46672c033625a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bd3a290a28dd8d2d", "status": "passed", "time": {"start": 1756785571644, "stop": 1756785596581, "duration": 24937}}]}, "5050d8dc816d181ca0c76dc56c8cb5f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "39e11b6cd927f209", "status": "passed", "time": {"start": 1756798776017, "stop": 1756798798694, "duration": 22677}}]}, "4276e587385154206726240ad06acd24": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ac2cf3846fc7c62d", "status": "passed", "time": {"start": 1756799120223, "stop": 1756799149218, "duration": 28995}}]}, "8acd3c85f9c9d0b7f252da4466c049e6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "aa9e50a8deaf5d9f", "status": "passed", "time": {"start": 1756790449355, "stop": 1756790478237, "duration": 28882}}]}, "00ae864f57f2146374ff8bf301b9b8af": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a5f4760952a69867", "status": "passed", "time": {"start": 1756797801547, "stop": 1756797828102, "duration": 26555}}]}, "a297156fb1e7af53c032ac3c6277feee": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8ec1f9a543133b16", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['today', 'The high is forecast as', '℃']，实际响应: '['what is the weather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756804256271, "stop": 1756804287558, "duration": 31287}}]}, "7a670647c2336e6a5a5d07824fe89da6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ac63939ab58a92af", "status": "passed", "time": {"start": 1756797320212, "stop": 1756797342441, "duration": 22229}}]}, "ce2018f4cca8041a465d2a753ade920b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f0fbf3dfa5252821", "status": "passed", "time": {"start": 1756788882646, "stop": 1756788905574, "duration": 22928}}]}, "098126ed77f375b3e0f5370b3ec7d0b7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7cbe0c58d8f729b", "status": "passed", "time": {"start": 1756784404787, "stop": 1756784489893, "duration": 85106}}]}, "76a63bd8a63882a3a1ada32e63f1c971": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "789f25ea20164cc9", "status": "passed", "time": {"start": 1756798902247, "stop": 1756798927259, "duration": 25012}}]}, "a0ea006ce61aacded2720f8d2a03ba5b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b011767611ee9cbe", "status": "passed", "time": {"start": 1756785770913, "stop": 1756785794521, "duration": 23608}}]}, "8971377f4371d1ea3384cde4ed276db1": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "bd42bab8bf767045", "status": "failed", "statusDetails": "AssertionError: 初始=48, 最终=88, 响应='['turn on brightness to 80', 'Brightness is at 80% now.', '', '', '', '', '', '', '', '', '']'\nassert 88 == 171", "time": {"start": 1756792663531, "stop": 1756792685716, "duration": 22185}}]}, "6748f677dff755a4da95c520c3f05506": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fb0f70d6750cab8f", "status": "passed", "time": {"start": 1756784817685, "stop": 1756784838777, "duration": 21092}}]}, "0ce2a3efa79db58c34a5590015948f51": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "771c27105ec44756", "status": "passed", "time": {"start": 1756797888205, "stop": 1756797916176, "duration": 27971}}]}, "e76af38ac3a594aa2b7d7173d57e98ad": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "923017adfaf4bb29", "status": "passed", "time": {"start": 1756802685384, "stop": 1756802718089, "duration": 32705}}]}, "2d2a77f9c5991a2193814340334cf247": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e9ce9b50d3cbc6f9", "status": "passed", "time": {"start": 1756793963627, "stop": 1756793998997, "duration": 35370}}]}, "7cd08c87d5de8ec73ac863e8a636c8aa": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ec6a759d2680a807", "status": "passed", "time": {"start": 1756790256219, "stop": 1756790277776, "duration": 21557}}]}, "11875095b9997cfc7edbe407c3074b7e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7d24befde98bebcc", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['check ram information', \"You've reached the image generation limit for today.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756795904173, "stop": 1756795926507, "duration": 22334}}]}, "d6ad2be1232377f5942b2d4f816b2e71": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "86cf3982a8e9d463", "status": "passed", "time": {"start": 1756792856063, "stop": 1756792879789, "duration": 23726}}]}, "063471c2e7f2d00ecd08e780860e0cf2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d5db3da844ab4b1c", "status": "passed", "time": {"start": 1756791844457, "stop": 1756791865995, "duration": 21538}}]}, "464c8ea2a15f7ff86b8e0a347a821945": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "770c79cb377aa8fa", "status": "passed", "time": {"start": 1756783631531, "stop": 1756783652296, "duration": 20765}}]}, "94acf463e3e6d87a1e9cf7ff754044a2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bb7b34760b482926", "status": "passed", "time": {"start": 1756799830870, "stop": 1756799855938, "duration": 25068}}]}, "d304f4e805a15a0351109cc931c26ffc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cfc2b0b0313517b6", "status": "passed", "time": {"start": 1756786458235, "stop": 1756786480142, "duration": 21907}}]}, "f4d12b1367b35df96178a58e48fe8f5e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8f4486399f2f43b", "status": "passed", "time": {"start": 1756783113791, "stop": 1756783140848, "duration": 27057}}]}, "dee08db8cb0f1293bf864f56326992d5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8418a814d2f54251", "status": "passed", "time": {"start": 1756785091617, "stop": 1756785116395, "duration": 24778}}]}, "34f3c9cc9098f792051e7099b7a9fdc1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "504b02b71b4eb7df", "status": "passed", "time": {"start": 1756803417329, "stop": 1756803441396, "duration": 24067}}]}, "578e52c6d5e868d5464682b454971c51": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "370a73bb3c230368", "status": "passed", "time": {"start": 1756794252123, "stop": 1756794286272, "duration": 34149}}]}, "306cbf11cdbcb045eb3c3c716515b1d6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4705c02e199ef313", "status": "passed", "time": {"start": 1756798417454, "stop": 1756798445073, "duration": 27619}}]}, "ed94d8d97b63a623a1f6438b57774ff1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a9e3f0c51bc1364", "status": "passed", "time": {"start": 1756800435359, "stop": 1756800464433, "duration": 29074}}]}, "c5050ea089fe0f7a5b962119cd32b32e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5d899eb4a5cb81e", "status": "passed", "time": {"start": 1756784654317, "stop": 1756784675971, "duration": 21654}}]}, "f5346ff0fa4cb76e4b6ceea6116693ee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "efedcfdc8f1999a4", "status": "passed", "time": {"start": 1756786039977, "stop": 1756786063372, "duration": 23395}}]}, "8f69a86b2d665eb6925fa007d973040e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9d2a34638cb2122d", "status": "passed", "time": {"start": 1756789864162, "stop": 1756789885705, "duration": 21543}}]}, "5fe7611f5b7d3ef438ce938b66e0b99f": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "3381659781da7319", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Screen recording started']，实际响应: '['start screen recording', 'Screen recording is already started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756791154256, "stop": 1756791181812, "duration": 27556}}]}, "5ad5ef8bf0f7913e709dbc1e706db1e5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ec27ec50031f0767", "status": "passed", "time": {"start": 1756803629971, "stop": 1756803654357, "duration": 24386}}]}, "79ed3b173757e627534e24ad9289f338": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cc36938971871f0c", "status": "passed", "time": {"start": 1756804517187, "stop": 1756804539729, "duration": 22542}}]}, "8d12bedb52d3f000f4269afc25f3fe30": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9f56622ca38b4d9a", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['what·s the weather today？', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756787591318, "stop": 1756787619867, "duration": 28549}}]}, "2bf170e8c0013ab361afb23f8f059db8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8e22c345ae86a3b", "status": "passed", "time": {"start": 1756797076221, "stop": 1756797098832, "duration": 22611}}]}, "b911308f3c1fe764715d778a884946c2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5702e1d7d4ee53ce", "status": "passed", "time": {"start": 1756804598120, "stop": 1756804620721, "duration": 22601}}]}, "2ecac23eb3f511651fafc6ba6a3725f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bb1b20068527f996", "status": "passed", "time": {"start": 1756792005920, "stop": 1756792034949, "duration": 29029}}]}, "29293108cad153db021139a26e3455ee": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6a53017a6099462c", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Generated by AI, for reference only']，实际响应: '['tell me a joke', 'A new note has been created for you.', '', '', '', '', '', '', '', '', '']'\nassert False", "time": {"start": 1756803589787, "stop": 1756803613042, "duration": 23255}}]}, "5cc849d46714fff99c626e94dc28932d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9f48874a1559be8a", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['Add the images and text on the screen to the note', '', '', '', 'I am sorry, but I am unable to perform this function currently.', 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', \"Dialogue Explore <PERSON><PERSON><PERSON> down to view earlier chats Sagittarius Horoscope Tomorrow How's the weather today? Help me write an email to make an appointment for a visit Add the images and text on the screen to the note I am sorry, but I am unable to perform this function currently. Generated by AI, for reference only Can you explain why? Are there any alternatives? I will try again later. DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "time": {"start": 1756794884121, "stop": 1756794907833, "duration": 23712}}]}, "d9e62135fb3d98a8cadd206c651db3d7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c42dabaff30bbc26", "status": "passed", "time": {"start": 1756790919170, "stop": 1756790941043, "duration": 21873}}]}, "a1088ee9683cc60d86b0994865138921": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "bbdccaafdedef1be", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['What would you like to search for?']，实际响应: '['', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats 12:17 pm Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh Math problem photo solving Barcelona Held by <PERSON><PERSON> as <PERSON>s The new TV is broken. Help me write a complaint letter DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "time": {"start": 1756786618211, "stop": 1756786662130, "duration": 43919}}]}, "a54454fcb441a45b0e29dcbbf21679aa": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "15b51821380ce18d", "status": "passed", "time": {"start": 1756789977223, "stop": 1756790000216, "duration": 22993}}]}, "9ba28642fd60826e21a949609570a951": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "390feda6a3cd11d3", "status": "passed", "time": {"start": 1756801299845, "stop": 1756801326275, "duration": 26430}}]}, "5dbd6c476e40c9de0215f0509dd43986": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1d4bc260f7ac644", "status": "passed", "time": {"start": 1756798734456, "stop": 1756798759218, "duration": 24762}}]}, "aff947fee562ec2636c3ce68a270b88d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a5cde4c741307bb4", "status": "passed", "time": {"start": 1756792624715, "stop": 1756792647189, "duration": 22474}}]}, "411d5cfcc1960041b8df4decf67232f6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "efefed3efb15880f", "status": "passed", "time": {"start": 1756798172957, "stop": 1756798196904, "duration": 23947}}]}, "fef04fbdd26caf7d3f0f60df2c3ed14d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ce81ca7c641752f", "status": "passed", "time": {"start": 1756797973209, "stop": 1756797997568, "duration": 24359}}]}, "7ebb09688c661659cc2b4a26d54a347f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8a8dd4da56ce743", "status": "passed", "time": {"start": 1756804433090, "stop": 1756804457673, "duration": 24583}}]}, "969451307307a13b4d89a24bb46ad0bb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d81e3c9ed18f3dde", "status": "passed", "time": {"start": 1756801818784, "stop": 1756801842874, "duration": 24090}}]}, "74da34fd3c558df1234d7c0e937641b3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f9ef083d14b5f8b4", "status": "passed", "time": {"start": 1756803711496, "stop": 1756803734793, "duration": 23297}}]}, "e1f3b35ff3714fe0225699eb84cc1b87": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "396de7cdc7d71f46", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['download in play store', 'Which app should I download?', '', '', '', '', '', '', '', '', '']'\nassert False", "time": {"start": 1756796740692, "stop": 1756796764560, "duration": 23868}}]}, "eda6bef994b1b0ef78f60f433fb1d4f8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dc837eb6bb86fa70", "status": "passed", "time": {"start": 1756786576006, "stop": 1756786602331, "duration": 26325}}]}, "8f73bb4e2ab622960daf9c39a4008510": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5dc505ecb9de62aa", "status": "passed", "time": {"start": 1756798861580, "stop": 1756798885282, "duration": 23702}}]}, "891e31ec8bf99ceed4f462ce0c8629db": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1d25e06267fc3c28", "status": "passed", "time": {"start": 1756796335298, "stop": 1756796358154, "duration": 22856}}]}, "084048a337d3081654dc4414f67fce70": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4630a23bbb99d387", "status": "passed", "time": {"start": 1756802219209, "stop": 1756802247387, "duration": 28178}}]}, "0a76822399c9a8e342924e5ae6cce12c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "206cec360d6aeb7c", "status": "passed", "time": {"start": 1756794970571, "stop": 1756794997936, "duration": 27365}}]}, "e82a80866bdbe9a7e1ac367f20c977b5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9af6a648ea720b89", "status": "passed", "time": {"start": 1756804034601, "stop": 1756804057349, "duration": 22748}}]}, "6727d4a9ee2f10fdd00778048be14e2a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c898ef9c79312c73", "status": "passed", "time": {"start": 1756793415820, "stop": 1756793450100, "duration": 34280}}]}, "b165a17be8ab35920a6af9be7611a2c9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5da98a53de32cd9f", "status": "passed", "time": {"start": 1756784505778, "stop": 1756784592029, "duration": 86251}}]}, "f46a1c12d07d5949dbcf4b31314824ec": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "141e911fa36d2127", "status": "passed", "time": {"start": 1756790583987, "stop": 1756790613659, "duration": 29672}}]}, "31c983fdcc8b62f5927f99c0482b828d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cd041e4a8c7f2200", "status": "passed", "time": {"start": 1756800997894, "stop": 1756801021517, "duration": 23623}}]}, "e3cc499fef74e68f1c802e097ccd0f42": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "17ce66bf6dc8d92c", "status": "passed", "time": {"start": 1756796822886, "stop": 1756796851207, "duration": 28321}}]}, "cf0ebfd1b4e2ab43e2f516ad6a1a6917": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ec30640219f0fab9", "status": "passed", "time": {"start": 1756787884067, "stop": 1756787908431, "duration": 24364}}]}, "89b134ac1374e88187e793daf9f8fcab": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f4bbc31ca4d1bc6b", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['sorry']，实际响应: '['set lockscreen passwords', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Password & Security | Lockscreen Passwords | Slide to Unlock | Fingerprint | Unenrolled | Face Unlock | Unenrolled | Mobile Anti-Theft | System Security | More Settings']'\nassert False", "time": {"start": 1756802124977, "stop": 1756802153794, "duration": 28817}}]}, "c2ecb960f7f893feeaa2f24a34c9d77e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2bc4d0f624fdd37a", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['download in playstore', 'Which app should I download?', '', '', '', '', '', '', '', '', '']'\nassert False", "time": {"start": 1756796781550, "stop": 1756796806084, "duration": 24534}}]}, "5cf50058091f34fd1ed89d0b8f717355": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "67ec5be20359e244", "status": "failed", "statusDetails": "AssertionError: 初始=False, 最终=False, 响应='['turn on the screen record', 'Screen recording started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756793069512, "stop": 1756793096641, "duration": 27129}}]}, "8c62567d8b8a27f77124afc90fa44336": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1381636e1a26ffa3", "status": "passed", "time": {"start": 1756783019070, "stop": 1756783060646, "duration": 41576}}]}, "cce948f7c988b6f22bd4e8d08ca74deb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d4e286bfd9992516", "status": "passed", "time": {"start": 1756803326734, "stop": 1756803351498, "duration": 24764}}]}, "de5ff490f92fc399976d91fe0edc371e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "96985908524f0fd2", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['I need to download Yandex Eats', 'Generated by AI, for reference only']，实际响应: '['order a takeaway', \"O<PERSON>, out of my reach, ask me again after I've learned it\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756794761672, "stop": 1756794784871, "duration": 23199}}]}, "1a94a631f074dc4c0ba2bd39c9518123": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d0b07a8f9836209d", "status": "passed", "time": {"start": 1756788921764, "stop": 1756788945055, "duration": 23291}}]}, "e60dab4e55edacbecf632d4d22f368e6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2e23bce29a8c5cbb", "status": "passed", "time": {"start": 1756801477260, "stop": 1756801507504, "duration": 30244}}]}, "728822fc623e888cd9efa450f4737787": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2fd4092ce101b40f", "status": "passed", "time": {"start": 1756795014969, "stop": 1756795040839, "duration": 25870}}]}, "04263bf3ad5402ebd901c9c0e6682325": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b3330098bd72e4f2", "status": "passed", "time": {"start": 1756797359537, "stop": 1756797381997, "duration": 22460}}]}, "7413abdce214459d0e44671ef65b660b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "86fc6aeced2cf58", "status": "passed", "time": {"start": 1756789060568, "stop": 1756789094490, "duration": 33922}}]}, "1498285b0d63f23df3a22c9c5262f7f3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "790e35cbf8e3e6e3", "status": "passed", "time": {"start": 1756788505152, "stop": 1756788615429, "duration": 110277}}]}, "bfd4a9e37b70dca0b14b0ccf5246fc4a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "584c9760addef7d2", "status": "passed", "time": {"start": 1756798507802, "stop": 1756798539180, "duration": 31378}}]}, "f7282303534c1c8599c3343608e6f453": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "22e78af1ed0eda9d", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the wheather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756785361085, "stop": 1756785383364, "duration": 22279}}]}, "80dab16fde357aadc4387b5d440ed276": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c46aec4319103dd8", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only', 'WhatsApp is not installed yet', 'I need to download WhatsApp to continue']，实际响应: '['send my recent photos to mom through whatsapp', 'No number for mom. Who do you want to send it to?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756801344189, "stop": 1756801368727, "duration": 24538}}]}, "bd0fc59491cba87a8192cc6066cdbab0": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2dae9904d84eaad9", "status": "failed", "statusDetails": "AssertionError: 命令执行失败: Translate the content written on the picture into French", "time": {"start": 1756794190854, "stop": 1756794232925, "duration": 42071}}]}, "7d3b4e67344145885187c529ee88a9aa": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3cab704595aa2c4c", "status": "passed", "time": {"start": 1756802171564, "stop": 1756802201750, "duration": 30186}}]}, "ea48444c2b0789e59a64850ccfab3722": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e521c1cc203b2823", "status": "passed", "time": {"start": 1756782981491, "stop": 1756783003465, "duration": 21974}}]}, "ca9dd7f70b2888aafceb94247d7986f0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d5e2eb4d621adafc", "status": "passed", "time": {"start": 1756802446839, "stop": 1756802470866, "duration": 24027}}]}, "cd2650c8b690a339f6c3696b18b9dc0d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3d61c6b212aa3f73", "status": "passed", "time": {"start": 1756790995552, "stop": 1756791057688, "duration": 62136}}]}, "7fb2c589f1fda51205a5af1b549d5045": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9a74d594b22a471d", "status": "passed", "time": {"start": 1756789010221, "stop": 1756789044749, "duration": 34528}}]}, "19fa56a92b4343c1894780564290d112": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "31ded42df0e00782", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['i want make a video call to', 'Please tell me the name or number to call.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756798692939, "stop": 1756798717327, "duration": 24388}}]}, "54b47105d42d2a9f18eec071fba40c73": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bcc6ef82ce024cd4", "status": "passed", "time": {"start": 1756782813143, "stop": 1756782844882, "duration": 31739}}]}, "f05a6eb960fbbc415e4c605538080373": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e1ef419137abc43d", "status": "passed", "time": {"start": 1756786678450, "stop": 1756786700646, "duration": 22196}}]}, "0f4d3881c287fa46a9fdaf099fc19f8d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "96311abdd7feba55", "status": "passed", "time": {"start": 1756792586797, "stop": 1756792608581, "duration": 21784}}]}, "2b2dcc407b5c428f968f62d94fe8025c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "eeb961fd9ab6ef74", "status": "passed", "time": {"start": 1756801769844, "stop": 1756801801517, "duration": 31673}}]}, "5d0a6cda1787168fa2fdaaae1dee86f3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c762d6aff1697697", "status": "passed", "time": {"start": 1756797556295, "stop": 1756797580267, "duration": 23972}}]}, "7c32e753573a480d7d5c09abab43469e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ecc4c8297ce83965", "status": "passed", "time": {"start": 1756783338391, "stop": 1756783373432, "duration": 35041}}]}, "ae0ee984c3712fd05ea04b52289e14fe": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ed9712ed2962e052", "status": "passed", "time": {"start": 1756783237327, "stop": 1756783270237, "duration": 32910}}]}, "a3c84dd7a2924ee198fdb33cbc4e20b6": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "11f83661c99d36c4", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Who do you want to check?']，实际响应: '['check contacts', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756795686161, "stop": 1756795717592, "duration": 31431}}]}, "918c52f1eb9803594ff76c724b43d5f8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9dc0d65f3898702e", "status": "passed", "time": {"start": 1756794387998, "stop": 1756794420528, "duration": 32530}}]}, "cc44ad4097a589726631a345e0cd01ad": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "59c466aa5d047c4e", "status": "passed", "time": {"start": 1756785489373, "stop": 1756785513555, "duration": 24182}}]}, "c3dde525f6a284fe4a3e4b670182329f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e3f6e3d2aa315fc2", "status": "passed", "time": {"start": 1756799561480, "stop": 1756799586702, "duration": 25222}}]}, "e32881dd9d54414fa74d523ef27b055c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "586e1f358588d2e2", "status": "passed", "time": {"start": 1756792702071, "stop": 1756792723712, "duration": 21641}}]}, "488c24d02f5d5348f35881280e505f32": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b8d438569a207120", "status": "passed", "time": {"start": 1756799734725, "stop": 1756799764646, "duration": 29921}}]}, "ab0169efb30d26689cbce230ff455598": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3b8800fd72098c36", "status": "passed", "time": {"start": 1756800661721, "stop": 1756800696736, "duration": 35015}}]}, "cf1bdc2b1d9b604939681e5b6ac6f506": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "99035abb7ee1be47", "status": "passed", "time": {"start": 1756792895978, "stop": 1756792917689, "duration": 21711}}]}, "963c2cd1bdb409e4cfe9589a18006e88": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "29dd1b71426b2644", "status": "passed", "time": {"start": 1756790106569, "stop": 1756790128087, "duration": 21518}}]}, "a5a3cc08eb97e600c97acb65a7439ec0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7f7cca08fc84bf16", "status": "passed", "time": {"start": 1756802787670, "stop": 1756802819311, "duration": 31641}}]}, "bd9c64dabd06671b98d60748492be267": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "70eca5e92b601f79", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['call mom through whatsapp', 'No number for mom. Please tell me the name or number to call.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756784731984, "stop": 1756784762486, "duration": 30502}}]}, "c2a64f07232d43585d1dfee25c2f9407": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "14dca835eaa3e362", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['°C', 'Generated by AI, for reference only']，实际响应: '[\"what's the wheather today?\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756787636254, "stop": 1756787659044, "duration": 22790}}]}, "a84e06839a37b7806fefd316aa632437": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "84d78037a969da7", "status": "passed", "time": {"start": 1756797759479, "stop": 1756797784740, "duration": 25261}}]}, "8b6d374ba70006ef7591c8e0bf72bb00": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "861b82035507a3d0", "status": "passed", "time": {"start": 1756803368613, "stop": 1756803400670, "duration": 32057}}]}, "eba7a9e7bc39f5ded45d5d0bd68c834e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "23b238f1c424e815", "status": "passed", "time": {"start": 1756793923383, "stop": 1756793958643, "duration": 35260}}]}, "489e5c631d3a3ce20f77bce4c7c6632a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d945566ecee6f0ed", "status": "passed", "time": {"start": 1756790844483, "stop": 1756790865644, "duration": 21161}}]}, "329fa4b06eb0b0d769c2c418ed03dab7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bfdaecc28ba1ba81", "status": "passed", "time": {"start": 1756784219175, "stop": 1756784241712, "duration": 22537}}]}, "66496441d7401e453a580b4a8c23d111": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cb22f338e3619aca", "status": "passed", "time": {"start": 1756790372405, "stop": 1756790396063, "duration": 23658}}]}, "169e5b613c0fec2cebd053175998bf17": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c254a39f81a0e8ad", "status": "passed", "time": {"start": 1756783288217, "stop": 1756783322503, "duration": 34286}}]}, "a5e15bb05795c5949d67f33200f4d69b": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b6fbaed39b0b8cf6", "status": "failed", "statusDetails": "AssertionError: 初始=0, 最终=0, 响应='['max notifications volume', 'Notification volume has been set to the maximum.', '', '', '', '', '', '', '', '', '']'\nassert 0 == 15", "time": {"start": 1756789939434, "stop": 1756789960948, "duration": 21514}}]}, "44b646d68146a0c48da2623a58b17f6f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8e9729d713bd0d91", "status": "passed", "time": {"start": 1756784103472, "stop": 1756784125072, "duration": 21600}}]}, "37d8f85ba7c46a46b390c4fc5ab20de7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "833092c955ef1999", "status": "passed", "time": {"start": 1756783389245, "stop": 1756783411084, "duration": 21839}}]}, "4f538fc772535a0c0811ad87d3aa9494": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "36966b99cbefbe9d", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Multiple settings options found']，实际响应: '['set personal hotspot', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.settings.wifi页面内容] Personal Hotspot | Portable Hotspot | Personal Hotspot | Hotspot Settings | Share Hotspot | Connection Management | Turn Off Hotspot Automatically | If no device or network is connected within a certain period, the hotspot will be turned off automatically. | AI Decides']'\nassert False", "time": {"start": 1756802393667, "stop": 1756802428863, "duration": 35196}}]}, "8814f1dafa698e785ee1f58faa6e745d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "678cd1e2675d973f", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only']，实际响应: '['Search for addresses on the screen', 'Unable to summarize the content on this page. You can try sending links or uploading documents.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756801168565, "stop": 1756801192112, "duration": 23547}}]}, "b12ee4e4a5d7e51ba0abe166b6c90352": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "251e3af6c8b510a3", "status": "passed", "time": {"start": 1756800602720, "stop": 1756800644097, "duration": 41377}}]}, "309f3fbb8586cc15e324e94cec37e7ea": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b8aa8895f47b3ea0", "status": "passed", "time": {"start": 1756784933392, "stop": 1756784958016, "duration": 24624}}]}, "08bb1ce458e948b9a8084fb3ec1f2c83": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3be7574bab778432", "status": "passed", "time": {"start": 1756798334682, "stop": 1756798358660, "duration": 23978}}]}, "822b86779d55ac868fa4493183547c8f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1ccb5dcab8a70e4f", "status": "passed", "time": {"start": 1756790724218, "stop": 1756790784913, "duration": 60695}}]}, "d9f01ef1af79559082ce9e9b2e40295f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4ececa0fe4e031b5", "status": "passed", "time": {"start": 1756782764531, "stop": 1756782797316, "duration": 32785}}]}, "984a0fd313bba0ca2f20f0bbff732eb8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "40eaf049c48a001", "status": "passed", "time": {"start": 1756803874927, "stop": 1756803897492, "duration": 22565}}]}, "e5dc64184617a9497ec52a3b74103b55": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9094b97778f1a7bb", "status": "passed", "time": {"start": 1756789637579, "stop": 1756789659713, "duration": 22134}}]}, "981b71ad744b9a603c31ab4832ff9439": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a72a27bc3daf3dfd", "status": "passed", "time": {"start": 1756788758796, "stop": 1756788827676, "duration": 68880}}]}, "3f0254c16b7bc20d18094d502f49138a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a72092d805ff2850", "status": "passed", "time": {"start": 1756798600767, "stop": 1756798626354, "duration": 25587}}]}, "461fa25c15c0a862f353e5384438bc5d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bb7171ccd7fed515", "status": "passed", "time": {"start": 1756798133657, "stop": 1756798155766, "duration": 22109}}]}, "aac47e3ff4229b41a579f7c9ec938afb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7572fb71f999e818", "status": "passed", "time": {"start": 1756786117164, "stop": 1756786141617, "duration": 24453}}]}, "a084b34b62992b8f17deb64af6e57e39": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "886e4daab3100a46", "status": "passed", "time": {"start": 1756804474641, "stop": 1756804500508, "duration": 25867}}]}, "600ddf60808e2a751a4a4742a65811c7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1392b3a02a4ec68f", "status": "passed", "time": {"start": 1756804074073, "stop": 1756804098338, "duration": 24265}}]}, "ca269ac93364dc6180676f5680956b32": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "83c87397e58c17d9", "status": "passed", "time": {"start": 1756800905573, "stop": 1756800936763, "duration": 31190}}]}, "8c5a5747e91f2cb0412111d5027bb7ec": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2d667af6faa830a3", "status": "passed", "time": {"start": 1756793030366, "stop": 1756793053496, "duration": 23130}}]}, "5b1e68388004fe021690469c3d83b485": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "df5a2fb9f23a883d", "status": "passed", "time": {"start": 1756790629634, "stop": 1756790670381, "duration": 40747}}]}, "e4bab2ec1074fdbc8b4508dfad12adfc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "524d9c64d9e4e06f", "status": "passed", "time": {"start": 1756790057727, "stop": 1756790090376, "duration": 32649}}]}, "afde8e86697e1ec7ad65ac0c993e60f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7357d9f1eb02bb7f", "status": "passed", "time": {"start": 1756792373890, "stop": 1756792396566, "duration": 22676}}]}, "bf94eb080274f830f3097dd5adde1ed1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2f5eb11c955f547a", "status": "passed", "time": {"start": 1756792149292, "stop": 1756792170828, "duration": 21536}}]}, "89e187687dfa3317845107c44a62f287": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fb8e26cfe46fbf8", "status": "passed", "time": {"start": 1756798094434, "stop": 1756798116864, "duration": 22430}}]}, "09f397887d36f3ef1e86e3c78272f1d6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "22e3826ff8b9284d", "status": "passed", "time": {"start": 1756794722876, "stop": 1756794744545, "duration": 21669}}]}, "fd79b0d35f1f4639521f70b269d3aadc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7e9e619bec896a96", "status": "passed", "time": {"start": 1756799968703, "stop": 1756799999914, "duration": 31211}}]}, "d18ed3013dc7867440fb611fb474ca05": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7eeb2e3866a443fd", "status": "passed", "time": {"start": 1756789188448, "stop": 1756789212009, "duration": 23561}}]}, "9631a765ba57ecef91f8ebeb45e7807b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ef36759a2c339fd", "status": "passed", "time": {"start": 1756793815234, "stop": 1756793850354, "duration": 35120}}]}, "599b7a465f619c38a4638073f59c38c0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d74f58526b0b2abf", "status": "passed", "time": {"start": 1756803995576, "stop": 1756804018138, "duration": 22562}}]}, "c612b04b455e8fbc03acd18c2fc89827": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "598e6ff9d46b0da2", "status": "passed", "time": {"start": 1756797677323, "stop": 1756797700881, "duration": 23558}}]}, "16f38913a9d7e0ffc4c5ac51d5acf5c7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d11d43de5dd9a3bb", "status": "passed", "time": {"start": 1756792739974, "stop": 1756792762569, "duration": 22595}}]}, "8bf93fd8ac952a757cc694ecc78b8d51": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e1176be1fa1f77b9", "status": "passed", "time": {"start": 1756783076500, "stop": 1756783097619, "duration": 21119}}]}, "18fb8c43c609a9825fe52e528761fd1b": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "91ba7b91595a5e19", "status": "failed", "statusDetails": "AssertionError: 初始=False, 最终=False, 响应='['screen record', 'Screen recording started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756790494539, "stop": 1756790522944, "duration": 28405}}]}, "519d11d818a361bc75d5af94c6a68b28": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "269a59f7fb8e877", "status": "passed", "time": {"start": 1756787716190, "stop": 1756787739310, "duration": 23120}}]}, "700a4f81d53d76c265778c230c99dd8c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "449148562bc15399", "status": "passed", "time": {"start": 1756797597088, "stop": 1756797619578, "duration": 22490}}]}, "b9bb05ac1dcf8926da63d4ecbb1524cd": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d3ab8f9da337262c", "status": "passed", "time": {"start": 1756789149504, "stop": 1756789172911, "duration": 23407}}]}, "57a9b2e6f318afd186b838ed42ebd55c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "cf2f8ab16bf69e82", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['enable running lock', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "time": {"start": 1756797196691, "stop": 1756797221658, "duration": 24967}}]}, "e21c5dda6a9f09862a68c3a0bcda554a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b2960c1cda35baf5", "status": "passed", "time": {"start": 1756802547405, "stop": 1756802577454, "duration": 30049}}]}, "dc901cadfe1de0042de7c0f7461a804e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "167c447369<PERSON>ce8d", "status": "passed", "time": {"start": 1756796037406, "stop": 1756796068280, "duration": 30874}}]}, "6ba20fdcdbf83dd327ea91bd93e697e0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2a2cdaf213449732", "status": "passed", "time": {"start": 1756799914400, "stop": 1756799947858, "duration": 33458}}]}, "e1ef5de48cb99781fc16bc01be62dca2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f408ecba22f81d3d", "status": "passed", "time": {"start": 1756798644286, "stop": 1756798676340, "duration": 32054}}]}, "543965b4120af95548616c95b1b70ef1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e8922b9c77833bc3", "status": "passed", "time": {"start": 1756787106916, "stop": 1756787131818, "duration": 24902}}]}, "07dafe2b3e3ed9841a34e9fd19de58be": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "53ca4c949793ab88", "status": "passed", "time": {"start": 1756803286194, "stop": 1756803309943, "duration": 23749}}]}, "c04d9357fdaf44e6ee27f8a97ece6c5d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c1d60b7b3150f577", "status": "passed", "time": {"start": 1756801943036, "stop": 1756801966526, "duration": 23490}}]}, "5d0294174a7d609e38392f61f2170810": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8d12a2158c7712fa", "status": "passed", "time": {"start": 1756795943752, "stop": 1756795976895, "duration": 33143}}]}, "3004e41c81a7ebd857f79d043aaf59df": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "67de07d83afe565a", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the weather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756785316739, "stop": 1756785345320, "duration": 28581}}]}, "d960192ea83ce0c13a534ec13ca1700e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8354314c9786cba4", "status": "passed", "time": {"start": 1756787382267, "stop": 1756787450233, "duration": 67966}}]}, "fa8d6ac5c42acf5f644e6f5370a9a773": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f50bd08b9a38ba0b", "status": "passed", "time": {"start": 1756796295656, "stop": 1756796318406, "duration": 22750}}]}, "544fc8b021d2dbcaf295cd05b798f816": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "380d63b0cd5a6321", "status": "passed", "time": {"start": 1756802735999, "stop": 1756802769884, "duration": 33885}}]}, "1470cf4116a3328d5a8812cd15bb56f8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4080df4128273d88", "status": "passed", "time": {"start": 1756793308757, "stop": 1756793331610, "duration": 22853}}]}, "53ec4c77118606257c016fc2f7b22065": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a893c0e2b18cf2e9", "status": "passed", "time": {"start": 1756784607957, "stop": 1756784638578, "duration": 30621}}]}, "85b61394b4b07c85faf6e5081371fbf2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b0806a2c5a9fd7e0", "status": "passed", "time": {"start": 1756792336826, "stop": 1756792357750, "duration": 20924}}]}, "1695232002b2ad29ffa1faf52965470d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e5f78864ee79fcda", "status": "passed", "time": {"start": 1756796658140, "stop": 1756796683521, "duration": 25381}}]}, "9fcc7f87aa3845b28893959bf17baa2b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b34d57df75c79a0e", "status": "passed", "time": {"start": 1756803670916, "stop": 1756803694986, "duration": 24070}}]}, "e1b97b8698ff620d6d8faf32f381c874": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8ee13e7ef8a1e3f2", "status": "passed", "time": {"start": 1756797515821, "stop": 1756797539069, "duration": 23248}}]}, "2060dd1cfd03194548c0456a10798266": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "20e4493209526e19", "status": "passed", "time": {"start": 1756785051540, "stop": 1756785075700, "duration": 24160}}]}, "0dc117f78053bbddaf3ffcf69df0af9d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7027aca53f5c0188", "status": "passed", "time": {"start": 1756800768012, "stop": 1756800796716, "duration": 28704}}]}, "43a8e6496d8d78f2b8bc066858f8bdd9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d79fc46a54bc03cf", "status": "passed", "time": {"start": 1756800304327, "stop": 1756800328563, "duration": 24236}}]}, "7c74ed3e622ad29dd79be61222ad59bc": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "aaf7cd0d489d21b3", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['set default charging mode']，实际响应: '['smart charge', 'The current charging device does not support switching charging modes.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756791074178, "stop": 1756791094255, "duration": 20077}}]}, "569e770c250388bfbcf64d0cbbb8b351": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "67a342af2c079ab6", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['No to-dos today']，实际响应: '['check my to-do list', 'The following images are generated for you.', '', '', '', '', '', '', '', '', '']'\nassert False", "time": {"start": 1756795855994, "stop": 1756795886780, "duration": 30786}}]}, "e7de7a828ef1b59725204585ed7e1d64": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "39acd313f167a5a4", "status": "passed", "time": {"start": 1756802306542, "stop": 1756802332464, "duration": 25922}}]}, "acdb323f998d6127fbebbc545f6e8a59": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a884ae420d086a2e", "status": "passed", "time": {"start": 1756783548048, "stop": 1756783578842, "duration": 30794}}]}, "edbb2ef8b0440e0325be2bfae4eb0bee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4376a64c2a4f5da8", "status": "passed", "time": {"start": 1756796374854, "stop": 1756796399833, "duration": 24979}}]}, "c076d6e18e779bfeb810e69b30339aa2": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f49def4e0cf06e90", "status": "failed", "statusDetails": "AssertionError: 文件不存在！\nassert False", "time": {"start": 1756791882153, "stop": 1756791929249, "duration": 47096}}]}, "d235f5ef7da67b833ad362b7c693c8f1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2dd1629c4a57f5c0", "status": "passed", "time": {"start": 1756792933689, "stop": 1756793014273, "duration": 80584}}]}, "8373737839693413a37e35b627a0f5de": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "925d1157a300a945", "status": "passed", "time": {"start": 1756792089275, "stop": 1756792132977, "duration": 43702}}]}, "4cfe8e55b2a91a62bbf1141ffc0cc530": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5d50a283ee9bbb2c", "status": "passed", "time": {"start": 1756796248279, "stop": 1756796279118, "duration": 30839}}]}, "d8a01bf3d9b9092622318d8d22f17d9e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b7430a4c48e10fa", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['OK, the voice is switched. You can also select other voices.']，实际响应: '['change (female/tone name) voice', '', '', '', '', '', '', '', '', '', '', 'Dialogue', 'Dialogue Explore Swipe down to view earlier chats Tax savings ideas change (female/tone name) voice The following images are generated for you. Generated by AI, for reference only Exit AI Image Generator DeepSeek-R1 Describe the image you want to generate']'\nassert False", "time": {"start": 1756795455960, "stop": 1756795484082, "duration": 28122}}]}, "d18ea588937139bb162adb1092a66013": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "96b5beef4eb3e5e1", "status": "passed", "time": {"start": 1756793113203, "stop": 1756793136246, "duration": 23043}}]}, "511b4baaab6d7793eefd3f92b3a77d8b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9e24795f9d2ea166", "status": "passed", "time": {"start": 1756790181763, "stop": 1756790202991, "duration": 21228}}]}, "b3fa1d22b59def5f059cd9b0eefbe2b0": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "686460907e75164b", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['How can I ']，实际响应: '['hello hello', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats 11:52 am <PERSON>, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh Help expand landscape photos What is the secret behind fireflies' synchronized flashing? G<PERSON><PERSON>y Brace Fires Dortmund to First Win hello hello <PERSON> is thinking… DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "time": {"start": 1756785173872, "stop": 1756785200265, "duration": 26393}}]}, "548362627ce690e10e5f8ca35d247c62": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c598ec1927a46c64", "status": "passed", "time": {"start": 1756790017955, "stop": 1756790040142, "duration": 22187}}]}, "359341836df6d43ec99426e6918dc6ca": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3336fc9681d6992", "status": "passed", "time": {"start": 1756788407565, "stop": 1756788489323, "duration": 81758}}]}, "6f7bfd4cd2610e6e2fcc48afbef9709b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "406bdbd909a2c22", "status": "passed", "time": {"start": 1756793695993, "stop": 1756793730682, "duration": 34689}}]}, "861f0c94cdad5a5d60cd9fc71e2429d6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8d5587d0aef71df8", "status": "passed", "time": {"start": 1756784854708, "stop": 1756784879274, "duration": 24566}}]}, "30903d6e764eebda77a45c5af4464d00": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3ce21b38743deb23", "status": "passed", "time": {"start": 1756786988325, "stop": 1756787010694, "duration": 22369}}]}, "7ba4a9d343c0f63e9f654ce03ea4fa51": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1c29f91cf69e9382", "status": "passed", "time": {"start": 1756802645518, "stop": 1756802667746, "duration": 22228}}]}, "5eaa5a3015ce02f75c4c021fdbd2f78d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a63d015b93603b05", "status": "passed", "time": {"start": 1756801124108, "stop": 1756801150719, "duration": 26611}}]}, "42bb23fa5566b20ae050e85bbee099ef": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d184d8955b287484", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['enable brightness locking', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "time": {"start": 1756797036349, "stop": 1756797059085, "duration": 22736}}]}, "fca782bf64e9cf595a09003471d4cc31": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b68233f80328711", "status": "passed", "time": {"start": 1756799255078, "stop": 1756799285020, "duration": 29942}}]}, "434b905bf8be3ce2ee79606468e155db": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "704dcba58d8ac7ca", "status": "passed", "time": {"start": 1756798816144, "stop": 1756798844973, "duration": 28829}}]}, "d4409d015660212a7021f4aa7f849f30": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7b255b7ad65bccaa", "status": "passed", "time": {"start": 1756795272680, "stop": 1756795304250, "duration": 31570}}]}, "6df22ddc82daabcf8389e60296dc694e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3125024931f92f88", "status": "passed", "time": {"start": 1756786758546, "stop": 1756786788998, "duration": 30452}}]}, "861aa58f9a3d0d9c9861d88316e784c5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ef317590cd47e820", "status": "passed", "time": {"start": 1756783594695, "stop": 1756783615603, "duration": 20908}}]}, "d28e2beb1494e42c051e9b99add608fb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f82326702960f3e2", "status": "passed", "time": {"start": 1756786495456, "stop": 1756786559922, "duration": 64466}}]}, "6b4c2fb43e48aa6ef45b7a33c8b2d9ef": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b503d26219879bb0", "status": "passed", "time": {"start": 1756800955168, "stop": 1756800980119, "duration": 24951}}]}, "cda905ef365af8bbcc5fba28f6bde9ea": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c5702f5fcaf230c0", "status": "passed", "time": {"start": 1756791287423, "stop": 1756791336659, "duration": 49236}}]}, "bc062eca91b16841cac5c9865921b5c1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8256d09d814b048d", "status": "passed", "time": {"start": 1756782908521, "stop": 1756782929351, "duration": 20830}}]}, "a455fee07fb3c7d389380cb95d9a092c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a5fc0713ed01d19a", "status": "passed", "time": {"start": 1756799478622, "stop": 1756799502411, "duration": 23789}}]}, "ff8d76af98b9fdfaa206acbf87daa843": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d0c86108d3d6cd11", "status": "passed", "time": {"start": 1756797115859, "stop": 1756797139759, "duration": 23900}}]}, "4a00cb3818a7086991fb9f7a4d2a3bb5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "87890609469f7d59", "status": "passed", "time": {"start": 1756804304899, "stop": 1756804329553, "duration": 24654}}]}, "1da800483d0bd7f8dbe657a8d5c37f76": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3dcae75c3d767d78", "status": "passed", "time": {"start": 1756796123814, "stop": 1756796145611, "duration": 21797}}]}, "81f981a4ddbff762d2de1cd977c5568a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fbc6d8aaec542778", "status": "passed", "time": {"start": 1756797477021, "stop": 1756797498854, "duration": 21833}}]}, "6f7052acfdd45e34e5dded44ad87416e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c01f7948bc85ea61", "status": "passed", "time": {"start": 1756789788707, "stop": 1756789811813, "duration": 23106}}]}, "fdcf3737e32a4361e11902caf25fed5f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4ce075fa73ad6e73", "status": "passed", "time": {"start": 1756792187133, "stop": 1756792209542, "duration": 22409}}]}, "00e9182de9c9d3297d90ff42d6771a57": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9b1720905aca7c10", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['set languages', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "time": {"start": 1756802083090, "stop": 1756802107460, "duration": 24370}}]}, "abcb20b3882e9c0dbf1add7f63082581": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "1b2b2a4710391a26", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'I need to download whatsapp', 'Generated by AI, for reference only']，实际响应: '['pls open whatsapp', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[android页面内容] Dual App | Enable dual app, you can use dual WhatsApp simultaneously.']'\nassert False", "time": {"start": 1756800858685, "stop": 1756800886997, "duration": 28312}}]}, "1ed60306ec6b3749ffacc2d410664db2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "905848e63b513aca", "status": "passed", "time": {"start": 1756788194088, "stop": 1756788260941, "duration": 66853}}]}, "d41a9c89a400d807309a9cecf36c0728": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8476c90c6af75578", "status": "passed", "time": {"start": 1756783984131, "stop": 1756784015214, "duration": 31083}}]}, "a6002c930e7d5977f441f6060af6308d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "aed36461473d14ce", "status": "passed", "time": {"start": 1756785810730, "stop": 1756785833128, "duration": 22398}}]}, "6c315a350a546e1382e435255d28245b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b1936700f65f1cc0", "status": "passed", "time": {"start": 1756801984651, "stop": 1756802014805, "duration": 30154}}]}, "6ce7f580e5f506e3d464a36918322d43": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6d61f5a7db38fb00", "status": "passed", "time": {"start": 1756793855717, "stop": 1756793917937, "duration": 62220}}]}, "d8bd499fa9e4e04741c5c255fac9036d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b29d4e7ab4c48844", "status": "passed", "time": {"start": 1756799344218, "stop": 1756799374195, "duration": 29977}}]}, "936ae2bf6db744b69d4acf28b22f7646": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b98574eddb739721", "status": "passed", "time": {"start": 1756783427072, "stop": 1756783457631, "duration": 30559}}]}, "436193bc4e8c44d21b6520da0589f88d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a16676ea446b66ce", "status": "passed", "time": {"start": 1756799073395, "stop": 1756799103120, "duration": 29725}}]}, "b2f52f3c587a626460e5698cac861baf": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b5227ebb2f9fc0ba", "status": "passed", "time": {"start": 1756791657518, "stop": 1756791678713, "duration": 21195}}]}, "78164cec6ab8359be9416229a4882ef9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7729eaed52204675", "status": "passed", "time": {"start": 1756784778659, "stop": 1756784801846, "duration": 23187}}]}, "f06396240414bb7ac3c5c049002eef1e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "30b7d54fcd7cc699", "status": "passed", "time": {"start": 1756801253061, "stop": 1756801281390, "duration": 28329}}]}, "bda3c1f82ca5c246958b5bf50db09a67": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ca37e920b11678c5", "status": "passed", "time": {"start": 1756803161362, "stop": 1756803184156, "duration": 22794}}]}, "78de5607a6208f59723ba6cf4fcf09c4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "be03d3832b019f11", "status": "passed", "time": {"start": 1756785529738, "stop": 1756785554992, "duration": 25254}}]}, "eda16efd838471b84f33f12ec91662c9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "97dd8a9829b6ab", "status": "passed", "time": {"start": 1756801386543, "stop": 1756801409963, "duration": 23420}}]}, "fbd0b78d6e1c10d8dc70e2bd96aa5bdc": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "52567220beaba496", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Your current location']，实际响应: '['please show me where i am', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats 04:13 pm Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh How treat poison ivy rashes in the wild? <PERSON>, Rolex Headline 2025 Watch Moments Open Camera please show me where i am Getting location failed. DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "time": {"start": 1756800814925, "stop": 1756800840269, "duration": 25344}}]}, "45b57073b776ed5666f2f20a47a4638f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "650cd2ac0bb49e3c", "status": "passed", "time": {"start": 1756791612441, "stop": 1756791641034, "duration": 28593}}]}, "9375a7a6d7b67755564dec18857d7c65": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "657d44506c19a88e", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Who do you want to check?']，实际响应: '['check contact', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756795637491, "stop": 1756795669186, "duration": 31695}}]}, "454f04318d433db60e7e6f2de5790fc3": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "67baaeb7d84dabd9", "status": "failed", "statusDetails": "AssertionError: 初始=False, 最终=False, 响应='['continue screen recording', 'Screen recording continued.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "time": {"start": 1756791242857, "stop": 1756791271457, "duration": 28600}}]}, "c45aa63628fe12b375ba7e65c39d93b1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b2dcd70c69442a2d", "status": "passed", "time": {"start": 1756787148348, "stop": 1756787172498, "duration": 24150}}]}, "cc158f7c05891fbac271a86692bc57a6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f702ae9d3a4ef0d", "status": "passed", "time": {"start": 1756803792587, "stop": 1756803817133, "duration": 24546}}]}, "fae56e9bcf9e0511ef4a7c93775731e3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "681f2267241c67fd", "status": "passed", "time": {"start": 1756794843077, "stop": 1756794867261, "duration": 24184}}]}, "85e8e199dba3ac2c9d89e132805a4404": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6eba3a48a4188e1d", "status": "passed", "time": {"start": 1756786197618, "stop": 1756786230984, "duration": 33366}}]}, "29390733aaf67e070f7c061b70bad8a5": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e06a6c9ea846f9ba", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Done']，实际响应: '['', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore <PERSON>wipe down to view earlier chats 12:56 pm Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh What are the restaurants nearby？ How to use Ask About Screen Liverpool Tops Premier League After Arsenal Win DeepSeek-R1 Feel free to ask me any questions…\", '[com.transsion.launcher3页面内容] Calendar | File Manager | Ella | Themes | Visha Player | Weather | Notepad | Freezer | HiOS Family | Tools | Chrome | Hot Apps | Hot Games | Maps | Facebook | WhatsApp | Phone | Messages | Camera']'\nassert False", "time": {"start": 1756788961194, "stop": 1756788994079, "duration": 32885}}]}, "221d94649ab3a384e6bc24767dceba21": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b3b51bd2699238cf", "status": "passed", "time": {"start": 1756799519358, "stop": 1756799543758, "duration": 24400}}]}, "228f8713a07d6ddbb8729f2f567c21d0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cb95f5ad0b201d87", "status": "passed", "time": {"start": 1756786325948, "stop": 1756786358825, "duration": 32877}}]}, "d2d9aa669417404f06e84a7a4387c55a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cbfbf9f515a15d56", "status": "passed", "time": {"start": 1756791567663, "stop": 1756791595954, "duration": 28291}}]}}