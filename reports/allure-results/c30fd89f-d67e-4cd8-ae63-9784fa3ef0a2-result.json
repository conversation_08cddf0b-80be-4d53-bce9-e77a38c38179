{"name": "测试more settings返回正确的不支持响应", "status": "passed", "description": "验证more settings指令返回预期的不支持响应", "steps": [{"name": "执行命令: more settings", "status": "passed", "steps": [{"name": "执行命令: more settings", "status": "passed", "start": 1756799645101, "stop": 1756799673759}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "943bad15-60b6-4127-98f1-59f9dc23dbab-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1933e594-5f2a-47cc-82c3-e50a0cfc0ec5-attachment.png", "type": "image/png"}], "start": 1756799673759, "stop": 1756799673964}], "start": 1756799645101, "stop": 1756799673964}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756799673964, "stop": 1756799673965}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2d446aa1-221b-4940-aba4-5112629376f6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "28a2eb2d-816b-4ac8-ab2d-ea4ee152d5e1-attachment.png", "type": "image/png"}], "start": 1756799673966, "stop": 1756799674206}], "attachments": [{"name": "stdout", "source": "26955575-86db-424f-9757-1e4945ea49e3-attachment.txt", "type": "text/plain"}], "start": 1756799645101, "stop": 1756799674208, "uuid": "df2b371f-bdee-4d29-9bea-a62f8ca883ce", "historyId": "6d49aaf4a5e11b32b961aa0aa3dbbf6e", "testCaseId": "6d49aaf4a5e11b32b961aa0aa3dbbf6e", "fullName": "testcases.test_ella.unsupported_commands.test_more_settings.TestEllaMoreSettings#test_more_settings", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_more_settings"}, {"name": "subSuite", "value": "TestEllaMoreSettings"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_more_settings"}]}