{"name": "测试open flashlight", "status": "passed", "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open flashlight", "status": "passed", "steps": [{"name": "执行命令: open flashlight", "status": "passed", "start": 1756790372405, "stop": 1756790395679}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "74b6d05a-9b8a-41fc-8844-c4aaf5fe9225-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "077bdce8-f7d4-4dff-917b-28d6b04165eb-attachment.png", "type": "image/png"}], "start": 1756790395679, "stop": 1756790395880}], "start": 1756790372405, "stop": 1756790395881}, {"name": "验证响应包含Done", "status": "passed", "start": 1756790395881, "stop": 1756790395882}, {"name": "验证flashlight已打开", "status": "passed", "start": 1756790395882, "stop": 1756790395882}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a1d11202-70a5-4dad-8652-8d366fc9116c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b31f3c25-cee6-4602-9a60-ede3ff280097-attachment.png", "type": "image/png"}], "start": 1756790395882, "stop": 1756790396062}], "attachments": [{"name": "stdout", "source": "d5565e4c-19cc-45ed-98fa-0a584bc7cb46-attachment.txt", "type": "text/plain"}], "start": 1756790372405, "stop": 1756790396063, "uuid": "ebfa1cf2-9b59-499a-8773-d4b89175eb03", "historyId": "66496441d7401e453a580b4a8c23d111", "testCaseId": "66496441d7401e453a580b4a8c23d111", "fullName": "testcases.test_ella.system_coupling.test_open_flashlight.TestEllaCommandConcise#test_open_flashlight", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "打开"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_open_flashlight"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_open_flashlight"}]}