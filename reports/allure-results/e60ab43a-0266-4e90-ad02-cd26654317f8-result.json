{"name": "测试search picture in my gallery能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['What would you like to search for?']，实际响应: '['', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats 12:17 pm Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh Math problem photo solving Barcelona Held by <PERSON><PERSON> as <PERSON>s The new TV is broken. Help me write a complaint letter DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "trace": "self = <testcases.test_ella.dialogue.test_search_picture_in_my_gallery.TestEllaHowIsWeatherToday object at 0x0000018BC602E9B0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6DB94E0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_search_picture_in_my_gallery(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['What would you like to search for?']，实际响应: '['', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats 12:17 pm Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh Math problem photo solving Barcelona Held by Rayo as Garcia Shines The new TV is broken. Help me write a complaint letter DeepSeek-R1 Feel free to ask me any questions…\"]'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_search_picture_in_my_gallery.py:33: AssertionError"}, "description": "search picture in my gallery", "steps": [{"name": "执行命令: search picture in my gallery", "status": "passed", "steps": [{"name": "执行命令: search picture in my gallery", "status": "passed", "start": 1756786618212, "stop": 1756786661946}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9473d2e1-d101-4d5a-adce-f07ce528c545-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1d218c90-7272-4f11-be13-1649078340de-attachment.png", "type": "image/png"}], "start": 1756786661946, "stop": 1756786662126}], "start": 1756786618211, "stop": 1756786662126}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['What would you like to search for?']，实际响应: '['', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats 12:17 pm Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh Math problem photo solving Barcelona Held by <PERSON><PERSON> as <PERSON>s The new TV is broken. Help me write a complaint letter DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\dialogue\\test_search_picture_in_my_gallery.py\", line 33, in test_search_picture_in_my_gallery\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756786662126, "stop": 1756786662129}], "attachments": [{"name": "stdout", "source": "4aea5b9e-8a38-4454-a3b5-9b9b7ce08393-attachment.txt", "type": "text/plain"}], "start": 1756786618211, "stop": 1756786662130, "uuid": "6e953c1d-2852-4279-a408-2377ce319a8d", "historyId": "a1088ee9683cc60d86b0994865138921", "testCaseId": "a1088ee9683cc60d86b0994865138921", "fullName": "testcases.test_ella.dialogue.test_search_picture_in_my_gallery.TestEllaHowIsWeatherToday#test_search_picture_in_my_gallery", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_search_picture_in_my_gallery"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_search_picture_in_my_gallery"}]}