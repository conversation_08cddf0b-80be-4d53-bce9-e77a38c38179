{"name": "测试previous song能正常执行", "status": "passed", "description": "previous song", "steps": [{"name": "执行命令: previous song", "status": "passed", "steps": [{"name": "执行命令: previous song", "status": "passed", "start": 1756786458236, "stop": 1756786479725}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e2cd5b6c-1403-4237-8417-cf819d2b6436-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0b62a3d8-ef08-4ad5-9c4a-88cb80fcafb4-attachment.png", "type": "image/png"}], "start": 1756786479725, "stop": 1756786479935}], "start": 1756786458236, "stop": 1756786479936}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756786479936, "stop": 1756786479937}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "84403499-271b-4d08-9b75-5032eae5fe90-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "273a863c-3804-472e-9f06-b11c1e921329-attachment.png", "type": "image/png"}], "start": 1756786479937, "stop": 1756786480142}], "attachments": [{"name": "stdout", "source": "6df632c5-24a1-48b0-bbfb-c6d078428568-attachment.txt", "type": "text/plain"}], "start": 1756786458235, "stop": 1756786480142, "uuid": "a461d107-e9b7-4999-89ae-8fb5bdf1b55a", "historyId": "d304f4e805a15a0351109cc931c26ffc", "testCaseId": "d304f4e805a15a0351109cc931c26ffc", "fullName": "testcases.test_ella.dialogue.test_previous_song.TestEllaHowIsWeatherToday#test_previous_song", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_previous_song"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_previous_song"}]}