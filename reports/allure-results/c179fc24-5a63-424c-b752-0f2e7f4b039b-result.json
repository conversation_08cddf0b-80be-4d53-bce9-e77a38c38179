{"name": "测试i want make a video call to能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['i want make a video call to', 'Please tell me the name or number to call.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_i_want_make_a_video_call_to.TestEllaIWantMakeVideoCall object at 0x0000018BC6524430>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC675FB80>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_i_want_make_a_video_call_to(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response_advanced(expected_text, response_text,match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['i want make a video call to', 'Please tell me the name or number to call.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_i_want_make_a_video_call_to.py:36: AssertionError"}, "description": "i want make a video call to", "steps": [{"name": "执行命令: i want make a video call to", "status": "passed", "steps": [{"name": "执行命令: i want make a video call to", "status": "passed", "start": 1756798692939, "stop": 1756798717075}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "47a08253-5750-48cf-9453-82d0b588cc59-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6b9ead5e-f079-4b4b-a5fe-33bf9aab821e-attachment.png", "type": "image/png"}], "start": 1756798717075, "stop": 1756798717323}], "start": 1756798692939, "stop": 1756798717323}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['i want make a video call to', 'Please tell me the name or number to call.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_i_want_make_a_video_call_to.py\", line 36, in test_i_want_make_a_video_call_to\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756798717323, "stop": 1756798717326}], "attachments": [{"name": "stdout", "source": "e81e62cc-abef-4791-b219-3105317ca7c6-attachment.txt", "type": "text/plain"}], "start": 1756798692939, "stop": 1756798717327, "uuid": "b5ba989b-2e9f-4d7f-a607-eb2a76a5a129", "historyId": "19fa56a92b4343c1894780564290d112", "testCaseId": "19fa56a92b4343c1894780564290d112", "fullName": "testcases.test_ella.unsupported_commands.test_i_want_make_a_video_call_to.TestEllaIWantMakeVideoCall#test_i_want_make_a_video_call_to", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_i_want_make_a_video_call_to"}, {"name": "subSuite", "value": "TestEllaIWantMakeVideoCall"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_i_want_make_a_video_call_to"}]}