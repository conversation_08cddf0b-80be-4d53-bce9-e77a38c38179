{"name": "测试Dial the number on the screen", "status": "passed", "description": "测试Ask Screen功能: Dial the number on the screen", "steps": [{"name": "准备测试数据", "status": "passed", "start": 1756793735925, "stop": 1756793744022}, {"name": "执行Ask Screen命令: Dial the number on the screen", "status": "passed", "steps": [{"name": "执行浮窗命令: Dial the number on the screen", "status": "passed", "steps": [{"name": "执行命令: Dial the number on the screen", "status": "passed", "start": 1756793744023, "stop": 1756793752421}, {"name": "等待并获取AI响应", "status": "passed", "start": 1756793752421, "stop": 1756793769373}, {"name": "验证响应内容", "status": "passed", "attachments": [{"name": "关键词验证结果", "source": "c189e630-796a-4d65-a8af-94e6cc010f0c-attachment.txt", "type": "text/plain"}], "start": 1756793769373, "stop": 1756793769375}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "浮窗测试总结", "source": "d7a188bd-a5b6-4722-be0c-f263aa971abc-attachment.txt", "type": "text/plain"}, {"name": "AI响应内容", "source": "ce0772ee-2b6e-4131-bc63-e9f7247c7a15-attachment.txt", "type": "text/plain"}], "start": 1756793769375, "stop": 1756793769377}], "start": 1756793744022, "stop": 1756793769377}, {"name": "截图记录测试完成状态", "status": "passed", "attachments": [{"name": "floating_test_completed", "source": "d92be12d-7156-4a00-9e56-fa2d7d3a0b04-attachment.png", "type": "image/png"}], "start": 1756793769377, "stop": 1756793769604}], "start": 1756793744022, "stop": 1756793770830}, {"name": "验证测试结果", "status": "passed", "start": 1756793770830, "stop": 1756793770831}], "attachments": [{"name": "stdout", "source": "90770b66-5a3f-4bd5-827b-47fdaa0d4917-attachment.txt", "type": "text/plain"}], "start": 1756793735925, "stop": 1756793770832, "uuid": "8098e3d3-251d-45b0-9635-b796c18ba174", "historyId": "35c27c2a65b1189393c74d916f6ae378", "testCaseId": "35c27c2a65b1189393c74d916f6ae378", "fullName": "testcases.test_ella.test_ask_screen.contact.test_dial_the_number_on_the_screen.TestAskScreenDialNumberScreen#test_dial_the_number_on_the_screen", "labels": [{"name": "epic", "value": "Ella浮窗测试"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ask Screen功能"}, {"name": "story", "value": "联系人相关"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.test_ask_screen.contact"}, {"name": "suite", "value": "test_dial_the_number_on_the_screen"}, {"name": "subSuite", "value": "TestAskScreenDialNumberScreen"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_ask_screen.contact.test_dial_the_number_on_the_screen"}]}