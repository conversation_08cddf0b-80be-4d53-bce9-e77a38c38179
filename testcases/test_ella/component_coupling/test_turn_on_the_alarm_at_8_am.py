"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaOpenClock(SimpleEllaTest):
    """Ella打开clock测试类"""

    @allure.title("测试turn on the alarm at 8 am")
    @allure.description("测试turn on the alarm at 8 am指令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_turn_on_the_alarm_at_8_am(self, ella_app):
        """测试turn on the alarm at 8 am命令"""
        command = "turn on the alarm at 8 am"
        expected_text = ["true", '8:00']

        tmp = []
        commands = ['delete all the alarms', 'set an alarm at 8 am', command, 'get all the alarms']

        with allure.step(f"执行命令: {commands[0]}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, commands[0], verify_status=True, verify_files=False
            )

        with allure.step(f"执行命令: {commands[1]}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, commands[1], verify_status=True, verify_files=False
            )
            # [tmp.append(x) for x in response_text if x not in commands]

        with allure.step(f"执行命令: {commands[2]}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, commands[2], verify_status=False, verify_files=False
            )
            # 执行设置这行文案不获取，而是通过查询记录来验证
            # [tmp.append(x) for x in response_text if x not in commands]

        with allure.step(f"执行命令: {commands[3]}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, commands[3], verify_status=False, verify_files=False
            )
            [tmp.append(x) for x in response_text if x not in commands]

        with allure.step("验证响应包含期望内容"):
            result = self.verify_expected_in_response(expected_text, tmp)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{tmp}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")