{"name": "测试merry christmas", "status": "passed", "description": "测试merry christmas指令", "steps": [{"name": "执行命令: merry christmas", "status": "passed", "steps": [{"name": "执行命令: merry christmas", "status": "passed", "start": 1756799561480, "stop": 1756799586272}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9ad3cbc7-c61b-47eb-9b94-8ab2cc88eabd-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "58649cc5-9391-4e1b-b8d6-0da2813b2ef1-attachment.png", "type": "image/png"}], "start": 1756799586272, "stop": 1756799586491}], "start": 1756799561480, "stop": 1756799586491}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756799586491, "stop": 1756799586492}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e413b6e9-d9df-43bf-a64e-b227dd8b608c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "be76e769-2fd4-4765-bbdd-7ad9db3b02a6-attachment.png", "type": "image/png"}], "start": 1756799586492, "stop": 1756799586702}], "attachments": [{"name": "stdout", "source": "861fe5d3-acd3-4807-b076-9027219d13bc-attachment.txt", "type": "text/plain"}], "start": 1756799561480, "stop": 1756799586702, "uuid": "e89a071b-3a4b-4171-9c15-546d3a092e29", "historyId": "c3dde525f6a284fe4a3e4b670182329f", "testCaseId": "c3dde525f6a284fe4a3e4b670182329f", "fullName": "testcases.test_ella.unsupported_commands.test_merry_christmas.TestEllaOpenPlayPoliticalNews#test_merry_christmas", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_merry_christmas"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_merry_christmas"}]}