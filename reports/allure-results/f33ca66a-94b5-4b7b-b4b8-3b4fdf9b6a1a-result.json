{"name": "测试set folding screen zone返回正确的不支持响应", "status": "passed", "description": "验证set folding screen zone指令返回预期的不支持响应", "steps": [{"name": "执行命令: set folding screen zone", "status": "passed", "steps": [{"name": "执行命令: set folding screen zone", "status": "passed", "start": 1756801943036, "stop": 1756801966025}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8bbc5070-b179-4b0d-ba4e-8b4b557b0980-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f2aa6640-f5f5-4a27-8f91-554e3ea9dd73-attachment.png", "type": "image/png"}], "start": 1756801966026, "stop": 1756801966273}], "start": 1756801943036, "stop": 1756801966274}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756801966274, "stop": 1756801966274}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "cbe6ad7e-0166-4ed0-aab7-bcf575f5a06e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2a9da7e6-dd7b-4ff0-b498-13f26872b63a-attachment.png", "type": "image/png"}], "start": 1756801966274, "stop": 1756801966526}], "attachments": [{"name": "stdout", "source": "d04f46f1-c1ea-4bfb-87ee-97b04863b053-attachment.txt", "type": "text/plain"}], "start": 1756801943036, "stop": 1756801966526, "uuid": "8a3d0aff-e9c6-4224-bd00-499d92ec215c", "historyId": "c04d9357fdaf44e6ee27f8a97ece6c5d", "testCaseId": "c04d9357fdaf44e6ee27f8a97ece6c5d", "fullName": "testcases.test_ella.unsupported_commands.test_set_folding_screen_zone.TestEllaSetFoldingScreenZone#test_set_folding_screen_zone", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_folding_screen_zone"}, {"name": "subSuite", "value": "TestEllaSetFoldingScreenZone"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_folding_screen_zone"}]}