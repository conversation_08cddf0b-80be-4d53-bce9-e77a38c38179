{"name": "测试continue music能正常执行", "status": "passed", "description": "continue music", "steps": [{"name": "执行命令: continue music", "status": "passed", "steps": [{"name": "执行命令: continue music", "status": "passed", "start": 1756782944840, "stop": 1756782965107}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "618f68a1-226c-40a3-8e39-dfd22a84769f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a9d734ea-e0d1-4051-ba5f-c308e4a6bd49-attachment.png", "type": "image/png"}], "start": 1756782965107, "stop": 1756782965329}], "start": 1756782944840, "stop": 1756782965329}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756782965329, "stop": 1756782965331}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4d689bea-7bcf-4ea4-a4de-87225ac8ed4f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "586eb86d-d03e-44f1-8ba3-63a25d194cc1-attachment.png", "type": "image/png"}], "start": 1756782965331, "stop": 1756782965519}], "attachments": [{"name": "stdout", "source": "5390bd47-010e-4016-8241-2d39bed3d829-attachment.txt", "type": "text/plain"}], "start": 1756782944839, "stop": 1756782965520, "uuid": "6fc3d6d5-5da4-45f5-8cd5-b3734e4a889a", "historyId": "87f3dc53ab72c729262e053c16a3dbcb", "testCaseId": "87f3dc53ab72c729262e053c16a3dbcb", "fullName": "testcases.test_ella.component_coupling.test_continue_music.TestEllaContinueMusic#test_continue_music", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_continue_music"}, {"name": "subSuite", "value": "TestEllaContinueMusic"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_continue_music"}]}