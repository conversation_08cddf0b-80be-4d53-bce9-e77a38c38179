{"name": "测试check rear camera information能正常执行", "status": "passed", "description": "check rear camera information", "steps": [{"name": "执行命令: check rear camera information", "status": "passed", "steps": [{"name": "执行命令: check rear camera information", "status": "passed", "start": 1756795943753, "stop": 1756795976484}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3d5ead0f-11d6-4992-b7c6-6fe09cc6e5e7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d3f4cdc1-6906-4fae-8747-5e47febb7be9-attachment.png", "type": "image/png"}], "start": 1756795976484, "stop": 1756795976692}], "start": 1756795943752, "stop": 1756795976692}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756795976692, "stop": 1756795976693}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "985e043c-c295-484b-8204-4f7071f5bf58-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "90fd91e2-7ade-4b9d-a8d9-ee5e0fe4e758-attachment.png", "type": "image/png"}], "start": 1756795976693, "stop": 1756795976894}], "attachments": [{"name": "stdout", "source": "b5d6d66e-333d-41fd-b2cb-110e4b7e49c2-attachment.txt", "type": "text/plain"}], "start": 1756795943752, "stop": 1756795976895, "uuid": "6f843bf6-361e-42aa-a8c6-46e754122307", "historyId": "5d0294174a7d609e38392f61f2170810", "testCaseId": "5d0294174a7d609e38392f61f2170810", "fullName": "testcases.test_ella.unsupported_commands.test_check_rear_camera_information.TestEllaCheckRearCameraInformation#test_check_rear_camera_information", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_rear_camera_information"}, {"name": "subSuite", "value": "TestEllaCheckRearCameraInformation"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_rear_camera_information"}]}