{"name": "测试tell me a joke能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Generated by AI, for reference only']，实际响应: '['tell me a joke', 'A new note has been created for you.', '', '', '', '', '', '', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_tell_me_a_joke.TestEllaTellMeJoke object at 0x0000018BC6691600>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6164F10>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_tell_me_a_joke(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Generated by AI, for reference only']，实际响应: '['tell me a joke', 'A new note has been created for you.', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_tell_me_a_joke.py:33: AssertionError"}, "description": "tell me a joke", "steps": [{"name": "执行命令: tell me a joke", "status": "passed", "steps": [{"name": "执行命令: tell me a joke", "status": "passed", "start": 1756803589787, "stop": 1756803612787}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ed2baed6-39f1-4031-8842-c80ac1d1a769-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5ca8f5cd-8c1a-4a63-8167-b5d129843252-attachment.png", "type": "image/png"}], "start": 1756803612789, "stop": 1756803613037}], "start": 1756803589787, "stop": 1756803613038}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Generated by AI, for reference only']，实际响应: '['tell me a joke', 'A new note has been created for you.', '', '', '', '', '', '', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_tell_me_a_joke.py\", line 33, in test_tell_me_a_joke\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756803613038, "stop": 1756803613041}], "attachments": [{"name": "stdout", "source": "e50ffea8-abde-446b-b34a-ea7f65caed4e-attachment.txt", "type": "text/plain"}], "start": 1756803589787, "stop": 1756803613042, "uuid": "29392f16-d67b-4da3-b59f-5e5c51451db2", "historyId": "29293108cad153db021139a26e3455ee", "testCaseId": "29293108cad153db021139a26e3455ee", "fullName": "testcases.test_ella.unsupported_commands.test_tell_me_a_joke.TestEllaTellMeJoke#test_tell_me_a_joke", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_tell_me_a_joke"}, {"name": "subSuite", "value": "TestEllaTellMeJoke"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_tell_me_a_joke"}]}