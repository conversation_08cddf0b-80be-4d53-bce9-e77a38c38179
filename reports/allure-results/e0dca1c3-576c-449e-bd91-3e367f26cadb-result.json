{"name": "测试add this number to tom", "status": "passed", "description": "测试Ask Screen功能: add this number to tom", "steps": [{"name": "准备测试数据", "status": "passed", "start": 1756793695993, "stop": 1756793704227}, {"name": "执行Ask Screen命令: add this number to tom", "status": "passed", "steps": [{"name": "执行浮窗命令: add this number to tom", "status": "passed", "steps": [{"name": "执行命令: add this number to tom", "status": "passed", "start": 1756793704228, "stop": 1756793713533}, {"name": "等待并获取AI响应", "status": "passed", "start": 1756793713533, "stop": 1756793729239}, {"name": "验证响应内容", "status": "passed", "attachments": [{"name": "关键词验证结果", "source": "ff50fffa-3687-4355-9e2c-f6ec5fd51f2d-attachment.txt", "type": "text/plain"}], "start": 1756793729239, "stop": 1756793729242}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "浮窗测试总结", "source": "129fdafb-ee30-41ab-87d3-159413a717ad-attachment.txt", "type": "text/plain"}, {"name": "AI响应内容", "source": "821123c6-c3d4-4eed-881c-af3d7b395e6a-attachment.txt", "type": "text/plain"}], "start": 1756793729242, "stop": 1756793729243}], "start": 1756793704227, "stop": 1756793729243}, {"name": "截图记录测试完成状态", "status": "passed", "attachments": [{"name": "floating_test_completed", "source": "f4eaa071-7154-4973-898f-8e21f409048b-attachment.png", "type": "image/png"}], "start": 1756793729243, "stop": 1756793729456}], "start": 1756793704227, "stop": 1756793730681}, {"name": "验证测试结果", "status": "passed", "start": 1756793730681, "stop": 1756793730682}], "attachments": [{"name": "stdout", "source": "dd809ff8-e434-457d-a58b-e1829cb58b4e-attachment.txt", "type": "text/plain"}], "start": 1756793695993, "stop": 1756793730682, "uuid": "52c380c4-3366-4553-af11-3bd220c2a398", "historyId": "6f7bfd4cd2610e6e2fcc48afbef9709b", "testCaseId": "6f7bfd4cd2610e6e2fcc48afbef9709b", "fullName": "testcases.test_ella.test_ask_screen.contact.test_add_this_number_to_tom.TestAskScreenAddNumberTom#test_add_this_number_to_tom", "labels": [{"name": "epic", "value": "Ella浮窗测试"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ask Screen功能"}, {"name": "story", "value": "联系人相关"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.test_ask_screen.contact"}, {"name": "suite", "value": "test_add_this_number_to_tom"}, {"name": "subSuite", "value": "TestAskScreenAddNumberTom"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_ask_screen.contact.test_add_this_number_to_tom"}]}