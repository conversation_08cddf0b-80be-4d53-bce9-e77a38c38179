{"name": "测试increase settings for special functions返回正确的不支持响应", "status": "passed", "description": "验证increase settings for special functions指令返回预期的不支持响应", "steps": [{"name": "执行命令: increase settings for special functions", "status": "passed", "steps": [{"name": "执行命令: increase settings for special functions", "status": "passed", "start": 1756798776017, "stop": 1756798798143}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b13651e5-2fc5-4e0e-a1d7-7ca240ae48ba-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8a9f1ad8-42be-4021-8e46-448c8056b4c9-attachment.png", "type": "image/png"}], "start": 1756798798143, "stop": 1756798798444}], "start": 1756798776017, "stop": 1756798798445}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756798798445, "stop": 1756798798447}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "38296350-475b-42b9-9910-7a0a7c6b8d2e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "51c76575-34c8-4690-bf11-051193e12615-attachment.png", "type": "image/png"}], "start": 1756798798447, "stop": 1756798798694}], "attachments": [{"name": "stdout", "source": "e1bc8e79-44ab-48c1-b214-7115f2e04d5b-attachment.txt", "type": "text/plain"}], "start": 1756798776017, "stop": 1756798798694, "uuid": "c689571a-b6d7-4def-b89a-6fbd09a0554e", "historyId": "5050d8dc816d181ca0c76dc56c8cb5f2", "testCaseId": "5050d8dc816d181ca0c76dc56c8cb5f2", "fullName": "testcases.test_ella.unsupported_commands.test_increase_settings_for_special_functions.TestEllaIncreaseSettingsSpecialFunctions#test_increase_settings_for_special_functions", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_increase_settings_for_special_functions"}, {"name": "subSuite", "value": "TestEllaIncreaseSettingsSpecialFunctions"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_increase_settings_for_special_functions"}]}