"""
pytest配置文件
定义全局的fixture和配置
"""
import pytest
import allure
from core.logger import log
from core.base_driver import driver_manager
from utils.file_utils import FileUtils
from utils.yaml_utils import YamlUtils


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """
    测试环境初始化
    在整个测试会话开始前执行一次
    """
    log.info("=" * 50)
    log.info("开始测试会话")
    log.info("=" * 50)
    
    # 确保必要的目录存在
    project_root = YamlUtils.get_project_root()
    
    # 创建报告目录
    FileUtils.ensure_dir(f"{project_root}/reports/screenshots")
    FileUtils.ensure_dir(f"{project_root}/reports/allure-results")
    FileUtils.ensure_dir(f"{project_root}/logs")
    
    # 获取设备信息
    try:
        device_info = driver_manager.get_device_info()
        log.info(f"测试设备信息: {device_info}")
        
        # 将设备信息添加到Allure报告
        allure.dynamic.feature("设备信息")
        allure.dynamic.story(f"设备型号: {device_info.get('model', 'Unknown')}")
        allure.dynamic.description(f"设备详情: {device_info}")
        
    except Exception as e:
        log.error(f"获取设备信息失败: {e}")
    
    yield
    
    log.info("=" * 50)
    log.info("测试会话结束")
    log.info("=" * 50)


@pytest.fixture(scope="function")
def setup_test_case():
    """
    测试用例初始化
    每个测试用例执行前都会执行
    """
    log.info("-" * 30)
    log.info("开始执行测试用例")
    
    yield
    
    log.info("测试用例执行完成")
    log.info("-" * 30)


@pytest.fixture(scope="function")
def take_screenshot_on_failure(request):
    """
    测试失败时自动截图
    """
    yield

    # 检查测试是否失败
    if request.node.rep_call.failed:
        try:
            # 生成截图文件名
            test_name = request.node.name
            test_class_name = ""

            # 尝试获取测试类名称
            if hasattr(request.node, 'cls') and request.node.cls:
                test_class_name = request.node.cls.__name__
            elif hasattr(request.node, 'parent') and hasattr(request.node.parent, 'name'):
                test_class_name = request.node.parent.name

            # 设置pytest当前项目信息，供FileUtils使用
            import pytest
            pytest.current_item = request.node

            screenshot_name = f"failure_{test_name}_{FileUtils.get_timestamp()}.png"

            # 截图（使用测试类目录）
            screenshot_path = driver_manager.screenshot(screenshot_name, use_test_class_dir=True)

            # 将截图添加到Allure报告
            allure.attach.file(
                screenshot_path,
                name=f"失败截图-{test_class_name}",
                attachment_type=allure.attachment_type.PNG
            )

            log.info(f"测试失败，已保存截图: {screenshot_path}")

        except Exception as e:
            log.error(f"失败截图保存失败: {e}")
        finally:
            # 清理pytest当前项目信息
            if hasattr(pytest, 'current_item'):
                pytest.current_item = None


@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item, call):
    """
    pytest钩子函数，用于获取测试结果
    """
    outcome = yield
    rep = outcome.get_result()
    setattr(item, "rep_" + rep.when, rep)


@pytest.fixture(scope="function")
def calculator_app():
    """
    计算器应用fixture
    """
    from pages.apps.calculator.main_page import CalculatorMainPage
    
    page = CalculatorMainPage()
    
    # 启动应用
    if not page.start_app():
        pytest.fail("启动计算器应用失败")
    
    # 等待页面加载
    if not page.wait_for_page_load():
        pytest.fail("计算器页面加载失败")
    
    yield page
    
    # 清理：停止应用
    try:
        page.stop_app()
    except Exception as e:
        log.warning(f"停止计算器应用失败: {e}")


@pytest.fixture(scope="function")
def settings_app():
    """
    设置应用fixture
    """
    from pages.apps.settings.main_page import SettingsMainPage
    
    page = SettingsMainPage()
    
    # 启动应用
    if not page.start_app():
        pytest.fail("启动设置应用失败")
    
    # 等待页面加载
    if not page.wait_for_page_load():
        pytest.fail("设置页面加载失败")
    
    yield page
    
    # 清理：停止应用
    try:
        page.stop_app()
    except Exception as e:
        log.warning(f"停止设置应用失败: {e}")


def pytest_configure(config):
    """
    pytest配置钩子
    """
    # 添加自定义标记
    config.addinivalue_line(
        "markers", "smoke: 冒烟测试标记"
    )
    config.addinivalue_line(
        "markers", "regression: 回归测试标记"
    )
    config.addinivalue_line(
        "markers", "critical: 关键功能测试标记"
    )


def pytest_collection_modifyitems(config, items):
    """
    修改测试用例收集
    """
    for item in items:
        # 为所有测试用例添加自动截图fixture
        item.fixturenames.append("take_screenshot_on_failure")
