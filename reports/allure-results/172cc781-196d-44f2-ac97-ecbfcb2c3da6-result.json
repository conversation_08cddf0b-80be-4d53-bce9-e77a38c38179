{"name": "测试set parallel windows返回正确的不支持响应", "status": "passed", "description": "验证set parallel windows指令返回预期的不支持响应", "steps": [{"name": "执行命令: set parallel windows", "status": "passed", "steps": [{"name": "执行命令: set parallel windows", "status": "passed", "start": 1756802350682, "stop": 1756802374977}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "97c296c1-6203-417c-a0bc-25b08b576774-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6500c1d4-ab55-4196-bc1b-de66e6ba7212-attachment.png", "type": "image/png"}], "start": 1756802374977, "stop": 1756802375234}], "start": 1756802350682, "stop": 1756802375234}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756802375234, "stop": 1756802375236}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "464b1f71-5378-4674-afaa-9eec79b4701e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "58037761-7cb4-4c58-99ba-f650be2cd7f7-attachment.png", "type": "image/png"}], "start": 1756802375236, "stop": 1756802375493}], "attachments": [{"name": "stdout", "source": "06d92a96-47d7-4182-a9d5-e21ad9a908d3-attachment.txt", "type": "text/plain"}], "start": 1756802350682, "stop": 1756802375493, "uuid": "90889533-46eb-49e3-be41-e0dee6587544", "historyId": "eb142151b4b5ba4125a1a866dc2b58ef", "testCaseId": "eb142151b4b5ba4125a1a866dc2b58ef", "fullName": "testcases.test_ella.unsupported_commands.test_set_parallel_windows.TestEllaSetParallelWindows#test_set_parallel_windows", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_parallel_windows"}, {"name": "subSuite", "value": "TestEllaSetParallelWindows"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_parallel_windows"}]}