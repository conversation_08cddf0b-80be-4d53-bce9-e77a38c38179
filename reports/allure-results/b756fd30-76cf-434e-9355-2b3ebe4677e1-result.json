{"name": "测试check my to-do list能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['No to-dos today']，实际响应: '['check my to-do list', 'The following images are generated for you.', '', '', '', '', '', '', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_check_my_to_do_list.TestEllaCheckMyDoList object at 0x0000018BC64323E0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC92CE050>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_check_my_to_do_list(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['No to-dos today']，实际响应: '['check my to-do list', 'The following images are generated for you.', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_check_my_to_do_list.py:33: AssertionError"}, "description": "check my to-do list", "steps": [{"name": "执行命令: check my to-do list", "status": "passed", "steps": [{"name": "执行命令: check my to-do list", "status": "passed", "start": 1756795855994, "stop": 1756795886503}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "009d4aaf-c74a-4f2e-80c3-715828a06f70-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "41679d38-2392-4d84-bd02-6782030e2e00-attachment.png", "type": "image/png"}], "start": 1756795886503, "stop": 1756795886776}], "start": 1756795855994, "stop": 1756795886776}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['No to-dos today']，实际响应: '['check my to-do list', 'The following images are generated for you.', '', '', '', '', '', '', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_check_my_to_do_list.py\", line 33, in test_check_my_to_do_list\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756795886776, "stop": 1756795886779}], "attachments": [{"name": "stdout", "source": "db58aa55-1353-431d-933c-0c4f7df9a791-attachment.txt", "type": "text/plain"}], "start": 1756795855994, "stop": 1756795886780, "uuid": "d9436e46-e38d-4e12-a7ab-3bd03a46a016", "historyId": "569e770c250388bfbcf64d0cbbb8b351", "testCaseId": "569e770c250388bfbcf64d0cbbb8b351", "fullName": "testcases.test_ella.unsupported_commands.test_check_my_to_do_list.TestEllaCheckMyDoList#test_check_my_to_do_list", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_my_to_do_list"}, {"name": "subSuite", "value": "TestEllaCheckMyDoList"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_my_to_do_list"}]}