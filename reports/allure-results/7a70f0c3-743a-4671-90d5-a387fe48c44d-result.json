{"name": "测试book a flight to paris返回正确的不支持响应", "status": "passed", "description": "验证book a flight to paris指令返回预期的不支持响应", "steps": [{"name": "执行命令: book a flight to paris", "status": "passed", "steps": [{"name": "执行命令: book a flight to paris", "status": "passed", "start": 1756784691763, "stop": 1756784715589}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "88888aab-51e6-411f-b983-afc4f8e3a873-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "01f695ee-ac63-488b-b78a-678b46b2bd50-attachment.png", "type": "image/png"}], "start": 1756784715589, "stop": 1756784715788}], "start": 1756784691763, "stop": 1756784715789}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756784715789, "stop": 1756784715790}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "db7f903e-1d3f-4b91-84e3-d697a827bd81-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fa00be45-baec-436c-80e4-167f709366c6-attachment.png", "type": "image/png"}], "start": 1756784715790, "stop": 1756784716011}], "attachments": [{"name": "stdout", "source": "c39f777f-b227-4c1b-a599-645a9f68fa84-attachment.txt", "type": "text/plain"}], "start": 1756784691763, "stop": 1756784716011, "uuid": "16860e4f-b6b7-45f5-a396-0137abb244e0", "historyId": "7b1fce8b7d3ff59dca969b439bb82f75", "testCaseId": "7b1fce8b7d3ff59dca969b439bb82f75", "fullName": "testcases.test_ella.dialogue.test_book_a_flight_to_paris.TestEllaBookFlightParis#test_book_a_flight_to_paris", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_book_a_flight_to_paris"}, {"name": "subSuite", "value": "TestEllaBookFlightParis"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_book_a_flight_to_paris"}]}