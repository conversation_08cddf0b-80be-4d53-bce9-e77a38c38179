{"name": "测试remember the parking space", "status": "passed", "description": "测试remember the parking space指令", "steps": [{"name": "执行命令: remember the parking space", "status": "passed", "steps": [{"name": "执行命令: remember the parking space", "status": "passed", "start": 1756801039823, "stop": 1756801064490}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "072a85e1-b93b-417c-80cb-05e799a43345-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3b565d74-72bd-4329-8b6d-7420496274e9-attachment.png", "type": "image/png"}], "start": 1756801064490, "stop": 1756801064710}], "start": 1756801039823, "stop": 1756801064712}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756801064712, "stop": 1756801064713}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e73a3f62-0adc-470f-97cd-0d5abbf613af-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9c705890-803f-4118-8f84-fd5137ba979d-attachment.png", "type": "image/png"}], "start": 1756801064713, "stop": 1756801064974}], "attachments": [{"name": "stdout", "source": "d6c6cf1a-f021-4665-befe-e06e8bfa1eee-attachment.txt", "type": "text/plain"}], "start": 1756801039823, "stop": 1756801064975, "uuid": "238e8456-d72e-4d83-b373-6ce6538b62ab", "historyId": "bd4f9d0c0f70cf6b24bb9923810b25c1", "testCaseId": "bd4f9d0c0f70cf6b24bb9923810b25c1", "fullName": "testcases.test_ella.unsupported_commands.test_remember_the_parking_space.TestEllaOpenPlayPoliticalNews#test_remember_the_parking_space", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_remember_the_parking_space"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_remember_the_parking_space"}]}