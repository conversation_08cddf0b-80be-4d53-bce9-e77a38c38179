{"name": "测试smart charge能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['set default charging mode']，实际响应: '['smart charge', 'The current charging device does not support switching charging modes.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.system_coupling.test_smart_charge.TestEllaSmartCharge object at 0x0000018BC61C0670>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC60BAFE0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_smart_charge(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含在期望中\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['set default charging mode']，实际响应: '['smart charge', 'The current charging device does not support switching charging modes.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_smart_charge.py:33: AssertionError"}, "description": "smart charge", "steps": [{"name": "执行命令: smart charge", "status": "passed", "steps": [{"name": "执行命令: smart charge", "status": "passed", "start": 1756791074178, "stop": 1756791094018}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2d6f5e86-3d0d-4267-a76c-245b849182db-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d26cb4fb-f73e-473c-9721-9d30e7a1caf8-attachment.png", "type": "image/png"}], "start": 1756791094018, "stop": 1756791094252}], "start": 1756791074178, "stop": 1756791094252}, {"name": "验证响应包含在期望中", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['set default charging mode']，实际响应: '['smart charge', 'The current charging device does not support switching charging modes.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\system_coupling\\test_smart_charge.py\", line 33, in test_smart_charge\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756791094252, "stop": 1756791094254}], "attachments": [{"name": "stdout", "source": "ef3b3eb0-c9b6-4af5-8aa0-d4477803a7f8-attachment.txt", "type": "text/plain"}], "start": 1756791074178, "stop": 1756791094255, "uuid": "bfad8ace-b845-4ff8-a043-84a1619261eb", "historyId": "7c74ed3e622ad29dd79be61222ad59bc", "testCaseId": "7c74ed3e622ad29dd79be61222ad59bc", "fullName": "testcases.test_ella.system_coupling.test_smart_charge.TestEllaSmartCharge#test_smart_charge", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_smart_charge"}, {"name": "subSuite", "value": "TestEllaSmartCharge"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_smart_charge"}]}