{"name": "测试navigate from to red square能正常执行", "status": "passed", "description": "navigate from to red square", "steps": [{"name": "执行命令: navigate from to red square", "status": "passed", "steps": [{"name": "执行命令: navigate from to red square", "status": "passed", "start": 1756794487628, "stop": 1756794518586}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f4eb8bfe-c898-4165-a418-c85485d1dcb3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fb912e86-dc76-4098-8b85-f5875ec068aa-attachment.png", "type": "image/png"}], "start": 1756794518586, "stop": 1756794518793}], "start": 1756794487628, "stop": 1756794518794}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756794518794, "stop": 1756794518795}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5fbb12f5-3a24-4354-9abf-f42803c58233-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "994256a2-dbd6-4a02-b98a-bc47eb2a13b6-attachment.png", "type": "image/png"}], "start": 1756794518795, "stop": 1756794518998}], "attachments": [{"name": "stdout", "source": "e38580f8-2d63-4a11-9d23-8f2cb83ce017-attachment.txt", "type": "text/plain"}], "start": 1756794487628, "stop": 1756794518998, "uuid": "f9a824d4-c55b-4075-8a25-05431e608160", "historyId": "e8f03971277a71512b5ebaad612bc964", "testCaseId": "e8f03971277a71512b5ebaad612bc964", "fullName": "testcases.test_ella.third_coupling.test_navigate_from_to_red_square.TestEllaNavigateFromRedSquare#test_navigate_from_to_red_square", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_navigate_from_to_red_square"}, {"name": "subSuite", "value": "TestEllaNavigateFromRedSquare"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_navigate_from_to_red_square"}]}