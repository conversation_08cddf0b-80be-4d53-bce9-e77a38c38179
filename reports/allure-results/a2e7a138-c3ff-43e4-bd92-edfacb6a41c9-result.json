{"name": "测试vedio call number by whatsapp能正常执行", "status": "passed", "description": "vedio call number by whatsapp", "steps": [{"name": "执行命令: vedio call number by whatsapp", "status": "passed", "steps": [{"name": "执行命令: vedio call number by whatsapp", "status": "passed", "start": 1756804074073, "stop": 1756804097813}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "568690fb-18d7-45d2-9aa7-98cd34733c95-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3ac9c0d1-0e72-45f5-bb30-73c7422a51d0-attachment.png", "type": "image/png"}], "start": 1756804097813, "stop": 1756804098080}], "start": 1756804074073, "stop": 1756804098081}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756804098081, "stop": 1756804098085}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0264b82f-2a06-4114-8e94-07790f636e35-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e5b7277b-221f-4ac9-909d-26e8b043d9da-attachment.png", "type": "image/png"}], "start": 1756804098085, "stop": 1756804098337}], "attachments": [{"name": "stdout", "source": "bc77ae41-da2a-4289-a037-5c4d4c9bf011-attachment.txt", "type": "text/plain"}], "start": 1756804074073, "stop": 1756804098338, "uuid": "f1ae3886-7efc-475d-853e-39bf76b056b5", "historyId": "600ddf60808e2a751a4a4742a65811c7", "testCaseId": "600ddf60808e2a751a4a4742a65811c7", "fullName": "testcases.test_ella.unsupported_commands.test_vedio_call_number_by_whatsapp.TestEllaVedioCallNumberWhatsapp#test_vedio_call_number_by_whatsapp", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_vedio_call_number_by_whatsapp"}, {"name": "subSuite", "value": "TestEllaVedioCallNumberWhatsapp"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_vedio_call_number_by_whatsapp"}]}