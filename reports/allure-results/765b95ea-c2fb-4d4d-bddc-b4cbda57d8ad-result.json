{"name": "测试open bt", "status": "passed", "description": "测试open bt指令", "steps": [{"name": "执行命令: open bt", "status": "passed", "steps": [{"name": "执行命令: open bt", "status": "passed", "start": 1756790333899, "stop": 1756790355962}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "384b4843-5c7a-4e40-ac40-dd1593ee8413-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "77689efb-3902-4a88-9a7f-3648de80187d-attachment.png", "type": "image/png"}], "start": 1756790355962, "stop": 1756790356164}], "start": 1756790333899, "stop": 1756790356164}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790356165, "stop": 1756790356166}, {"name": "验证bluetooth已打开", "status": "passed", "start": 1756790356166, "stop": 1756790356166}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1cbaf77c-9ce0-431b-9bb0-f06211f2cca3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "21843eef-d329-4132-81e7-77d77d56104a-attachment.png", "type": "image/png"}], "start": 1756790356166, "stop": 1756790356361}], "attachments": [{"name": "stdout", "source": "75fdf7e2-f145-4f5d-83aa-a8b848d08f63-attachment.txt", "type": "text/plain"}], "start": 1756790333899, "stop": 1756790356361, "uuid": "3bf1279b-694c-411a-9af0-763df86c3c70", "historyId": "9511be8e6426d5078713c6e78f3b02e3", "testCaseId": "9511be8e6426d5078713c6e78f3b02e3", "fullName": "testcases.test_ella.system_coupling.test_open_bt.TestEllaOpenBluetooth#test_open_bt", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_open_bt"}, {"name": "subSuite", "value": "TestEllaOpenBluetooth"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_open_bt"}]}