{"name": "测试check contacts能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Who do you want to check?']，实际响应: '['check contacts', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_check_contacts.TestEllaCheckContacts object at 0x0000018BC6430D30>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC92CE4D0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_check_contacts(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Who do you want to check?']，实际响应: '['check contacts', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_check_contacts.py:33: AssertionError"}, "description": "check contacts", "steps": [{"name": "执行命令: check contacts", "status": "passed", "steps": [{"name": "执行命令: check contacts", "status": "passed", "start": 1756795686161, "stop": 1756795717321}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a5ebcf20-3fba-41b2-96a0-9a33cb804c8f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8e122e9e-f6b2-459d-97de-2b604f91dcad-attachment.png", "type": "image/png"}], "start": 1756795717321, "stop": 1756795717588}], "start": 1756795686161, "stop": 1756795717588}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Who do you want to check?']，实际响应: '['check contacts', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_check_contacts.py\", line 33, in test_check_contacts\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756795717588, "stop": 1756795717590}], "attachments": [{"name": "stdout", "source": "bf5edee3-4103-4151-b7f0-c4a7c544d9bf-attachment.txt", "type": "text/plain"}], "start": 1756795686161, "stop": 1756795717592, "uuid": "b0a177db-3f8e-4441-a2a5-e88ec48a192d", "historyId": "a3c84dd7a2924ee198fdb33cbc4e20b6", "testCaseId": "a3c84dd7a2924ee198fdb33cbc4e20b6", "fullName": "testcases.test_ella.unsupported_commands.test_check_contacts.TestEllaCheckContacts#test_check_contacts", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_contacts"}, {"name": "subSuite", "value": "TestEllaCheckContacts"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_contacts"}]}