{"name": "测试turn up ring volume能正常执行", "status": "passed", "description": "turn up ring volume", "steps": [{"name": "执行命令: turn up ring volume", "status": "passed", "steps": [{"name": "执行命令: turn up ring volume", "status": "passed", "start": 1756793231173, "stop": 1756793253692}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "212eabd4-5876-479e-b1cc-b62e7b9c28a5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9f0848b3-6cbf-475a-bb32-a6d80db25215-attachment.png", "type": "image/png"}], "start": 1756793253692, "stop": 1756793253898}], "start": 1756793231173, "stop": 1756793253898}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756793253898, "stop": 1756793253899}, {"name": "验证ring volume提升", "status": "passed", "start": 1756793253899, "stop": 1756793253899}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d11e3083-d22e-44d6-8699-c9202e9ec0da-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cc276bee-7c44-404e-b127-0d181541a038-attachment.png", "type": "image/png"}], "start": 1756793253899, "stop": 1756793254106}], "attachments": [{"name": "stdout", "source": "95508832-dd6d-4f1e-a471-48cd9ff177e6-attachment.txt", "type": "text/plain"}], "start": 1756793231172, "stop": 1756793254107, "uuid": "9e3a1482-6be0-4590-9747-eb4118336caa", "historyId": "7b90ee3ed7bdd2837a37215aac61cfd9", "testCaseId": "7b90ee3ed7bdd2837a37215aac61cfd9", "fullName": "testcases.test_ella.system_coupling.test_turn_up_ring_volume.TestEllaTurnUpRingVolume#test_turn_up_ring_volume", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_up_ring_volume"}, {"name": "subSuite", "value": "TestEllaTurnUpRingVolume"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_up_ring_volume"}]}