{"name": "测试remember the parking lot能正常执行", "status": "passed", "description": "remember the parking lot", "steps": [{"name": "执行命令: remember the parking lot", "status": "passed", "steps": [{"name": "执行命令: remember the parking lot", "status": "passed", "start": 1756800997894, "stop": 1756801021005}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "93e3f82e-19b7-4f88-9310-3c86f886e333-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4239f812-b61d-49a6-845f-2802135a0952-attachment.png", "type": "image/png"}], "start": 1756801021005, "stop": 1756801021257}], "start": 1756800997894, "stop": 1756801021257}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756801021257, "stop": 1756801021258}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c3a38c2b-6f33-4c00-bceb-3940db7d42ee-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "22529faa-2f13-47a5-a0a1-a49c51e4b43e-attachment.png", "type": "image/png"}], "start": 1756801021258, "stop": 1756801021517}], "attachments": [{"name": "stdout", "source": "168928dc-c094-4c4f-8d91-464e9d809eea-attachment.txt", "type": "text/plain"}], "start": 1756800997894, "stop": 1756801021517, "uuid": "04086cb2-0ac7-46d2-b8a3-1209f67d3d7f", "historyId": "31c983fdcc8b62f5927f99c0482b828d", "testCaseId": "31c983fdcc8b62f5927f99c0482b828d", "fullName": "testcases.test_ella.unsupported_commands.test_remember_the_parking_lot.TestEllaRememberParkingLot#test_remember_the_parking_lot", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_remember_the_parking_lot"}, {"name": "subSuite", "value": "TestEllaRememberParkingLot"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_remember_the_parking_lot"}]}