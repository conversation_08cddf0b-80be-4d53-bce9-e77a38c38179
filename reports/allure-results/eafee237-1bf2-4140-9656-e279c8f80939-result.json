{"name": "测试set Battery Saver setting能正常执行", "status": "passed", "description": "set Battery Saver setting", "steps": [{"name": "执行命令: set Battery Saver setting", "status": "passed", "steps": [{"name": "执行命令: set Battery Saver setting", "status": "passed", "start": 1756790800703, "stop": 1756790828350}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6ba433f7-cb36-4310-9629-c73f9a93b706-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d84a0d15-ce40-4e5b-9ffd-8d31910d9c3b-attachment.png", "type": "image/png"}], "start": 1756790828350, "stop": 1756790828567}], "start": 1756790800703, "stop": 1756790828567}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790828567, "stop": 1756790828569}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fabdb082-6ed0-4138-bc14-630d61079df7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0edd29a5-f7fc-4b63-a560-51b5d4e4f772-attachment.png", "type": "image/png"}], "start": 1756790828569, "stop": 1756790828766}], "attachments": [{"name": "stdout", "source": "bd54d627-214f-42fd-b723-70a9ee67e4c1-attachment.txt", "type": "text/plain"}], "start": 1756790800703, "stop": 1756790828767, "uuid": "ef3e9b48-bce5-467c-85bf-3045ebbada7d", "historyId": "92c8c8e017b096314ffde2f610a6791e", "testCaseId": "92c8c8e017b096314ffde2f610a6791e", "fullName": "testcases.test_ella.system_coupling.test_set_battery_saver_setting.TestEllaSetBatterySaverSetting#test_set_battery_saver_setting", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_battery_saver_setting"}, {"name": "subSuite", "value": "TestEllaSetBatterySaverSetting"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_battery_saver_setting"}]}