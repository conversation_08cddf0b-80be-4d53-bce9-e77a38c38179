{"name": "测试play music on boomplayer", "status": "passed", "description": "测试play music on boomplayer指令", "steps": [{"name": "执行命令: play music on boomplayer", "status": "passed", "steps": [{"name": "执行命令: play music on boomplayer", "status": "passed", "start": 1756786286433, "stop": 1756786309859}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "76bb3e70-0018-4ae2-bdcc-52c7b1988adf-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "bc87cd95-ed3b-4ee8-b59d-2a4445712af5-attachment.png", "type": "image/png"}], "start": 1756786309859, "stop": 1756786310076}], "start": 1756786286433, "stop": 1756786310077}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756786310077, "stop": 1756786310078}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b8804de5-59b0-4b79-931a-70eba83883dc-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "79d59762-0003-4286-b5d5-f7a18b9b6231-attachment.png", "type": "image/png"}], "start": 1756786310078, "stop": 1756786310287}], "attachments": [{"name": "stdout", "source": "ae99fd59-579d-40cc-935b-f5fe9403adc5-attachment.txt", "type": "text/plain"}], "start": 1756786286433, "stop": 1756786310288, "uuid": "48aa58f0-1032-4cdb-88a4-8781ee459891", "historyId": "0e7fd56ff1d5c85e0ce1830d9899313d", "testCaseId": "0e7fd56ff1d5c85e0ce1830d9899313d", "fullName": "testcases.test_ella.dialogue.test_play_music_on_boomplayer.TestEllaOpenPlayPoliticalNews#test_play_music_on_boomplayer", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_music_on_boomplayer"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_music_on_boomplayer"}]}