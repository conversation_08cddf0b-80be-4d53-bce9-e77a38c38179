"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaOpenMaps(SimpleEllaTest):
    """Ella打开youtube测试类"""

    @allure.title("测试display the route go company")
    @allure.description("测试display the route go company指令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_display_the_route_go_company(self, ella_app):
        """测试display the route go company命令"""
        command = "display the route go company"
        expected_text = ['No company address set up yet']
        app_name = 'maps'

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应包含期望内容"):

            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        # with allure.step(f"验证{app_name}已打开"):
        #     assert final_status, f"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
