{"name": "测试help me generate a picture of an airplane", "status": "passed", "description": "测试help me generate a picture of an airplane指令", "steps": [{"name": "执行命令: help me generate a picture of an airplane", "status": "passed", "steps": [{"name": "执行命令: help me generate a picture of an airplane", "status": "passed", "start": 1756798172957, "stop": 1756798196454}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0ef2665d-ff05-4fd2-8d4c-4d050b53e4d7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "379183f2-04de-4dda-b317-3df7ceb12af4-attachment.png", "type": "image/png"}], "start": 1756798196454, "stop": 1756798196673}], "start": 1756798172957, "stop": 1756798196674}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756798196674, "stop": 1756798196675}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a9640cd0-2b5f-4532-ad52-6b656b64fa4d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c7cf0fbd-53aa-4be6-8078-546bb0ac7fcb-attachment.png", "type": "image/png"}], "start": 1756798196675, "stop": 1756798196904}], "attachments": [{"name": "stdout", "source": "b997c580-58e3-4fd8-a735-52f74d8b3f16-attachment.txt", "type": "text/plain"}], "start": 1756798172957, "stop": 1756798196904, "uuid": "6a9363f1-0a8d-42b4-95dd-daf5b9640b54", "historyId": "411d5cfcc1960041b8df4decf67232f6", "testCaseId": "411d5cfcc1960041b8df4decf67232f6", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_an_airplane.TestEllaOpenPlayPoliticalNews#test_help_me_generate_a_picture_of_an_airplane", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_generate_a_picture_of_an_airplane"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_an_airplane"}]}