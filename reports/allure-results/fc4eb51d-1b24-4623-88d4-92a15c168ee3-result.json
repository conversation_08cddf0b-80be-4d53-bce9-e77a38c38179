{"name": "测试disable accelerate dialogue返回正确的不支持响应", "status": "passed", "description": "验证disable accelerate dialogue指令返回预期的不支持响应", "steps": [{"name": "执行命令: disable accelerate dialogue", "status": "passed", "steps": [{"name": "执行命令: disable accelerate dialogue", "status": "passed", "start": 1756796248279, "stop": 1756796278677}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ced020ea-b24d-4985-88bf-14d971a3c032-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "100bb075-bd82-48ed-a91e-007591c48549-attachment.png", "type": "image/png"}], "start": 1756796278677, "stop": 1756796278893}], "start": 1756796248279, "stop": 1756796278894}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756796278894, "stop": 1756796278896}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "347da9f5-7d35-4622-b3c4-9d14556bc8fd-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7acb63f7-95aa-4bc0-8a38-4775aacb7601-attachment.png", "type": "image/png"}], "start": 1756796278896, "stop": 1756796279117}], "attachments": [{"name": "stdout", "source": "0f7c5c91-58ca-4899-b5c1-28ac0bc5f75e-attachment.txt", "type": "text/plain"}], "start": 1756796248279, "stop": 1756796279118, "uuid": "20dc61b5-88fa-4072-87be-0d2e3a6350ab", "historyId": "4cfe8e55b2a91a62bbf1141ffc0cc530", "testCaseId": "4cfe8e55b2a91a62bbf1141ffc0cc530", "fullName": "testcases.test_ella.unsupported_commands.test_disable_accelerate_dialogue.TestEllaDisableAccelerateDialogue#test_disable_accelerate_dialogue", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_accelerate_dialogue"}, {"name": "subSuite", "value": "TestEllaDisableAccelerateDialogue"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_accelerate_dialogue"}]}