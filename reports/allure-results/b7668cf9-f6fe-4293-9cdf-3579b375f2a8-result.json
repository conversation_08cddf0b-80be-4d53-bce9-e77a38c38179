{"name": "测试add the mom's and lucy's number", "status": "failed", "statusDetails": {"message": "Failed: 响应中未找到任何期望关键词: ['following number is recognized', 'save it as a contact']", "trace": "self = <testcases.test_ella.test_ask_screen.contact.test_add_the_mom_s_and_lucy_s_number.TestAskScreenAddMomSLucySNumber object at 0x0000018BC627BEB0>\nella_floating_page = <pages.apps.ella.floating_page.EllaFloatingPage object at 0x0000018BC7F974F0>\n\n    @allure.title(\"测试add the mom's and lucy's number\")\n    @allure.description(\"测试Ask Screen功能: add the mom's and lucy's number\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_add_the_mom_s_and_lucy_s_number(self, ella_floating_page):\n        \"\"\"测试add the mom's and lucy's number命令\"\"\"\n        command = \"add the mom's and lucy's number\"\n        expected_keywords = ['following number is recognized', 'save it as a contact']\n    \n        # 数据准备\n        with allure.step(\"准备测试数据\"):\n            from tools.file_pusher import file_pusher\n            # 推送测试图片到设备\n            push_result = file_pusher.push_ask_screen_image(\"add the mom's and lucy's number\")\n            assert push_result, f\"推送图片失败: add the mom's and lucy's number\"\n    \n            # 打开图库并选择图片\n            photos_page = AiGalleryPhotosPage()\n            result = photos_page.start_app()\n            if result:\n                photos_page.wait_for_page_load()\n                photos_page.click_photo()\n    \n        # 执行命令并验证\n        with allure.step(f\"执行Ask Screen命令: {command}\"):\n            success, response_texts, verification_result = self.simple_floating_command_test(\n                ella_floating_page, command, expected_keywords, verify_response=True\n            )\n            # 关闭图库应用\n            photos_page.stop_app()\n        # 断言结果\n        with allure.step(\"验证测试结果\"):\n            assert success, f\"命令执行失败: {command}\"\n            assert response_texts, \"未获取到响应文本\"\n    \n            # 验证响应包含期望关键词（至少匹配一个）\n>           self.assert_floating_response_contains(response_texts, expected_keywords, match_all=False)\n\ntestcases\\test_ella\\test_ask_screen\\contact\\test_add_the_mom_s_and_lucy_s_number.py:53: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <testcases.test_ella.test_ask_screen.contact.test_add_the_mom_s_and_lucy_s_number.TestAskScreenAddMomSLucySNumber object at 0x0000018BC627BEB0>\nresponse_texts = ['No number recognized. Please check and try again.', \"add the mom's and lucy's number\"], expected_keywords = ['following number is recognized', 'save it as a contact']\nmatch_all = False\n\n    def assert_floating_response_contains(self, response_texts: List[str],\n                                          expected_keywords: List[str],\n                                          match_all: bool = True):\n        \"\"\"\n        断言浮窗响应包含期望的关键词\n    \n        Args:\n            response_texts: 响应文本列表\n            expected_keywords: 期望的关键词列表\n            match_all: 是否需要匹配所有关键词，False表示匹配任意一个即可\n        \"\"\"\n        if not response_texts:\n            pytest.fail(\"响应文本为空\")\n    \n        if not expected_keywords:\n            log.info(\"✅ 未指定期望关键词，跳过断言\")\n            return\n    \n        all_response_text = \" \".join(response_texts).lower()\n    \n        if match_all:\n            # 需要匹配所有关键词\n            missing_keywords = []\n            for keyword in expected_keywords:\n                if keyword.lower() not in all_response_text:\n                    missing_keywords.append(keyword)\n    \n            if missing_keywords:\n                pytest.fail(f\"响应中缺失期望关键词: {missing_keywords}\")\n            else:\n                log.info(f\"✅ 响应包含所有期望关键词: {expected_keywords}\")\n        else:\n            # 只需要匹配任意一个关键词\n            found_any = any(keyword.lower() in all_response_text for keyword in expected_keywords)\n    \n            if not found_any:\n>               pytest.fail(f\"响应中未找到任何期望关键词: {expected_keywords}\")\nE               Failed: 响应中未找到任何期望关键词: ['following number is recognized', 'save it as a contact']\n\ntestcases\\test_ella\\test_ask_screen\\base_ask_screen_test.py:404: Failed"}, "description": "测试Ask Screen功能: add the mom's and lucy's number", "steps": [{"name": "准备测试数据", "status": "passed", "start": 1756793534938, "stop": 1756793543132}, {"name": "执行Ask Screen命令: add the mom's and lucy's number", "status": "passed", "steps": [{"name": "执行浮窗命令: add the mom's and lucy's number", "status": "passed", "steps": [{"name": "执行命令: add the mom's and lucy's number", "status": "passed", "start": 1756793543132, "stop": 1756793552300}, {"name": "等待并获取AI响应", "status": "passed", "start": 1756793552301, "stop": 1756793568308}, {"name": "验证响应内容", "status": "passed", "attachments": [{"name": "关键词验证结果", "source": "265864be-aba3-44f0-96d7-1e123a26ec68-attachment.txt", "type": "text/plain"}], "start": 1756793568308, "stop": 1756793568310}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "浮窗测试总结", "source": "0eb22c23-7fb6-4105-ab02-2258f3085dd1-attachment.txt", "type": "text/plain"}, {"name": "AI响应内容", "source": "81b9631e-a819-41e4-961e-c2e06941bd72-attachment.txt", "type": "text/plain"}], "start": 1756793568310, "stop": 1756793568312}], "start": 1756793543132, "stop": 1756793568312}, {"name": "截图记录测试完成状态", "status": "passed", "attachments": [{"name": "floating_test_completed", "source": "760ea6ef-e10d-485b-b416-3cacde60412e-attachment.png", "type": "image/png"}], "start": 1756793568312, "stop": 1756793568541}], "start": 1756793543132, "stop": 1756793569766}, {"name": "验证测试结果", "status": "failed", "statusDetails": {"message": "Failed: 响应中未找到任何期望关键词: ['following number is recognized', 'save it as a contact']\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\test_ask_screen\\contact\\test_add_the_mom_s_and_lucy_s_number.py\", line 53, in test_add_the_mom_s_and_lucy_s_number\n    self.assert_floating_response_contains(response_texts, expected_keywords, match_all=False)\n  File \"D:\\app_test\\testcases\\test_ella\\test_ask_screen\\base_ask_screen_test.py\", line 404, in assert_floating_response_contains\n    pytest.fail(f\"响应中未找到任何期望关键词: {expected_keywords}\")\n  File \"D:\\app_test\\.venv\\lib\\site-packages\\_pytest\\outcomes.py\", line 177, in fail\n    raise Failed(msg=reason, pytrace=pytrace)\n"}, "start": 1756793569766, "stop": 1756793569766}], "attachments": [{"name": "stdout", "source": "28858a8a-f269-440e-837f-f4ce3c685131-attachment.txt", "type": "text/plain"}], "start": 1756793534938, "stop": 1756793569768, "uuid": "54a7167f-2e55-4a85-ab68-2edd904854c3", "historyId": "274ccc19fc9f00ce49f6545c8286db01", "testCaseId": "274ccc19fc9f00ce49f6545c8286db01", "fullName": "testcases.test_ella.test_ask_screen.contact.test_add_the_mom_s_and_lucy_s_number.TestAskScreenAddMomSLucySNumber#test_add_the_mom_s_and_lucy_s_number", "labels": [{"name": "epic", "value": "Ella浮窗测试"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ask Screen功能"}, {"name": "story", "value": "联系人相关"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.test_ask_screen.contact"}, {"name": "suite", "value": "test_add_the_mom_s_and_lucy_s_number"}, {"name": "subSuite", "value": "TestAskScreenAddMomSLucySNumber"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_ask_screen.contact.test_add_the_mom_s_and_lucy_s_number"}]}