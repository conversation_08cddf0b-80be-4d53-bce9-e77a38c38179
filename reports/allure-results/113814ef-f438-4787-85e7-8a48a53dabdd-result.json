{"name": "测试enable unfreeze返回正确的不支持响应", "status": "passed", "description": "验证enable unfreeze指令返回预期的不支持响应", "steps": [{"name": "执行命令: enable unfreeze", "status": "passed", "steps": [{"name": "执行命令: enable unfreeze", "status": "passed", "start": 1756797277751, "stop": 1756797302519}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a0a270f2-e3f0-45d6-b0dc-e0e7c71544b4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "dcd3e7ff-6e51-41ee-bf10-81d53e11b70f-attachment.png", "type": "image/png"}], "start": 1756797302519, "stop": 1756797302748}], "start": 1756797277751, "stop": 1756797302748}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756797302748, "stop": 1756797302749}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fa66bbd3-43c1-4c69-a8a1-77cedc92788f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f2905d66-dd6e-406e-8d97-16f11a13d552-attachment.png", "type": "image/png"}], "start": 1756797302749, "stop": 1756797302984}], "attachments": [{"name": "stdout", "source": "1a27748a-aa2e-4452-befd-5c1b0da39f3b-attachment.txt", "type": "text/plain"}], "start": 1756797277751, "stop": 1756797302984, "uuid": "26b7c9e2-9d53-4edc-8ab3-0d25ffa482cc", "historyId": "afa6af304cfb25a990764680de5fa777", "testCaseId": "afa6af304cfb25a990764680de5fa777", "fullName": "testcases.test_ella.unsupported_commands.test_enable_unfreeze.TestEllaEnableUnfreeze#test_enable_unfreeze", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_unfreeze"}, {"name": "subSuite", "value": "TestEllaEnableUnfreeze"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_unfreeze"}]}