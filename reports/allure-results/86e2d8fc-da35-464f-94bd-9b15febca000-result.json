{"name": "测试check battery information返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['check battery information', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_check_battery_information.TestEllaCheckBatteryInformation object at 0x0000018BC6430070>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC69767D0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_check_battery_information(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['check battery information', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_check_battery_information.py:34: AssertionError"}, "description": "验证check battery information指令返回预期的不支持响应", "steps": [{"name": "执行命令: check battery information", "status": "passed", "steps": [{"name": "执行命令: check battery information", "status": "passed", "start": 1756795597498, "stop": 1756795620234}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7cc2590b-30fd-4479-832b-2e1895011b42-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "66e7ffe5-e9f3-4ac4-adad-920bc930acbb-attachment.png", "type": "image/png"}], "start": 1756795620234, "stop": 1756795620456}], "start": 1756795597498, "stop": 1756795620457}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['check battery information', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_check_battery_information.py\", line 34, in test_check_battery_information\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756795620457, "stop": 1756795620459}], "attachments": [{"name": "stdout", "source": "d4c7339b-38c1-4b8e-b88d-69311b2e2949-attachment.txt", "type": "text/plain"}], "start": 1756795597498, "stop": 1756795620460, "uuid": "08f46688-81c0-42c4-951c-f6b6a0447d40", "historyId": "613ef0933e4be87696bbedd56b4f0052", "testCaseId": "613ef0933e4be87696bbedd56b4f0052", "fullName": "testcases.test_ella.unsupported_commands.test_check_battery_information.TestEllaCheckBatteryInformation#test_check_battery_information", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_battery_information"}, {"name": "subSuite", "value": "TestEllaCheckBatteryInformation"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_battery_information"}]}