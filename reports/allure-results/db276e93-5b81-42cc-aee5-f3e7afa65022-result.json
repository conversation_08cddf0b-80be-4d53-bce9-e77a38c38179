{"name": "stop  screen recording能正常执行", "status": "passed", "description": "stop  screen recording", "steps": [{"name": "执行命令: stop screen recording", "status": "passed", "steps": [{"name": "执行命令: stop screen recording", "status": "passed", "start": 1756790539654, "stop": 1756790567438}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2558eb36-f0f8-4c72-ba3e-c1bcdac4635c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "bd70c060-31a9-4782-9345-8ef2705fb92a-attachment.png", "type": "image/png"}], "start": 1756790567438, "stop": 1756790567636}], "start": 1756790539654, "stop": 1756790567636}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790567636, "stop": 1756790567637}, {"name": "验证已打开", "status": "passed", "start": 1756790567637, "stop": 1756790567637}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c8f9e142-4ad8-4af6-a7a6-f389c5813506-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b69dc86d-8e6b-44c0-82ed-a1bec68443a8-attachment.png", "type": "image/png"}], "start": 1756790567637, "stop": 1756790567839}], "attachments": [{"name": "stdout", "source": "9e5f5a5c-012b-4656-abce-3e0eb04ba607-attachment.txt", "type": "text/plain"}], "start": 1756790539654, "stop": 1756790567839, "uuid": "ede57333-2642-4651-afc7-fa3f21f32a09", "historyId": "75c9edd252211f9e74fd8c1a2faeefd1", "testCaseId": "75c9edd252211f9e74fd8c1a2faeefd1", "fullName": "testcases.test_ella.system_coupling.test_screen_record.TestEllaScreenRecord#test_stop_screen_recording", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_screen_record"}, {"name": "subSuite", "value": "TestEllaScreenRecord"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_screen_record"}]}