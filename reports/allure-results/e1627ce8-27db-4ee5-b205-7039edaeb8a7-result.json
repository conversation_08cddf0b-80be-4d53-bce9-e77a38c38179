{"name": "测试check front camera information能正常执行", "status": "passed", "description": "check front camera information", "steps": [{"name": "执行命令: check front camera information", "status": "passed", "steps": [{"name": "执行命令: check front camera information", "status": "passed", "start": 1756789010222, "stop": 1756789044325}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "11957a46-31bb-4b9b-8a15-4ca9cfbe7c54-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1c22343e-e03e-4c63-9b86-526cda9b47c2-attachment.png", "type": "image/png"}], "start": 1756789044325, "stop": 1756789044559}], "start": 1756789010222, "stop": 1756789044559}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756789044559, "stop": 1756789044560}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5764d6ca-7ce4-4e2d-a36d-996b502b468c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b51655cf-21d8-4ce7-831c-639b06b752c2-attachment.png", "type": "image/png"}], "start": 1756789044560, "stop": 1756789044748}], "attachments": [{"name": "stdout", "source": "3d4e2abd-83e6-48d8-bda7-dcccbbe51b2a-attachment.txt", "type": "text/plain"}], "start": 1756789010221, "stop": 1756789044749, "uuid": "1d53e859-c0b6-4de7-a143-14acbcb65432", "historyId": "7fb2c589f1fda51205a5af1b549d5045", "testCaseId": "7fb2c589f1fda51205a5af1b549d5045", "fullName": "testcases.test_ella.system_coupling.test_check_front_camera_information.TestEllaCheckFrontCameraInformation#test_check_front_camera_information", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_check_front_camera_information"}, {"name": "subSuite", "value": "TestEllaCheckFrontCameraInformation"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_check_front_camera_information"}]}