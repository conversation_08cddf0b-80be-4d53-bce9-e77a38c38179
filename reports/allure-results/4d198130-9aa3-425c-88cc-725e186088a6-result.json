{"name": "测试adjustment the brightness to maximun能正常执行", "status": "passed", "description": "adjustment the brightness to maximun", "steps": [{"name": "执行命令: adjustment the brightness to maximun", "status": "passed", "steps": [{"name": "执行命令: adjustment the brightness to maximun", "status": "passed", "start": 1756788882646, "stop": 1756788905146}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7794eb62-5845-4647-9acc-33ca1a49bfbe-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "03d7728a-cd58-45da-aac0-a54ac89dca5f-attachment.png", "type": "image/png"}], "start": 1756788905147, "stop": 1756788905359}], "start": 1756788882646, "stop": 1756788905360}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756788905360, "stop": 1756788905361}, {"name": "验证应用已打开", "status": "passed", "start": 1756788905361, "stop": 1756788905361}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4242bae3-2771-4bb9-9a0e-c975366479ee-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "62cc568e-96a7-4303-9a55-b52fcbb2a7c4-attachment.png", "type": "image/png"}], "start": 1756788905361, "stop": 1756788905574}], "attachments": [{"name": "stdout", "source": "*************-42fd-a500-6a9e87cac703-attachment.txt", "type": "text/plain"}], "start": 1756788882646, "stop": 1756788905574, "uuid": "078dab15-c7c6-40a2-90b9-a3c554f72a6c", "historyId": "ce2018f4cca8041a465d2a753ade920b", "testCaseId": "ce2018f4cca8041a465d2a753ade920b", "fullName": "testcases.test_ella.system_coupling.test_adjustment_the_brightness_to_maximun.TestEllaAdjustmentBrightnessMaximun#test_adjustment_the_brightness_to_maximun", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_adjustment_the_brightness_to_maximun"}, {"name": "subSuite", "value": "TestEllaAdjustmentBrightnessMaximun"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_adjustment_the_brightness_to_maximun"}]}