{"name": "测试start screen recording能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Screen recording started']，实际响应: '['start screen recording', 'Screen recording is already started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.system_coupling.test_start_screen_recording.TestEllaStartScreenRecording object at 0x0000018BC61AAA40>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC4DFCA00>\n\n    @allure.title(f\"测试start screen recording能正常执行\")\n    @allure.description(f\"start screen recording\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_start_screen_recording(self, ella_app):\n        command = \"start screen recording\"\n        expected_text = ['Screen recording started']\n        f\"\"\"{command}\"\"\"\n    \n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Screen recording started']，实际响应: '['start screen recording', 'Screen recording is already started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_start_screen_recording.py:32: AssertionError"}, "description": "start screen recording", "steps": [{"name": "执行命令: start screen recording", "status": "passed", "steps": [{"name": "执行命令: start screen recording", "status": "passed", "start": 1756791154256, "stop": 1756791181596}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e59db8a5-8c9d-45c3-83bf-078bbaa85aba-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8fd510d5-0a86-4479-8920-fc38639aac6c-attachment.png", "type": "image/png"}], "start": 1756791181596, "stop": 1756791181809}], "start": 1756791154256, "stop": 1756791181809}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Screen recording started']，实际响应: '['start screen recording', 'Screen recording is already started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\system_coupling\\test_start_screen_recording.py\", line 32, in test_start_screen_recording\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756791181809, "stop": 1756791181811}], "attachments": [{"name": "stdout", "source": "461edf06-315a-4b6f-9113-9152d0385b74-attachment.txt", "type": "text/plain"}], "start": 1756791154256, "stop": 1756791181812, "uuid": "34ff7a48-5f51-46d5-af48-d7a919529a4b", "historyId": "5fe7611f5b7d3ef438ce938b66e0b99f", "testCaseId": "5fe7611f5b7d3ef438ce938b66e0b99f", "fullName": "testcases.test_ella.system_coupling.test_start_screen_recording.TestEllaStartScreenRecording#test_start_screen_recording", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_start_screen_recording"}, {"name": "subSuite", "value": "TestEllaStartScreenRecording"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_start_screen_recording"}]}