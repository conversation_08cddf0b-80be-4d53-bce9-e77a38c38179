{"name": "测试start record能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=False, 最终=False, 响应='['start record', 'Screen recording started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.system_coupling.test_start_record.TestEllaStartRecord object at 0x0000018BC61C0D00>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC3E54D60>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_start_record(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证已打开\"):\n>           assert final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=False, 最终=False, 响应='['start record', 'Screen recording started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_start_record.py:36: AssertionError"}, "description": "start record", "steps": [{"name": "执行命令: start record", "status": "passed", "steps": [{"name": "执行命令: start record", "status": "passed", "start": 1756791110519, "stop": 1756791137945}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0dd6c926-c503-49bc-bc2c-2a712266a15f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d3344c8d-ef3a-405c-9462-6144b00e0e0b-attachment.png", "type": "image/png"}], "start": 1756791137945, "stop": 1756791138172}], "start": 1756791110519, "stop": 1756791138172}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756791138172, "stop": 1756791138173}, {"name": "验证已打开", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=False, 最终=False, 响应='['start record', 'Screen recording started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\system_coupling\\test_start_record.py\", line 36, in test_start_record\n    assert final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1756791138173, "stop": 1756791138173}], "attachments": [{"name": "stdout", "source": "d968ffd4-c6e3-4944-a4a5-0ff5bc94fb1d-attachment.txt", "type": "text/plain"}], "start": 1756791110519, "stop": 1756791138174, "uuid": "d12f68a6-6638-446f-81dd-3e09cee9c51c", "historyId": "7acb737855a3a3110ed556a3e5fe1256", "testCaseId": "7acb737855a3a3110ed556a3e5fe1256", "fullName": "testcases.test_ella.system_coupling.test_start_record.TestEllaStartRecord#test_start_record", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_start_record"}, {"name": "subSuite", "value": "TestEllaStartRecord"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_start_record"}]}