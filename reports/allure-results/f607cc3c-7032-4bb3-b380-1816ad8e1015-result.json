{"name": "测试open camera", "status": "passed", "description": "测试open camera指令", "steps": [{"name": "执行命令: open camera", "status": "passed", "steps": [{"name": "执行命令: open camera", "status": "passed", "start": 1756799914400, "stop": 1756799947397}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "41c40a48-0df5-479d-98ee-5b4172aa8dce-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "94454fe9-fc03-4eee-bd69-6b651dd4a2cc-attachment.png", "type": "image/png"}], "start": 1756799947397, "stop": 1756799947627}], "start": 1756799914400, "stop": 1756799947627}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756799947627, "stop": 1756799947629}, {"name": "验证camera已打开", "status": "passed", "start": 1756799947629, "stop": 1756799947629}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6d39245b-5490-4b96-8f97-19d7a870b6ef-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f33ea5ac-4d34-4513-8703-80d58d870ccb-attachment.png", "type": "image/png"}], "start": 1756799947629, "stop": 1756799947857}], "attachments": [{"name": "stdout", "source": "2ca3b182-26ea-464c-b553-0baffcc1b16a-attachment.txt", "type": "text/plain"}], "start": 1756799914400, "stop": 1756799947858, "uuid": "7376ea85-3a5b-4db8-9f40-3ac52ce2661c", "historyId": "6ba20fdcdbf83dd327ea91bd93e697e0", "testCaseId": "6ba20fdcdbf83dd327ea91bd93e697e0", "fullName": "testcases.test_ella.unsupported_commands.test_open_camera.TestEllaOpenPlayPoliticalNews#test_open_camera", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_open_camera"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_open_camera"}]}