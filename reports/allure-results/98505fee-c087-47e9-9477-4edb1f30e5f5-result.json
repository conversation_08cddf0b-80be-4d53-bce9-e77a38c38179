{"name": "测试resume music能正常执行", "status": "passed", "description": "resume music", "steps": [{"name": "执行命令: resume music", "status": "passed", "steps": [{"name": "执行命令: resume music", "status": "passed", "start": 1756784103472, "stop": 1756784124631}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "907f6db7-e60b-4aa1-9320-0effcb138d6b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ecdf6b20-602a-4eea-bf2f-9ea773ed367b-attachment.png", "type": "image/png"}], "start": 1756784124631, "stop": 1756784124855}], "start": 1756784103472, "stop": 1756784124855}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756784124855, "stop": 1756784124857}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "222e7b78-06bd-49d4-938b-ebe41b0358cd-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f960d5e8-b9ec-4564-8186-bd5988ffeea3-attachment.png", "type": "image/png"}], "start": 1756784124857, "stop": 1756784125072}], "attachments": [{"name": "stdout", "source": "9f1e520c-a909-4c60-b2ba-1b0f8c90eca0-attachment.txt", "type": "text/plain"}], "start": 1756784103472, "stop": 1756784125072, "uuid": "80d3f4ff-6d7e-41b6-acec-3e191705df20", "historyId": "44b646d68146a0c48da2623a58b17f6f", "testCaseId": "44b646d68146a0c48da2623a58b17f6f", "fullName": "testcases.test_ella.component_coupling.test_resume_music.TestEllaResumeMusic#test_resume_music", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_resume_music"}, {"name": "subSuite", "value": "TestEllaResumeMusic"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_resume_music"}]}