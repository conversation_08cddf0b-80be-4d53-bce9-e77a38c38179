{"name": "测试next music能正常执行", "status": "passed", "description": "next music", "steps": [{"name": "执行命令: next music", "status": "passed", "steps": [{"name": "执行命令: next music", "status": "passed", "start": 1756785961291, "stop": 1756785984726}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c271fc5e-4159-4d83-907c-36be81adab0c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "81f1247f-d2ba-41ce-8753-4ab3c036ac61-attachment.png", "type": "image/png"}], "start": 1756785984726, "stop": 1756785984965}], "start": 1756785961291, "stop": 1756785984965}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756785984965, "stop": 1756785984966}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f4cc29b9-1839-4110-9e03-15360d7310ba-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9e10bc9a-fb85-4353-ad84-a17f47ef5677-attachment.png", "type": "image/png"}], "start": 1756785984966, "stop": 1756785985151}], "attachments": [{"name": "stdout", "source": "f8b03549-75e7-48f0-b0e3-28464bd43a9b-attachment.txt", "type": "text/plain"}], "start": 1756785961291, "stop": 1756785985151, "uuid": "39dd65d0-1890-40e8-9c46-5ae600462a8d", "historyId": "b236a18e19a6aa2218c85b80afef2746", "testCaseId": "b236a18e19a6aa2218c85b80afef2746", "fullName": "testcases.test_ella.dialogue.test_next_music.TestEllaHowIsWeatherToday#test_next_music", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_next_music"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_next_music"}]}