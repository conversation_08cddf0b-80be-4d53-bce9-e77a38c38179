{"name": "测试open whatsapp", "status": "failed", "statusDetails": {"message": "AssertionError: whatsapp: 初始=False, 最终=False, 响应='['open whatsapp', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[android页面内容] Dual App | Enable dual app, you can use dual WhatsApp simultaneously.']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_open_whatsapp.TestEllaOpenWhatsapp object at 0x0000018BC6599420>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC70A5BD0>\n\n    @allure.title(\"测试open whatsapp\")\n    @allure.description(\"测试open whatsapp指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_whatsapp(self, ella_app):\n        \"\"\"测试open whatsapp命令\"\"\"\n        command = \"open whatsapp\"\n        expected_text = ['Done']\n        # expected_text = ['WhatsApp is not installed yet','I need to download whatsapp','I need to download WhatsApp']\n        app_name = 'whatsapp'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n    \n            result = self.verify_expected_in_response_advanced(expected_text, response_text,match_any=True)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证{app_name}已打开\"):\n>           assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: whatsapp: 初始=False, 最终=False, 响应='['open whatsapp', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[android页面内容] Dual App | Enable dual app, you can use dual WhatsApp simultaneously.']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_open_whatsapp.py:36: AssertionError"}, "description": "测试open whatsapp指令", "steps": [{"name": "执行命令: open whatsapp", "status": "passed", "steps": [{"name": "执行命令: open whatsapp", "status": "passed", "start": 1756800218393, "stop": 1756800246766}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fd518ddd-cfda-490d-acd4-d30752fc072a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "49b03704-84fd-4615-ad53-081532dc9e6f-attachment.png", "type": "image/png"}], "start": 1756800246766, "stop": 1756800247116}], "start": 1756800218393, "stop": 1756800247116}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756800247117, "stop": 1756800247121}, {"name": "验证whatsapp已打开", "status": "failed", "statusDetails": {"message": "AssertionError: whatsapp: 初始=False, 最终=False, 响应='['open whatsapp', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[android页面内容] Dual App | Enable dual app, you can use dual WhatsApp simultaneously.']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_open_whatsapp.py\", line 36, in test_open_whatsapp\n    assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1756800247121, "stop": 1756800247121}], "attachments": [{"name": "stdout", "source": "0ec41536-3620-4d33-8771-7ce394300fce-attachment.txt", "type": "text/plain"}], "start": 1756800218392, "stop": 1756800247122, "uuid": "842d5bcf-ec77-45ce-b64e-f9566d8f9103", "historyId": "51b4b46de04a8c1e37077a9f688cb490", "testCaseId": "51b4b46de04a8c1e37077a9f688cb490", "fullName": "testcases.test_ella.unsupported_commands.test_open_whatsapp.TestEllaOpenWhatsapp#test_open_whatsapp", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_open_whatsapp"}, {"name": "subSuite", "value": "TestEllaOpenWhatsapp"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_open_whatsapp"}]}