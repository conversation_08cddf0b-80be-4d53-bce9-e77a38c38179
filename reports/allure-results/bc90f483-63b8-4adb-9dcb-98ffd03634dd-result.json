{"name": "测试play video", "status": "passed", "description": "测试play video指令", "steps": [{"name": "执行命令: play video", "status": "passed", "steps": [{"name": "执行命令: play video", "status": "passed", "start": 1756800714775, "stop": 1756800748133}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5f05dffd-4807-4e23-9139-514bf87409f6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e8d89c62-fbd1-4fec-9513-45e2b931e116-attachment.png", "type": "image/png"}], "start": 1756800748133, "stop": 1756800748375}], "start": 1756800714775, "stop": 1756800748375}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756800748375, "stop": 1756800748376}, {"name": "验证youtube已打开", "status": "passed", "start": 1756800748376, "stop": 1756800748376}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "19970a25-b650-4589-9ca0-1c6a111aef4c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a730377b-7e15-4bbf-97ef-8d9414565288-attachment.png", "type": "image/png"}], "start": 1756800748376, "stop": 1756800748638}], "attachments": [{"name": "stdout", "source": "c518add5-bad7-4bb5-8691-c64e3d283e91-attachment.txt", "type": "text/plain"}], "start": 1756800714774, "stop": 1756800748639, "uuid": "3dab750a-2408-40d6-b050-16b1088438f4", "historyId": "05cfb3e6f186373be53bb1a7166ac69f", "testCaseId": "05cfb3e6f186373be53bb1a7166ac69f", "fullName": "testcases.test_ella.unsupported_commands.test_play_video.TestEllaOpenPlayPoliticalNews#test_play_video", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_video"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_video"}]}