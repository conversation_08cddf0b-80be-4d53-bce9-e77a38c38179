{"name": "测试close power saving mode返回正确的不支持响应", "status": "passed", "description": "验证close power saving mode指令返回预期的不支持响应", "steps": [{"name": "执行命令: close power saving mode", "status": "passed", "steps": [{"name": "执行命令: close power saving mode", "status": "passed", "start": 1756796123815, "stop": 1756796145185}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f53b0f59-2715-4680-99c7-0bdd642e1e73-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0871ea1b-6b24-4b65-9a02-5aa936bee409-attachment.png", "type": "image/png"}], "start": 1756796145186, "stop": 1756796145400}], "start": 1756796123815, "stop": 1756796145400}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756796145400, "stop": 1756796145402}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d276569f-bd83-46cd-adae-17a3865c7408-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "34ccdc5a-0e80-47f6-8708-974f5175da51-attachment.png", "type": "image/png"}], "start": 1756796145402, "stop": 1756796145610}], "attachments": [{"name": "stdout", "source": "55d1ab9a-**************-54e8c9763322-attachment.txt", "type": "text/plain"}], "start": 1756796123814, "stop": 1756796145611, "uuid": "3fd74cca-3909-4e37-89f5-cbb782993843", "historyId": "1da800483d0bd7f8dbe657a8d5c37f76", "testCaseId": "1da800483d0bd7f8dbe657a8d5c37f76", "fullName": "testcases.test_ella.unsupported_commands.test_close_power_saving_mode.TestEllaClosePowerSavingMode#test_close_power_saving_mode", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_close_power_saving_mode"}, {"name": "subSuite", "value": "TestEllaClosePowerSavingMode"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_close_power_saving_mode"}]}