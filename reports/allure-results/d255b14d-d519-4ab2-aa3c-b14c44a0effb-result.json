{"name": "测试take a joke能正常执行", "status": "passed", "description": "take a joke", "steps": [{"name": "执行命令: take a joke", "status": "passed", "steps": [{"name": "执行命令: take a joke", "status": "passed", "start": 1756787106916, "stop": 1756787131344}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b42e2f0b-d46c-42e9-887f-b1208c7b46d9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fa076670-6c79-474b-b0a9-765b484c17ce-attachment.png", "type": "image/png"}], "start": 1756787131345, "stop": 1756787131603}], "start": 1756787106916, "stop": 1756787131604}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756787131604, "stop": 1756787131605}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "66242e4e-9745-4a41-95ad-1d2bcea61504-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "16184980-be85-46a8-a537-43e6482a1abc-attachment.png", "type": "image/png"}], "start": 1756787131605, "stop": 1756787131818}], "attachments": [{"name": "stdout", "source": "ed43ec3a-7cd7-4ebb-b57f-5999cbeb936f-attachment.txt", "type": "text/plain"}], "start": 1756787106916, "stop": 1756787131818, "uuid": "f824199d-da11-41a5-8282-908437fc3045", "historyId": "543965b4120af95548616c95b1b70ef1", "testCaseId": "543965b4120af95548616c95b1b70ef1", "fullName": "testcases.test_ella.dialogue.test_take_a_joke.TestEllaTakeJoke#test_take_a_joke", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_take_a_joke"}, {"name": "subSuite", "value": "TestEllaTakeJoke"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_take_a_joke"}]}