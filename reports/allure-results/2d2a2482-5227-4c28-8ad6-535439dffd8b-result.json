{"name": "测试disable call on hold返回正确的不支持响应", "status": "passed", "description": "验证disable call on hold指令返回预期的不支持响应", "steps": [{"name": "执行命令: disable call on hold", "status": "passed", "steps": [{"name": "执行命令: disable call on hold", "status": "passed", "start": 1756783076500, "stop": 1756783097205}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "52953995-ea44-4640-bafa-e5f1c91bdbd7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "658aa0e5-6c57-42fa-ac6c-37400c2a1772-attachment.png", "type": "image/png"}], "start": 1756783097205, "stop": 1756783097414}], "start": 1756783076500, "stop": 1756783097414}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756783097414, "stop": 1756783097415}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dda8d5bc-08b8-4bcb-9aa4-facf5778a976-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "399eb92f-1d9e-4f3b-9f43-f37def66f55f-attachment.png", "type": "image/png"}], "start": 1756783097416, "stop": 1756783097619}], "attachments": [{"name": "stdout", "source": "cffdff79-6758-485c-b01a-feec5a13caa1-attachment.txt", "type": "text/plain"}], "start": 1756783076500, "stop": 1756783097619, "uuid": "ebb669be-6463-47a5-9ace-a5dbf635cdb9", "historyId": "8bf93fd8ac952a757cc694ecc78b8d51", "testCaseId": "8bf93fd8ac952a757cc694ecc78b8d51", "fullName": "testcases.test_ella.component_coupling.test_disable_call_on_hold.TestEllaDisableCallHold#test_disable_call_on_hold", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_disable_call_on_hold"}, {"name": "subSuite", "value": "TestEllaDisableCallHold"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_disable_call_on_hold"}]}