{"name": "测试open contact命令", "status": "passed", "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open contact", "status": "passed", "steps": [{"name": "执行命令: open contact", "status": "passed", "start": 1756783338391, "stop": 1756783373017}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8ea32a9c-907f-4da2-84a9-dcdeca6d2a07-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "211dc67f-7153-4674-aab6-926bad6a2c25-attachment.png", "type": "image/png"}], "start": 1756783373017, "stop": 1756783373223}], "start": 1756783338391, "stop": 1756783373223}, {"name": "验证响应包含Done", "status": "passed", "start": 1756783373223, "stop": 1756783373225}, {"name": "验证Dalier应用已打开", "status": "passed", "start": 1756783373225, "stop": 1756783373225}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ea8d0078-c1e6-4a80-8b15-4fec1f28e685-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7d3cb6b3-9c7c-423f-8da4-200f4c311701-attachment.png", "type": "image/png"}], "start": 1756783373225, "stop": 1756783373431}], "attachments": [{"name": "stdout", "source": "b669ef30-6b85-4492-a75c-123ac6c0e75e-attachment.txt", "type": "text/plain"}], "start": 1756783338391, "stop": 1756783373432, "uuid": "97366e06-eb24-4b91-aa70-cc07067a72cb", "historyId": "7c32e753573a480d7d5c09abab43469e", "testCaseId": "7c32e753573a480d7d5c09abab43469e", "fullName": "testcases.test_ella.component_coupling.test_open_contact.TestEllaContactCommandConcise#test_open_contact", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "联系人控制命令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_contact"}, {"name": "subSuite", "value": "TestEllaContactCommandConcise"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_contact"}]}