{"name": "测试i am your voice assistant", "status": "passed", "description": "测试i am your voice assistant指令", "steps": [{"name": "执行命令: i am your voice assistant", "status": "passed", "steps": [{"name": "执行命令: i am your voice assistant", "status": "passed", "start": 1756798557398, "stop": 1756798582715}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b41e4549-09fa-4cfa-8914-ab46bd65ec73-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c2002266-92aa-47fc-840b-342ca9e53c8c-attachment.png", "type": "image/png"}], "start": 1756798582715, "stop": 1756798582939}], "start": 1756798557398, "stop": 1756798582939}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756798582939, "stop": 1756798582941}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "79d37c3b-e807-4b90-a7b8-f4a454565859-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0f411b79-e5c3-4784-9b88-6b79da314bad-attachment.png", "type": "image/png"}], "start": 1756798582941, "stop": 1756798583178}], "attachments": [{"name": "stdout", "source": "2c0c5412-2a97-4cdb-b706-25b2dc67b9b5-attachment.txt", "type": "text/plain"}], "start": 1756798557398, "stop": 1756798583179, "uuid": "cc05b1e2-097b-4f18-af72-7b203176e6b4", "historyId": "c5debd3414c81b3cc4349236d4020867", "testCaseId": "c5debd3414c81b3cc4349236d4020867", "fullName": "testcases.test_ella.unsupported_commands.test_i_am_your_voice_assistant.TestEllaOpenPlayPoliticalNews#test_i_am_your_voice_assistant", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_i_am_your_voice_assistant"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_i_am_your_voice_assistant"}]}