{"name": "测试turn on show battery percentage返回正确的不支持响应", "status": "passed", "description": "验证turn on show battery percentage指令返回预期的不支持响应", "steps": [{"name": "执行命令: turn on show battery percentage", "status": "passed", "steps": [{"name": "执行命令: turn on show battery percentage", "status": "passed", "start": 1756804034601, "stop": 1756804056869}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "049183c9-9779-4a37-a37a-466e29bb170d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "49a99e6c-0745-4fc8-ae4a-843dee76cc27-attachment.png", "type": "image/png"}], "start": 1756804056871, "stop": 1756804057142}], "start": 1756804034601, "stop": 1756804057142}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756804057142, "stop": 1756804057143}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "977cdd83-b9d3-41de-8a51-c5b45acea2d3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7ac25644-aab4-4aae-b20e-e7364c1efe2d-attachment.png", "type": "image/png"}], "start": 1756804057143, "stop": 1756804057348}], "attachments": [{"name": "stdout", "source": "7d16e488-9b8d-4fe8-8d4a-18c9b65844ab-attachment.txt", "type": "text/plain"}], "start": 1756804034601, "stop": 1756804057349, "uuid": "220d8265-0cad-4ec8-a63b-555429ff771e", "historyId": "e82a80866bdbe9a7e1ac367f20c977b5", "testCaseId": "e82a80866bdbe9a7e1ac367f20c977b5", "fullName": "testcases.test_ella.unsupported_commands.test_turn_on_show_battery_percentage.TestEllaTurnShowBatteryPercentage#test_turn_on_show_battery_percentage", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_on_show_battery_percentage"}, {"name": "subSuite", "value": "TestEllaTurnShowBatteryPercentage"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_on_show_battery_percentage"}]}