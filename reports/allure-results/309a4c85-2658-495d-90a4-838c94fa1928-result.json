{"name": "测试what's your name？能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含[\"I'm <PERSON>\", 'assistant']，实际响应: '[\"what's your name？\", '', '', '', \"My name is <PERSON>. I'm here to help!\", 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', \"Dialogue Explore <PERSON>wipe down to view earlier chats Send my recent photo to mom on WhatsApp Real Madrid, Athletic Club Lead La Liga How's the weather today? what's your name？ My name is <PERSON>. I'm here to help! Generated by AI, for reference only Do you have a last name? Are you a real person? <PERSON> is a helpful AI. DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "trace": "self = <testcases.test_ella.dialogue.test_what_s_your_name.TestEllaWhatSYourName object at 0x0000018BC606FAF0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC4D3C0A0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_what_s_your_name(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含[\"I'm <PERSON>\", 'assistant']，实际响应: '[\"what's your name？\", '', '', '', \"My name is <PERSON>. I'm here to help!\", 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats Send my recent photo to mom on WhatsApp Real Madrid, Athletic Club Lead La Liga How's the weather today? what's your name？ My name is Ella. I'm here to help! Generated by AI, for reference only Do you have a last name? Are you a real person? Ella is a helpful AI. DeepSeek-R1 Feel free to ask me any questions…\"]'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_what_s_your_name.py:34: AssertionError"}, "description": "what's your name？", "steps": [{"name": "执行命令: what's your name？", "status": "passed", "steps": [{"name": "执行命令: what's your name？", "status": "passed", "start": 1756787675291, "stop": 1756787699426}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b5818e8b-19e5-47c1-a6d8-e8295d359ed4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a754031e-0384-4971-ab9a-4d7d56404c63-attachment.png", "type": "image/png"}], "start": 1756787699426, "stop": 1756787699649}], "start": 1756787675291, "stop": 1756787699649}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含[\"I'm <PERSON>\", 'assistant']，实际响应: '[\"what's your name？\", '', '', '', \"My name is <PERSON>. I'm here to help!\", 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', \"Dialogue Explore <PERSON>wipe down to view earlier chats Send my recent photo to mom on WhatsApp Real Madrid, Athletic Club Lead La Liga How's the weather today? what's your name？ My name is <PERSON>. I'm here to help! Generated by AI, for reference only Do you have a last name? Are you a real person? <PERSON> is a helpful AI. DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\dialogue\\test_what_s_your_name.py\", line 34, in test_what_s_your_name\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756787699649, "stop": 1756787699651}], "attachments": [{"name": "stdout", "source": "ce9d29c1-4e9b-4293-b32d-cd37d2487c0c-attachment.txt", "type": "text/plain"}], "start": 1756787675291, "stop": 1756787699652, "uuid": "1f63180a-ae80-4f86-ba6b-294fdd9bf4be", "historyId": "c0d6ce0b7c5e41242c01a6e0c0186608", "testCaseId": "c0d6ce0b7c5e41242c01a6e0c0186608", "fullName": "testcases.test_ella.dialogue.test_what_s_your_name.TestEllaWhatSYourName#test_what_s_your_name", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_s_your_name"}, {"name": "subSuite", "value": "TestEllaWhatSYourName"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_s_your_name"}]}