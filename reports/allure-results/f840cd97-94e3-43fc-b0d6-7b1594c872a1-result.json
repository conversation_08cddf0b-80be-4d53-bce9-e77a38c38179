{"name": "测试tell me joke能正常执行", "status": "passed", "description": "tell me joke", "steps": [{"name": "执行命令: tell me joke", "status": "passed", "steps": [{"name": "执行命令: tell me joke", "status": "passed", "start": 1756803629977, "stop": 1756803653832}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b43ab2b9-bbdf-4bd4-a3ed-fc99db4edf57-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d9061983-73df-4ff0-aecc-962d9cefbcb3-attachment.png", "type": "image/png"}], "start": 1756803653832, "stop": 1756803654067}], "start": 1756803629977, "stop": 1756803654067}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756803654067, "stop": 1756803654069}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2ffa2c97-67b6-41cb-b6b4-33435d2e2ec8-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "738a275a-68b3-433f-a605-f7fefa66660c-attachment.png", "type": "image/png"}], "start": 1756803654069, "stop": 1756803654356}], "attachments": [{"name": "stdout", "source": "9e0d9212-1db5-4843-8e78-0a435eba0bee-attachment.txt", "type": "text/plain"}], "start": 1756803629971, "stop": 1756803654357, "uuid": "7387a913-720c-4b82-b3f5-364f872642f8", "historyId": "5ad5ef8bf0f7913e709dbc1e706db1e5", "testCaseId": "5ad5ef8bf0f7913e709dbc1e706db1e5", "fullName": "testcases.test_ella.unsupported_commands.test_tell_me_joke.TestEllaTellMeJoke#test_tell_me_joke", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_tell_me_joke"}, {"name": "subSuite", "value": "TestEllaTellMeJoke"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_tell_me_joke"}]}