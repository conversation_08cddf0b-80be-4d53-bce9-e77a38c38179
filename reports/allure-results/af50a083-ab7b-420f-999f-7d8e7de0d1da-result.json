{"name": "测试set floating windows返回正确的不支持响应", "status": "passed", "description": "验证set floating windows指令返回预期的不支持响应", "steps": [{"name": "执行命令: set floating windows", "status": "passed", "steps": [{"name": "执行命令: set floating windows", "status": "passed", "start": 1756801902033, "stop": 1756801925350}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ede0ac77-6d38-49e7-a96d-1eb92209e778-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b2edab8a-176a-4044-befa-350e22597606-attachment.png", "type": "image/png"}], "start": 1756801925350, "stop": 1756801925657}], "start": 1756801902033, "stop": 1756801925659}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756801925659, "stop": 1756801925661}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b5322943-7064-4c5f-a5fd-0de458ee161f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "26371846-9df6-4c6c-9af8-c6885c4aef68-attachment.png", "type": "image/png"}], "start": 1756801925661, "stop": 1756801925935}], "attachments": [{"name": "stdout", "source": "de748093-99d7-421d-baa4-9a7bbf86f9f2-attachment.txt", "type": "text/plain"}], "start": 1756801902033, "stop": 1756801925935, "uuid": "71250607-f161-46d3-8c69-0f395d2218a7", "historyId": "ff945a5d436679bddd13261b231955ec", "testCaseId": "ff945a5d436679bddd13261b231955ec", "fullName": "testcases.test_ella.unsupported_commands.test_set_floating_windows.TestEllaSetFloatingWindows#test_set_floating_windows", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_floating_windows"}, {"name": "subSuite", "value": "TestEllaSetFloatingWindows"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_floating_windows"}]}