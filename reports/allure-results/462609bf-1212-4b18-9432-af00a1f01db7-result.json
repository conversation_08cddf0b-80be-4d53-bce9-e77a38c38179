{"name": "测试check system update", "status": "passed", "description": "测试check system update指令", "steps": [{"name": "执行命令: check system update", "status": "passed", "steps": [{"name": "执行命令: check system update", "status": "passed", "start": 1756795993238, "stop": 1756796020543}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "bf75c3fb-d394-47e8-b8e2-662ccfbef1d9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "75a38196-5d59-4e87-89b9-6f7accd63029-attachment.png", "type": "image/png"}], "start": 1756796020543, "stop": 1756796020728}], "start": 1756795993238, "stop": 1756796020729}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756796020729, "stop": 1756796020729}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c6618ead-ac50-426e-92a0-46806e7f5cc4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "838fb9f4-5581-4e19-b95d-bb51ea3c866a-attachment.png", "type": "image/png"}], "start": 1756796020729, "stop": 1756796020951}], "attachments": [{"name": "stdout", "source": "2acb6ff7-1e01-4891-be82-9c771ef6157f-attachment.txt", "type": "text/plain"}], "start": 1756795993238, "stop": 1756796020951, "uuid": "6d9f8553-2ffa-4738-9a5f-a0d870bd5c4d", "historyId": "c45251d74b80bdc9105dde9b444ef1c7", "testCaseId": "c45251d74b80bdc9105dde9b444ef1c7", "fullName": "testcases.test_ella.unsupported_commands.test_check_system_update.TestEllaOpenPlayPoliticalNews#test_check_ram_information", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_system_update"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_system_update"}]}