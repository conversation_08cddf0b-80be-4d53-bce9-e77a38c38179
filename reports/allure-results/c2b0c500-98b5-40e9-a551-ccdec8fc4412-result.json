{"name": "测试check contact能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Who do you want to check?']，实际响应: '['check contact', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_check_contact.TestEllaCheckContact object at 0x0000018BC64306D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC70A7580>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_check_contact(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Who do you want to check?']，实际响应: '['check contact', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_check_contact.py:33: AssertionError"}, "description": "check contact", "steps": [{"name": "执行命令: check contact", "status": "passed", "steps": [{"name": "执行命令: check contact", "status": "passed", "start": 1756795637492, "stop": 1756795668947}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d597f0d8-fd25-4887-a5ca-c79fb6e31992-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "14a79b9e-a13f-4d02-9f9e-e8b233c02534-attachment.png", "type": "image/png"}], "start": 1756795668947, "stop": 1756795669181}], "start": 1756795637492, "stop": 1756795669181}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Who do you want to check?']，实际响应: '['check contact', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_check_contact.py\", line 33, in test_check_contact\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756795669181, "stop": 1756795669184}], "attachments": [{"name": "stdout", "source": "ea794d7a-957c-4ad7-898a-3d3db634b49f-attachment.txt", "type": "text/plain"}], "start": 1756795637491, "stop": 1756795669186, "uuid": "33a89175-83c5-4c6d-a983-c966f5297fe4", "historyId": "9375a7a6d7b67755564dec18857d7c65", "testCaseId": "9375a7a6d7b67755564dec18857d7c65", "fullName": "testcases.test_ella.unsupported_commands.test_check_contact.TestEllaCheckContact#test_check_contact", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_contact"}, {"name": "subSuite", "value": "TestEllaCheckContact"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_contact"}]}