{"name": "测试disable call rejection返回正确的不支持响应", "status": "passed", "description": "验证disable call rejection指令返回预期的不支持响应", "steps": [{"name": "执行命令: disable call rejection", "status": "passed", "steps": [{"name": "执行命令: disable call rejection", "status": "passed", "start": 1756796416817, "stop": 1756796439391}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fde6b82b-ce38-4f65-a905-57ae78853f03-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "69b85ee2-f38a-4448-b707-c3d429611852-attachment.png", "type": "image/png"}], "start": 1756796439391, "stop": 1756796439619}], "start": 1756796416817, "stop": 1756796439619}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756796439619, "stop": 1756796439622}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ff195109-64b4-4078-928b-680d1c900df2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ec7630aa-6eea-4291-a932-baeb6b9d3930-attachment.png", "type": "image/png"}], "start": 1756796439622, "stop": 1756796439826}], "attachments": [{"name": "stdout", "source": "7df6041a-ff92-46fd-9a96-da471dbc96ae-attachment.txt", "type": "text/plain"}], "start": 1756796416817, "stop": 1756796439828, "uuid": "5cb8c9bf-9ea8-432b-ab4b-bf9978fe4478", "historyId": "1ca8d9c300d55584cdcf637ede08bdba", "testCaseId": "1ca8d9c300d55584cdcf637ede08bdba", "fullName": "testcases.test_ella.unsupported_commands.test_disable_call_rejection.TestEllaDisableCallRejection#test_disable_call_rejection", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_call_rejection"}, {"name": "subSuite", "value": "TestEllaDisableCallRejection"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_call_rejection"}]}