{"name": "测试summarize content on this page", "status": "passed", "description": "测试summarize content on this page指令", "steps": [{"name": "执行命令: summarize content on this page", "status": "passed", "steps": [{"name": "执行命令: summarize content on this page", "status": "passed", "start": 1756803246437, "stop": 1756803268754}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4be0f4fc-1116-481e-bb37-6836adc20c48-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a3585486-0445-41ec-8416-071ce6cda256-attachment.png", "type": "image/png"}], "start": 1756803268754, "stop": 1756803269010}], "start": 1756803246437, "stop": 1756803269010}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756803269010, "stop": 1756803269011}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "25e9ad35-caab-4e10-b299-90f6a0631f50-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5ad26981-74d0-455e-84a5-50d90b752e8e-attachment.png", "type": "image/png"}], "start": 1756803269011, "stop": 1756803269249}], "attachments": [{"name": "stdout", "source": "48e13ada-2db5-49b6-8127-3d7899f212ba-attachment.txt", "type": "text/plain"}], "start": 1756803246437, "stop": 1756803269249, "uuid": "bfc120fd-e501-4d1d-b81f-bdd8fbd3a6a1", "historyId": "c052c8813edd9c2261dc1bcc29786fe9", "testCaseId": "c052c8813edd9c2261dc1bcc29786fe9", "fullName": "testcases.test_ella.unsupported_commands.test_summarize_content_on_this_page.TestEllaOpenPlayPoliticalNews#test_summarize_content_on_this_page", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_summarize_content_on_this_page"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_summarize_content_on_this_page"}]}