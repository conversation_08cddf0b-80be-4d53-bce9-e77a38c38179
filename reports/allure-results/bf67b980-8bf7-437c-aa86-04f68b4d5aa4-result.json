{"name": "测试Adjustment the brightness to 50%能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=255, 最终=22, 响应='['Adjustment the brightness to 50%', 'Brightness is at 50% now.', '', '', '', '', '', '', '', '', '']'\nassert 22 == 75", "trace": "self = <testcases.test_ella.system_coupling.test_adjustment_the_brightness_to.TestEllaAdjustmentBrightness object at 0x0000018BC611D1B0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6DB92D0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_adjustment_the_brightness_to(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证应用已打开\"):\n>           assert  final_status==75, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=255, 最终=22, 响应='['Adjustment the brightness to 50%', 'Brightness is at 50% now.', '', '', '', '', '', '', '', '', '']'\nE           assert 22 == 75\n\ntestcases\\test_ella\\system_coupling\\test_adjustment_the_brightness_to.py:36: AssertionError"}, "description": "Adjustment the brightness to 50%", "steps": [{"name": "执行命令: Adjustment the brightness to 50%", "status": "passed", "steps": [{"name": "执行命令: Adjustment the brightness to 50%", "status": "passed", "start": 1756788843750, "stop": 1756788866629}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d290df65-c207-4b65-902f-3fdc2cc3a6ab-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6f5b55fa-6c46-4c60-98d6-8f0e5d9356d0-attachment.png", "type": "image/png"}], "start": 1756788866629, "stop": 1756788866876}], "start": 1756788843750, "stop": 1756788866876}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756788866876, "stop": 1756788866877}, {"name": "验证应用已打开", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=255, 最终=22, 响应='['Adjustment the brightness to 50%', 'Brightness is at 50% now.', '', '', '', '', '', '', '', '', '']'\nassert 22 == 75\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\system_coupling\\test_adjustment_the_brightness_to.py\", line 36, in test_adjustment_the_brightness_to\n    assert  final_status==75, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1756788866877, "stop": 1756788866877}], "attachments": [{"name": "stdout", "source": "1db48d13-f441-4378-8b83-b43bca0519a7-attachment.txt", "type": "text/plain"}], "start": 1756788843750, "stop": 1756788866879, "uuid": "48534a7f-5a5f-4564-87da-486b44e2eaa3", "historyId": "ad18e983dce31052b87b7404f3b347ce", "testCaseId": "ad18e983dce31052b87b7404f3b347ce", "fullName": "testcases.test_ella.system_coupling.test_adjustment_the_brightness_to.TestEllaAdjustmentBrightness#test_adjustment_the_brightness_to", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_adjustment_the_brightness_to"}, {"name": "subSuite", "value": "TestEllaAdjustmentBrightness"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_adjustment_the_brightness_to"}]}