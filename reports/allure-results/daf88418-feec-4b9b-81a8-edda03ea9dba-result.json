{"name": "测试continue playing能正常执行", "status": "passed", "description": "continue playing", "steps": [{"name": "执行命令: continue playing", "status": "passed", "steps": [{"name": "执行命令: continue playing", "status": "passed", "start": 1756784933392, "stop": 1756784957592}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "391c5f51-3bd8-46f6-a50f-abf65579c874-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "51ce8ca4-e420-4c12-85e2-408f3a647b0c-attachment.png", "type": "image/png"}], "start": 1756784957593, "stop": 1756784957806}], "start": 1756784933392, "stop": 1756784957807}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756784957807, "stop": 1756784957807}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "949b7222-8bb0-4d4b-bda5-fef0a172ce23-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "37a92f61-fcfc-4890-bf86-4fc4f90c56c1-attachment.png", "type": "image/png"}], "start": 1756784957807, "stop": 1756784958015}], "attachments": [{"name": "stdout", "source": "d0038590-fc3b-470f-a6f4-a38409249c17-attachment.txt", "type": "text/plain"}], "start": 1756784933392, "stop": 1756784958016, "uuid": "439825ef-dbec-4978-80f8-9fe000c3d17b", "historyId": "309f3fbb8586cc15e324e94cec37e7ea", "testCaseId": "309f3fbb8586cc15e324e94cec37e7ea", "fullName": "testcases.test_ella.dialogue.test_continue_playing.TestEllaHowIsWeatherToday#test_continue_playing", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_continue_playing"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_continue_playing"}]}