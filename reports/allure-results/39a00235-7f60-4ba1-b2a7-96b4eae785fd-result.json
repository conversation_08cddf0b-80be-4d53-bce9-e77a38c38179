{"name": "测试min ring volume能正常执行", "status": "passed", "description": "min ring volume", "steps": [{"name": "执行命令: min ring volume", "status": "passed", "steps": [{"name": "执行命令: min ring volume", "status": "passed", "start": 1756790218933, "stop": 1756790239575}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8123b4e2-8d30-4925-a308-e4f471855505-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "51df015f-b8f7-4aa6-87c7-65be06ab6657-attachment.png", "type": "image/png"}], "start": 1756790239575, "stop": 1756790239776}], "start": 1756790218933, "stop": 1756790239777}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790239777, "stop": 1756790239778}, {"name": "验证应用已打开", "status": "passed", "start": 1756790239778, "stop": 1756790239778}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "bfaab185-f3b4-4b64-9db4-3f909033fdda-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "00d4c0c3-ccd2-457d-9b37-010361e43da7-attachment.png", "type": "image/png"}], "start": 1756790239778, "stop": 1756790239986}], "attachments": [{"name": "stdout", "source": "707bc96c-93fc-4310-9c42-5b3465e8c837-attachment.txt", "type": "text/plain"}], "start": 1756790218933, "stop": 1756790239986, "uuid": "daacf5c3-5894-42f7-a3ae-440204bd98f2", "historyId": "4072ba85d37e03a8ef0a5dd9d0741631", "testCaseId": "4072ba85d37e03a8ef0a5dd9d0741631", "fullName": "testcases.test_ella.system_coupling.test_min_ring_volume.TestEllaMinRingVolume#test_min_ring_volume", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_min_ring_volume"}, {"name": "subSuite", "value": "TestEllaMinRingVolume"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_min_ring_volume"}]}