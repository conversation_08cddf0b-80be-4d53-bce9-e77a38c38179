{"name": "测试take notes on how to build a treehouse能正常执行", "status": "passed", "description": "take notes on how to build a treehouse", "steps": [{"name": "执行命令: take notes on how to build a treehouse", "status": "passed", "steps": [{"name": "执行命令: take notes on how to build a treehouse", "status": "passed", "start": 1756787189129, "stop": 1756787216425}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3a89a2ed-56b3-4f46-bf87-3e8c95045100-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e606c1b3-1c40-4991-8268-142a6c63e51c-attachment.png", "type": "image/png"}], "start": 1756787216425, "stop": 1756787216618}], "start": 1756787189129, "stop": 1756787216618}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756787216618, "stop": 1756787216619}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0c54aff2-2a05-4df6-90c3-2770ce1bb39d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e6930948-fbd4-43a3-b658-deada7b5460b-attachment.png", "type": "image/png"}], "start": 1756787216619, "stop": 1756787216843}], "attachments": [{"name": "stdout", "source": "61137df0-c885-4971-8a30-44819d9e6bb5-attachment.txt", "type": "text/plain"}], "start": 1756787189128, "stop": 1756787216843, "uuid": "95c1b811-6da5-4740-9830-0d1f2432f40e", "historyId": "772728b3468560788490a3673352724d", "testCaseId": "772728b3468560788490a3673352724d", "fullName": "testcases.test_ella.dialogue.test_take_notes_on_how_to_build_a_treehouse.TestEllaTakeNotesHowBuildTreehouse#test_take_notes_on_how_to_build_a_treehouse", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_take_notes_on_how_to_build_a_treehouse"}, {"name": "subSuite", "value": "TestEllaTakeNotesHowBuildTreehouse"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_take_notes_on_how_to_build_a_treehouse"}]}