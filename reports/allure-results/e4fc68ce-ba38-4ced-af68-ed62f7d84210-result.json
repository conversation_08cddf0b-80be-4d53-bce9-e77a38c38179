{"name": "测试jump to call notifications返回正确的不支持响应", "status": "passed", "description": "验证jump to call notifications指令返回预期的不支持响应", "steps": [{"name": "执行命令: jump to call notifications", "status": "passed", "steps": [{"name": "执行命令: jump to call notifications", "status": "passed", "start": 1756799166379, "stop": 1756799197718}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8d4d8ad0-379a-4e8b-b877-3387f201a006-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f7d9c932-012b-4962-bdf8-e14b908fd27a-attachment.png", "type": "image/png"}], "start": 1756799197718, "stop": 1756799197952}], "start": 1756799166379, "stop": 1756799197953}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756799197953, "stop": 1756799197953}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "85bfb441-65ee-4e5c-a09f-8f2461897a82-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4b72e0ab-faff-41a7-bca4-e78aff6931c4-attachment.png", "type": "image/png"}], "start": 1756799197953, "stop": 1756799198151}], "attachments": [{"name": "stdout", "source": "cf50dde4-2ce3-41c8-aa14-5187d01e9388-attachment.txt", "type": "text/plain"}], "start": 1756799166379, "stop": 1756799198151, "uuid": "b916e56a-f993-4bb7-8880-8122064df2c8", "historyId": "540cff5d6d552c22ec37f66efd17315f", "testCaseId": "540cff5d6d552c22ec37f66efd17315f", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_call_notifications.TestEllaJumpCallNotifications#test_jump_to_call_notifications", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_call_notifications"}, {"name": "subSuite", "value": "TestEllaJumpCallNotifications"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_call_notifications"}]}