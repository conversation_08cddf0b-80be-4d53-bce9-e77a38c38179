{"name": "测试A cute little boy is skiing", "status": "passed", "description": "测试A cute little boy is skiing指令", "steps": [{"name": "执行命令: A cute little boy is skiing", "status": "passed", "steps": [{"name": "执行命令: A cute little boy is skiing", "status": "passed", "start": 1756795057326, "stop": 1756795082423}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "249bab03-7887-4454-9679-6faf91143100-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5283662b-572b-44fb-91ea-9c23538efc23-attachment.png", "type": "image/png"}], "start": 1756795082423, "stop": 1756795082644}], "start": 1756795057326, "stop": 1756795082644}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756795082644, "stop": 1756795082645}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5016bdab-cfe8-4ef9-be5b-39577c2fe386-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "033c2d1f-ef54-42f4-ae55-af33d99ea60c-attachment.png", "type": "image/png"}], "start": 1756795082645, "stop": 1756795082883}], "attachments": [{"name": "stdout", "source": "0703131f-8325-43a7-846d-20f63651fdce-attachment.txt", "type": "text/plain"}], "start": 1756795057326, "stop": 1756795082883, "uuid": "e2829fb7-ae57-4295-86ad-0802938bace4", "historyId": "a306f4c2244f596f1ab838aa7a80dd45", "testCaseId": "a306f4c2244f596f1ab838aa7a80dd45", "fullName": "testcases.test_ella.unsupported_commands.test_a_cute_little_boy_is_skiing.TestEllaOpenPlayPoliticalNews#test_a_cute_little_boy_is_skiing", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_a_cute_little_boy_is_skiing"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_a_cute_little_boy_is_skiing"}]}