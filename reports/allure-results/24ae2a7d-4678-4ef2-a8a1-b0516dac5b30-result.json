{"name": "测试set flex-still mode返回正确的不支持响应", "status": "passed", "description": "验证set flex-still mode指令返回预期的不支持响应", "steps": [{"name": "执行命令: set flex-still mode", "status": "passed", "steps": [{"name": "执行命令: set flex-still mode", "status": "passed", "start": 1756801818785, "stop": 1756801842304}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8a3bf15f-ee75-467c-9a6d-1142cf16d45d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6cc9ff15-85df-4144-8167-f23dafa94d7c-attachment.png", "type": "image/png"}], "start": 1756801842304, "stop": 1756801842620}], "start": 1756801818785, "stop": 1756801842621}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756801842621, "stop": 1756801842622}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1184d1dc-01d0-4d1e-9371-121894776a58-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "05365756-73aa-4b33-87bd-719691bfe0ac-attachment.png", "type": "image/png"}], "start": 1756801842622, "stop": 1756801842873}], "attachments": [{"name": "stdout", "source": "80f9ae1d-743b-4a2b-a0d9-f5d3d4346ca8-attachment.txt", "type": "text/plain"}], "start": 1756801818784, "stop": 1756801842874, "uuid": "f63128ed-8c30-46e1-98b7-037bf2185e9f", "historyId": "969451307307a13b4d89a24bb46ad0bb", "testCaseId": "969451307307a13b4d89a24bb46ad0bb", "fullName": "testcases.test_ella.unsupported_commands.test_set_flex_still_mode.TestEllaSetFlexStillMode#test_set_flex_still_mode", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_flex_still_mode"}, {"name": "subSuite", "value": "TestEllaSetFlexStillMode"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_flex_still_mode"}]}