{"name": "测试help me write an thanks letter能正常执行", "status": "passed", "description": "help me write an thanks letter", "steps": [{"name": "执行命令: help me write an thanks letter", "status": "passed", "steps": [{"name": "执行命令: help me write an thanks letter", "status": "passed", "start": 1756798461808, "stop": 1756798489247}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "51b819fb-5d17-4f01-bf2d-7b801a813476-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "08a45541-c152-49dd-b61e-133006f2f744-attachment.png", "type": "image/png"}], "start": 1756798489247, "stop": 1756798489507}], "start": 1756798461808, "stop": 1756798489507}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756798489507, "stop": 1756798489508}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7be50540-6c71-4c51-a891-159ca5bf782f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "07dd559a-7d5b-4c24-8f46-5e4f7fd7750c-attachment.png", "type": "image/png"}], "start": 1756798489509, "stop": 1756798489743}], "attachments": [{"name": "stdout", "source": "5b49aa85-2460-468a-8ef6-08859e527c3e-attachment.txt", "type": "text/plain"}], "start": 1756798461807, "stop": 1756798489744, "uuid": "86316524-320a-4eda-a634-69263f68c60f", "historyId": "bfddb3863bb9971cace5dae92df6977d", "testCaseId": "bfddb3863bb9971cace5dae92df6977d", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_write_an_thanks_letter.TestEllaHelpMeWriteAnThanksLetter#test_help_me_write_an_thanks_letter", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella技能"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_write_an_thanks_letter"}, {"name": "subSuite", "value": "TestEllaHelpMeWriteAnThanksLetter"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_write_an_thanks_letter"}]}