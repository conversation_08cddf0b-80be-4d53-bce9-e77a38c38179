{"name": "测试summarize content on this page能正常执行", "status": "passed", "description": "summarize content on this page", "steps": [{"name": "执行命令: summarize content on this page", "status": "passed", "steps": [{"name": "执行命令: summarize content on this page", "status": "passed", "start": 1756787067617, "stop": 1756787090034}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "70c1b574-0bde-4145-9ed7-7850e86ec82d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d5d071de-8d4c-4b91-b882-124fc893084a-attachment.png", "type": "image/png"}], "start": 1756787090034, "stop": 1756787090266}], "start": 1756787067617, "stop": 1756787090266}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756787090266, "stop": 1756787090268}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "11d1069d-1ae7-4f73-ad3c-c56ee6284193-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7fbecca6-3921-4b3b-8f78-6903fcadd25c-attachment.png", "type": "image/png"}], "start": 1756787090268, "stop": 1756787090469}], "attachments": [{"name": "stdout", "source": "9e52425a-d07c-4ce0-b980-83e2988b0feb-attachment.txt", "type": "text/plain"}], "start": 1756787067617, "stop": 1756787090469, "uuid": "ea9bec8f-239a-4ba1-8b8f-61f38b648bef", "historyId": "3e1e4da6344de7cdf40fa1d59c43dcc3", "testCaseId": "3e1e4da6344de7cdf40fa1d59c43dcc3", "fullName": "testcases.test_ella.dialogue.test_summarize_content_on_this_page.TestEllaSummarizeContentThisPage#test_summarize_content_on_this_page", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_summarize_content_on_this_page"}, {"name": "subSuite", "value": "TestEllaSummarizeContentThisPage"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_summarize_content_on_this_page"}]}