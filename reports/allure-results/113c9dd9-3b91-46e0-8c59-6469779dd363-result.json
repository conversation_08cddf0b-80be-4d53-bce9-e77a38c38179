{"name": "测试open the settings", "status": "passed", "description": "测试open the settings指令", "steps": [{"name": "执行命令: open the settings", "status": "passed", "steps": [{"name": "执行命令: open the settings", "status": "passed", "start": 1756800170780, "stop": 1756800200884}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "21b9a406-479c-4fb3-92f8-7945faf6ea95-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "80d67cc8-3d64-40c1-bc91-3be4cfc455a2-attachment.png", "type": "image/png"}], "start": 1756800200884, "stop": 1756800201212}], "start": 1756800170780, "stop": 1756800201213}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756800201213, "stop": 1756800201217}, {"name": "验证settings已打开", "status": "passed", "start": 1756800201217, "stop": 1756800201217}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "30c10246-40f4-4794-86b9-e745c4321800-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c25d45d7-0e8f-4eb8-bd7f-5843da149e49-attachment.png", "type": "image/png"}], "start": 1756800201217, "stop": 1756800201504}], "attachments": [{"name": "stdout", "source": "8dac55e6-8737-41c1-a14a-305e158546be-attachment.txt", "type": "text/plain"}], "start": 1756800170779, "stop": 1756800201504, "uuid": "b5490ce8-bb08-480d-a9da-d65775683dff", "historyId": "c7b8111fa78410a413dfc969cfe6f0e1", "testCaseId": "c7b8111fa78410a413dfc969cfe6f0e1", "fullName": "testcases.test_ella.unsupported_commands.test_open_the_settings.TestEllaOpenPlayPoliticalNews#test_open_the_settings", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_open_the_settings"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_open_the_settings"}]}