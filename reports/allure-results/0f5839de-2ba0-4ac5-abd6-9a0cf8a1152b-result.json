{"name": "测试enable touch optimization返回正确的不支持响应", "status": "passed", "description": "验证enable touch optimization指令返回预期的不支持响应", "steps": [{"name": "执行命令: enable touch optimization", "status": "passed", "steps": [{"name": "执行命令: enable touch optimization", "status": "passed", "start": 1756797238800, "stop": 1756797260225}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "39f814a1-8c0a-4a5b-b7a0-46521e764966-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "698ba051-041d-46e1-b770-25d5ee27352d-attachment.png", "type": "image/png"}], "start": 1756797260225, "stop": 1756797260459}], "start": 1756797238800, "stop": 1756797260459}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756797260459, "stop": 1756797260461}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ca2acb20-d2cc-4cd7-b140-6da2671010a0-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6c8eaca1-1236-4058-83d5-2e13ea62a792-attachment.png", "type": "image/png"}], "start": 1756797260461, "stop": 1756797260674}], "attachments": [{"name": "stdout", "source": "3b2e53cb-942a-4ad3-9fe5-e4cdf0afa29e-attachment.txt", "type": "text/plain"}], "start": 1756797238800, "stop": 1756797260675, "uuid": "ce086b78-efea-4785-9778-c85b3cc60557", "historyId": "fc75b92fb4a100575b2c948dd6c5a008", "testCaseId": "fc75b92fb4a100575b2c948dd6c5a008", "fullName": "testcases.test_ella.unsupported_commands.test_enable_touch_optimization.TestEllaEnableTouchOptimization#test_enable_touch_optimization", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_touch_optimization"}, {"name": "subSuite", "value": "TestEllaEnableTouchOptimization"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_touch_optimization"}]}