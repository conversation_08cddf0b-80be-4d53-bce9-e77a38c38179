{"name": "测试delete the 8 o'clock alarm", "status": "passed", "description": "测试delete the 8 o'clock alarm指令", "steps": [{"name": "执行命令: set an alarm at 8 am", "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "status": "passed", "start": 1756789403000, "stop": 1756789425279}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e020c5df-7a3a-4e2f-a348-a8d4ea4eac84-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6ca5237c-1700-4bb7-a512-1a1dc9cebd46-attachment.png", "type": "image/png"}], "start": 1756789425279, "stop": 1756789425508}], "start": 1756789403000, "stop": 1756789425508}, {"name": "执行命令: delete the 8 o'clock alarm", "status": "passed", "steps": [{"name": "执行命令: delete the 8 o'clock alarm", "status": "passed", "start": 1756789425508, "stop": 1756789447130}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c720af31-e22e-431d-8a61-77a3007bf119-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8e6a61ee-f8b7-4953-8e26-39ebd9c05efa-attachment.png", "type": "image/png"}], "start": 1756789447130, "stop": 1756789447378}], "start": 1756789425508, "stop": 1756789447378}, {"name": "执行命令: get all the alarms", "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "status": "passed", "start": 1756789447378, "stop": 1756789469403}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "666c5bc2-6d4b-4b1f-a294-b94d9bfa3343-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b5dd8f5e-e54d-461f-bd44-0238bf139f9b-attachment.png", "type": "image/png"}], "start": 1756789469404, "stop": 1756789469599}], "start": 1756789447378, "stop": 1756789469599}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756789469599, "stop": 1756789469600}, {"name": "验证响应不包含期望内容", "status": "passed", "start": 1756789469600, "stop": 1756789469602}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "48b76f33-4b7b-4c6e-940e-0074bc1fb14a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b3a35f0c-bc49-4bb5-afee-151fcb027905-attachment.png", "type": "image/png"}], "start": 1756789469602, "stop": 1756789469810}], "attachments": [{"name": "stdout", "source": "f698157d-c266-48c9-83da-ee11258702b2-attachment.txt", "type": "text/plain"}], "start": 1756789402998, "stop": 1756789469811, "uuid": "58540a05-8061-40c9-b0c1-5062518b850e", "historyId": "0c59a39b6298b394468a10532e127070", "testCaseId": "0c59a39b6298b394468a10532e127070", "fullName": "testcases.test_ella.system_coupling.test_delete_the_o_clock_alarm.TestEllaOpenDeleteOClockAlarm#test_delete_the_o_clock_alarm", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_delete_the_o_clock_alarm"}, {"name": "subSuite", "value": "TestEllaOpenDeleteOClockAlarm"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_delete_the_o_clock_alarm"}]}