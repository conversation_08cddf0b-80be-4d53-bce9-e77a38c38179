{"name": "测试enable all ai magic box features返回正确的不支持响应", "status": "passed", "description": "验证enable all ai magic box features指令返回预期的不支持响应", "steps": [{"name": "执行命令: enable all ai magic box features", "status": "passed", "steps": [{"name": "执行命令: enable all ai magic box features", "status": "passed", "start": 1756796955134, "stop": 1756796978702}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "096a565c-fc9a-489b-8cc5-beba834d9517-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d30d69f9-7a54-4507-97ae-6a40197aeb03-attachment.png", "type": "image/png"}], "start": 1756796978703, "stop": 1756796978895}], "start": 1756796955134, "stop": 1756796978896}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756796978896, "stop": 1756796978897}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d8dfe6a0-9a53-4ae3-8b1e-d5f24ba543fe-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "88df2d0a-e170-43f7-826b-cbc89e5a3680-attachment.png", "type": "image/png"}], "start": 1756796978897, "stop": 1756796979096}], "attachments": [{"name": "stdout", "source": "3976c3cd-35f9-4215-bde5-7c3df5fa67b1-attachment.txt", "type": "text/plain"}], "start": 1756796955134, "stop": 1756796979097, "uuid": "bab195ac-35fb-47f8-be16-c21aa16bb059", "historyId": "d4ded95517fa8a5af49f09554cc49725", "testCaseId": "d4ded95517fa8a5af49f09554cc49725", "fullName": "testcases.test_ella.unsupported_commands.test_enable_all_ai_magic_box_features.TestEllaEnableAllAiMagicBoxFeatures#test_enable_all_ai_magic_box_features", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_all_ai_magic_box_features"}, {"name": "subSuite", "value": "TestEllaEnableAllAiMagicBoxFeatures"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_all_ai_magic_box_features"}]}