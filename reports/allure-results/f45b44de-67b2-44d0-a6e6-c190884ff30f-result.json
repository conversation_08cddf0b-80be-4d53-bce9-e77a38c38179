{"name": "测试memory cleanup能正常执行", "status": "passed", "description": "memory cleanup", "steps": [{"name": "执行命令: memory cleanup", "status": "passed", "steps": [{"name": "执行命令: memory cleanup", "status": "passed", "start": 1756790057727, "stop": 1756790089959}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fd731f78-21ea-4e68-b4b2-9b76cf13acc0-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9fd494a8-d2e1-4815-9dd3-093e73b29b0c-attachment.png", "type": "image/png"}], "start": 1756790089959, "stop": 1756790090177}], "start": 1756790057727, "stop": 1756790090178}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790090178, "stop": 1756790090180}, {"name": "验证PhoneMaster应用已打开", "status": "passed", "start": 1756790090180, "stop": 1756790090180}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b0beb2e4-b4eb-446a-9e2d-8aad3311a602-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "68a397b1-8972-48f4-8a35-06bb0a7e09e1-attachment.png", "type": "image/png"}], "start": 1756790090180, "stop": 1756790090376}], "attachments": [{"name": "stdout", "source": "a04b886a-ab95-4025-87af-9a2919ba62b9-attachment.txt", "type": "text/plain"}], "start": 1756790057727, "stop": 1756790090376, "uuid": "1ad85f18-00c2-466e-85ca-fd70b4ef6d75", "historyId": "e4bab2ec1074fdbc8b4508dfad12adfc", "testCaseId": "e4bab2ec1074fdbc8b4508dfad12adfc", "fullName": "testcases.test_ella.system_coupling.test_memory_cleanup.TestEllaMemoryCleanup#test_memory_cleanup", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_memory_cleanup"}, {"name": "subSuite", "value": "TestEllaMemoryCleanup"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_memory_cleanup"}]}