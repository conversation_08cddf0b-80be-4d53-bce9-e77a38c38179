{"name": "测试enable auto pickup返回正确的不支持响应", "status": "passed", "description": "验证enable auto pickup指令返回预期的不支持响应", "steps": [{"name": "执行命令: enable auto pickup", "status": "passed", "steps": [{"name": "执行命令: enable auto pickup", "status": "passed", "start": 1756796996238, "stop": 1756797019030}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c9f35a50-f7fb-401f-babe-97441b7726c9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "135e0cd8-a8c8-499e-afcf-def73118820f-attachment.png", "type": "image/png"}], "start": 1756797019030, "stop": 1756797019261}], "start": 1756796996238, "stop": 1756797019262}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756797019262, "stop": 1756797019263}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4fc0cef4-23ad-4f5d-b19f-1f3eb9c633d4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "65b6dfba-c534-4b08-8484-7df7996b6378-attachment.png", "type": "image/png"}], "start": 1756797019263, "stop": 1756797019490}], "attachments": [{"name": "stdout", "source": "d13c6ee1-462e-49d6-9b36-e8ca9fbcd1cd-attachment.txt", "type": "text/plain"}], "start": 1756796996238, "stop": 1756797019491, "uuid": "d8238585-a943-41a9-9ddf-02fe41a0caae", "historyId": "57acf2797af332487c1fdb9a53a30e4f", "testCaseId": "57acf2797af332487c1fdb9a53a30e4f", "fullName": "testcases.test_ella.unsupported_commands.test_enable_auto_pickup.TestEllaEnableAutoPickup#test_enable_auto_pickup", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_auto_pickup"}, {"name": "subSuite", "value": "TestEllaEnableAutoPickup"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_auto_pickup"}]}