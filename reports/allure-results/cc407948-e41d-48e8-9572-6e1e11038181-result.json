{"name": "测试call mom through whatsapp能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['call mom through whatsapp', 'No number for mom. Please tell me the name or number to call.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.dialogue.test_call_mom_through_whatsapp.TestEllaCallMomThroughWhatsapp object at 0x0000018BC4D1FE50>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6B7BD90>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_call_mom_through_whatsapp(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['call mom through whatsapp', 'No number for mom. Please tell me the name or number to call.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_call_mom_through_whatsapp.py:33: AssertionError"}, "description": "call mom through whatsapp", "steps": [{"name": "执行命令: call mom through whatsapp", "status": "passed", "steps": [{"name": "执行命令: call mom through whatsapp", "status": "passed", "start": 1756784731984, "stop": 1756784762273}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "850a631b-3f3a-4052-9e5b-50661d7e8041-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "22cf89d3-9c72-4992-997a-8a952dd71ad2-attachment.png", "type": "image/png"}], "start": 1756784762274, "stop": 1756784762483}], "start": 1756784731984, "stop": 1756784762483}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['call mom through whatsapp', 'No number for mom. Please tell me the name or number to call.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\dialogue\\test_call_mom_through_whatsapp.py\", line 33, in test_call_mom_through_whatsapp\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756784762483, "stop": 1756784762485}], "attachments": [{"name": "stdout", "source": "a02c1ed1-37d7-45f5-be61-728c7d5a9711-attachment.txt", "type": "text/plain"}], "start": 1756784731984, "stop": 1756784762486, "uuid": "0f903203-a06b-42e1-bbf3-bc0a89165033", "historyId": "bd9c64dabd06671b98d60748492be267", "testCaseId": "bd9c64dabd06671b98d60748492be267", "fullName": "testcases.test_ella.dialogue.test_call_mom_through_whatsapp.TestEllaCallMomThroughWhatsapp#test_call_mom_through_whatsapp", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_call_mom_through_whatsapp"}, {"name": "subSuite", "value": "TestEllaCallMomThroughWhatsapp"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_call_mom_through_whatsapp"}]}