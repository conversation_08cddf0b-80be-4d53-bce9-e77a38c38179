{"name": "测试Add the schedule on the screen", "status": "passed", "description": "测试Ask Screen功能: Add the schedule on the screen", "steps": [{"name": "准备测试数据", "status": "passed", "start": 1756793855717, "stop": 1756793864769}, {"name": "执行Ask Screen命令: Add the schedule on the screen", "status": "passed", "steps": [{"name": "执行浮窗命令: Add the schedule on the screen", "status": "passed", "steps": [{"name": "执行命令: Add the schedule on the screen", "status": "passed", "start": 1756793864770, "stop": 1756793873851}, {"name": "等待并获取AI响应", "status": "passed", "start": 1756793873851, "stop": 1756793916471}, {"name": "验证响应内容", "status": "passed", "attachments": [{"name": "关键词验证结果", "source": "2d783b99-05e9-4d54-92b4-0d55991da422-attachment.txt", "type": "text/plain"}], "start": 1756793916471, "stop": 1756793916472}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "浮窗测试总结", "source": "d18b02a4-8c61-4863-99b2-7e78c7a7eeb2-attachment.txt", "type": "text/plain"}, {"name": "AI响应内容", "source": "05897f73-a12a-4757-9031-eaecc9863a6f-attachment.txt", "type": "text/plain"}], "start": 1756793916472, "stop": 1756793916474}], "start": 1756793864769, "stop": 1756793916474}, {"name": "截图记录测试完成状态", "status": "passed", "attachments": [{"name": "floating_test_completed", "source": "77d90663-71dd-43da-bf8c-99ece7052db0-attachment.png", "type": "image/png"}], "start": 1756793916474, "stop": 1756793916698}], "start": 1756793864769, "stop": 1756793917933}, {"name": "验证测试结果", "status": "passed", "start": 1756793917933, "stop": 1756793917934}, {"name": "记录测试完成", "status": "passed", "start": 1756793917935, "stop": 1756793917936}], "attachments": [{"name": "stdout", "source": "6b65da21-e3b0-40d6-bf4a-da301fb696ed-attachment.txt", "type": "text/plain"}], "start": 1756793855717, "stop": 1756793917937, "uuid": "9cf52323-3f9a-4c69-acea-2c970c4b7c53", "historyId": "6ce7f580e5f506e3d464a36918322d43", "testCaseId": "6ce7f580e5f506e3d464a36918322d43", "fullName": "testcases.test_ella.test_ask_screen.math_calculation.test_add_the_schedule_on_the_screen.TestAskScreenAddScheduleScreen#test_add_the_schedule_on_the_screen", "labels": [{"name": "story", "value": "数学计算"}, {"name": "epic", "value": "Ella浮窗测试"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ask Screen功能"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.test_ask_screen.math_calculation"}, {"name": "suite", "value": "test_add_the_schedule_on_the_screen"}, {"name": "subSuite", "value": "TestAskScreenAddScheduleScreen"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_ask_screen.math_calculation.test_add_the_schedule_on_the_screen"}]}