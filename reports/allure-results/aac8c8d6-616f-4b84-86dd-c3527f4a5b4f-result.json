{"name": "测试what time is it能正常执行", "status": "passed", "description": "what time is it", "steps": [{"name": "执行命令: what time is it", "status": "passed", "steps": [{"name": "执行命令: what time is it", "status": "passed", "start": 1756804389919, "stop": 1756804415334}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e58a2648-0629-4dd5-af4c-f37f47187920-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8476150a-1577-47ba-9d54-4599177588c2-attachment.png", "type": "image/png"}], "start": 1756804415334, "stop": 1756804415608}], "start": 1756804389919, "stop": 1756804415609}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756804415609, "stop": 1756804415611}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "52d08f68-2a71-437e-b641-900ca0b6a05a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "17dd54c2-9f09-4913-9a82-e74ae643bbc5-attachment.png", "type": "image/png"}], "start": 1756804415611, "stop": 1756804415854}], "attachments": [{"name": "stdout", "source": "66ea9f06-2561-414a-9c09-a140e4fe3563-attachment.txt", "type": "text/plain"}], "start": 1756804389918, "stop": 1756804415854, "uuid": "08053e18-b17a-4201-a699-49020ab5bf19", "historyId": "fa47cb0b4427dd62fb8f91c9e5e15ace", "testCaseId": "fa47cb0b4427dd62fb8f91c9e5e15ace", "fullName": "testcases.test_ella.unsupported_commands.test_what_time_is_it.TestEllaWhatTimeIsIt#test_what_time_is_it", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_what_time_is_it"}, {"name": "subSuite", "value": "TestEllaWhatTimeIsIt"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_what_time_is_it"}]}