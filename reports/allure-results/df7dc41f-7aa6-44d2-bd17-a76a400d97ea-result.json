{"name": "测试set screen to minimum brightness返回正确的不支持响应", "status": "passed", "description": "验证set screen to minimum brightness指令返回预期的不支持响应", "steps": [{"name": "执行命令: set screen to minimum brightness", "status": "passed", "steps": [{"name": "执行命令: set screen to minimum brightness", "status": "passed", "start": 1756802735999, "stop": 1756802769339}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e237bfe5-409d-42fc-9d1c-37cec9226389-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cb34f1f8-74a9-49e8-a99f-d3eed5cd1950-attachment.png", "type": "image/png"}], "start": 1756802769339, "stop": 1756802769596}], "start": 1756802735999, "stop": 1756802769596}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756802769596, "stop": 1756802769598}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "89610c26-24c4-491f-804c-52fb5fa1e54a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "bc363968-d0da-4b8a-9e32-9b2eec0e625d-attachment.png", "type": "image/png"}], "start": 1756802769598, "stop": 1756802769882}], "attachments": [{"name": "stdout", "source": "63052a1f-eb7a-4f54-80fd-ae722bd9849c-attachment.txt", "type": "text/plain"}], "start": 1756802735999, "stop": 1756802769884, "uuid": "9c435254-b6cb-4eb4-8436-05f9a58f1f83", "historyId": "544fc8b021d2dbcaf295cd05b798f816", "testCaseId": "544fc8b021d2dbcaf295cd05b798f816", "fullName": "testcases.test_ella.unsupported_commands.test_set_screen_to_minimum_brightness.TestEllaSetScreenMinimumBrightness#test_set_screen_to_minimum_brightness", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_screen_to_minimum_brightness"}, {"name": "subSuite", "value": "TestEllaSetScreenMinimumBrightness"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_screen_to_minimum_brightness"}]}