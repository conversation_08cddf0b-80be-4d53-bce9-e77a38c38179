{"name": "测试a clear and pink crystal necklace in the water", "status": "passed", "description": "测试a clear and pink crystal necklace in the water指令", "steps": [{"name": "执行命令: a clear and pink crystal necklace in the water", "status": "passed", "steps": [{"name": "执行命令: a clear and pink crystal necklace in the water", "status": "passed", "start": 1756794970572, "stop": 1756794997474}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e2505d5a-fdc1-4438-bfc4-eb28e1358c5c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "06eaebbe-b818-4f69-9678-672c7fae81f6-attachment.png", "type": "image/png"}], "start": 1756794997474, "stop": 1756794997707}], "start": 1756794970572, "stop": 1756794997708}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756794997708, "stop": 1756794997708}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c73362f1-85f2-4a61-8016-4a299762f5d7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fa7ccf10-4736-4c10-ad94-ec3e9014cd28-attachment.png", "type": "image/png"}], "start": 1756794997708, "stop": 1756794997936}], "attachments": [{"name": "stdout", "source": "e0605972-7576-49ff-97b6-d46090ad5628-attachment.txt", "type": "text/plain"}], "start": 1756794970571, "stop": 1756794997936, "uuid": "d64907d6-554f-4b59-87f6-c694de3726aa", "historyId": "0a76822399c9a8e342924e5ae6cce12c", "testCaseId": "0a76822399c9a8e342924e5ae6cce12c", "fullName": "testcases.test_ella.unsupported_commands.test_a_clear_and_pink_crystal_necklace_in_the_water.TestEllaOpenPlayPoliticalNews#test_a_clear_and_pink_crystal_necklace_in_the_water", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_a_clear_and_pink_crystal_necklace_in_the_water"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_a_clear_and_pink_crystal_necklace_in_the_water"}]}