{"name": "测试show me premier leaguage goal ranking能正常执行", "status": "passed", "description": "show me premier leaguage goal ranking", "steps": [{"name": "执行命令: show me premier leaguage goal ranking", "status": "passed", "steps": [{"name": "执行命令: show me premier leaguage goal ranking", "status": "passed", "start": 1756786758546, "stop": 1756786788612}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a883cc63-aeab-4b30-b42c-d6fa7f5654cf-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "270eb781-1f02-4664-957f-e2e55e74cc2d-attachment.png", "type": "image/png"}], "start": 1756786788612, "stop": 1756786788815}], "start": 1756786758546, "stop": 1756786788816}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756786788816, "stop": 1756786788817}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0b0a8c82-742b-4564-9cfe-fbd460e6dfbf-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1dec2d42-83cb-4b73-b037-954876104d70-attachment.png", "type": "image/png"}], "start": 1756786788817, "stop": 1756786788996}], "attachments": [{"name": "stdout", "source": "7deff797-d947-4b7e-a01b-d470f19a2520-attachment.txt", "type": "text/plain"}], "start": 1756786758546, "stop": 1756786788998, "uuid": "ab701910-8adc-41ae-811f-a822bff2d9a1", "historyId": "6df22ddc82daabcf8389e60296dc694e", "testCaseId": "6df22ddc82daabcf8389e60296dc694e", "fullName": "testcases.test_ella.dialogue.test_show_me_premier_leaguage_goal_ranking.TestEllaShowMePremierLeaguageGoalRanking#test_show_me_premier_leaguage_goal_ranking", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_show_me_premier_le<PERSON><PERSON>_goal_ranking"}, {"name": "subSuite", "value": "TestEllaShowMePremierLeaguageGoalRanking"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_show_me_premier_le<PERSON><PERSON>_goal_ranking"}]}