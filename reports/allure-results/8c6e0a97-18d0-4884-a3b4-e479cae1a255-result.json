{"name": "测试can you give me a coin能正常执行", "status": "passed", "description": "can you give me a coin", "steps": [{"name": "执行命令: can you give me a coin", "status": "passed", "steps": [{"name": "执行命令: can you give me a coin", "status": "passed", "start": 1756784778659, "stop": 1756784801449}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6cda1d0f-75a1-46d0-ade1-cf6fa0fbc9db-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c37781e4-ed56-46bf-918d-b479ca860f2c-attachment.png", "type": "image/png"}], "start": 1756784801449, "stop": 1756784801642}], "start": 1756784778659, "stop": 1756784801643}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756784801643, "stop": 1756784801644}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0715c19e-5ed8-4a5d-957f-46561fd16c5c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a7b4adbc-aac9-4b33-802f-2edd74a0a5dc-attachment.png", "type": "image/png"}], "start": 1756784801644, "stop": 1756784801845}], "attachments": [{"name": "stdout", "source": "3b5d03c9-5537-4ca7-90f6-e9019d3d8e3c-attachment.txt", "type": "text/plain"}], "start": 1756784778659, "stop": 1756784801846, "uuid": "2171ab16-76ca-4b7c-96ab-d3bf85cd17f4", "historyId": "78164cec6ab8359be9416229a4882ef9", "testCaseId": "78164cec6ab8359be9416229a4882ef9", "fullName": "testcases.test_ella.dialogue.test_can_you_give_me_a_coin.TestEllaCanYouGiveMeCoin#test_can_you_give_me_a_coin", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_can_you_give_me_a_coin"}, {"name": "subSuite", "value": "TestEllaCanYouGiveMeCoin"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_can_you_give_me_a_coin"}]}