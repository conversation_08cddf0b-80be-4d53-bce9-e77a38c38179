{"name": "测试show my all alarms能正常执行", "status": "passed", "description": "show my all alarms", "steps": [{"name": "执行命令:  Set an alarm at 10 am tomorrow", "status": "passed", "steps": [{"name": "执行命令: Set an alarm at 10 am tomorrow", "status": "passed", "start": 1756786805458, "stop": 1756786827423}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fc5d3ea2-2d88-4383-a968-82253b190698-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c0646d2e-7dbe-4724-a248-8f07cd7d8a95-attachment.png", "type": "image/png"}], "start": 1756786827423, "stop": 1756786827643}], "start": 1756786805458, "stop": 1756786827644}, {"name": "执行命令: show my all alarms", "status": "passed", "steps": [{"name": "执行命令: show my all alarms", "status": "passed", "start": 1756786827644, "stop": 1756786850915}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "361deb22-eebb-4cd1-b861-3de3d159015a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d7e2401b-2b74-4612-9f0d-4407ca2b2bd2-attachment.png", "type": "image/png"}], "start": 1756786850915, "stop": 1756786851135}], "start": 1756786827644, "stop": 1756786851136}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756786851136, "stop": 1756786851137}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "985b43da-890c-4b32-86fd-64c5892b868b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "51c4649f-5c88-4b6c-96b2-2ceba497efaa-attachment.png", "type": "image/png"}], "start": 1756786851138, "stop": 1756786851333}], "attachments": [{"name": "stdout", "source": "a87ffcc2-6fc4-4b3c-ac66-be41ddbcb171-attachment.txt", "type": "text/plain"}], "start": 1756786805458, "stop": 1756786851333, "uuid": "69ae3798-61e2-4a15-8605-d1f55ac8dc3c", "historyId": "1147a84f37b71eb5b15008169cadcc53", "testCaseId": "1147a84f37b71eb5b15008169cadcc53", "fullName": "testcases.test_ella.dialogue.test_show_my_all_alarms.TestEllaHowIsWeatherToday#test_show_my_all_alarms", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_show_my_all_alarms"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_show_my_all_alarms"}]}