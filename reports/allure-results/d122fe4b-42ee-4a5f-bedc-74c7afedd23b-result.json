{"name": "测试set call back with last used sim返回正确的不支持响应", "status": "passed", "description": "验证set call back with last used sim指令返回预期的不支持响应", "steps": [{"name": "执行命令: set call back with last used sim", "status": "passed", "steps": [{"name": "执行命令: set call back with last used sim", "status": "passed", "start": 1756801524627, "stop": 1756801547640}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f2a9f603-0cee-4a9e-b5fe-7c73fee514ca-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4aab54d9-162d-4a3f-bc89-f75684cd8ad9-attachment.png", "type": "image/png"}], "start": 1756801547640, "stop": 1756801547908}], "start": 1756801524626, "stop": 1756801547909}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756801547909, "stop": 1756801547911}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8a490573-d702-468c-a092-b1963ef6b86c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b091aee6-9094-4ad1-b82a-1ed174bd2fd8-attachment.png", "type": "image/png"}], "start": 1756801547911, "stop": 1756801548120}], "attachments": [{"name": "stdout", "source": "19bbc3ef-2587-4564-b14d-8ee12a0287cf-attachment.txt", "type": "text/plain"}], "start": 1756801524626, "stop": 1756801548120, "uuid": "c488d9ae-b6f5-4422-88e0-5f9171d4d95a", "historyId": "d45827de1782723ad9f8cd9d38f067dc", "testCaseId": "d45827de1782723ad9f8cd9d38f067dc", "fullName": "testcases.test_ella.unsupported_commands.test_set_call_back_with_last_used_sim.TestEllaSetCallBackLastUsedSim#test_set_call_back_with_last_used_sim", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_call_back_with_last_used_sim"}, {"name": "subSuite", "value": "TestEllaSetCallBackLastUsedSim"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_call_back_with_last_used_sim"}]}