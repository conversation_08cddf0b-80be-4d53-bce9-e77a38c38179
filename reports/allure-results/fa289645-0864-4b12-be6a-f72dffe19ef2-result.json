{"name": "测试can u check the notebook", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['can u check the notebook', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_can_u_check_the_notebook.TestEllaOpenPlayPoliticalNews object at 0x0000018BC640E0E0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC692FC10>\n\n    @allure.title(\"测试can u check the notebook\")\n    @allure.description(\"测试can u check the notebook指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_can_u_check_the_notebook(self, ella_app):\n        \"\"\"测试can u check the notebook命令\"\"\"\n        command = \"can u check the notebook\"\n        app_name = 'notes'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = ['Done']\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['can u check the notebook', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_can_u_check_the_notebook.py:31: AssertionError"}, "description": "测试can u check the notebook指令", "steps": [{"name": "执行命令: can u check the notebook", "status": "passed", "steps": [{"name": "执行命令: can u check the notebook", "status": "passed", "start": 1756795411949, "stop": 1756795438924}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "62053b62-af81-479c-9738-fdac15876a3a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "49c082a6-ef0a-4445-b198-1bcdc2e363e9-attachment.png", "type": "image/png"}], "start": 1756795438924, "stop": 1756795439137}], "start": 1756795411949, "stop": 1756795439137}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['can u check the notebook', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_can_u_check_the_notebook.py\", line 31, in test_can_u_check_the_notebook\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756795439137, "stop": 1756795439139}], "attachments": [{"name": "stdout", "source": "cc5fd9c9-041d-4c65-ad44-6d8ab9d70aaa-attachment.txt", "type": "text/plain"}], "start": 1756795411949, "stop": 1756795439140, "uuid": "eb786bb5-a603-4e91-9e41-e5e8de941383", "historyId": "400a9b197316d3b1e59fe33ed78a836a", "testCaseId": "400a9b197316d3b1e59fe33ed78a836a", "fullName": "testcases.test_ella.unsupported_commands.test_can_u_check_the_notebook.TestEllaOpenPlayPoliticalNews#test_can_u_check_the_notebook", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_can_u_check_the_notebook"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_can_u_check_the_notebook"}]}