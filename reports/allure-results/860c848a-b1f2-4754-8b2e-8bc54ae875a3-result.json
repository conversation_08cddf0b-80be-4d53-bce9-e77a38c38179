{"name": "测试help me generate a picture of a white facial cleanser product advertisement", "status": "passed", "description": "测试help me generate a picture of a white facial cleanser product advertisement指令", "steps": [{"name": "执行命令: help me generate a picture of a white facial cleanser product advertisement", "status": "passed", "steps": [{"name": "执行命令: help me generate a picture of a white facial cleanser product advertisement", "status": "passed", "start": 1756798133657, "stop": 1756798155270}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "129fc320-3623-42c0-980c-8752f7dfbda0-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "38b235fa-ad0e-40ae-b3fd-e266be8b0257-attachment.png", "type": "image/png"}], "start": 1756798155270, "stop": 1756798155533}], "start": 1756798133657, "stop": 1756798155533}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756798155533, "stop": 1756798155535}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0f52188b-0824-4797-b20a-7cf20fd576ca-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "55c680af-e4cc-410e-8ca9-efc7b5c3e919-attachment.png", "type": "image/png"}], "start": 1756798155535, "stop": 1756798155766}], "attachments": [{"name": "stdout", "source": "2217c754-357b-4315-81c1-c0d2d042eeb2-attachment.txt", "type": "text/plain"}], "start": 1756798133657, "stop": 1756798155766, "uuid": "574a9519-3ed9-4a64-b7bb-5570bbc7197b", "historyId": "461fa25c15c0a862f353e5384438bc5d", "testCaseId": "461fa25c15c0a862f353e5384438bc5d", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_a_white_facial_cleanser.TestEllaOpenPlayPoliticalNews#test_help_me_generate_a_picture_of_a_white_facial_cleanser", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_generate_a_picture_of_a_white_facial_cleanser"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_a_white_facial_cleanser"}]}