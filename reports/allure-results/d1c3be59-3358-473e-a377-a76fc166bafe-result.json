{"name": "测试Help me write an email to make an appointment for a visit能正常执行", "status": "passed", "description": "Help me write an email to make an appointment for a visit", "steps": [{"name": "执行命令: Help me write an email to make an appointment for a visit", "status": "passed", "steps": [{"name": "执行命令: Help me write an email to make an appointment for a visit", "status": "passed", "start": 1756785217254, "stop": 1756785257118}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "22036e13-d327-4ff4-bfe2-54d285a69504-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "dbbfcfa3-49e9-4c9b-bcbd-63304cda71cd-attachment.png", "type": "image/png"}], "start": 1756785257118, "stop": 1756785257336}], "start": 1756785217254, "stop": 1756785257336}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756785257337, "stop": 1756785257337}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "44b83f42-b820-44e9-8826-ea2435d9e686-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1355e39d-cc80-47ad-9e72-7c48aeef1028-attachment.png", "type": "image/png"}], "start": 1756785257337, "stop": 1756785257560}], "attachments": [{"name": "stdout", "source": "0a2e142d-bccd-4502-9785-a0244fff1439-attachment.txt", "type": "text/plain"}], "start": 1756785217254, "stop": 1756785257561, "uuid": "e8e87b92-745d-4f55-9075-2f160ae4f1e0", "historyId": "12eb3852c333145c5906579f2346c37a", "testCaseId": "12eb3852c333145c5906579f2346c37a", "fullName": "testcases.test_ella.dialogue.test_help_me_write_an_email_to_make_an_appointment_for_a_visit.TestEllaHelpMeWriteAnEmailMakeAnAppointmentVisit#test_help_me_write_an_email_to_make_an_appointment_for_a_visit", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_help_me_write_an_email_to_make_an_appointment_for_a_visit"}, {"name": "subSuite", "value": "TestEllaHelpMeWriteAnEmailMakeAnAppointmentVisit"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_help_me_write_an_email_to_make_an_appointment_for_a_visit"}]}