{"name": "测试open maps", "status": "passed", "description": "测试open maps指令", "steps": [{"name": "执行命令: open maps", "status": "passed", "steps": [{"name": "执行命令: open maps", "status": "passed", "start": 1756800017401, "stop": 1756800054670}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4db8299c-74c5-4af4-97ca-15320ff272c9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8209e1be-659b-49ff-9047-8a93321e3f32-attachment.png", "type": "image/png"}], "start": 1756800054670, "stop": 1756800054915}], "start": 1756800017401, "stop": 1756800054915}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756800054915, "stop": 1756800054916}, {"name": "验证google_maps已打开", "status": "passed", "start": 1756800054916, "stop": 1756800054916}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7d319796-1188-40b1-93e9-3d986b70148c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "dc833c94-3a6f-4aaf-8c8a-d0788790df93-attachment.png", "type": "image/png"}], "start": 1756800054916, "stop": 1756800055153}], "attachments": [{"name": "stdout", "source": "32104c27-d6e6-4816-9503-848222b8fbbe-attachment.txt", "type": "text/plain"}], "start": 1756800017401, "stop": 1756800055154, "uuid": "eb59064e-e54f-4be0-b988-7cebc4a81d30", "historyId": "2b4b7555520a6b757239820871731d81", "testCaseId": "2b4b7555520a6b757239820871731d81", "fullName": "testcases.test_ella.unsupported_commands.test_open_maps.TestEllaOpenPlayPoliticalNews#test_open_maps", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_open_maps"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_open_maps"}]}