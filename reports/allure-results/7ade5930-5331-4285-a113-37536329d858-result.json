{"name": "测试Generate a landscape painting image for me", "status": "passed", "description": "测试Generate a landscape painting image for me指令", "steps": [{"name": "执行命令: Generate a landscape painting image for me", "status": "passed", "steps": [{"name": "执行命令: Generate a landscape painting image for me", "status": "passed", "start": 1756797515821, "stop": 1756797538579}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "82a452cc-d854-4692-b668-f696390cd8ff-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "26b30bd5-bfe0-4772-b76c-05c5d83c73c1-attachment.png", "type": "image/png"}], "start": 1756797538580, "stop": 1756797538849}], "start": 1756797515821, "stop": 1756797538850}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756797538850, "stop": 1756797538851}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "810bf229-e2ad-47e4-9fb6-eca23b86344f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "432348da-439e-4114-99c4-53762801ae2a-attachment.png", "type": "image/png"}], "start": 1756797538851, "stop": 1756797539068}], "attachments": [{"name": "stdout", "source": "c60ef7f9-796b-40ef-b193-132876ef6116-attachment.txt", "type": "text/plain"}], "start": 1756797515821, "stop": 1756797539069, "uuid": "549215e1-5959-4613-847c-39286e404044", "historyId": "e1b97b8698ff620d6d8faf32f381c874", "testCaseId": "e1b97b8698ff620d6d8faf32f381c874", "fullName": "testcases.test_ella.unsupported_commands.test_generate_a_landscape_painting_image_for_me.TestEllaOpenPlayPoliticalNews#test_generate_a_landscape_painting_image_for_me", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_generate_a_landscape_painting_image_for_me"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_generate_a_landscape_painting_image_for_me"}]}