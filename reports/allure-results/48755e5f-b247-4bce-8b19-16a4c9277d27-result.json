{"name": "测试play music", "status": "passed", "description": "测试play music指令", "steps": [{"name": "执行命令: play music", "status": "passed", "steps": [{"name": "执行命令: play music", "status": "passed", "start": 1756783886271, "stop": 1756783916359}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "00da6348-9db5-40fb-b6ca-eead54081e53-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e1b3dbaa-fac3-4dd7-b5cb-e911cbdd6c96-attachment.png", "type": "image/png"}], "start": 1756783916359, "stop": 1756783916594}], "start": 1756783886271, "stop": 1756783916594}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756783916594, "stop": 1756783916596}, {"name": "验证visha已打开", "status": "passed", "start": 1756783916596, "stop": 1756783916596}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "bdc9343a-5522-4887-82b2-e7765805cc72-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f7c24979-df04-44d7-b40d-789de1622c53-attachment.png", "type": "image/png"}], "start": 1756783916596, "stop": 1756783916817}], "attachments": [{"name": "stdout", "source": "e59ed7bc-f80d-4cd6-8876-20d3c340aa9b-attachment.txt", "type": "text/plain"}], "start": 1756783886271, "stop": 1756783916818, "uuid": "c73ddb0e-4ca8-41bd-9698-be6ba7037277", "historyId": "148d3ba280bfe2b41b8464beec5f6763", "testCaseId": "148d3ba280bfe2b41b8464beec5f6763", "fullName": "testcases.test_ella.component_coupling.test_play_music.TestEllaOpenVisha#test_play_music", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_music"}, {"name": "subSuite", "value": "TestEllaOpen<PERSON>a"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_music"}]}