{"name": "测试turn off the 8 am alarm", "status": "passed", "description": "测试turn off the 8 am alarm指令", "steps": [{"name": "执行命令: delete all the alarms", "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "status": "passed", "start": 1756792449370, "stop": 1756792470137}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "27178349-c339-441d-bf88-0150b9ca5437-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0dd409f6-4122-44b8-a327-e93c8defb7b6-attachment.png", "type": "image/png"}], "start": 1756792470137, "stop": 1756792470384}], "start": 1756792449370, "stop": 1756792470384}, {"name": "执行命令: set an alarm at 8 am", "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "status": "passed", "start": 1756792470384, "stop": 1756792490972}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f8a2e07f-280b-49de-b4c1-104516ac4aef-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5ede986f-1a3f-4c35-8b54-a496a4a0ddf0-attachment.png", "type": "image/png"}], "start": 1756792490972, "stop": 1756792491195}], "start": 1756792470384, "stop": 1756792491195}, {"name": "执行命令: turn off the 8 am alarm", "status": "passed", "steps": [{"name": "执行命令: turn off the 8 am alarm", "status": "passed", "start": 1756792491195, "stop": 1756792511597}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ece8b318-8cdc-489f-b016-dcec750c93bb-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d7d95709-c89f-4cb5-b52c-559134bd6a4a-attachment.png", "type": "image/png"}], "start": 1756792511597, "stop": 1756792511796}], "start": 1756792491195, "stop": 1756792511796}, {"name": "执行命令: get all the alarms", "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "status": "passed", "start": 1756792511796, "stop": 1756792532197}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "33c31e9a-3e0f-459b-b627-afb0be055484-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "972205bf-1810-44e7-8853-a3d1523822f3-attachment.png", "type": "image/png"}], "start": 1756792532198, "stop": 1756792532425}], "start": 1756792511796, "stop": 1756792532426}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756792532426, "stop": 1756792532427}], "attachments": [{"name": "stdout", "source": "90631409-21a9-45c4-82f5-947be03fc4fb-attachment.txt", "type": "text/plain"}], "start": 1756792449369, "stop": 1756792532427, "uuid": "07e7392e-ca40-44f7-9000-604f7346155a", "historyId": "c33807b5ffc8a9f3d1a58e446637a66b", "testCaseId": "c33807b5ffc8a9f3d1a58e446637a66b", "fullName": "testcases.test_ella.system_coupling.test_turn_off_the_am_alarm.TestEllaOpenTurnOffAmAlarm#test_turn_off_the_am_alarm", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_off_the_am_alarm"}, {"name": "subSuite", "value": "TestEllaOpenTurnOffAmAlarm"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_off_the_am_alarm"}]}