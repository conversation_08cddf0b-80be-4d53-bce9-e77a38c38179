{"name": "测试min alarm clock volume", "status": "passed", "description": "测试min alarm clock volume指令", "steps": [{"name": "执行命令: min alarm clock volume", "status": "passed", "steps": [{"name": "执行命令: min alarm clock volume", "status": "passed", "start": 1756790106569, "stop": 1756790127658}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9ac78511-e8df-425b-94cf-44121171e29e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "994ecaae-382d-45e7-9beb-275eca304051-attachment.png", "type": "image/png"}], "start": 1756790127658, "stop": 1756790127852}], "start": 1756790106569, "stop": 1756790127852}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790127852, "stop": 1756790127853}, {"name": "验证alarm_volume已打开", "status": "passed", "start": 1756790127853, "stop": 1756790127853}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a14f9e3a-1ce9-42be-8984-0eb16aae3a45-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f5646806-3cb0-4004-86ac-e863fce9d618-attachment.png", "type": "image/png"}], "start": 1756790127853, "stop": 1756790128087}], "attachments": [{"name": "stdout", "source": "2efdbfcc-dfbb-45fd-9bef-bf53b02ceb10-attachment.txt", "type": "text/plain"}], "start": 1756790106569, "stop": 1756790128087, "uuid": "37845c84-bbcc-4da5-8168-5085a85205ae", "historyId": "963c2cd1bdb409e4cfe9589a18006e88", "testCaseId": "963c2cd1bdb409e4cfe9589a18006e88", "fullName": "testcases.test_ella.system_coupling.test_min_alarm_clock_volume.TestEllaOpenAlarmVolume#test_min_alarm_clock_volume", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_min_alarm_clock_volume"}, {"name": "subSuite", "value": "TestEllaOpenAlarmVolume"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_min_alarm_clock_volume"}]}