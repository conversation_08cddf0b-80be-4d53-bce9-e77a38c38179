"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaOpenPlayPoliticalNews(SimpleEllaTest):
    """Ella打开Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations测试类"""

    @allure.title("测试Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations")
    @allure.description("测试Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations指令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_design_a_high_end_jewelry_ring_themed_around_flamingo_elements_for_e_commerce_illustrations(self, ella_app):
        """测试Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations命令"""
        command = "Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations"
        expected_text = ['The following images are generated for you.','reached the image generation limit for today','Generated by AI, for reference only']
        ''

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应包含期望内容"):
            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"


        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
