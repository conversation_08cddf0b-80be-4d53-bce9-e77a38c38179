{"name": "测试the mobile phone is very hot", "status": "passed", "description": "测试the mobile phone is very hot指令", "steps": [{"name": "执行命令: the mobile phone is very hot", "status": "passed", "steps": [{"name": "执行命令: the mobile phone is very hot", "status": "passed", "start": 1756803670917, "stop": 1756803694486}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8621962b-6f9f-4a6f-b0eb-a9cd3982b261-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b9cf0013-1cc4-4319-b1f0-09a5b117602c-attachment.png", "type": "image/png"}], "start": 1756803694486, "stop": 1756803694739}], "start": 1756803670916, "stop": 1756803694739}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756803694739, "stop": 1756803694742}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2b7012b3-e05e-422f-bb15-76e952b12f83-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "36d60fb7-ae44-4f5f-afb5-d0f89e9f3365-attachment.png", "type": "image/png"}], "start": 1756803694742, "stop": 1756803694985}], "attachments": [{"name": "stdout", "source": "7d2386e5-4c88-41e9-9a93-65c5ed76837c-attachment.txt", "type": "text/plain"}], "start": 1756803670916, "stop": 1756803694986, "uuid": "70be55e7-1567-405f-919b-6f7f0fa00835", "historyId": "9fcc7f87aa3845b28893959bf17baa2b", "testCaseId": "9fcc7f87aa3845b28893959bf17baa2b", "fullName": "testcases.test_ella.unsupported_commands.test_the_mobile_phone_is_very_hot.TestEllaOpenPlayPoliticalNews#test_the_mobile_phone_is_very_hot", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_the_mobile_phone_is_very_hot"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_the_mobile_phone_is_very_hot"}]}