{"name": "测试switch to performance mode返回正确的不支持响应", "status": "passed", "description": "验证switch to performance mode指令返回预期的不支持响应", "steps": [{"name": "执行命令: switch to performance mode", "status": "passed", "steps": [{"name": "执行命令: switch to performance mode", "status": "passed", "start": 1756803417330, "stop": 1756803440874}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "46ebee14-5f82-406a-9b40-79699d4276a2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "77b68d4a-9923-4617-88e9-ee5fccb25a71-attachment.png", "type": "image/png"}], "start": 1756803440874, "stop": 1756803441135}], "start": 1756803417330, "stop": 1756803441136}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756803441136, "stop": 1756803441138}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1c3d776c-cc13-48cc-bbae-caf5f61f3669-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "61ef3512-ad40-4c98-8f0c-6eef902154e2-attachment.png", "type": "image/png"}], "start": 1756803441138, "stop": 1756803441395}], "attachments": [{"name": "stdout", "source": "6f2a8419-4592-4a8c-be1b-ad3e83c36668-attachment.txt", "type": "text/plain"}], "start": 1756803417329, "stop": 1756803441396, "uuid": "e96f9fa7-9044-4e0e-bf31-367e2f2ac3da", "historyId": "34f3c9cc9098f792051e7099b7a9fdc1", "testCaseId": "34f3c9cc9098f792051e7099b7a9fdc1", "fullName": "testcases.test_ella.unsupported_commands.test_switch_to_performance_mode.TestEllaSwitchPerformanceMode#test_switch_to_performance_mode", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_switch_to_performance_mode"}, {"name": "subSuite", "value": "TestEllaSwitchPerformanceMode"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_switch_to_performance_mode"}]}