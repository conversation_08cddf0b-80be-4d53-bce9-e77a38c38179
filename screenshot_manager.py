"""
截图管理工具
提供截图的查看、统计、清理等功能
"""
import os
import shutil
from datetime import datetime, timedelta
from utils.screenshot_utils import screenshot_manager
from utils.yaml_utils import YamlUtils
from core.logger import log


def show_screenshot_summary():
    """显示截图统计信息"""
    log.info("📊 截图统计信息")
    log.info("=" * 60)
    
    summary = screenshot_manager.get_screenshot_summary()
    
    log.info(f"总文件数: {summary['total_files']}")
    log.info(f"总大小: {summary['total_size'] / 1024 / 1024:.2f} MB")
    log.info(f"测试类数量: {len(summary['test_classes'])}")
    
    if summary['test_classes']:
        log.info("\n📂 按测试类分组:")
        for test_class in sorted(summary['test_classes']):
            class_info = summary['by_class'][test_class]
            log.info(f"  {test_class}:")
            log.info(f"    文件数: {class_info['file_count']}")
            log.info(f"    大小: {class_info['size'] / 1024:.2f} KB")


def show_directory_structure():
    """显示目录结构"""
    log.info("\n📁 截图目录结构")
    log.info("=" * 60)
    
    project_root = YamlUtils.get_project_root()
    screenshot_base = os.path.join(project_root, "reports", "screenshots")
    
    if not os.path.exists(screenshot_base):
        log.info("截图目录不存在")
        return
    
    log.info(f"根目录: {screenshot_base}")
    
    # 统计信息
    total_dirs = 0
    total_files = 0
    
    for item in sorted(os.listdir(screenshot_base)):
        item_path = os.path.join(screenshot_base, item)
        
        if os.path.isdir(item_path):
            total_dirs += 1
            png_files = [f for f in os.listdir(item_path) if f.endswith('.png')]
            total_files += len(png_files)
            
            log.info(f"\n📂 {item}/ ({len(png_files)} 个文件)")
            
            # 按时间排序显示文件
            png_files_with_time = []
            for png_file in png_files:
                file_path = os.path.join(item_path, png_file)
                mtime = os.path.getmtime(file_path)
                png_files_with_time.append((png_file, mtime))
            
            png_files_with_time.sort(key=lambda x: x[1], reverse=True)
            
            for i, (png_file, mtime) in enumerate(png_files_with_time):
                time_str = datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S")
                file_path = os.path.join(item_path, png_file)
                size_kb = os.path.getsize(file_path) / 1024
                
                log.info(f"    📷 {png_file} ({size_kb:.1f}KB, {time_str})")
                
                if i >= 4:  # 只显示最新的5个文件
                    remaining = len(png_files_with_time) - 5
                    if remaining > 0:
                        log.info(f"    ... 还有 {remaining} 个文件")
                    break
        
        elif item.endswith('.png'):
            total_files += 1
            file_path = os.path.join(screenshot_base, item)
            mtime = os.path.getmtime(file_path)
            time_str = datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S")
            size_kb = os.path.getsize(file_path) / 1024
            log.info(f"📷 {item} ({size_kb:.1f}KB, {time_str}) [根目录]")
    
    log.info(f"\n📈 统计: {total_dirs} 个测试类目录, {total_files} 个截图文件")


def cleanup_old_screenshots(days: int = 7, dry_run: bool = True):
    """清理旧截图"""
    log.info(f"\n🧹 清理 {days} 天前的截图")
    log.info("=" * 60)
    
    if dry_run:
        log.info("⚠️ 这是预览模式，不会实际删除文件")
    
    project_root = YamlUtils.get_project_root()
    screenshot_base = os.path.join(project_root, "reports", "screenshots")
    
    if not os.path.exists(screenshot_base):
        log.info("截图目录不存在")
        return
    
    cutoff_time = datetime.now() - timedelta(days=days)
    cutoff_timestamp = cutoff_time.timestamp()
    
    deleted_files = []
    deleted_size = 0
    
    for root, dirs, files in os.walk(screenshot_base):
        for file in files:
            if file.endswith('.png'):
                file_path = os.path.join(root, file)
                mtime = os.path.getmtime(file_path)
                
                if mtime < cutoff_timestamp:
                    file_size = os.path.getsize(file_path)
                    relative_path = os.path.relpath(file_path, screenshot_base)
                    
                    deleted_files.append({
                        'path': relative_path,
                        'size': file_size,
                        'mtime': datetime.fromtimestamp(mtime)
                    })
                    deleted_size += file_size
                    
                    if not dry_run:
                        os.remove(file_path)
                        log.info(f"删除: {relative_path}")
    
    if deleted_files:
        log.info(f"找到 {len(deleted_files)} 个需要清理的文件:")
        for file_info in deleted_files[:10]:  # 只显示前10个
            log.info(f"  {file_info['path']} ({file_info['size']/1024:.1f}KB, {file_info['mtime'].strftime('%Y-%m-%d %H:%M:%S')})")
        
        if len(deleted_files) > 10:
            log.info(f"  ... 还有 {len(deleted_files) - 10} 个文件")
        
        log.info(f"总计: {deleted_size / 1024 / 1024:.2f} MB")
        
        if dry_run:
            log.info("\n💡 要实际删除这些文件，请运行: python screenshot_manager.py --cleanup --confirm")
    else:
        log.info("没有找到需要清理的文件")


def backup_screenshots():
    """备份截图"""
    log.info("\n💾 备份截图")
    log.info("=" * 60)
    
    project_root = YamlUtils.get_project_root()
    screenshot_base = os.path.join(project_root, "reports", "screenshots")
    
    if not os.path.exists(screenshot_base):
        log.info("截图目录不存在")
        return
    
    # 创建备份目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = os.path.join(project_root, "reports", f"screenshots_backup_{timestamp}")
    
    try:
        shutil.copytree(screenshot_base, backup_dir)
        
        # 计算备份大小
        total_size = 0
        file_count = 0
        for root, dirs, files in os.walk(backup_dir):
            for file in files:
                if file.endswith('.png'):
                    file_path = os.path.join(root, file)
                    total_size += os.path.getsize(file_path)
                    file_count += 1
        
        log.info(f"✅ 备份完成: {backup_dir}")
        log.info(f"备份了 {file_count} 个文件, 总大小 {total_size / 1024 / 1024:.2f} MB")
        
    except Exception as e:
        log.error(f"❌ 备份失败: {e}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="截图管理工具")
    parser.add_argument("--summary", action="store_true", help="显示截图统计信息")
    parser.add_argument("--structure", action="store_true", help="显示目录结构")
    parser.add_argument("--cleanup", type=int, default=1, help="清理N天前的截图")
    parser.add_argument("--confirm", action="store_true", help="确认执行清理操作")
    parser.add_argument("--backup", action="store_true", help="备份截图")
    parser.add_argument("--all", action="store_true", help="显示所有信息")
    
    args = parser.parse_args()
    
    log.info("🖼️ 截图管理工具")
    log.info("=" * 80)
    
    if args.all or len([arg for arg in vars(args).values() if arg]) == 0:
        # 默认显示所有信息
        show_screenshot_summary()
        show_directory_structure()
        cleanup_old_screenshots(args.cleanup, dry_run=True)
    else:
        if args.summary:
            show_screenshot_summary()
        
        if args.structure:
            show_directory_structure()
        
        if args.cleanup:
            cleanup_old_screenshots(args.cleanup, dry_run=not args.confirm)
        
        if args.backup:
            backup_screenshots()
    
    log.info("\n" + "=" * 80)
    log.info("🎯 截图功能优化总结:")
    log.info("✅ 1. 截图按测试类名称自动分文件夹保存")
    log.info("✅ 2. 支持自动生成包含测试方法名的文件名")
    log.info("✅ 3. 提供完整的截图统计和管理功能")
    log.info("✅ 4. 保持向后兼容性，不影响现有代码")
    log.info("✅ 5. 集成到pytest和Allure报告系统")
    
    log.info("\n💡 使用建议:")
    log.info("- 定期运行 --cleanup 清理旧截图")
    log.info("- 重要测试前运行 --backup 备份截图")
    log.info("- 使用 --structure 查看测试覆盖情况")


if __name__ == "__main__":
    # main()
    cleanup_old_screenshots(days=1,dry_run=False)
