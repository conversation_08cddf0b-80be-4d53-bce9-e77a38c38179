{"name": "测试a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance", "status": "passed", "description": "测试a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance指令", "steps": [{"name": "执行命令: a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance", "status": "passed", "steps": [{"name": "执行命令: a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance", "status": "passed", "start": 1756795230246, "stop": 1756795255348}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4b875a9b-a018-4b14-9c50-05ab722302a2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "482d94f2-972b-415b-95c6-49f104e37c43-attachment.png", "type": "image/png"}], "start": 1756795255348, "stop": 1756795255575}], "start": 1756795230246, "stop": 1756795255575}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756795255575, "stop": 1756795255576}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b855570f-4a7b-4e88-8cd2-0e0fe7f5e562-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ea09ee9e-ee78-4e04-8a30-9ac3342f6b7d-attachment.png", "type": "image/png"}], "start": 1756795255576, "stop": 1756795255818}], "attachments": [{"name": "stdout", "source": "fef9e4a1-3731-4695-ac5c-a553a39e8cd8-attachment.txt", "type": "text/plain"}], "start": 1756795230246, "stop": 1756795255819, "uuid": "ecce3e54-b287-43d3-8e4b-2c1282016fe8", "historyId": "468e62202269d66d1ea3c0ae6e2a0e21", "testCaseId": "468e62202269d66d1ea3c0ae6e2a0e21", "fullName": "testcases.test_ella.unsupported_commands.test_a_little_raccoon_walks_on_a_forest_meadow.TestEllaOpenPlayPoliticalNews#test_a_little_raccoon_walks_on_a_forest_meadow", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_a_little_raccoon_walks_on_a_forest_meadow"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_a_little_raccoon_walks_on_a_forest_meadow"}]}