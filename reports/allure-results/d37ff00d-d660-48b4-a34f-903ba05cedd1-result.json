{"name": "测试start running能正常执行", "status": "passed", "description": "start running", "steps": [{"name": "执行命令: start running", "status": "passed", "steps": [{"name": "执行命令: start running", "status": "passed", "start": 1756803201148, "stop": 1756803229140}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "439b3377-c1d8-469f-b006-c60f858605e2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8882d852-55d4-4a16-b3b8-ebc0198f5866-attachment.png", "type": "image/png"}], "start": 1756803229141, "stop": 1756803229427}], "start": 1756803201148, "stop": 1756803229428}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756803229428, "stop": 1756803229429}, {"name": "验证Health应用已打开", "status": "passed", "start": 1756803229429, "stop": 1756803229429}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1d8358d7-ff8c-4f1d-a45c-603480c31514-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "932747ab-42b4-4aa1-bac6-3f7ff11fa0a2-attachment.png", "type": "image/png"}], "start": 1756803229429, "stop": 1756803229658}], "attachments": [{"name": "stdout", "source": "b74a4acc-7d8f-474a-91a9-d0a9b42a3f0c-attachment.txt", "type": "text/plain"}], "start": 1756803201148, "stop": 1756803229658, "uuid": "d03c2f91-3c3d-41fc-bc46-e67c96fb7a66", "historyId": "5c75e7ecaa2fe55a9b9666aae0ca1b5a", "testCaseId": "5c75e7ecaa2fe55a9b9666aae0ca1b5a", "fullName": "testcases.test_ella.unsupported_commands.test_start_running.TestEllaStartRunning#test_start_running", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_start_running"}, {"name": "subSuite", "value": "TestEllaStartRunning"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_start_running"}]}