{"name": "测试where is the carlcare service outlet能正常执行", "status": "passed", "description": "where is the carlcare service outlet", "steps": [{"name": "执行命令: where is the carlcare service outlet", "status": "passed", "steps": [{"name": "执行命令: where is the carlcare service outlet", "status": "passed", "start": 1756793384033, "stop": 1756793410747}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a8852297-6178-400a-a3eb-b8a316b16f56-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "aecaca07-f5b7-4546-8f4a-1d186244bd82-attachment.png", "type": "image/png"}], "start": 1756793410747, "stop": 1756793410953}], "start": 1756793384033, "stop": 1756793410954}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756793410954, "stop": 1756793410955}, {"name": "验证应用已打开", "status": "passed", "start": 1756793410955, "stop": 1756793410955}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "26f6bc4c-dad9-4133-a215-e0df744bff44-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a3ba608a-7227-4de3-80d7-3b5f72d1b65e-attachment.png", "type": "image/png"}], "start": 1756793410955, "stop": 1756793411163}], "attachments": [{"name": "stdout", "source": "e2ec83eb-5edf-40b8-bfcd-4b45d69e6abe-attachment.txt", "type": "text/plain"}], "start": 1756793384033, "stop": 1756793411163, "uuid": "2044ad5d-f277-46b4-a9bb-9cbba61e9f15", "historyId": "4f84a6588e41dde581e4eef4fccd6344", "testCaseId": "4f84a6588e41dde581e4eef4fccd6344", "fullName": "testcases.test_ella.system_coupling.test_where_is_the_carlcare_service_outlet.TestEllaWhereIsCarlcareServiceOutlet#test_where_is_the_carlcare_service_outlet", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_where_is_the_carlcare_service_outlet"}, {"name": "subSuite", "value": "TestEllaWhereIsCarlcareServiceOutlet"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_where_is_the_carlcare_service_outlet"}]}