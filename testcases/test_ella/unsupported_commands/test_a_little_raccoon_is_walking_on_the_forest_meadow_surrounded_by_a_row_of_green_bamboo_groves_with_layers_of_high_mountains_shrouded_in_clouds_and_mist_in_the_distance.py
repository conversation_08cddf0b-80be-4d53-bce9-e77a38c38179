"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaOpenPlayPoliticalNews(SimpleEllaTest):
    """Ella打开A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance测试类"""

    @allure.title("测试A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance")
    @allure.description("测试A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance指令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_a_little_raccoon_is_walking_on_the_forest_meadow_surrounded_by_a_row_of_green_bamboo_groves_with_layers_of_high_mountains_shrouded_in_clouds_and_mist_in_the_distance(self, ella_app):
        """测试A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance命令"""
        command = "A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance"
        expected_text = ['The following images are generated for you.','Generated by AI, for reference only','reached the image generation limit for today']
        ''

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应包含期望内容"):
            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"


        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
