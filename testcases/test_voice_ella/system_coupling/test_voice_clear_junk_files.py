"""
Ella语音助手语音指令测试
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaClearJunkFilesVoice(SimpleEllaTest):
    @allure.title("测试clear junk files命令")
    @allure.description("使用简化的测试框架测试phonemaster开启命令，验证响应包含Done且实际打开PhoneMaster")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_clear_junk_files_voice(self, ella_app):
        """测试clear junk files命令 - 简洁版本"""
        command = "clear junk files"
        voice_language = self.voice_language
        voice_duration = self.voice_duration

        with allure.step(f"执行语音命令: {command} (语言: {voice_language})"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command,
                is_voice=True,  # 语音标识为True
                voice_duration=voice_duration,
                voice_language=voice_language, verify_files=False, verify_status=True,
            )

        with allure.step("验证响应包含Done"):
            result = self.verify_expected_in_response("done", response_text)
            assert result, f"响应文本应包含'Done'，实际响应: '{response_text}'"

        with allure.step(f"验证PhoneMaster应用已打开"):
            assert final_status, f"PhoneMaster应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            # 添加语音相关信息
            voice_info = f"""
语音模式: 是
语音语言: {voice_language}
语音持续时间: {voice_duration}秒"""
            summary += voice_info
            # 添加额外的验证信息
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")

        # pytest测试函数不应该返回值，所有验证都应该通过assert完成
