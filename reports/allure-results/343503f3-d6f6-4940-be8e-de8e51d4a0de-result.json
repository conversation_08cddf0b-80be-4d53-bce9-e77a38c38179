{"name": "测试how's the weather today?返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['℃', 'today']，实际响应: '[\"how's the weather today?\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.dialogue.test_how_s_the_weather_today.TestEllaHowSWeatherToday object at 0x0000018BC4CDB250>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6B78730>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_how_s_the_weather_today(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['℃', 'today']，实际响应: '[\"how's the weather today?\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_how_s_the_weather_today.py:36: AssertionError"}, "description": "验证how's the weather today?指令返回预期的不支持响应", "steps": [{"name": "执行命令: how's the weather today?", "status": "passed", "steps": [{"name": "执行命令: how's the weather today?", "status": "passed", "start": 1756785399612, "stop": 1756785427437}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e6b30f57-5017-43ab-a954-40721e12757b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "adccd26d-5633-4423-bd2e-fe297e92abbf-attachment.png", "type": "image/png"}], "start": 1756785427437, "stop": 1756785427651}], "start": 1756785399612, "stop": 1756785427652}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['℃', 'today']，实际响应: '[\"how's the weather today?\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\dialogue\\test_how_s_the_weather_today.py\", line 36, in test_how_s_the_weather_today\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756785427652, "stop": 1756785427655}], "attachments": [{"name": "stdout", "source": "9b610bb7-dd3e-442f-a091-84f1b3e83c16-attachment.txt", "type": "text/plain"}], "start": 1756785399612, "stop": 1756785427655, "uuid": "8a0e8216-3139-4ed7-90fb-9fc257c9bbab", "historyId": "f2f6762c5ec83e110ace25b47e3112d5", "testCaseId": "f2f6762c5ec83e110ace25b47e3112d5", "fullName": "testcases.test_ella.dialogue.test_how_s_the_weather_today.TestEllaHowSWeatherToday#test_how_s_the_weather_today", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_s_the_weather_today"}, {"name": "subSuite", "value": "TestEllaHowSWeatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_s_the_weather_today"}]}