{"name": "测试play political news", "status": "passed", "description": "测试play political news指令", "steps": [{"name": "执行命令: play political news", "status": "passed", "steps": [{"name": "执行命令: play political news", "status": "passed", "start": 1756786415721, "stop": 1756786441956}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "da900e44-263a-417d-aaf8-eff71e14dac8-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b1a703a9-2108-429c-ab2c-7b95027d103e-attachment.png", "type": "image/png"}], "start": 1756786441957, "stop": 1756786442207}], "start": 1756786415721, "stop": 1756786442208}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756786442208, "stop": 1756786442209}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d954db72-0086-4680-b249-c9172e322f24-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3d55dbb7-9b2a-481b-b945-bbcc814108b0-attachment.png", "type": "image/png"}], "start": 1756786442210, "stop": 1756786442462}], "attachments": [{"name": "stdout", "source": "522b7a00-c386-44f3-9411-84e2a02bd2fe-attachment.txt", "type": "text/plain"}], "start": 1756786415721, "stop": 1756786442462, "uuid": "7607d43a-70fd-45f9-a053-1ad6bc0aab01", "historyId": "1bf9bd9c91ab7da6f818ff587cfff7da", "testCaseId": "1bf9bd9c91ab7da6f818ff587cfff7da", "fullName": "testcases.test_ella.dialogue.test_play_political_news.TestEllaOpenPlayPoliticalNews#test_play_political_news", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_political_news"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_political_news"}]}