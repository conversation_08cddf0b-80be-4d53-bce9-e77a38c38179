{"name": "测试there are many yellow sunflowers on the ground", "status": "passed", "description": "测试there are many yellow sunflowers on the ground指令", "steps": [{"name": "执行命令: there are many yellow sunflowers on the ground", "status": "passed", "steps": [{"name": "执行命令: there are many yellow sunflowers on the ground", "status": "passed", "start": 1756803711496, "stop": 1756803734307}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1ea59615-a474-4946-a927-c884a46b8401-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f7d45ae0-25e6-4ef4-ae94-15971c876732-attachment.png", "type": "image/png"}], "start": 1756803734307, "stop": 1756803734567}], "start": 1756803711496, "stop": 1756803734567}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756803734567, "stop": 1756803734569}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "07e774c5-9332-44fe-bfe4-43e7b26158f3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3776e247-7c40-44db-8bfe-ac66fe77796a-attachment.png", "type": "image/png"}], "start": 1756803734569, "stop": 1756803734793}], "attachments": [{"name": "stdout", "source": "4f0e77c8-2241-4360-8b11-92ed889eb741-attachment.txt", "type": "text/plain"}], "start": 1756803711496, "stop": 1756803734793, "uuid": "6681031c-1755-4bb4-a174-3cb5b81da4dc", "historyId": "74da34fd3c558df1234d7c0e937641b3", "testCaseId": "74da34fd3c558df1234d7c0e937641b3", "fullName": "testcases.test_ella.unsupported_commands.test_there_are_many_yellow_sunflowers_on_the_ground.TestEllaOpenPlayPoliticalNews#test_there_are_many_yellow_sunflowers_on_the_ground", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_there_are_many_yellow_sunflowers_on_the_ground"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_there_are_many_yellow_sunflowers_on_the_ground"}]}