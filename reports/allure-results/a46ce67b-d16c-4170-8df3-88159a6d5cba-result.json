{"name": "测试enable brightness locking返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['enable brightness locking', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_enable_brightness_locking.TestEllaEnableBrightnessLocking object at 0x0000018BC6498490>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC3E54700>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_enable_brightness_locking(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['enable brightness locking', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_enable_brightness_locking.py:34: AssertionError"}, "description": "验证enable brightness locking指令返回预期的不支持响应", "steps": [{"name": "执行命令: enable brightness locking", "status": "passed", "steps": [{"name": "执行命令: enable brightness locking", "status": "passed", "start": 1756797036349, "stop": 1756797058886}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "30f62945-30f2-44de-a818-3b6f698830e2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b02eaa16-8cb8-4907-bfc4-b4f356f1aea0-attachment.png", "type": "image/png"}], "start": 1756797058886, "stop": 1756797059082}], "start": 1756797036349, "stop": 1756797059082}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['enable brightness locking', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_enable_brightness_locking.py\", line 34, in test_enable_brightness_locking\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756797059082, "stop": 1756797059084}], "attachments": [{"name": "stdout", "source": "14e5bd4d-54cb-4ba3-aaae-678cc682c075-attachment.txt", "type": "text/plain"}], "start": 1756797036349, "stop": 1756797059085, "uuid": "91d148d5-9880-4d7d-9f5e-bbc97d86fc74", "historyId": "42bb23fa5566b20ae050e85bbee099ef", "testCaseId": "42bb23fa5566b20ae050e85bbee099ef", "fullName": "testcases.test_ella.unsupported_commands.test_enable_brightness_locking.TestEllaEnableBrightnessLocking#test_enable_brightness_locking", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_brightness_locking"}, {"name": "subSuite", "value": "TestEllaEnableBrightnessLocking"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_brightness_locking"}]}