{"name": "测试min notifications volume能正常执行", "status": "passed", "description": "min notifications volume", "steps": [{"name": "执行命令: min notifications volume", "status": "passed", "steps": [{"name": "执行命令: min notifications volume", "status": "passed", "start": 1756790181763, "stop": 1756790202527}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9d0ac986-fa36-48ca-b189-c2a4b21a166b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "341a4727-662c-4664-b861-0c813062ad4c-attachment.png", "type": "image/png"}], "start": 1756790202527, "stop": 1756790202772}], "start": 1756790181763, "stop": 1756790202773}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790202773, "stop": 1756790202774}, {"name": "验证应用已打开", "status": "passed", "start": 1756790202774, "stop": 1756790202774}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "61c2337f-d2ee-45ba-8c25-19928d7ddbaf-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "828e24d3-d442-4475-977f-0354c10f15fa-attachment.png", "type": "image/png"}], "start": 1756790202774, "stop": 1756790202990}], "attachments": [{"name": "stdout", "source": "dc3ef82a-aaff-4d38-a846-62c6a6445d47-attachment.txt", "type": "text/plain"}], "start": 1756790181763, "stop": 1756790202991, "uuid": "c5f4ba4f-bf14-4acf-a876-5dc08ba8ecd0", "historyId": "511b4baaab6d7793eefd3f92b3a77d8b", "testCaseId": "511b4baaab6d7793eefd3f92b3a77d8b", "fullName": "testcases.test_ella.system_coupling.test_min_notifications_volume.TestEllaMinNotificationsVolume#test_min_notifications_volume", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_min_notifications_volume"}, {"name": "subSuite", "value": "TestEllaMinNotificationsVolume"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_min_notifications_volume"}]}