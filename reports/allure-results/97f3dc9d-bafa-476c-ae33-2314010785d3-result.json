{"name": "测试A cute little girl with long hair, wearing a scarf, a white cotton-padded jacket, and carrying a backpack on her back, has a kitten at her feet and colorful little butterflies fluttering around her", "status": "passed", "description": "测试A cute little girl with long hair, wearing a scarf, a white cotton-padded jacket, and carrying a backpack on her back, has a kitten at her feet and colorful little butterflies fluttering around her指令", "steps": [{"name": "执行命令: A cute little girl with long hair, wearing a scarf, a white cotton-padded jacket, and carrying a backpack on her back, has a kitten at her feet and colorful little butterflies fluttering around her", "status": "passed", "steps": [{"name": "执行命令: A cute little girl with long hair, wearing a scarf, a white cotton-padded jacket, and carrying a backpack on her back, has a kitten at her feet and colorful little butterflies fluttering around her", "status": "passed", "start": 1756795099484, "stop": 1756795125199}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "59e58866-de66-4485-ac8c-a0ddc41406a8-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "63c3a77b-857d-4547-b15b-8a123f51ecc3-attachment.png", "type": "image/png"}], "start": 1756795125199, "stop": 1756795125422}], "start": 1756795099484, "stop": 1756795125422}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756795125422, "stop": 1756795125423}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0fd71b2f-9a72-4caf-9a2f-3e41f7d6eda9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ef01893d-e13d-45cc-8bd0-86a71e0f50e1-attachment.png", "type": "image/png"}], "start": 1756795125423, "stop": 1756795125656}], "attachments": [{"name": "stdout", "source": "fd41fdd3-b69d-4e47-afc3-138b3b231b9c-attachment.txt", "type": "text/plain"}], "start": 1756795099484, "stop": 1756795125657, "uuid": "dcd353e2-f4f7-433c-a4e6-76cb99093c65", "historyId": "356db57bafcf61266d2f62fd1b8ab4e2", "testCaseId": "356db57bafcf61266d2f62fd1b8ab4e2", "fullName": "testcases.test_ella.unsupported_commands.test_a_cute_little_girl_with_long_hair.TestEllaOpenPlayPoliticalNews#test_a_cute_little_girl_with_long_hair", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_a_cute_little_girl_with_long_hair"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_a_cute_little_girl_with_long_hair"}]}