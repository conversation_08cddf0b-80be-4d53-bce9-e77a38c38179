{"name": "测试help me take a long screenshot能正常执行", "status": "passed", "description": "help me take a long screenshot", "steps": [{"name": "执行命令: help me take a long screenshot", "status": "passed", "steps": [{"name": "执行命令: help me take a long screenshot", "status": "passed", "start": 1756789560784, "stop": 1756789582177}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f8e0fb09-82f4-4e52-838b-4a02bf45619d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a123d5f6-4dce-4d73-b49c-162a384ae6b3-attachment.png", "type": "image/png"}], "start": 1756789582177, "stop": 1756789582400}], "start": 1756789560784, "stop": 1756789582400}, {"name": "验证文件存在", "status": "passed", "start": 1756789582400, "stop": 1756789582400}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "560ab8a8-d6ed-44f8-9175-768f4cafd8ec-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "72cf3f4b-6580-4d50-996f-5de6ee0f4b46-attachment.png", "type": "image/png"}], "start": 1756789582400, "stop": 1756789582597}], "attachments": [{"name": "stdout", "source": "53e1fc52-cf4c-4858-b5d8-ce93e3fcdc6c-attachment.txt", "type": "text/plain"}], "start": 1756789560784, "stop": 1756789582597, "uuid": "3353acee-db7f-4560-aaea-35d3f2b6537e", "historyId": "fe3d09fe0bad56e7804ef2f5ea49d283", "testCaseId": "fe3d09fe0bad56e7804ef2f5ea49d283", "fullName": "testcases.test_ella.system_coupling.test_help_me_take_a_long_screenshot.TestEllaHelpMeTakeLongScreenshot#test_help_me_take_a_long_screenshot", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_help_me_take_a_long_screenshot"}, {"name": "subSuite", "value": "TestEllaHelpMeTakeLongScreenshot"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_help_me_take_a_long_screenshot"}]}