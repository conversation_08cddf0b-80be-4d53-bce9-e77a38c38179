{"name": "测试turn up alarm clock volume", "status": "passed", "description": "测试turn up alarm clock volume指令", "steps": [{"name": "执行命令: turn up alarm clock volume", "status": "passed", "steps": [{"name": "执行命令: turn up alarm clock volume", "status": "passed", "start": 1756793152117, "stop": 1756793176236}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7c316316-4d43-4fba-8d47-cbb01b9231f8-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d3de2115-6d1a-4b3f-9143-a5a19bab3b1d-attachment.png", "type": "image/png"}], "start": 1756793176236, "stop": 1756793176441}], "start": 1756793152117, "stop": 1756793176441}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756793176441, "stop": 1756793176442}, {"name": "验证clock已打开", "status": "passed", "start": 1756793176442, "stop": 1756793176442}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a55349dc-f058-4ecd-9a0d-9dc77866b3b0-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f6c56291-3b6f-40c8-8d9f-969a59140635-attachment.png", "type": "image/png"}], "start": 1756793176442, "stop": 1756793176621}], "attachments": [{"name": "stdout", "source": "71e33614-17ec-4579-9df7-6864a83d55f7-attachment.txt", "type": "text/plain"}], "start": 1756793152117, "stop": 1756793176621, "uuid": "248a7a3d-6d42-4c0a-897b-dc11f660f5c9", "historyId": "0fa1017773031b1388876c73f0e0e653", "testCaseId": "0fa1017773031b1388876c73f0e0e653", "fullName": "testcases.test_ella.system_coupling.test_turn_up_alarm_clock_volume.TestEllaOpenClock#test_turn_up_alarm_clock_volume", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_up_alarm_clock_volume"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_up_alarm_clock_volume"}]}