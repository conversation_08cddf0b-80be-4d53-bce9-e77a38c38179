{"name": "测试Add the number on the screen to contacts", "status": "passed", "description": "测试Ask Screen功能: Add the number on the screen to contacts", "steps": [{"name": "准备测试数据", "status": "passed", "start": 1756793575004, "stop": 1756793583218}, {"name": "执行Ask Screen命令: Add the number on the screen to contacts", "status": "passed", "steps": [{"name": "执行浮窗命令: Add the number on the screen to contacts", "status": "passed", "steps": [{"name": "执行命令: Add the number on the screen to contacts", "status": "passed", "start": 1756793583218, "stop": 1756793591666}, {"name": "等待并获取AI响应", "status": "passed", "start": 1756793591666, "stop": 1756793608499}, {"name": "验证响应内容", "status": "passed", "attachments": [{"name": "关键词验证结果", "source": "d0148354-8e81-4b9f-ada9-a8223df6d9dc-attachment.txt", "type": "text/plain"}], "start": 1756793608499, "stop": 1756793608502}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "浮窗测试总结", "source": "8240e4f2-a90f-4dca-a44a-b16057dbd905-attachment.txt", "type": "text/plain"}, {"name": "AI响应内容", "source": "9c1e05bb-2d3e-49d5-b91c-e0661db28dc9-attachment.txt", "type": "text/plain"}], "start": 1756793608502, "stop": 1756793608504}], "start": 1756793583218, "stop": 1756793608504}, {"name": "截图记录测试完成状态", "status": "passed", "attachments": [{"name": "floating_test_completed", "source": "2c1cf229-e0cf-4cad-8e00-bc64ed236d09-attachment.png", "type": "image/png"}], "start": 1756793608504, "stop": 1756793608719}], "start": 1756793583218, "stop": 1756793609951}, {"name": "验证测试结果", "status": "passed", "start": 1756793609951, "stop": 1756793609952}], "attachments": [{"name": "stdout", "source": "64c14a8b-7ed7-4dc4-9340-a53821fc7c8a-attachment.txt", "type": "text/plain"}], "start": 1756793575004, "stop": 1756793609952, "uuid": "d336e696-ce3b-49d3-982b-a3f9e4991956", "historyId": "7327f3fdeeac58d0323250ec95196667", "testCaseId": "7327f3fdeeac58d0323250ec95196667", "fullName": "testcases.test_ella.test_ask_screen.contact.test_add_the_number_on_the_screen_to_contacts.TestAskScreenAddNumberScreenContacts#test_add_the_number_on_the_screen_to_contacts", "labels": [{"name": "epic", "value": "Ella浮窗测试"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ask Screen功能"}, {"name": "story", "value": "联系人相关"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.test_ask_screen.contact"}, {"name": "suite", "value": "test_add_the_number_on_the_screen_to_contacts"}, {"name": "subSuite", "value": "TestAskScreenAddNumberScreenContacts"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_ask_screen.contact.test_add_the_number_on_the_screen_to_contacts"}]}