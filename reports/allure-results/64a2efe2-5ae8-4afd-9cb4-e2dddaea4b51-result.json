{"name": "测试display the route go company", "status": "passed", "description": "测试display the route go company指令", "steps": [{"name": "执行命令: display the route go company", "status": "passed", "steps": [{"name": "执行命令: display the route go company", "status": "passed", "start": 1756783113791, "stop": 1756783140430}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "642ab083-63f2-4261-80fb-9c515ef882ed-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fa6364cb-886a-496c-bef9-a366087fa4fa-attachment.png", "type": "image/png"}], "start": 1756783140430, "stop": 1756783140637}], "start": 1756783113791, "stop": 1756783140638}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756783140638, "stop": 1756783140639}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7ef6b799-cd36-4cb7-aa01-7fd08ed9ceea-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c17a41f2-f86d-41a7-aeb5-3e4fbb4f911e-attachment.png", "type": "image/png"}], "start": 1756783140639, "stop": 1756783140847}], "attachments": [{"name": "stdout", "source": "4ecae01d-caef-47af-b7fc-aa22b0969fbf-attachment.txt", "type": "text/plain"}], "start": 1756783113791, "stop": 1756783140848, "uuid": "d7b7c71f-79d3-45ab-94b3-d632dc138f42", "historyId": "f4d12b1367b35df96178a58e48fe8f5e", "testCaseId": "f4d12b1367b35df96178a58e48fe8f5e", "fullName": "testcases.test_ella.component_coupling.test_display_the_route_go_company.TestEllaOpenMaps#test_display_the_route_go_company", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_display_the_route_go_company"}, {"name": "subSuite", "value": "TestEllaOpenMaps"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_display_the_route_go_company"}]}