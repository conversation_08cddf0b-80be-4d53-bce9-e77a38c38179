{"name": "测试delete the 8 o'clock alarm", "status": "passed", "description": "测试delete the 8 o'clock alarm指令", "steps": [{"name": "执行命令: set  8 o'clock alarm", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\component_coupling\\test_delete_the_8_o_clock_alarm.py\", line 25, in test_delete_the_8_o_clock_alarm\n    initial_status, final_status, response_text, files_status = self.simple_command_test(\n                                                                ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 1382, in simple_command_test\n    initial_status, final_status, response_text, files_status = self.execute_command_and_verify(\n                                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 383, in execute_command_and_verify\n    self._execute_command(ella_app, command, is_voice, voice_duration, voice_language)\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 894, in _execute_command\n    self._handle_popup_after_command(ella_app, command)\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 188, in _handle_popup_after_command\n    success = popup_tool.detect_and_close_popup_once(debug_mode=False)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\core\\popup_tool.py\", line 741, in detect_and_close_popup_once\n    if not self.is_popup_present_ultra_fast():\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\core\\popup_tool.py\", line 707, in is_popup_present_ultra_fast\n    if self.device(resourceId=res_id).exists(timeout=0.1):\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\uiautomator2\\utils.py\", line 100, in __call__\n    return self.uiobject.wait(timeout=timeout)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\uiautomator2\\_selector.py\", line 312, in wait\n    return self.jsonrpc.waitForExists(self.selector,\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\uiautomator2\\__init__.py\", line 185, in __call__\n    return self.server.jsonrpc_call(self.method, params, http_timeout)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\uiautomator2\\core.py\", line 279, in jsonrpc_call\n    return _jsonrpc_call(self._dev, method, params, timeout, self._debug)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\uiautomator2\\core.py\", line 141, in _jsonrpc_call\n    r = _http_request(dev, \"POST\", \"/jsonrpc/0\", payload, timeout=timeout, print_request=print_request)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\uiautomator2\\core.py\", line 113, in _http_request\n    r = requests.request(method, url, json=data, timeout=timeout, headers=headers)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\requests\\api.py\", line 59, in request\n    return session.request(method=method, url=url, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\requests\\sessions.py\", line 589, in request\n    resp = self.send(prep, **send_kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\requests\\sessions.py\", line 703, in send\n    r = adapter.send(request, **kwargs)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\requests\\adapters.py\", line 486, in send\n    resp = conn.urlopen(\n           ^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 787, in urlopen\n    response = self._make_request(\n               ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 534, in _make_request\n    response = conn.getresponse()\n               ^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\urllib3\\connection.py\", line 516, in getresponse\n    httplib_response = super().getresponse()\n                       ^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\http\\client.py\", line 1374, in getresponse\n    response.begin()\n  File \"D:\\SystemApplication\\Lib\\http\\client.py\", line 318, in begin\n    version, status, reason = self._read_status()\n                              ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\http\\client.py\", line 279, in _read_status\n    line = str(self.fp.readline(_MAXLINE + 1), \"iso-8859-1\")\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\socket.py\", line 705, in readinto\n    return self._sock.recv_into(b)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "执行命令: set  8 o'clock alarm", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 1382, in simple_command_test\n    initial_status, final_status, response_text, files_status = self.execute_command_and_verify(\n                                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 383, in execute_command_and_verify\n    self._execute_command(ella_app, command, is_voice, voice_duration, voice_language)\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 894, in _execute_command\n    self._handle_popup_after_command(ella_app, command)\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 188, in _handle_popup_after_command\n    success = popup_tool.detect_and_close_popup_once(debug_mode=False)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\core\\popup_tool.py\", line 741, in detect_and_close_popup_once\n    if not self.is_popup_present_ultra_fast():\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\core\\popup_tool.py\", line 707, in is_popup_present_ultra_fast\n    if self.device(resourceId=res_id).exists(timeout=0.1):\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\uiautomator2\\utils.py\", line 100, in __call__\n    return self.uiobject.wait(timeout=timeout)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\uiautomator2\\_selector.py\", line 312, in wait\n    return self.jsonrpc.waitForExists(self.selector,\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\uiautomator2\\__init__.py\", line 185, in __call__\n    return self.server.jsonrpc_call(self.method, params, http_timeout)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\uiautomator2\\core.py\", line 279, in jsonrpc_call\n    return _jsonrpc_call(self._dev, method, params, timeout, self._debug)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\uiautomator2\\core.py\", line 141, in _jsonrpc_call\n    r = _http_request(dev, \"POST\", \"/jsonrpc/0\", payload, timeout=timeout, print_request=print_request)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\uiautomator2\\core.py\", line 113, in _http_request\n    r = requests.request(method, url, json=data, timeout=timeout, headers=headers)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\requests\\api.py\", line 59, in request\n    return session.request(method=method, url=url, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\requests\\sessions.py\", line 589, in request\n    resp = self.send(prep, **send_kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\requests\\sessions.py\", line 703, in send\n    r = adapter.send(request, **kwargs)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\requests\\adapters.py\", line 486, in send\n    resp = conn.urlopen(\n           ^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 787, in urlopen\n    response = self._make_request(\n               ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 534, in _make_request\n    response = conn.getresponse()\n               ^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\urllib3\\connection.py\", line 516, in getresponse\n    httplib_response = super().getresponse()\n                       ^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\http\\client.py\", line 1374, in getresponse\n    response.begin()\n  File \"D:\\SystemApplication\\Lib\\http\\client.py\", line 318, in begin\n    version, status, reason = self._read_status()\n                              ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\http\\client.py\", line 279, in _read_status\n    line = str(self.fp.readline(_MAXLINE + 1), \"iso-8859-1\")\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\socket.py\", line 705, in readinto\n    return self._sock.recv_into(b)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1756799953165, "stop": 1756799958697}], "start": 1756799953165, "stop": 1756799958720}], "start": 1756799953165, "stop": 1756799958725, "uuid": "a3cc01a5-9ad8-4be1-a125-49ebf2d1c541", "testCaseId": "8c62567d8b8a27f77124afc90fa44336", "fullName": "testcases.test_ella.component_coupling.test_delete_the_8_o_clock_alarm.TestEllaOpenClock#test_delete_the_8_o_clock_alarm"}