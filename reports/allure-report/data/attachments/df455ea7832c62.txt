2025-09-02 14:13:32 | INFO | tools.file_pusher:batch_clean_directories:487 | 开始批量清理目录
2025-09-02 14:13:32 | INFO | tools.file_pusher:batch_clean_directories:512 | 正在清理: 相机拍照目录 (/sdcard/DCIM/Camera)
2025-09-02 14:13:32 | INFO | tools.file_pusher:clean_target_directory:464 | ✅ 清理目标目录成功: /sdcard/DCIM/Camera
2025-09-02 14:13:32 | INFO | tools.file_pusher:batch_clean_directories:526 | ✅ 相机拍照目录 清理成功
2025-09-02 14:13:32 | INFO | tools.file_pusher:batch_clean_directories:512 | 正在清理: 截图目录 (/sdcard/Pictures/Screenshot)
2025-09-02 14:13:33 | INFO | tools.file_pusher:clean_target_directory:464 | ✅ 清理目标目录成功: /sdcard/Pictures/Screenshot
2025-09-02 14:13:33 | INFO | tools.file_pusher:batch_clean_directories:526 | ✅ 截图目录 清理成功
2025-09-02 14:13:33 | INFO | tools.file_pusher:batch_clean_directories:512 | 正在清理: 录屏目录 (/sdcard/Movies/ScreenRecord)
2025-09-02 14:13:33 | INFO | tools.file_pusher:clean_target_directory:464 | ✅ 清理目标目录成功: /sdcard/Movies/ScreenRecord
2025-09-02 14:13:33 | INFO | tools.file_pusher:batch_clean_directories:526 | ✅ 录屏目录 清理成功
2025-09-02 14:13:34 | INFO | tools.file_pusher:batch_clean_directories:512 | 正在清理: 下载目录 (/sdcard/Download)
2025-09-02 14:13:34 | WARNING | tools.file_pusher:clean_target_directory:467 | 清理目标目录可能失败: rm: /sdcard/Download/AIMonkey: Is a directory

2025-09-02 14:13:34 | INFO | tools.file_pusher:batch_clean_directories:526 | ✅ 下载目录 清理成功
2025-09-02 14:13:34 | INFO | tools.file_pusher:batch_clean_directories:538 | 批量清理完成: 成功 4/4 个目录
2025-09-02 14:13:34 | INFO | core.base_page:__init__:38 | 初始化页面: ella - floating_page
2025-09-02 14:13:35 | INFO | testcases.test_ella.test_ask_screen.base_ask_screen_test:ella_floating_page:36 | ✅ Ella浮窗页面初始化成功
2025-09-02 14:13:35 | INFO | tools.gallery_cleaner:quick_clear_all:69 | ⚡ 快速清理AI Gallery...
2025-09-02 14:13:35 | INFO | tools.gallery_cleaner:quick_clear_all:81 | ⚡ 快速清理完成
2025-09-02 14:13:35 | INFO | tools.file_pusher:push_ask_screen_image:155 | 开始推送ask_screen图片: add_this_number 到 /sdcard/Download
2025-09-02 14:13:35 | INFO | tools.file_pusher:_find_ask_screen_image:222 | 直接匹配找到文件: D:\app_test\data\static\ask_screen\add_this_number.jpg
2025-09-02 14:13:35 | INFO | tools.file_pusher:push_ask_screen_image:169 | 找到图片文件: D:\app_test\data\static\ask_screen\add_this_number.jpg
2025-09-02 14:13:36 | INFO | tools.file_pusher:_push_single_file:420 | ✅ 文件推送成功: add_this_number.jpg
2025-09-02 14:13:36 | INFO | tools.file_pusher:push_ask_screen_image:178 | ✅ 成功推送图片: add_this_number.jpg 到 /sdcard/Download
2025-09-02 14:13:36 | INFO | tools.file_pusher:_refresh_media_library:641 | 刷新媒体库: /sdcard/Download
2025-09-02 14:13:36 | INFO | tools.file_pusher:_refresh_media_library:648 | ✅ 媒体扫描器扫描成功
2025-09-02 14:13:36 | WARNING | tools.file_pusher:_refresh_media_library:659 | 媒体库刷新失败: 
Exception occurred while executing 'broadcast':
java.lang.SecurityException: Permission Denial: not allowed to send broadcast android.intent.action.MEDIA_MOUNTED from pid=10734, uid=2000
	at com.android.server.am.BroadcastController.broadcastIntentLockedTraced(BroadcastController.java:1132)
	at com.android.server.am.BroadcastController.broadcastIntentLocked(BroadcastController.java:870)
	at com.android.server.am.BroadcastController.broadcastIntentWithFeature(BroadcastController.java:782)
	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:15398)
	at com.android.server.am.ActivityManagerShellCommand.runSendBroadcast(ActivityManagerShellCommand.java:1096)
	at com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:282)
	at com.android.modules.utils.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:97)
	at android.os.ShellCommand.exec(ShellCommand.java:38)
	at com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:11040)
	at android.os.Binder.shellCommand(Binder.java:1151)
	at android.os.Binder.onTransact(Binder.java:953)
	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:6120)
	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2932)
	at android.os.Binder.execTransactInternal(Binder.java:1426)
	at android.os.Binder.execTransact(Binder.java:1365)

2025-09-02 14:13:36 | INFO | tools.file_pusher:_refresh_media_library:666 | ✅ 媒体存储服务重启成功
2025-09-02 14:13:39 | INFO | core.base_page:__init__:38 | 初始化页面: ai_gallery - photos_page
2025-09-02 14:13:39 | INFO | pages.apps.ai_gallery.photos_page:start_app:200 | 启动AI Gallery应用
2025-09-02 14:13:40 | INFO | pages.apps.ai_gallery.photos_page:start_app:208 | 尝试启动Activity: com.gallery20.HomeActivity
2025-09-02 14:13:43 | INFO | pages.apps.ai_gallery.photos_page:start_app:213 | ✅ AI Gallery应用启动成功（指定Activity）
2025-09-02 14:13:43 | INFO | pages.apps.ai_gallery.photos_page:wait_for_page_load:258 | 等待AI Gallery Photos页面加载完成 (超时: 15秒)
2025-09-02 14:13:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [底部操作栏], 超时时间: 15秒
2025-09-02 14:13:43 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 14:13:43 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [底部操作栏]
2025-09-02 14:13:43 | INFO | pages.apps.ai_gallery.photos_page:wait_for_page_load:262 | ✅ 底部导航栏已出现，页面加载完成
2025-09-02 14:13:43 | INFO | pages.apps.ai_gallery.photos_page:click_photo:310 | 点击第1张照片
2025-09-02 14:13:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [照片封面], 超时时间: 5秒
2025-09-02 14:13:43 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 14:13:43 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [照片封面]
2025-09-02 14:13:44 | INFO | core.base_element:click:231 | 点击元素成功 [照片封面]
2025-09-02 14:13:44 | INFO | pages.apps.ai_gallery.photos_page:click_photo:315 | ✅ 点击照片封面成功
2025-09-02 14:13:44 | INFO | testcases.test_ella.test_ask_screen.base_ask_screen_test:execute_floating_command_and_verify:106 | 在浮窗中执行命令并验证: add this number
2025-09-02 14:13:44 | INFO | testcases.test_ella.test_ask_screen.base_ask_screen_test:_execute_floating_command_with_retry:163 | 尝试执行命令 (第1次): add this number
2025-09-02 14:13:44 | INFO | pages.apps.ella.floating_page:open_floating_window:1257 | 尝试打开Ella浮窗
2025-09-02 14:13:44 | INFO | pages.apps.ella.floating_page:open_floating_window:1261 | 方法1: 尝试通过长按power键唤起Ella浮窗
2025-09-02 14:13:44 | INFO | pages.apps.ella.floating_page:_long_press_power_key:1329 | 模拟长按power键 3.0秒
2025-09-02 14:13:44 | INFO | pages.apps.ella.floating_page:_long_press_power_key:1335 | ✅ 长按power键命令执行成功
2025-09-02 14:13:47 | INFO | pages.apps.ella.floating_page:open_floating_window:1267 | ✅ 通过长按power键成功打开浮窗
2025-09-02 14:13:47 | INFO | pages.apps.ella.floating_page:execute_text_command_in_floating:1677 | 🚀 开始在浮窗中执行文本命令: add this number
2025-09-02 14:13:47 | INFO | pages.apps.ella.floating_page:_prepare_text_input_environment:2036 | 📋 步骤2: 准备文本输入环境...
2025-09-02 14:13:47 | INFO | pages.apps.ella.floating_page:_prepare_text_input_environment:2042 | ✅ 文本输入模式已就绪
2025-09-02 14:13:47 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [浮窗输入框], 超时时间: 3秒
2025-09-02 14:13:47 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 14:13:47 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [浮窗输入框]
2025-09-02 14:13:47 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [浮窗输入框], 超时时间: 5秒
2025-09-02 14:13:47 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 14:13:47 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [浮窗输入框]
2025-09-02 14:13:48 | INFO | pages.apps.ella.floating_page:_prepare_text_input_environment:2048 | ✅ 浮窗输入框已就绪
2025-09-02 14:13:48 | INFO | pages.apps.ella.floating_page:_execute_command_input:2059 | 📋 步骤3: 执行命令输入...
2025-09-02 14:13:48 | INFO | pages.apps.ella.floating_page:_find_available_input_element:2081 | 🔍 查找可用的输入框...
2025-09-02 14:13:48 | INFO | pages.apps.ella.floating_page:_find_available_input_element:2091 | ✅ 找到主输入框
2025-09-02 14:13:48 | INFO | pages.apps.ella.floating_page:_clear_and_input_command:2102 | 🧹 清空输入框...
2025-09-02 14:13:48 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [浮窗输入框], 超时时间: 5秒
2025-09-02 14:13:48 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 14:13:48 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [浮窗输入框]
2025-09-02 14:13:48 | INFO | core.base_element:clear_text:325 | 清空文本成功 [浮窗输入框]
2025-09-02 14:13:49 | INFO | pages.apps.ella.floating_page:_clear_and_input_command:2108 | ⌨️ 输入命令: add this number
2025-09-02 14:13:49 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [浮窗输入框], 超时时间: 5秒
2025-09-02 14:13:49 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 14:13:49 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [浮窗输入框]
2025-09-02 14:13:49 | INFO | core.base_element:send_keys:302 | 输入文本成功 [浮窗输入框]: add this number
2025-09-02 14:13:49 | INFO | pages.apps.ella.floating_page:_clear_and_input_command:2113 | ✅ 命令输入成功
2025-09-02 14:13:50 | INFO | pages.apps.ella.floating_page:_execute_command_input:2072 | ✅ 命令输入完成
2025-09-02 14:13:50 | INFO | pages.apps.ella.floating_page:_send_command_with_fallback:2124 | 📋 步骤4: 发送命令...
2025-09-02 14:13:50 | INFO | pages.apps.ella.floating_page:_send_command_with_fallback:2125 | 💡 注意: 文本模式需要点击发送按钮，语音模式会自动发送
2025-09-02 14:13:50 | INFO | pages.apps.ella.floating_page:_try_standard_send_button:2157 | 🔍 第一阶段：尝试标准发送按钮...
2025-09-02 14:13:50 | INFO | pages.apps.ella.floating_page:_try_standard_send_button:2159 | 📤 找到发送按钮，点击发送...
2025-09-02 14:13:50 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-09-02 14:13:50 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 14:13:50 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-09-02 14:13:51 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-09-02 14:13:51 | INFO | pages.apps.ella.floating_page:_send_command_with_fallback:2129 | ✅ 第一阶段：标准发送按钮发送成功
2025-09-02 14:13:51 | INFO | pages.apps.ella.floating_page:_verify_command_execution:2246 | 📋 步骤5: 验证命令执行结果...
2025-09-02 14:13:52 | INFO | pages.apps.ella.floating_page:_find_available_input_element:2081 | 🔍 查找可用的输入框...
2025-09-02 14:13:52 | INFO | pages.apps.ella.floating_page:_find_available_input_element:2091 | ✅ 找到主输入框
2025-09-02 14:13:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [浮窗输入框], 超时时间: 5秒
2025-09-02 14:13:53 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 14:13:53 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [浮窗输入框]
2025-09-02 14:13:53 | INFO | pages.apps.ella.floating_page:_verify_command_execution:2251 | ✅ 第一阶段：命令发送成功
2025-09-02 14:13:53 | INFO | testcases.test_ella.test_ask_screen.base_ask_screen_test:_execute_floating_command_with_retry:166 | ✅ 命令执行成功 (第1次尝试)
2025-09-02 14:13:53 | INFO | testcases.test_ella.test_ask_screen.base_ask_screen_test:execute_floating_command_and_verify:119 | ✅ 命令执行成功: add this number
2025-09-02 14:13:53 | INFO | testcases.test_ella.test_ask_screen.base_ask_screen_test:_wait_and_get_floating_response:200 | 等待浮窗AI响应 (超时: 8秒)
2025-09-02 14:13:56 | INFO | pages.apps.ella.floating_page:wait_for_floating_response:2353 | 等待浮窗AI响应 (超时: 8秒)
2025-09-02 14:13:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [浮窗响应区域], 超时时间: 2秒
2025-09-02 14:13:58 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-09-02 14:13:58 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [浮窗响应区域]
2025-09-02 14:13:58 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [浮窗聊天列表], 超时时间: 2秒
2025-09-02 14:14:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-09-02 14:14:00 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [浮窗聊天列表]
2025-09-02 14:14:00 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [浮窗响应文本(备选)], 超时时间: 2秒
2025-09-02 14:14:02 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-09-02 14:14:02 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [浮窗响应文本(备选)]
2025-09-02 14:14:06 | WARNING | pages.apps.ella.floating_page:wait_for_floating_response:2391 | ❌ 浮窗响应等待超时
2025-09-02 14:14:06 | INFO | pages.apps.ella.floating_page:get_response_all_text:2475 | 获取Ella浮窗返回的所有文案
2025-09-02 14:14:06 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [联系人相关文本(备选)], 超时时间: 5秒
2025-09-02 14:14:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 14:14:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [联系人相关文本(备选)]
2025-09-02 14:14:08 | INFO | pages.apps.ella.floating_page:get_response_all_text:2488 | 获取到 3 个联系人相关文案
2025-09-02 14:14:09 | INFO | pages.apps.ella.floating_page:get_response_all_text:2522 | ✅ 获取到 3 个响应文案
2025-09-02 14:14:09 | INFO | testcases.test_ella.test_ask_screen.base_ask_screen_test:_wait_and_get_floating_response:216 | ✅ 获取到响应文本: 3 条
2025-09-02 14:14:09 | INFO | testcases.test_ella.test_ask_screen.base_ask_screen_test:execute_floating_command_and_verify:127 | ✅ 获取到响应文本: ['The following number is recognized. You can save it as a contact after confirmation.', 'add this number', 'Save'] 
2025-09-02 14:14:09 | INFO | testcases.test_ella.test_ask_screen.base_ask_screen_test:_verify_response_keywords:259 | ✅ 找到期望关键词: following number is recognized
2025-09-02 14:14:09 | INFO | testcases.test_ella.test_ask_screen.base_ask_screen_test:_verify_response_keywords:259 | ✅ 找到期望关键词: save it as a contact
2025-09-02 14:14:09 | INFO | testcases.test_ella.test_ask_screen.base_ask_screen_test:_verify_response_keywords:268 | ✅ 关键词验证通过，找到所有 2 个关键词
2025-09-02 14:14:09 | INFO | testcases.test_ella.test_ask_screen.base_ask_screen_test:_record_floating_test_result:328 | ✅ 测试结果已记录: add this number
2025-09-02 14:14:09 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\app_test\reports/screenshots\TestAskScreenAddNumber\floating_test_completed.png
2025-09-02 14:14:09 | INFO | testcases.test_ella.test_ask_screen.base_ask_screen_test:simple_floating_command_test:365 | 🎉 浮窗命令测试完成: add this number
2025-09-02 14:14:09 | INFO | pages.apps.ai_gallery.photos_page:stop_app:281 | 停止AI Gallery应用
2025-09-02 14:14:11 | INFO | pages.apps.ai_gallery.photos_page:stop_app:290 | ✅ AI Gallery应用停止命令已执行
2025-09-02 14:14:11 | INFO | testcases.test_ella.test_ask_screen.base_ask_screen_test:assert_floating_response_contains:406 | ✅ 响应包含期望关键词中的至少一个: ['following number is recognized', 'save it as a contact']
2025-09-02 14:14:11 | INFO | testcases.test_ella.test_ask_screen.contact.test_add_this_number:test_add_this_number:60 | ✅ Ask Screen测试完成: add this number
2025-09-02 14:14:11 | INFO | pages.apps.ella.floating_page:close_floating_window:1432 | 尝试关闭Ella浮窗
2025-09-02 14:14:13 | INFO | pages.apps.ella.floating_page:close_floating_window:1441 | ✅ 通过back键成功关闭浮窗
2025-09-02 14:14:13 | INFO | testcases.test_ella.test_ask_screen.base_ask_screen_test:ella_floating_page:47 | ✅ Ella浮窗已关闭
