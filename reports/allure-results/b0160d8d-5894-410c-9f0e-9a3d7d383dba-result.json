{"name": "测试Save the number on the screen to contact <PERSON>", "status": "passed", "description": "测试Ask Screen功能: Save the number on the screen to contact <PERSON>", "steps": [{"name": "准备测试数据", "status": "passed", "start": 1756793775979, "stop": 1756793784296}, {"name": "执行Ask Screen命令: Save the number on the screen to contact <PERSON>", "status": "passed", "steps": [{"name": "执行浮窗命令: Save the number on the screen to contact <PERSON>", "status": "passed", "steps": [{"name": "执行命令: Save the number on the screen to contact <PERSON>", "status": "passed", "start": 1756793784296, "stop": 1756793793367}, {"name": "等待并获取AI响应", "status": "passed", "start": 1756793793367, "stop": 1756793809793}, {"name": "验证响应内容", "status": "passed", "attachments": [{"name": "关键词验证结果", "source": "182a7c11-c350-492c-ab5c-bf5eee61f5cf-attachment.txt", "type": "text/plain"}], "start": 1756793809793, "stop": 1756793809795}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "浮窗测试总结", "source": "605a7686-2943-469b-abdf-0bb009078e51-attachment.txt", "type": "text/plain"}, {"name": "AI响应内容", "source": "578332d3-4ee1-43d0-b3cd-2606551eeb2e-attachment.txt", "type": "text/plain"}], "start": 1756793809795, "stop": 1756793809796}], "start": 1756793784296, "stop": 1756793809796}, {"name": "截图记录测试完成状态", "status": "passed", "attachments": [{"name": "floating_test_completed", "source": "ccf86ca8-b2c6-4924-a950-2b5f8d288ffc-attachment.png", "type": "image/png"}], "start": 1756793809796, "stop": 1756793810003}], "start": 1756793784296, "stop": 1756793810004}, {"name": "验证测试结果", "status": "passed", "start": 1756793810004, "stop": 1756793810004}], "attachments": [{"name": "stdout", "source": "26a52ed5-1f9c-496a-9dd3-c1d5156c6b77-attachment.txt", "type": "text/plain"}], "start": 1756793775978, "stop": 1756793810005, "uuid": "de08b41c-dbba-446e-8797-4c38b2953e84", "historyId": "0231bab2d0366f671b068e02756c1a71", "testCaseId": "0231bab2d0366f671b068e02756c1a71", "fullName": "testcases.test_ella.test_ask_screen.contact.test_save_the_number_on_the_screen_to_contact_lulu.TestAskScreenSaveNumberScreenContactLulu#test_save_the_number_on_the_screen_to_contact_lulu", "labels": [{"name": "epic", "value": "Ella浮窗测试"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ask Screen功能"}, {"name": "story", "value": "联系人相关"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.test_ask_screen.contact"}, {"name": "suite", "value": "test_save_the_number_on_the_screen_to_contact_lulu"}, {"name": "subSuite", "value": "TestAskScreenSaveNumberScreenContactLulu"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_ask_screen.contact.test_save_the_number_on_the_screen_to_contact_lulu"}]}