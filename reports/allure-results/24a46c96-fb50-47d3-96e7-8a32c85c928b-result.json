{"name": "continue  screen recording能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=False, 最终=False, 响应='['continue screen recording', 'Screen recording continued.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.system_coupling.test_start_screen_recording.TestEllaStartScreenRecording object at 0x0000018BC61C1870>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6B7A320>\n\n    @allure.title(f\"continue  screen recording能正常执行\")\n    @allure.description(f\"continue  screen recording\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_continue_screen_recording(self, ella_app):\n        command = \"continue screen recording\"\n        expected_text = ['Screen recording continued']\n        f\"\"\"{command}\"\"\"\n    \n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证已打开\"):\n>           assert final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=False, 最终=False, 响应='['continue screen recording', 'Screen recording continued.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_start_screen_recording.py:89: AssertionError"}, "description": "continue  screen recording", "steps": [{"name": "执行命令: continue screen recording", "status": "passed", "steps": [{"name": "执行命令: continue screen recording", "status": "passed", "start": 1756791242857, "stop": 1756791271227}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "89d0981b-bd57-49fb-ad09-7ba6b2170c71-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4f23a970-163e-4999-a1e6-839ee5118c39-attachment.png", "type": "image/png"}], "start": 1756791271228, "stop": 1756791271455}], "start": 1756791242857, "stop": 1756791271455}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756791271455, "stop": 1756791271456}, {"name": "验证已打开", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=False, 最终=False, 响应='['continue screen recording', 'Screen recording continued.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\system_coupling\\test_start_screen_recording.py\", line 89, in test_continue_screen_recording\n    assert final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1756791271456, "stop": 1756791271456}], "attachments": [{"name": "stdout", "source": "03db1fa6-5999-414d-ba82-fbdb07e228d2-attachment.txt", "type": "text/plain"}], "start": 1756791242857, "stop": 1756791271457, "uuid": "4469cff2-8733-4cd5-b6f0-c8eebb025a13", "historyId": "454f04318d433db60e7e6f2de5790fc3", "testCaseId": "454f04318d433db60e7e6f2de5790fc3", "fullName": "testcases.test_ella.system_coupling.test_start_screen_recording.TestEllaStartScreenRecording#test_continue_screen_recording", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_start_screen_recording"}, {"name": "subSuite", "value": "TestEllaStartScreenRecording"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_start_screen_recording"}]}