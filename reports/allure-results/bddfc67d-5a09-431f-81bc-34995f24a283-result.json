{"name": "测试play afro strut", "status": "passed", "description": "测试play afro strut指令", "steps": [{"name": "执行命令: play afro strut", "status": "passed", "steps": [{"name": "执行命令: play afro strut", "status": "passed", "start": 1756783749054, "stop": 1756783786007}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b0af980d-271f-42d2-b193-eacb6bb48cfe-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "62e392b9-4d39-4d15-98b2-99832b1891a9-attachment.png", "type": "image/png"}], "start": 1756783786007, "stop": 1756783786200}], "start": 1756783749054, "stop": 1756783786201}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756783786201, "stop": 1756783786203}, {"name": "验证play afro strut已打开", "status": "passed", "start": 1756783786203, "stop": 1756783786203}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1041a920-2861-41bd-9abe-5bec0dec6d35-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "207b8276-bd06-407b-b238-9192741b0f83-attachment.png", "type": "image/png"}], "start": 1756783786203, "stop": 1756783786399}], "attachments": [{"name": "stdout", "source": "b473677c-2a36-4fa4-afbe-7d5344796ada-attachment.txt", "type": "text/plain"}], "start": 1756783749054, "stop": 1756783786399, "uuid": "e4061b73-658b-412b-b94f-feaca7e6bcc8", "historyId": "ffb0a39af30beaa699329479ec564117", "testCaseId": "ffb0a39af30beaa699329479ec564117", "fullName": "testcases.test_ella.component_coupling.test_play_afro_strut.TestEllaOpenPlayAfroStrut#test_play_afro_strut", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_afro_strut"}, {"name": "subSuite", "value": "TestEllaOpenPlayAfroStrut"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_afro_strut"}]}