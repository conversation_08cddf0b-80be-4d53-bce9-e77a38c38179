{"name": "测试open whatsapp", "status": "passed", "description": "测试open whatsapp指令", "steps": [{"name": "执行命令: open whatsapp", "status": "passed", "steps": [{"name": "执行命令: open whatsapp", "status": "passed", "start": 1756794678039, "stop": 1756794705628}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0d64204c-5e80-4e64-9a27-38282274a7f9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2e5897fc-1486-4450-919c-9b1775395b4c-attachment.png", "type": "image/png"}], "start": 1756794705628, "stop": 1756794705847}], "start": 1756794678039, "stop": 1756794705847}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756794705847, "stop": 1756794705848}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6230d5b2-7f9a-4251-930f-a044a14eeca2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4e46d0ad-dc86-4b7a-a805-81d24d528491-attachment.png", "type": "image/png"}], "start": 1756794705848, "stop": 1756794706066}], "attachments": [{"name": "stdout", "source": "a63b1da9-9a90-4127-8bd3-95977d331536-attachment.txt", "type": "text/plain"}], "start": 1756794678039, "stop": 1756794706066, "uuid": "aae052a4-34a6-4c87-b871-8f6aa09f269f", "historyId": "fcabf101e08450542157f8740eeec9a7", "testCaseId": "fcabf101e08450542157f8740eeec9a7", "fullName": "testcases.test_ella.third_coupling.test_open_whatsapp.TestEllaOpenWhatsapp#test_open_whatsapp", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_open_whatsapp"}, {"name": "subSuite", "value": "TestEllaOpenWhatsapp"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_open_whatsapp"}]}