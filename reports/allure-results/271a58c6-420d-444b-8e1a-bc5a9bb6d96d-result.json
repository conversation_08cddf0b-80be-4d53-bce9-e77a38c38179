{"name": "测试make a call on whatsapp to a能正常执行", "status": "passed", "description": "make a call on whatsapp to a", "steps": [{"name": "执行命令: make a call on whatsapp to a", "status": "passed", "steps": [{"name": "执行命令: make a call on whatsapp to a", "status": "passed", "start": 1756799519358, "stop": 1756799543301}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "89c70dcb-553b-4f11-a603-7d0322f0190d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "27b0ad1f-da9b-4af8-9626-956954e3716c-attachment.png", "type": "image/png"}], "start": 1756799543301, "stop": 1756799543531}], "start": 1756799519358, "stop": 1756799543531}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756799543531, "stop": 1756799543532}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b3843376-f82d-4c99-b6fa-ee743d4b3699-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "efdf5fdb-558d-4965-a232-b5acf1d89a63-attachment.png", "type": "image/png"}], "start": 1756799543532, "stop": 1756799543758}], "attachments": [{"name": "stdout", "source": "ec5c8af1-7fd0-45d9-aee8-78634d481d73-attachment.txt", "type": "text/plain"}], "start": 1756799519358, "stop": 1756799543758, "uuid": "272634c0-8d1d-48f5-93d5-b05dd0c23aeb", "historyId": "221d94649ab3a384e6bc24767dceba21", "testCaseId": "221d94649ab3a384e6bc24767dceba21", "fullName": "testcases.test_ella.unsupported_commands.test_make_a_call_on_whatsapp_to_a.TestEllaMakeCallWhatsapp#test_make_a_call_on_whatsapp_to_a", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_make_a_call_on_whatsapp_to_a"}, {"name": "subSuite", "value": "TestEllaMakeCallWhatsapp"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_make_a_call_on_whatsapp_to_a"}]}