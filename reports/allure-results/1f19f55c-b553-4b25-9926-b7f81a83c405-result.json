{"name": "测试previous music能正常执行", "status": "passed", "description": "previous music", "steps": [{"name": "执行命令: previous music", "status": "passed", "steps": [{"name": "执行命令: previous music", "status": "passed", "start": 1756784030802, "stop": 1756784050462}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8cad59dd-2b94-476e-bf17-010696d2a15a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5497e977-d0c8-4e12-a22e-4798b9082500-attachment.png", "type": "image/png"}], "start": 1756784050462, "stop": 1756784050685}], "start": 1756784030802, "stop": 1756784050685}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756784050685, "stop": 1756784050687}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d361db6f-50da-4d8b-a3bc-8612ab45ee6d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4ab69135-bbb9-4169-8caa-fcd3aa4e53a9-attachment.png", "type": "image/png"}], "start": 1756784050687, "stop": 1756784050899}], "attachments": [{"name": "stdout", "source": "8e23baca-21a5-470a-92a1-c2d8d8b7eab5-attachment.txt", "type": "text/plain"}], "start": 1756784030802, "stop": 1756784050900, "uuid": "68a431e1-ee6d-43b7-9465-af5285a059a6", "historyId": "0eb27e9cfa9ed24b7ee5e6be6e495cb7", "testCaseId": "0eb27e9cfa9ed24b7ee5e6be6e495cb7", "fullName": "testcases.test_ella.component_coupling.test_previous_music.TestEllaPreviousMusic#test_previous_music", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_previous_music"}, {"name": "subSuite", "value": "TestEllaPreviousMusic"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_previous_music"}]}