{"name": "测试set the alarm at 9 o'clock on weekends", "status": "passed", "description": "测试set the alarm at 9 o'clock on weekends指令", "steps": [{"name": "执行命令:  delete all the alarms", "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "status": "passed", "start": 1756790995552, "stop": 1756791016423}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6e870eff-143e-4aa3-a7ce-78cbca74ab6b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1534dbff-1a64-47c8-8337-3e6b38aa6bb5-attachment.png", "type": "image/png"}], "start": 1756791016423, "stop": 1756791016648}], "start": 1756790995552, "stop": 1756791016649}, {"name": "执行命令: set the alarm at 9 o'clock on weekends", "status": "passed", "steps": [{"name": "执行命令: set the alarm at 9 o'clock on weekends", "status": "passed", "start": 1756791016649, "stop": 1756791036967}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6032b35d-d032-4b84-b6da-a01a219023d9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "74d1610b-b148-4bce-9837-479dbee396ae-attachment.png", "type": "image/png"}], "start": 1756791036967, "stop": 1756791037177}], "start": 1756791016649, "stop": 1756791037177}, {"name": "执行命令: get all the alarms", "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "status": "passed", "start": 1756791037177, "stop": 1756791057300}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0049370f-0725-4c63-81f9-a2f79a0a78bb-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f64524fa-170f-42df-a239-a3725127cfe6-attachment.png", "type": "image/png"}], "start": 1756791057300, "stop": 1756791057497}], "start": 1756791037177, "stop": 1756791057499}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756791057499, "stop": 1756791057501}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e4bc29c5-26a2-4966-a6ff-c678c5d36b30-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b751dd44-6204-4167-afce-57574f4740bb-attachment.png", "type": "image/png"}], "start": 1756791057501, "stop": 1756791057687}], "attachments": [{"name": "stdout", "source": "52011748-a73d-4972-a93f-ea66482cb768-attachment.txt", "type": "text/plain"}], "start": 1756790995552, "stop": 1756791057688, "uuid": "ddf7dc5b-c19d-456c-81b3-2bb354f2df97", "historyId": "cd2650c8b690a339f6c3696b18b9dc0d", "testCaseId": "cd2650c8b690a339f6c3696b18b9dc0d", "fullName": "testcases.test_ella.system_coupling.test_set_the_alarm_at_9_o_clock_on_weekends.TestEllaOpenClock#test_set_the_alarm_at_o_clock_on_weekends", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_the_alarm_at_9_o_clock_on_weekends"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_the_alarm_at_9_o_clock_on_weekends"}]}