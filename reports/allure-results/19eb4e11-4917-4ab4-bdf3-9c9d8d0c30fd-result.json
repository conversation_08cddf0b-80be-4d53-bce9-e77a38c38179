{"name": "测试disable network enhancement返回正确的不支持响应", "status": "passed", "description": "验证disable network enhancement指令返回预期的不支持响应", "steps": [{"name": "执行命令: disable network enhancement", "status": "passed", "steps": [{"name": "执行命令: disable network enhancement", "status": "passed", "start": 1756796534524, "stop": 1756796557880}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "58b020be-f498-4c01-89ec-b4fd15cf076b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "bac5ab08-bbcc-4cf8-8b92-bf2ec1ef448f-attachment.png", "type": "image/png"}], "start": 1756796557880, "stop": 1756796558086}], "start": 1756796534524, "stop": 1756796558086}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756796558086, "stop": 1756796558087}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "380edd96-4123-4ea1-9bf2-6efe55ed640d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2e9d54ba-313a-46e6-ad46-94f38760ed89-attachment.png", "type": "image/png"}], "start": 1756796558087, "stop": 1756796558309}], "attachments": [{"name": "stdout", "source": "db187cb8-3c9f-48b3-9e1b-5c43605563b2-attachment.txt", "type": "text/plain"}], "start": 1756796534524, "stop": 1756796558309, "uuid": "70c31c3f-f033-4663-8ce2-979e3a752465", "historyId": "3d685d9ca6a0d7795be3c96921595318", "testCaseId": "3d685d9ca6a0d7795be3c96921595318", "fullName": "testcases.test_ella.unsupported_commands.test_disable_network_enhancement.TestEllaDisableNetworkEnhancement#test_disable_network_enhancement", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_network_enhancement"}, {"name": "subSuite", "value": "TestEllaDisableNetworkEnhancement"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_network_enhancement"}]}