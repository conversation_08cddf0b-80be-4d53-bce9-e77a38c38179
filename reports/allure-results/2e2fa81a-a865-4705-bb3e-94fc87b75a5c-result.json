{"name": "测试play music by yandex music", "status": "passed", "description": "测试play music by yandex music指令", "steps": [{"name": "执行命令: play music by yandex music", "status": "passed", "steps": [{"name": "执行命令: play music by yandex music", "status": "passed", "start": 1756786246613, "stop": 1756786270101}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "02198988-27ea-4144-a8e2-cc4444abb352-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "393b62cd-7367-4378-85fc-bf9d27a93e76-attachment.png", "type": "image/png"}], "start": 1756786270101, "stop": 1756786270320}], "start": 1756786246613, "stop": 1756786270320}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756786270320, "stop": 1756786270322}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e15deee1-ce48-4000-b8f1-648dc893a0a1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "25d391b6-9600-40af-911a-f5d582978f9a-attachment.png", "type": "image/png"}], "start": 1756786270322, "stop": 1756786270509}], "attachments": [{"name": "stdout", "source": "95e56a85-6108-426f-a06e-1b8bb8180915-attachment.txt", "type": "text/plain"}], "start": 1756786246613, "stop": 1756786270510, "uuid": "5afeb976-c724-4493-96f5-f7ee7cabcc14", "historyId": "6c59bb8b6ba250c58da738ab8237ed3c", "testCaseId": "6c59bb8b6ba250c58da738ab8237ed3c", "fullName": "testcases.test_ella.dialogue.test_play_music_by_yandex_music.TestEllaOpenPlayPoliticalNews#test_play_music_by_yandex_music", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_music_by_yandex_music"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_music_by_yandex_music"}]}