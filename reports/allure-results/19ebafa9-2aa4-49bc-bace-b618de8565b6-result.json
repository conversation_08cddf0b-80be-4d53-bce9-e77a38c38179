{"name": "测试switch to equilibrium mode能正常执行", "status": "passed", "description": "switch to equilibrium mode", "steps": [{"name": "执行命令: switch to equilibrium mode", "status": "passed", "steps": [{"name": "执行命令: switch to equilibrium mode", "status": "passed", "start": 1756791612441, "stop": 1756791640616}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4364e30f-ab02-4a7c-a111-5e47c786bfdd-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "36011047-43ec-4d56-969a-bd1bff447fdb-attachment.png", "type": "image/png"}], "start": 1756791640616, "stop": 1756791640812}], "start": 1756791612441, "stop": 1756791640812}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1756791640812, "stop": 1756791640814}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a144efa3-af13-4ad0-8ca1-02a2495ff3af-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "af709ff2-b92e-4d65-85c0-8d2715711be0-attachment.png", "type": "image/png"}], "start": 1756791640814, "stop": 1756791641034}], "attachments": [{"name": "stdout", "source": "34e2b544-e90b-4c8f-8722-a68bd174f521-attachment.txt", "type": "text/plain"}], "start": 1756791612441, "stop": 1756791641034, "uuid": "4585f861-ea3b-4be1-8033-e368b3315819", "historyId": "45b57073b776ed5666f2f20a47a4638f", "testCaseId": "45b57073b776ed5666f2f20a47a4638f", "fullName": "testcases.test_ella.system_coupling.test_switch_to_equilibrium_mode.TestEllaSwitchToEquilibriumMode#test_switch_to_equilibrium_mode", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_equilibrium_mode"}, {"name": "subSuite", "value": "TestEllaSwitchToEquilibriumMode"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_equilibrium_mode"}]}