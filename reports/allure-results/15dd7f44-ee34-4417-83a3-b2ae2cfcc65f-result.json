{"name": "测试turn off light theme能正常执行", "status": "passed", "description": "turn off light theme", "steps": [{"name": "执行命令: turn off light theme", "status": "passed", "steps": [{"name": "执行命令: turn off light theme", "status": "passed", "start": 1756792336826, "stop": 1756792357332}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "86bb3fba-81d5-4950-a2bd-4ac407f65ce5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1c05893f-f5f6-4aec-a804-73fdccfc2b16-attachment.png", "type": "image/png"}], "start": 1756792357332, "stop": 1756792357527}], "start": 1756792336826, "stop": 1756792357527}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756792357527, "stop": 1756792357528}, {"name": "验证应用已打开", "status": "passed", "start": 1756792357528, "stop": 1756792357528}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "bd29d27a-38bf-4c52-a496-ee25d8788b52-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d5f50412-28fa-42ed-988a-1f20ba60013c-attachment.png", "type": "image/png"}], "start": 1756792357528, "stop": 1756792357748}], "attachments": [{"name": "stdout", "source": "5d3c58cb-fa9b-4c2c-b70e-d91979c95341-attachment.txt", "type": "text/plain"}], "start": 1756792336826, "stop": 1756792357750, "uuid": "046801e5-d784-474b-ac5c-fb6e3c5f88da", "historyId": "85b61394b4b07c85faf6e5081371fbf2", "testCaseId": "85b61394b4b07c85faf6e5081371fbf2", "fullName": "testcases.test_ella.system_coupling.test_turn_off_light_theme.TestEllaTurnOffLightTheme#test_turn_off_light_theme", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_off_light_theme"}, {"name": "subSuite", "value": "TestEllaTurnOffLightTheme"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_off_light_theme"}]}