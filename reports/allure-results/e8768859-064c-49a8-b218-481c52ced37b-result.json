{"name": "测试privacy policy", "status": "passed", "description": "测试privacy policy指令", "steps": [{"name": "执行命令: privacy policy", "status": "passed", "steps": [{"name": "执行命令: privacy policy", "status": "passed", "start": 1756800905574, "stop": 1756800936285}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "45beab94-8c3c-413e-ad99-291b2563b029-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1d4362ef-e35d-4868-ad70-274440025336-attachment.png", "type": "image/png"}], "start": 1756800936285, "stop": 1756800936537}], "start": 1756800905573, "stop": 1756800936537}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756800936537, "stop": 1756800936538}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "abf02779-e83b-4d5e-9cbb-d3504e95ad5f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "*************-4df1-95e8-93a6a7d09471-attachment.png", "type": "image/png"}], "start": 1756800936538, "stop": 1756800936763}], "attachments": [{"name": "stdout", "source": "cd0fdb1d-cc05-4dfb-92b4-9d1a25b179a3-attachment.txt", "type": "text/plain"}], "start": 1756800905573, "stop": 1756800936763, "uuid": "5ba3970d-60a7-468f-999f-932628206eed", "historyId": "ca269ac93364dc6180676f5680956b32", "testCaseId": "ca269ac93364dc6180676f5680956b32", "fullName": "testcases.test_ella.unsupported_commands.test_privacy_policy.TestEllaOpenPlayPoliticalNews#test_privacy_policy", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_privacy_policy"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_privacy_policy"}]}