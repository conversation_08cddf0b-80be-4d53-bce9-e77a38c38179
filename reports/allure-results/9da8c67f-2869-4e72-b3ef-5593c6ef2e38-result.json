{"name": "测试A furry little monkey", "status": "passed", "description": "测试A furry little monkey指令", "steps": [{"name": "执行命令: A furry little monkey", "status": "passed", "steps": [{"name": "执行命令: A furry little monkey", "status": "passed", "start": 1756795142476, "stop": 1756795168077}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3bbbd91b-7b1b-4e70-841a-0d64a0ae3e5d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "79807a55-27ac-4129-9af8-44aa05ecfb7f-attachment.png", "type": "image/png"}], "start": 1756795168077, "stop": 1756795168319}], "start": 1756795142476, "stop": 1756795168320}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756795168320, "stop": 1756795168322}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "54d48640-2ac9-4874-a5e7-2af47feb23c5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e54cf45f-c13c-4b00-8610-7c07d69b773b-attachment.png", "type": "image/png"}], "start": 1756795168322, "stop": 1756795168549}], "attachments": [{"name": "stdout", "source": "7c74e13c-9191-40b4-a6b6-9a79fce1ce2c-attachment.txt", "type": "text/plain"}], "start": 1756795142475, "stop": 1756795168550, "uuid": "fdacf4df-73ce-42d1-bf86-47652f0eb088", "historyId": "286e9cba8578d73b1f445f9d6d3a7d2e", "testCaseId": "286e9cba8578d73b1f445f9d6d3a7d2e", "fullName": "testcases.test_ella.unsupported_commands.test_a_furry_little_monkey.TestEllaOpenPlayPoliticalNews#test_a_furry_little_monkey", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_a_furry_little_monkey"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_a_furry_little_monkey"}]}