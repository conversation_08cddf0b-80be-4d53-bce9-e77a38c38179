{"name": "测试how to set screenshots返回正确的不支持响应", "status": "passed", "description": "验证how to set screenshots指令返回预期的不支持响应", "steps": [{"name": "执行命令: how to set screenshots", "status": "passed", "steps": [{"name": "执行命令: how to set screenshots", "status": "passed", "start": 1756798507802, "stop": 1756798538756}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "75a0e4fd-8d0a-4ab2-8cf8-ea69b07e4557-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "aff929a3-1faf-47e0-a023-121a3a2470cc-attachment.png", "type": "image/png"}], "start": 1756798538756, "stop": 1756798538963}], "start": 1756798507802, "stop": 1756798538964}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756798538964, "stop": 1756798538965}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "499e081d-9079-47e6-98e1-84860f4aa8b9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b08604cf-a884-4119-8b43-a64a98f611a0-attachment.png", "type": "image/png"}], "start": 1756798538965, "stop": 1756798539180}], "attachments": [{"name": "stdout", "source": "4968ec4e-996d-450c-9621-68eef7fe71e2-attachment.txt", "type": "text/plain"}], "start": 1756798507802, "stop": 1756798539180, "uuid": "143ae620-cba7-49de-b287-5b21015a623f", "historyId": "bfd4a9e37b70dca0b14b0ccf5246fc4a", "testCaseId": "bfd4a9e37b70dca0b14b0ccf5246fc4a", "fullName": "testcases.test_ella.unsupported_commands.test_how_to_set_screenshots.TestEllaHowSetScreenshots#test_how_to_set_screenshots", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_how_to_set_screenshots"}, {"name": "subSuite", "value": "TestEllaHowSetScreenshots"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_how_to_set_screenshots"}]}