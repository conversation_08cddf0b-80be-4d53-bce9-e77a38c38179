{"name": "测试turn off driving mode返回正确的不支持响应", "status": "passed", "description": "验证turn off driving mode指令返回预期的不支持响应", "steps": [{"name": "执行命令: turn off driving mode", "status": "passed", "steps": [{"name": "执行命令: turn off driving mode", "status": "passed", "start": 1756803874927, "stop": 1756803897056}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b949d5dc-2461-4a81-8add-324f8d563fe8-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "62e0ccbd-fa74-4e55-84b5-0e681abeca89-attachment.png", "type": "image/png"}], "start": 1756803897056, "stop": 1756803897261}], "start": 1756803874927, "stop": 1756803897261}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756803897261, "stop": 1756803897262}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6a6b8c27-3973-46bf-af91-649fb3d9d6f9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1d8493c9-b27d-4451-9064-8a2932ad4de0-attachment.png", "type": "image/png"}], "start": 1756803897262, "stop": 1756803897492}], "attachments": [{"name": "stdout", "source": "2dc088d3-f84b-42cf-b7b2-b62ac6530a7e-attachment.txt", "type": "text/plain"}], "start": 1756803874927, "stop": 1756803897492, "uuid": "ef9a20b8-3eed-4608-9f93-53426ac10ced", "historyId": "984a0fd313bba0ca2f20f0bbff732eb8", "testCaseId": "984a0fd313bba0ca2f20f0bbff732eb8", "fullName": "testcases.test_ella.unsupported_commands.test_turn_off_driving_mode.TestEllaTurnOffDrivingMode#test_turn_off_driving_mode", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_off_driving_mode"}, {"name": "subSuite", "value": "TestEllaTurnOffDrivingMode"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_off_driving_mode"}]}