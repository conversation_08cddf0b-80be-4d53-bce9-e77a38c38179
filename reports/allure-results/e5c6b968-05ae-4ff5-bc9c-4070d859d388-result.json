{"name": "测试turn up the brightness to the max能正常执行", "status": "passed", "description": "turn up the brightness to the max", "steps": [{"name": "执行命令: turn up the brightness to the max", "status": "passed", "steps": [{"name": "执行命令: turn up the brightness to the max", "status": "passed", "start": 1756793269897, "stop": 1756793292191}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "880e864a-4fb0-41a0-aee5-38c5acbe1ce4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "59b7a447-6824-4611-b561-b6d7c07332f0-attachment.png", "type": "image/png"}], "start": 1756793292191, "stop": 1756793292385}], "start": 1756793269897, "stop": 1756793292386}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756793292386, "stop": 1756793292387}, {"name": "验证应用已打开", "status": "passed", "start": 1756793292387, "stop": 1756793292387}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e78d5d02-f9f3-40ef-9a84-2c80c4bc2525-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7963dfaa-312c-40cd-9321-3ffaf008c5fe-attachment.png", "type": "image/png"}], "start": 1756793292387, "stop": 1756793292583}], "attachments": [{"name": "stdout", "source": "33848877-d6d4-4903-8da0-97e90767a893-attachment.txt", "type": "text/plain"}], "start": 1756793269897, "stop": 1756793292584, "uuid": "81c9a3a1-580b-4f5c-8803-584ea874e3df", "historyId": "1f9da5f1f2977bbbae31e0d3334eff82", "testCaseId": "1f9da5f1f2977bbbae31e0d3334eff82", "fullName": "testcases.test_ella.system_coupling.test_turn_up_the_brightness_to_the_max.TestEllaTurnUpBrightnessMax#test_turn_up_the_brightness_to_the_max", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_up_the_brightness_to_the_max"}, {"name": "subSuite", "value": "TestEllaTurnUpBrightnessMax"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_up_the_brightness_to_the_max"}]}