{"name": "测试switch to smart charge能正常执行", "status": "passed", "description": "switch to smart charge", "steps": [{"name": "执行命令: switch to smart charge", "status": "passed", "steps": [{"name": "执行命令: switch to smart charge", "status": "passed", "start": 1756791806885, "stop": 1756791827780}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6c1d09cb-ffdf-401c-8c1f-bbfff4c18304-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f9d6a060-329c-49bf-862f-3899d65036b8-attachment.png", "type": "image/png"}], "start": 1756791827780, "stop": 1756791828050}], "start": 1756791806885, "stop": 1756791828050}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1756791828050, "stop": 1756791828052}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "65f3f59a-5e08-424d-b37a-4faafbb044d0-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4a84a0d2-7843-4ff0-939d-3b5333912598-attachment.png", "type": "image/png"}], "start": 1756791828052, "stop": 1756791828252}], "attachments": [{"name": "stdout", "source": "c56b3020-8abb-4cb0-b72f-15cf14d68372-attachment.txt", "type": "text/plain"}], "start": 1756791806885, "stop": 1756791828252, "uuid": "1189036f-5ee1-4027-a767-2ace7da5eba0", "historyId": "643a7bbfbf5c5eedbae7ae814fbc8b52", "testCaseId": "643a7bbfbf5c5eedbae7ae814fbc8b52", "fullName": "testcases.test_ella.system_coupling.test_switch_to_smart_charge.TestEllaSwitchToSmartCharge#test_switch_to_smart_charge", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_smart_charge"}, {"name": "subSuite", "value": "TestEllaSwitchToSmartCharge"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_smart_charge"}]}