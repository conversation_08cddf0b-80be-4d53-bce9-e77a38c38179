{"name": "测试add this number", "status": "passed", "description": "测试Ask Screen功能: add this number", "steps": [{"name": "准备测试数据", "status": "passed", "start": 1756793615063, "stop": 1756793624153}, {"name": "执行Ask Screen命令: add this number", "status": "passed", "steps": [{"name": "执行浮窗命令: add this number", "status": "passed", "steps": [{"name": "执行命令: add this number", "status": "passed", "start": 1756793624153, "stop": 1756793633378}, {"name": "等待并获取AI响应", "status": "passed", "start": 1756793633378, "stop": 1756793649565}, {"name": "验证响应内容", "status": "passed", "attachments": [{"name": "关键词验证结果", "source": "6ee17ac0-b792-4613-b6ab-e7729ebf950c-attachment.txt", "type": "text/plain"}], "start": 1756793649566, "stop": 1756793649592}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "浮窗测试总结", "source": "1b3e5256-1cd8-4004-9008-bff6cb2405fe-attachment.txt", "type": "text/plain"}, {"name": "AI响应内容", "source": "76e667e8-c06a-425d-a5ec-0d2fcf3e37ef-attachment.txt", "type": "text/plain"}], "start": 1756793649592, "stop": 1756793649594}], "start": 1756793624153, "stop": 1756793649594}, {"name": "截图记录测试完成状态", "status": "passed", "attachments": [{"name": "floating_test_completed", "source": "4fff5e1a-2245-4b34-9a56-0577615a279e-attachment.png", "type": "image/png"}], "start": 1756793649594, "stop": 1756793649809}], "start": 1756793624153, "stop": 1756793651001}, {"name": "验证测试结果", "status": "passed", "start": 1756793651001, "stop": 1756793651002}, {"name": "记录测试完成", "status": "passed", "start": 1756793651002, "stop": 1756793651002}], "attachments": [{"name": "stdout", "source": "3703d891-da46-4bc9-bfd5-9248dc91c96a-attachment.txt", "type": "text/plain"}], "start": 1756793615063, "stop": 1756793651003, "uuid": "18ab2f44-79cd-44be-82e0-4b7f2135f136", "historyId": "71970769dc57394634693dcd9db391b9", "testCaseId": "71970769dc57394634693dcd9db391b9", "fullName": "testcases.test_ella.test_ask_screen.contact.test_add_this_number.TestAskScreenAddNumber#test_add_this_number", "labels": [{"name": "epic", "value": "Ella浮窗测试"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ask Screen功能"}, {"name": "story", "value": "联系人相关"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.test_ask_screen.contact"}, {"name": "suite", "value": "test_add_this_number"}, {"name": "subSuite", "value": "TestAskScreenAddNumber"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_ask_screen.contact.test_add_this_number"}]}