{"name": "测试Enable Call on Hold返回正确的不支持响应", "status": "passed", "description": "验证Enable Call on Hold指令返回预期的不支持响应", "steps": [{"name": "执行命令: Enable Call on Hold", "status": "passed", "steps": [{"name": "执行命令: Enable Call on Hold", "status": "passed", "start": 1756797076222, "stop": 1756797098402}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1d58794b-a797-412a-9088-a96d50ae4e82-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0023319e-9b27-4e3e-b902-211821b04151-attachment.png", "type": "image/png"}], "start": 1756797098403, "stop": 1756797098627}], "start": 1756797076222, "stop": 1756797098627}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756797098627, "stop": 1756797098628}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7a706fa5-c84b-46a8-b424-6d37549d7c12-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "18b0fbd0-d477-446a-8f9c-cfdf83df825c-attachment.png", "type": "image/png"}], "start": 1756797098629, "stop": 1756797098831}], "attachments": [{"name": "stdout", "source": "1bb4b79d-7252-4204-b7b2-e8251bf1abc2-attachment.txt", "type": "text/plain"}], "start": 1756797076221, "stop": 1756797098832, "uuid": "be28e37e-d518-4650-b074-f1bac94d0f4a", "historyId": "2bf170e8c0013ab361afb23f8f059db8", "testCaseId": "2bf170e8c0013ab361afb23f8f059db8", "fullName": "testcases.test_ella.unsupported_commands.test_enable_call_on_hold.TestEllaEnableCallHold#test_enable_call_on_hold", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_call_on_hold"}, {"name": "subSuite", "value": "TestEllaEnableCallHold"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_call_on_hold"}]}