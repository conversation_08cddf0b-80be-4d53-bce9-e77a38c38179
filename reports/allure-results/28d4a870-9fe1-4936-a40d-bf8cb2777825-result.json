{"name": "测试open wifi", "status": "passed", "description": "测试open wifi指令", "steps": [{"name": "执行命令: open wifi", "status": "passed", "steps": [{"name": "执行命令: open wifi", "status": "passed", "start": 1756790412045, "stop": 1756790433084}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "cdb2d830-bfc5-4c4f-a387-cc29914efdb3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "eaa8a59b-d599-40da-9992-991f8331fdaa-attachment.png", "type": "image/png"}], "start": 1756790433084, "stop": 1756790433278}], "start": 1756790412045, "stop": 1756790433279}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790433279, "stop": 1756790433280}, {"name": "验证wifi已打开", "status": "passed", "start": 1756790433280, "stop": 1756790433280}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8ae2aa87-c7b4-4428-8f9d-eaee272e42c5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f109dcc7-424a-4446-b082-7a93abfdfe81-attachment.png", "type": "image/png"}], "start": 1756790433280, "stop": 1756790433482}], "attachments": [{"name": "stdout", "source": "065521ba-9501-4b6f-b74a-6f5211713aa3-attachment.txt", "type": "text/plain"}], "start": 1756790412045, "stop": 1756790433483, "uuid": "5dbb9923-52b4-43c8-bf1f-fb2b99828d53", "historyId": "99709ca7d9951f6f7049b49ea81d0cd1", "testCaseId": "99709ca7d9951f6f7049b49ea81d0cd1", "fullName": "testcases.test_ella.system_coupling.test_open_wifi.TestEllaOpenWifi#test_open_wifi", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_open_wifi"}, {"name": "subSuite", "value": "TestEllaOpenWifi"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_open_wifi"}]}