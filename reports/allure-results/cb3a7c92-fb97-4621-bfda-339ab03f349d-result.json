{"name": "测试set screen refresh rate返回正确的不支持响应", "status": "passed", "description": "验证set screen refresh rate指令返回预期的不支持响应", "steps": [{"name": "执行命令: set screen refresh rate", "status": "passed", "steps": [{"name": "执行命令: set screen refresh rate", "status": "passed", "start": 1756802594793, "stop": 1756802627629}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7d08cb54-2bca-4789-887d-c7d761cab5c0-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "21712899-1158-49a9-8dd2-eeb0dbc066f1-attachment.png", "type": "image/png"}], "start": 1756802627629, "stop": 1756802627868}], "start": 1756802594793, "stop": 1756802627869}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756802627869, "stop": 1756802627877}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a45c5ca7-a591-457b-bf42-3c4c6b221c5d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "78817826-0e45-4e97-84f8-e6c75e505b0b-attachment.png", "type": "image/png"}], "start": 1756802627877, "stop": 1756802628099}], "attachments": [{"name": "stdout", "source": "c23ae649-2298-4d29-b7db-74028f2f00aa-attachment.txt", "type": "text/plain"}], "start": 1756802594793, "stop": 1756802628100, "uuid": "c61a755c-f5b8-41a1-88b0-6142ebcf4aac", "historyId": "1bc9389e45f0f75c30d3dfb39134948d", "testCaseId": "1bc9389e45f0f75c30d3dfb39134948d", "fullName": "testcases.test_ella.unsupported_commands.test_set_screen_refresh_rate.TestEllaSetScreenRefreshRate#test_set_screen_refresh_rate", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_screen_refresh_rate"}, {"name": "subSuite", "value": "TestEllaSetScreenRefreshRate"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_screen_refresh_rate"}]}