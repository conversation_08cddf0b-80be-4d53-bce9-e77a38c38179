{"name": "测试disable unfreeze返回正确的不支持响应", "status": "passed", "description": "验证disable unfreeze指令返回预期的不支持响应", "steps": [{"name": "执行命令: disable unfreeze", "status": "passed", "steps": [{"name": "执行命令: disable unfreeze", "status": "passed", "start": 1756796658140, "stop": 1756796683057}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e1308286-bb6a-4e37-8445-fe6e6257cb49-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a7e14c06-9f44-4089-9695-532b649e8868-attachment.png", "type": "image/png"}], "start": 1756796683057, "stop": 1756796683306}], "start": 1756796658140, "stop": 1756796683306}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756796683306, "stop": 1756796683307}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "60175add-b4ef-44fb-bd21-68a3cb195293-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5defa6a0-e4a2-48b7-8630-427106693e7d-attachment.png", "type": "image/png"}], "start": 1756796683307, "stop": 1756796683520}], "attachments": [{"name": "stdout", "source": "aedada72-f2af-4233-9ec2-e1fd60cf5936-attachment.txt", "type": "text/plain"}], "start": 1756796658140, "stop": 1756796683521, "uuid": "b998fd20-6bb9-47f1-9d0a-47cbaf7cb280", "historyId": "1695232002b2ad29ffa1faf52965470d", "testCaseId": "1695232002b2ad29ffa1faf52965470d", "fullName": "testcases.test_ella.unsupported_commands.test_disable_unfreeze.TestEllaDisableUnfreeze#test_disable_unfreeze", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_unfreeze"}, {"name": "subSuite", "value": "TestEllaDisableUnfreeze"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_unfreeze"}]}