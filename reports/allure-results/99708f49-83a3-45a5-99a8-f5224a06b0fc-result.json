{"name": "测试switch charging modes能正常执行", "status": "passed", "description": "switch charging modes", "steps": [{"name": "执行命令: switch charging modes", "status": "passed", "steps": [{"name": "执行命令: switch charging modes", "status": "passed", "start": 1756791419217, "stop": 1756791439570}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ee7ed70c-7c68-4721-8284-708081439f59-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c2fb02ee-7268-40ea-99e8-0cac7a230ddc-attachment.png", "type": "image/png"}], "start": 1756791439570, "stop": 1756791439793}], "start": 1756791419217, "stop": 1756791439793}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1756791439793, "stop": 1756791439794}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5a5bc3c5-d905-479d-aea2-4268ed78c8af-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e312dccc-54ce-4425-ac12-2ce62d93701a-attachment.png", "type": "image/png"}], "start": 1756791439794, "stop": 1756791439997}], "attachments": [{"name": "stdout", "source": "5b1f731f-a794-4362-9601-55b53ba1da4e-attachment.txt", "type": "text/plain"}], "start": 1756791419217, "stop": 1756791439998, "uuid": "ac622d3f-e2f4-407c-bc54-92850aa78ac3", "historyId": "a4af452e0448ec3c1ecc9afcc30459be", "testCaseId": "a4af452e0448ec3c1ecc9afcc30459be", "fullName": "testcases.test_ella.system_coupling.test_switch_charging_modes.TestEllaSwitchChargingModes#test_switch_charging_modes", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_charging_modes"}, {"name": "subSuite", "value": "TestEllaSwitchChargingModes"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_charging_modes"}]}