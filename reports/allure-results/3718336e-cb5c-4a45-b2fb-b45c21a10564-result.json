{"name": "测试close bluetooth能正常执行", "status": "passed", "description": "close bluetooth", "steps": [{"name": "执行命令: close bluetooth", "status": "passed", "steps": [{"name": "执行命令: close bluetooth", "status": "passed", "start": 1756789149505, "stop": 1756789172496}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "93ece73e-fae3-4ea6-812e-5030aeed07ca-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1d292cd6-b6a3-437f-81ef-217271d482b8-attachment.png", "type": "image/png"}], "start": 1756789172496, "stop": 1756789172711}], "start": 1756789149504, "stop": 1756789172712}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756789172712, "stop": 1756789172713}, {"name": "验证应用已打开", "status": "passed", "start": 1756789172713, "stop": 1756789172713}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d2fc6825-f7ce-4863-afbc-efd717348e65-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "de604631-940e-4742-8f4d-3dcc98b07cdb-attachment.png", "type": "image/png"}], "start": 1756789172713, "stop": 1756789172910}], "attachments": [{"name": "stdout", "source": "88bbba8e-e14d-49c5-bd16-181e66985ff6-attachment.txt", "type": "text/plain"}], "start": 1756789149504, "stop": 1756789172911, "uuid": "da4276d2-6a97-4f9d-a4d6-1276f327310c", "historyId": "b9bb05ac1dcf8926da63d4ecbb1524cd", "testCaseId": "b9bb05ac1dcf8926da63d4ecbb1524cd", "fullName": "testcases.test_ella.system_coupling.test_close_bluetooth.TestEllaCloseBluetooth#test_close_bluetooth", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_close_bluetooth"}, {"name": "subSuite", "value": "TestEllaCloseBluetooth"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_close_bluetooth"}]}