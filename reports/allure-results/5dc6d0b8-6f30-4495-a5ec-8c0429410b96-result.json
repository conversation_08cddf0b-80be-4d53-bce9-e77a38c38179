{"name": "测试play music on visha", "status": "passed", "description": "测试play music on visha指令", "steps": [{"name": "执行命令: play music on visha", "status": "passed", "steps": [{"name": "执行命令: play music on visha", "status": "passed", "start": 1756786325948, "stop": 1756786358408}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "64f737bd-2ddb-4fae-a4b6-d44f3a369758-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3ff75102-6f92-4d18-b2f4-8d5741f25042-attachment.png", "type": "image/png"}], "start": 1756786358408, "stop": 1756786358601}], "start": 1756786325948, "stop": 1756786358601}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756786358601, "stop": 1756786358602}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1f1dcb2f-660d-42cb-9113-6cb318f58a0e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a0dcf4fb-b0f5-4cec-9c5d-15aebb1e6138-attachment.png", "type": "image/png"}], "start": 1756786358602, "stop": 1756786358824}], "attachments": [{"name": "stdout", "source": "235ccd95-a39b-4688-8162-acb4ca0c7b38-attachment.txt", "type": "text/plain"}], "start": 1756786325948, "stop": 1756786358825, "uuid": "5ed4a2a8-9b98-477d-bbd6-53f514cc1341", "historyId": "228f8713a07d6ddbb8729f2f567c21d0", "testCaseId": "228f8713a07d6ddbb8729f2f567c21d0", "fullName": "testcases.test_ella.dialogue.test_play_music_on_visha.TestEllaOpenPlayPoliticalNews#test_play_music_on_visha", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_music_on_visha"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_music_on_visha"}]}