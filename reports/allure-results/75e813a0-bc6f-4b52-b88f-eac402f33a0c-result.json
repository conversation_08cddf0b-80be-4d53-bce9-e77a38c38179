{"name": "测试could you please search an for me能正常执行", "status": "passed", "description": "could you please search an for me", "steps": [{"name": "执行命令: could you please search an for me", "status": "passed", "steps": [{"name": "执行命令: could you please search an for me", "status": "passed", "start": 1756784973965, "stop": 1756784996916}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "560afcfd-ec6d-405a-af63-836c3c51c333-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b7311928-4251-4363-9b5d-978e80f17f28-attachment.png", "type": "image/png"}], "start": 1756784996916, "stop": 1756784997126}], "start": 1756784973965, "stop": 1756784997126}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756784997126, "stop": 1756784997127}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "54f70e30-350c-47ac-bcdb-************-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7db49682-b02a-4b57-98cb-266f9746a209-attachment.png", "type": "image/png"}], "start": 1756784997127, "stop": 1756784997341}], "attachments": [{"name": "stdout", "source": "40f3e86c-d87a-4be4-975d-3b85c2f53cf2-attachment.txt", "type": "text/plain"}], "start": 1756784973965, "stop": 1756784997341, "uuid": "d85910f7-297c-4c47-b5a1-41cb4d8370c2", "historyId": "19cc9ff22947964a912a8f57a87a3c68", "testCaseId": "19cc9ff22947964a912a8f57a87a3c68", "fullName": "testcases.test_ella.dialogue.test_could_you_please_search_an_for_me.TestEllaCouldYouPleaseSearchAnMe#test_could_you_please_search_an_for_me", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_could_you_please_search_an_for_me"}, {"name": "subSuite", "value": "TestEllaCouldYouPleaseSearchAnMe"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_could_you_please_search_an_for_me"}]}