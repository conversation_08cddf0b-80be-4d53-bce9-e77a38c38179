{"name": "测试phone boost能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore <PERSON>wi<PERSON> down to view earlier chats 11:28 am <PERSON>, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh How do campfires repel insects at night? Extract key points from document <PERSON> After Linsanity Fame DeepSeek-R1 Feel free to ask me any questions…\", '[com.transsion.phonemaster页面] 未获取到文本内容']'\nassert False", "trace": "self = <testcases.test_ella.component_coupling.test_phone_boost.TestEllaPhoneBoost object at 0x0000018BC4E03670>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6977160>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_phone_boost(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats 11:28 am Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh How do campfires repel insects at night? Extract key points from document Jeremy Lin Retires After Linsanity Fame DeepSeek-R1 Feel free to ask me any questions…\", '[com.transsion.phonemaster页面] 未获取到文本内容']'\nE           assert False\n\ntestcases\\test_ella\\component_coupling\\test_phone_boost.py:33: AssertionError"}, "description": "phone boost", "steps": [{"name": "执行命令: phone boost", "status": "passed", "steps": [{"name": "执行命令: phone boost", "status": "passed", "start": 1756783705036, "stop": 1756783732651}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f98e9c96-5d0c-4386-b613-ddd0253294c4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "45c0ae20-02ae-4dce-9f1b-1f2e44506a21-attachment.png", "type": "image/png"}], "start": 1756783732651, "stop": 1756783732829}], "start": 1756783705036, "stop": 1756783732831}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore <PERSON>wi<PERSON> down to view earlier chats 11:28 am <PERSON>, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh How do campfires repel insects at night? Extract key points from document <PERSON> After Linsanity Fame DeepSeek-R1 Feel free to ask me any questions…\", '[com.transsion.phonemaster页面] 未获取到文本内容']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\component_coupling\\test_phone_boost.py\", line 33, in test_phone_boost\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756783732831, "stop": 1756783732833}], "attachments": [{"name": "stdout", "source": "07816743-a1fe-47e9-9400-9ad38698ec9d-attachment.txt", "type": "text/plain"}], "start": 1756783705036, "stop": 1756783732834, "uuid": "b316a7ac-96af-4f34-8cff-9286886e4417", "historyId": "762b1ab748e39965c1484eb7fe38bfe4", "testCaseId": "762b1ab748e39965c1484eb7fe38bfe4", "fullName": "testcases.test_ella.component_coupling.test_phone_boost.TestEllaPhoneBoost#test_phone_boost", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_phone_boost"}, {"name": "subSuite", "value": "TestEllaPhoneBoost"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_phone_boost"}]}