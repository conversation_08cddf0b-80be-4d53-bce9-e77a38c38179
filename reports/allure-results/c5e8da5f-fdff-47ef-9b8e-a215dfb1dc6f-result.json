{"name": "测试Voice setting page返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['sorry']，实际响应: '['Voice setting page', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Accessibility | Downloaded apps | Ella | Hi Translate | ScrollCaptureAccessibilityService | Select to Speak | Hear selected text | Smart Translate | TalkBack | Speak items on screen | Display | Text and Display | Extra dim | Dim screen beyond your phone’s minimum brightness | Magnification | Zoom in on the screen | Interaction controls | Accessibility Menu']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_voice_setting_page.TestEllaVoiceSettingPage object at 0x0000018BC66B79D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC9D7FF10>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_voice_setting_page(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['sorry']，实际响应: '['Voice setting page', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Accessibility | Downloaded apps | Ella | Hi Translate | ScrollCaptureAccessibilityService | Select to Speak | Hear selected text | Smart Translate | TalkBack | Speak items on screen | Display | Text and Display | Extra dim | Dim screen beyond your phone’s minimum brightness | Magnification | Zoom in on the screen | Interaction controls | Accessibility Menu']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_voice_setting_page.py:34: AssertionError"}, "description": "验证Voice setting page指令返回预期的不支持响应", "steps": [{"name": "执行命令: Voice setting page", "status": "passed", "steps": [{"name": "执行命令: Voice setting page", "status": "passed", "start": 1756804161649, "stop": 1756804195919}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1d17d711-a885-47ca-a2cd-76be5703d458-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3ee0a826-e52d-43d4-8c41-355ead49445a-attachment.png", "type": "image/png"}], "start": 1756804195920, "stop": 1756804196224}], "start": 1756804161649, "stop": 1756804196225}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['sorry']，实际响应: '['Voice setting page', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Accessibility | Downloaded apps | Ella | Hi Translate | ScrollCaptureAccessibilityService | Select to Speak | Hear selected text | Smart Translate | TalkBack | Speak items on screen | Display | Text and Display | Extra dim | Dim screen beyond your phone’s minimum brightness | Magnification | Zoom in on the screen | Interaction controls | Accessibility Menu']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_voice_setting_page.py\", line 34, in test_voice_setting_page\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756804196225, "stop": 1756804196228}], "attachments": [{"name": "stdout", "source": "d8850238-c1ac-4107-bd50-0f124381a0a5-attachment.txt", "type": "text/plain"}], "start": 1756804161648, "stop": 1756804196229, "uuid": "c48e3107-cb9a-45fa-af13-ce6ef344f5f7", "historyId": "9c301cfc137fb94f119957b5f74291ec", "testCaseId": "9c301cfc137fb94f119957b5f74291ec", "fullName": "testcases.test_ella.unsupported_commands.test_voice_setting_page.TestEllaVoiceSettingPage#test_voice_setting_page", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_voice_setting_page"}, {"name": "subSuite", "value": "TestEllaVoiceSettingPage"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_voice_setting_page"}]}