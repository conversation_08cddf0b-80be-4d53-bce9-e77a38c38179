{"name": "测试last channel能正常执行", "status": "passed", "description": "last channel", "steps": [{"name": "执行命令: last channel", "status": "passed", "steps": [{"name": "执行命令: last channel", "status": "passed", "start": 1756785770913, "stop": 1756785794091}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d674d934-1478-4cc4-bf11-66bd4e6e5a04-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "29793fb1-77f4-4e9c-b94c-07560f4a79f2-attachment.png", "type": "image/png"}], "start": 1756785794091, "stop": 1756785794308}], "start": 1756785770913, "stop": 1756785794309}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756785794309, "stop": 1756785794310}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "32d19545-d652-449f-bafb-5cba6754da3f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ee2016a6-97f8-402a-aa3b-25f25f96a4c0-attachment.png", "type": "image/png"}], "start": 1756785794310, "stop": 1756785794521}], "attachments": [{"name": "stdout", "source": "fc06bc5a-594b-4355-a3a7-5e4e5d98f356-attachment.txt", "type": "text/plain"}], "start": 1756785770913, "stop": 1756785794521, "uuid": "96e0269b-f1e6-481b-b7e7-adf1869055e8", "historyId": "a0ea006ce61aacded2720f8d2a03ba5b", "testCaseId": "a0ea006ce61aacded2720f8d2a03ba5b", "fullName": "testcases.test_ella.dialogue.test_last_channel.TestEllaHowIsWeatherToday#test_last_channel", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_last_channel"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_last_channel"}]}