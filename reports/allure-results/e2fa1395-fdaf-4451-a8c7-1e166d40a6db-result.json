{"name": "测试A sports car is parked on the street side", "status": "passed", "description": "测试A sports car is parked on the street side 指令", "steps": [{"name": "执行命令: A sports car is parked on the street side", "status": "passed", "steps": [{"name": "执行命令: A sports car is parked on the street side", "status": "passed", "start": 1756795320815, "stop": 1756795352606}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "cebdcd16-8545-4661-ad41-cf62fa3ba25f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "24d43bfc-43b1-4530-bf1c-164b0bf896ad-attachment.png", "type": "image/png"}], "start": 1756795352606, "stop": 1756795352862}], "start": 1756795320815, "stop": 1756795352863}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756795352863, "stop": 1756795352864}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "81657807-b3c0-43f1-8460-3e3c29745866-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9093726b-42fa-46ee-9b4e-e40355d99782-attachment.png", "type": "image/png"}], "start": 1756795352864, "stop": 1756795353100}], "attachments": [{"name": "stdout", "source": "3387cc06-5b50-465a-abc0-70dfc8ad5fb2-attachment.txt", "type": "text/plain"}], "start": 1756795320815, "stop": 1756795353100, "uuid": "ec4fee38-a26e-4638-ae77-3274439969fe", "historyId": "b6eee20d1fad16a048ce8490d7189be4", "testCaseId": "b6eee20d1fad16a048ce8490d7189be4", "fullName": "testcases.test_ella.unsupported_commands.test_a_sports_car_is_parked_on_the_street_side.TestEllaOpenPlayPoliticalNews#test_a_sports_car_is_parked_on_the_street_side", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_a_sports_car_is_parked_on_the_street_side"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_a_sports_car_is_parked_on_the_street_side"}]}