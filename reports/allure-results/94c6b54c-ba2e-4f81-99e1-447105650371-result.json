{"name": "测试remove the people from the image", "status": "passed", "description": "测试remove the people from the image指令", "steps": [{"name": "执行命令: remove the people from the image", "status": "passed", "steps": [{"name": "执行命令: remove the people from the image", "status": "passed", "start": 1756801082763, "stop": 1756801105739}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3ab6f4a1-b3ab-4fd3-9f2f-79f231f9bfa2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b7695ac6-f741-4b7e-9d1e-ad3e0ecf8434-attachment.png", "type": "image/png"}], "start": 1756801105739, "stop": 1756801105993}], "start": 1756801082763, "stop": 1756801105993}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756801105993, "stop": 1756801105995}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "821f2fc7-1ec3-4501-9978-3c60b59946f6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fd7d7da6-9589-44fb-a664-dc565432e742-attachment.png", "type": "image/png"}], "start": 1756801105995, "stop": 1756801106255}], "attachments": [{"name": "stdout", "source": "1b3d3fba-28a4-4395-bfbb-7a63539ebfaf-attachment.txt", "type": "text/plain"}], "start": 1756801082763, "stop": 1756801106256, "uuid": "3c1c732d-626b-43c3-ba5b-c1f315a63aba", "historyId": "c121335a9bce64dd570aaf5b221b21df", "testCaseId": "c121335a9bce64dd570aaf5b221b21df", "fullName": "testcases.test_ella.unsupported_commands.test_remove_the_people_from_the_image.TestEllaOpenPlayPoliticalNews#test_remove_the_people_from_the_image", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_remove_the_people_from_the_image"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_remove_the_people_from_the_image"}]}