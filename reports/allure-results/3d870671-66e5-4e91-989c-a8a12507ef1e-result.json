{"name": "测试play music by boomplay", "status": "passed", "description": "测试play music by boomplay指令", "steps": [{"name": "执行命令: play music by boomplay", "status": "passed", "steps": [{"name": "执行命令: play music by boomplay", "status": "passed", "start": 1756786157379, "stop": 1756786181221}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ea3cad3f-cbc5-4e7c-ade5-991257f24c7b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7e81a778-ff8c-46eb-bc90-c6c2710ba6d1-attachment.png", "type": "image/png"}], "start": 1756786181221, "stop": 1756786181431}], "start": 1756786157379, "stop": 1756786181431}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756786181431, "stop": 1756786181432}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f93edb05-c407-4779-95e3-5f47aa56634e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "45e911fa-a3c8-4ad0-a754-1dd3cf2d0186-attachment.png", "type": "image/png"}], "start": 1756786181432, "stop": 1756786181633}], "attachments": [{"name": "stdout", "source": "1bf5f7c1-af36-407c-af97-1b5c95f1cb56-attachment.txt", "type": "text/plain"}], "start": 1756786157378, "stop": 1756786181634, "uuid": "117f8f7c-216f-4964-b9d2-43462c8232fb", "historyId": "d29b58e8e7ac85336b7dc255831fd5ba", "testCaseId": "d29b58e8e7ac85336b7dc255831fd5ba", "fullName": "testcases.test_ella.dialogue.test_play_music_by_boomplay.TestEllaOpenPlayPoliticalNews#test_play_music_by_boomplay", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_music_by_boomplay"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_music_by_boomplay"}]}