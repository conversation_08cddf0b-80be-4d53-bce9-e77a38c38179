{"name": "测试A photo of a transparent glass cup ", "status": "passed", "description": "测试A photo of a transparent glass cup 指令", "steps": [{"name": "执行命令: A photo of a transparent glass cup ", "status": "passed", "steps": [{"name": "执行命令: A photo of a transparent glass cup ", "status": "passed", "start": 1756795272680, "stop": 1756795303759}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "098eee19-b439-4230-8e59-2336fa2f3e00-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e1ebeb33-1bae-4367-83c5-1763b76a3c07-attachment.png", "type": "image/png"}], "start": 1756795303759, "stop": 1756795304002}], "start": 1756795272680, "stop": 1756795304003}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756795304003, "stop": 1756795304004}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b313761a-d7b1-4a8e-8514-0186db0e5d3c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "44630acc-8e5d-4aed-9029-427d446f5b58-attachment.png", "type": "image/png"}], "start": 1756795304004, "stop": 1756795304250}], "attachments": [{"name": "stdout", "source": "33f2342e-5536-4113-858f-eb744d6c24af-attachment.txt", "type": "text/plain"}], "start": 1756795272680, "stop": 1756795304250, "uuid": "b6607379-3303-4127-8dfa-0f5dd1fc3f68", "historyId": "d4409d015660212a7021f4aa7f849f30", "testCaseId": "d4409d015660212a7021f4aa7f849f30", "fullName": "testcases.test_ella.unsupported_commands.test_a_photo_of_a_transparent_glass_cup.TestEllaOpenPlayPoliticalNews#test_a_photo_of_a_transparent_glass_cup", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_a_photo_of_a_transparent_glass_cup"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_a_photo_of_a_transparent_glass_cup"}]}