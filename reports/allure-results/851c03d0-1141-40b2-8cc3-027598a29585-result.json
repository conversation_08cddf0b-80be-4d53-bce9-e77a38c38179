{"name": "测试what's the wheather today?能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['°C', 'Generated by AI, for reference only']，实际响应: '[\"what's the wheather today?\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.dialogue.test_what_s_the_wheather_today.TestEllaWhatSWheatherToday object at 0x0000018BC606F400>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC4D3F700>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_what_s_the_wheather_today(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response_advanced(expected_text, response_text,match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['°C', 'Generated by AI, for reference only']，实际响应: '[\"what's the wheather today?\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_what_s_the_wheather_today.py:33: AssertionError"}, "description": "what's the wheather today?", "steps": [{"name": "执行命令: what's the wheather today?", "status": "passed", "steps": [{"name": "执行命令: what's the wheather today?", "status": "passed", "start": 1756787636254, "stop": 1756787658825}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fd272eae-711d-436d-8653-7afb53427e26-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a0550a23-0654-4157-9d37-a2550c157644-attachment.png", "type": "image/png"}], "start": 1756787658825, "stop": 1756787659041}], "start": 1756787636254, "stop": 1756787659041}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['°C', 'Generated by AI, for reference only']，实际响应: '[\"what's the wheather today?\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\dialogue\\test_what_s_the_wheather_today.py\", line 33, in test_what_s_the_wheather_today\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756787659041, "stop": 1756787659043}], "attachments": [{"name": "stdout", "source": "62fb7396-9f8b-42e8-9ab5-fa0a39e0cc3a-attachment.txt", "type": "text/plain"}], "start": 1756787636254, "stop": 1756787659044, "uuid": "b53cc218-b813-45e4-a71c-3dc45e06823a", "historyId": "c2a64f07232d43585d1dfee25c2f9407", "testCaseId": "c2a64f07232d43585d1dfee25c2f9407", "fullName": "testcases.test_ella.dialogue.test_what_s_the_wheather_today.TestEllaWhatSWheatherToday#test_what_s_the_wheather_today", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_s_the_wheather_today"}, {"name": "subSuite", "value": "TestEllaWhatSWheatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_s_the_wheather_today"}]}