{"name": "测试new year wishes", "status": "passed", "description": "测试new year wishes指令", "steps": [{"name": "执行命令: new year wishes", "status": "passed", "steps": [{"name": "执行命令: new year wishes", "status": "passed", "start": 1756799830870, "stop": 1756799855474}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "29df1a17-390c-4be6-bcba-014fda7a56e9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "41e41d9c-fe40-497a-8261-a98857026e14-attachment.png", "type": "image/png"}], "start": 1756799855474, "stop": 1756799855733}], "start": 1756799830870, "stop": 1756799855734}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756799855734, "stop": 1756799855735}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "bae85803-ef13-444a-813a-75646c15bebd-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "50991ed0-82e4-4c27-8ca3-609e0a7784bc-attachment.png", "type": "image/png"}], "start": 1756799855735, "stop": 1756799855938}], "attachments": [{"name": "stdout", "source": "87cb2d08-05ea-484f-9fb1-f95db5896b02-attachment.txt", "type": "text/plain"}], "start": 1756799830870, "stop": 1756799855938, "uuid": "1a05c688-7cb1-4639-9065-eef63111a019", "historyId": "94acf463e3e6d87a1e9cf7ff754044a2", "testCaseId": "94acf463e3e6d87a1e9cf7ff754044a2", "fullName": "testcases.test_ella.unsupported_commands.test_new_year_wishes.TestEllaOpenPlayPoliticalNews#test_new_year_wishes", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_new_year_wishes"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_new_year_wishes"}]}