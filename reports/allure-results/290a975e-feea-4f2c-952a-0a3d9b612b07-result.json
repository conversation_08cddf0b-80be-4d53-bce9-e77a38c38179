{"name": "测试enable running lock返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['enable running lock', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_enable_running_lock.TestEllaEnableRunningLock object at 0x0000018BC6499840>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6975A80>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_enable_running_lock(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['enable running lock', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_enable_running_lock.py:34: AssertionError"}, "description": "验证enable running lock指令返回预期的不支持响应", "steps": [{"name": "执行命令: enable running lock", "status": "passed", "steps": [{"name": "执行命令: enable running lock", "status": "passed", "start": 1756797196691, "stop": 1756797221408}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "94ef4203-60de-4843-922f-dccbf62fcd32-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "94a648ad-e7d7-452f-b9ad-6f792a9e36cf-attachment.png", "type": "image/png"}], "start": 1756797221408, "stop": 1756797221655}], "start": 1756797196691, "stop": 1756797221655}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['enable running lock', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_enable_running_lock.py\", line 34, in test_enable_running_lock\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756797221655, "stop": 1756797221657}], "attachments": [{"name": "stdout", "source": "2f6c2735-ac01-4452-98ef-c449d3850814-attachment.txt", "type": "text/plain"}], "start": 1756797196691, "stop": 1756797221658, "uuid": "95189b56-be0e-4ab5-ada1-6cef8399d060", "historyId": "57a9b2e6f318afd186b838ed42ebd55c", "testCaseId": "57a9b2e6f318afd186b838ed42ebd55c", "fullName": "testcases.test_ella.unsupported_commands.test_enable_running_lock.TestEllaEnableRunningLock#test_enable_running_lock", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_running_lock"}, {"name": "subSuite", "value": "TestEllaEnableRunningLock"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_running_lock"}]}