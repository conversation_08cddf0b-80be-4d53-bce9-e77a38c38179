{"name": "测试yandex eats返回正确的不支持响应", "status": "passed", "description": "验证yandex eats指令返回预期的不支持响应", "steps": [{"name": "执行命令: yandex eats", "status": "passed", "steps": [{"name": "执行命令: yandex eats", "status": "passed", "start": 1756804598121, "stop": 1756804620224}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "77e54824-ebfe-434b-8fa6-9cbf656f9ebf-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f99927f4-5f3e-4b15-ae05-77ff53efe068-attachment.png", "type": "image/png"}], "start": 1756804620224, "stop": 1756804620492}], "start": 1756804598121, "stop": 1756804620492}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756804620492, "stop": 1756804620494}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "89c78f5f-f146-41f3-b475-73e4354c1679-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c36ec5ae-0a1a-41e7-b350-9516208e0d8a-attachment.png", "type": "image/png"}], "start": 1756804620494, "stop": 1756804620721}], "attachments": [{"name": "stdout", "source": "aee71111-054a-4abb-bfd8-93b3ca89af43-attachment.txt", "type": "text/plain"}], "start": 1756804598120, "stop": 1756804620721, "uuid": "081f35da-854e-4b09-a49c-e6b31906e807", "historyId": "b911308f3c1fe764715d778a884946c2", "testCaseId": "b911308f3c1fe764715d778a884946c2", "fullName": "testcases.test_ella.unsupported_commands.test_yandex_eats.TestEllaYandexEats#test_yandex_eats", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_yandex_eats"}, {"name": "subSuite", "value": "TestEllaYandexEats"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_yandex_eats"}]}