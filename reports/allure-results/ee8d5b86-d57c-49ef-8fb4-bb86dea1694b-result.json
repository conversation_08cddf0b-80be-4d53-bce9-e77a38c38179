{"name": "测试change man voice能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['OK, the voice is switched. You can also select other voices.']，实际响应: '['change man voice', '', '', '', '', '', '', '', '', '', '', 'Dialogue', 'Dialogue Explore Swipe down to view earlier chats Close WhatsApp Convert image to Word document change man voice The following images are generated for you. Generated by AI, for reference only Exit AI Image Generator DeepSeek-R1 Describe the image you want to generate']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_change_man_voice.TestEllaChangeManVoice object at 0x0000018BC640D210>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC4CD9ED0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_change_man_voice(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['OK, the voice is switched. You can also select other voices.']，实际响应: '['change man voice', '', '', '', '', '', '', '', '', '', '', 'Dialogue', 'Dialogue Explore Swipe down to view earlier chats Close WhatsApp Convert image to Word document change man voice The following images are generated for you. Generated by AI, for reference only Exit AI Image Generator DeepSeek-R1 Describe the image you want to generate']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_change_man_voice.py:33: AssertionError"}, "description": "change man voice", "steps": [{"name": "执行命令: change man voice", "status": "passed", "steps": [{"name": "执行命令: change man voice", "status": "passed", "start": 1756795500846, "stop": 1756795534975}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ec93da4f-f2b1-436f-99e4-267601325c4a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "15aeb210-da21-4330-b02f-62210e5f1bdc-attachment.png", "type": "image/png"}], "start": 1756795534975, "stop": 1756795535207}], "start": 1756795500846, "stop": 1756795535208}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['OK, the voice is switched. You can also select other voices.']，实际响应: '['change man voice', '', '', '', '', '', '', '', '', '', '', 'Dialogue', 'Dialogue Explore Swipe down to view earlier chats Close WhatsApp Convert image to Word document change man voice The following images are generated for you. Generated by AI, for reference only Exit AI Image Generator DeepSeek-R1 Describe the image you want to generate']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_change_man_voice.py\", line 33, in test_change_man_voice\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756795535208, "stop": 1756795535210}], "attachments": [{"name": "stdout", "source": "366a19bb-46d8-4ed4-960c-6e1887abaf7c-attachment.txt", "type": "text/plain"}], "start": 1756795500845, "stop": 1756795535211, "uuid": "2bf9b156-3255-4857-bc60-f000fa777cc0", "historyId": "178c119ddfd51f19a22377df428e3fc1", "testCaseId": "178c119ddfd51f19a22377df428e3fc1", "fullName": "testcases.test_ella.unsupported_commands.test_change_man_voice.TestEllaChangeManVoice#test_change_man_voice", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "ella技能"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_change_man_voice"}, {"name": "subSuite", "value": "TestEllaChangeManVoice"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_change_man_voice"}]}