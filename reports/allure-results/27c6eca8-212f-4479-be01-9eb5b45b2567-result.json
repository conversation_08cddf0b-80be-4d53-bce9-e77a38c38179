{"name": "测试Add the images and text on the screen to the note", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['Add the images and text on the screen to the note', '', '', '', 'I am sorry, but I am unable to perform this function currently.', 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', \"Dialogue Explore <PERSON><PERSON><PERSON> down to view earlier chats Sagittarius Horoscope Tomorrow How's the weather today? Help me write an email to make an appointment for a visit Add the images and text on the screen to the note I am sorry, but I am unable to perform this function currently. Generated by AI, for reference only Can you explain why? Are there any alternatives? I will try again later. DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_Add_the_images_and_text_on_the_screen_to_the_note.TestEllaOpenPlayPoliticalNews object at 0x0000018BC63E5E10>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC82E8760>\n\n    @allure.title(\"测试Add the images and text on the screen to the note\")\n    @allure.description(\"测试Add the images and text on the screen to the note指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_Add_the_images_and_text_on_the_screen_to_the_note(self, ella_app):\n        \"\"\"测试Add the images and text on the screen to the note命令\"\"\"\n        command = \"Add the images and text on the screen to the note\"\n        # expected_text = ['Sorry', 'Oops', 'out of my reach']\n        expected_text = ['Done']\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n    \n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['Add the images and text on the screen to the note', '', '', '', 'I am sorry, but I am unable to perform this function currently.', 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats Sagittarius Horoscope Tomorrow How's the weather today? Help me write an email to make an appointment for a visit Add the images and text on the screen to the note I am sorry, but I am unable to perform this function currently. Generated by AI, for reference only Can you explain why? Are there any alternatives? I will try again later. DeepSeek-R1 Feel free to ask me any questions…\"]'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_Add_the_images_and_text_on_the_screen_to_the_note.py:32: AssertionError"}, "description": "测试Add the images and text on the screen to the note指令", "steps": [{"name": "执行命令: Add the images and text on the screen to the note", "status": "passed", "steps": [{"name": "执行命令: Add the images and text on the screen to the note", "status": "passed", "start": 1756794884121, "stop": 1756794907597}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "963d0578-82d1-4e28-8fff-6cd4b52dd1e1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "adb0094e-8f15-45d0-9800-d64f5b8dd204-attachment.png", "type": "image/png"}], "start": 1756794907597, "stop": 1756794907830}], "start": 1756794884121, "stop": 1756794907831}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['Add the images and text on the screen to the note', '', '', '', 'I am sorry, but I am unable to perform this function currently.', 'Generated by AI, for reference only', '', '', '', '', '', 'Dialogue', \"Dialogue Explore <PERSON><PERSON><PERSON> down to view earlier chats Sagittarius Horoscope Tomorrow How's the weather today? Help me write an email to make an appointment for a visit Add the images and text on the screen to the note I am sorry, but I am unable to perform this function currently. Generated by AI, for reference only Can you explain why? Are there any alternatives? I will try again later. DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_Add_the_images_and_text_on_the_screen_to_the_note.py\", line 32, in test_Add_the_images_and_text_on_the_screen_to_the_note\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756794907831, "stop": 1756794907832}], "attachments": [{"name": "stdout", "source": "fd808dc4-6703-4b7e-bbff-09accaca4a85-attachment.txt", "type": "text/plain"}], "start": 1756794884121, "stop": 1756794907833, "uuid": "f79fc169-1b04-45e0-a604-d7fe3a319a4f", "historyId": "5cc849d46714fff99c626e94dc28932d", "testCaseId": "5cc849d46714fff99c626e94dc28932d", "fullName": "testcases.test_ella.unsupported_commands.test_Add_the_images_and_text_on_the_screen_to_the_note.TestEllaOpenPlayPoliticalNews#test_Add_the_images_and_text_on_the_screen_to_the_note", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_Add_the_images_and_text_on_the_screen_to_the_note"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_Add_the_images_and_text_on_the_screen_to_the_note"}]}