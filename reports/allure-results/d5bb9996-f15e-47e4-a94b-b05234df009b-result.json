{"name": "测试Switch to Hyper Charge能正常执行", "status": "passed", "description": "Switch to Hyper Charge", "steps": [{"name": "执行命令: Switch to Hyper Charge", "status": "passed", "steps": [{"name": "执行命令: Switch to Hyper Charge", "status": "passed", "start": 1756791695042, "stop": 1756791715928}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d6978400-e950-48d2-a1a3-d5082fd5c859-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7acb5c61-df42-4ddb-81e5-49bec7c2219b-attachment.png", "type": "image/png"}], "start": 1756791715928, "stop": 1756791716124}], "start": 1756791695042, "stop": 1756791716125}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1756791716125, "stop": 1756791716127}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2f44462f-**************-34ad1cb42fea-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d7838b76-ec46-49ea-8f3c-56bf364a0e3a-attachment.png", "type": "image/png"}], "start": 1756791716127, "stop": 1756791716309}], "attachments": [{"name": "stdout", "source": "f9f09fb0-7504-4bcb-ac5b-4deafc2855c3-attachment.txt", "type": "text/plain"}], "start": 1756791695041, "stop": 1756791716309, "uuid": "ba513313-01ee-480c-8262-4f2805cfd325", "historyId": "ffd9b4e8a27f1469e05050bd5989e500", "testCaseId": "ffd9b4e8a27f1469e05050bd5989e500", "fullName": "testcases.test_ella.system_coupling.test_switch_to_hyper_charge.TestEllaSwitchToHyperCharge#test_switch_to_hyper_charge", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_hyper_charge"}, {"name": "subSuite", "value": "TestEllaSwitchToHyperCharge"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_hyper_charge"}]}