{"name": "测试Language List", "status": "passed", "description": "测试Language List指令", "steps": [{"name": "执行命令: Language List", "status": "passed", "steps": [{"name": "执行命令: Language List", "status": "passed", "start": 1756794924968, "stop": 1756794953190}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a3678bd5-d9a8-47b1-bd55-317f5a6b1d62-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c2b24440-5225-41d2-af9e-5013cfd85e14-attachment.png", "type": "image/png"}], "start": 1756794953191, "stop": 1756794953397}], "start": 1756794924968, "stop": 1756794953398}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756794953398, "stop": 1756794953398}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b3507eca-e541-45d9-9ac5-897c7faf275b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b9ad1975-7348-42c7-a599-e46ff12d80e0-attachment.png", "type": "image/png"}], "start": 1756794953398, "stop": 1756794953614}], "attachments": [{"name": "stdout", "source": "e407b0f9-0b8b-4c7d-a8bd-8d507113402b-attachment.txt", "type": "text/plain"}], "start": 1756794924968, "stop": 1756794953614, "uuid": "989cb59f-4758-429d-a683-b5870111c6c0", "historyId": "f9558282973df5c72bd1c57fb0e19984", "testCaseId": "f9558282973df5c72bd1c57fb0e19984", "fullName": "testcases.test_ella.unsupported_commands.test_Language_List.TestEllaOpenPlayPoliticalNews#test_Language_List", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_Language_List"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_Language_List"}]}