{"name": "测试Scan this QR code 能正常执行", "status": "passed", "description": "Scan this QR code ", "steps": [{"name": "执行命令: Scan this QR code ", "status": "passed", "steps": [{"name": "执行命令: Scan this QR code ", "status": "passed", "start": 1756788632641, "stop": 1756788742105}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "67f6e246-732b-4441-a519-f6afafb2c32a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cf5b0030-cd26-4b11-859e-31ffece3d0ed-attachment.png", "type": "image/png"}], "start": 1756788742105, "stop": 1756788742313}], "start": 1756788632641, "stop": 1756788742313}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756788742313, "stop": 1756788742314}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "90d5171b-e322-44e3-9c70-dadc12efe733-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "95d8cc75-0820-4062-aafd-daa4405435ac-attachment.png", "type": "image/png"}], "start": 1756788742314, "stop": 1756788742547}], "attachments": [{"name": "stdout", "source": "b73e4c0c-d102-4cfa-a2ff-df4f4fe136e8-attachment.txt", "type": "text/plain"}], "start": 1756788632641, "stop": 1756788742548, "uuid": "fa6ed46d-362d-495c-b778-3fab4ba6df26", "historyId": "5697ea2b6f1cefef94d5b32e213e05a7", "testCaseId": "5697ea2b6f1cefef94d5b32e213e05a7", "fullName": "testcases.test_ella.self_function.test_scan_this_qr_code.TestEllaScanThisQrCode#test_scan_this_qr_code", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.self_function"}, {"name": "suite", "value": "test_scan_this_qr_code"}, {"name": "subSuite", "value": "TestEllaScanThisQrCode"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.self_function.test_scan_this_qr_code"}]}