{"name": "测试set special function返回正确的不支持响应", "status": "passed", "description": "验证set special function指令返回预期的不支持响应", "steps": [{"name": "执行命令: set special function", "status": "passed", "steps": [{"name": "执行命令: set special function", "status": "passed", "start": 1756802928649, "stop": 1756802952909}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "091115aa-7dcf-459d-941c-693e16c62332-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "10b83e34-376a-4a01-a6a1-256c561c05ae-attachment.png", "type": "image/png"}], "start": 1756802952909, "stop": 1756802953146}], "start": 1756802928649, "stop": 1756802953147}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756802953147, "stop": 1756802953148}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1401cb8a-9455-43b6-aeb1-6b2b62559977-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8900fa74-71c5-4890-9f2d-c4241e6f2c4a-attachment.png", "type": "image/png"}], "start": 1756802953148, "stop": 1756802953408}], "attachments": [{"name": "stdout", "source": "1a4fbf80-0bee-4f04-8178-54e3b2ef74be-attachment.txt", "type": "text/plain"}], "start": 1756802928649, "stop": 1756802953409, "uuid": "904acdb0-65d0-4342-bcb2-4f2734ad0203", "historyId": "c046a1c6e6cc8effa10641e329b1cfad", "testCaseId": "c046a1c6e6cc8effa10641e329b1cfad", "fullName": "testcases.test_ella.unsupported_commands.test_set_special_function.TestEllaSetSpecialFunction#test_set_special_function", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_special_function"}, {"name": "subSuite", "value": "TestEllaSetSpecialFunction"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_special_function"}]}