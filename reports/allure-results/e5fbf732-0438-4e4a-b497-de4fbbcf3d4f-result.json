{"name": "测试hello hello能正常执行", "status": "passed", "description": "hello hello", "steps": [{"name": "执行命令: hello hello", "status": "passed", "steps": [{"name": "执行命令: hello hello", "status": "passed", "start": 1756797888205, "stop": 1756797915759}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d7468de3-ed75-42cf-951c-71d1acd2816f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "88cca5bf-d89b-43c2-9e37-a95fc0f93ec3-attachment.png", "type": "image/png"}], "start": 1756797915759, "stop": 1756797915950}], "start": 1756797888205, "stop": 1756797915950}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756797915950, "stop": 1756797915952}, {"name": "验证应用已打开", "status": "passed", "start": 1756797915952, "stop": 1756797915952}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "967e886c-01a5-41d2-97a4-75e94764dc8b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7ec6120d-b502-4bf2-b524-79957e1e1a21-attachment.png", "type": "image/png"}], "start": 1756797915952, "stop": 1756797916175}], "attachments": [{"name": "stdout", "source": "9943c36b-1866-4ea8-abee-17c4b38f2c75-attachment.txt", "type": "text/plain"}], "start": 1756797888205, "stop": 1756797916176, "uuid": "30d8f5f4-505c-489d-9100-67af4ab19b7e", "historyId": "0ce2a3efa79db58c34a5590015948f51", "testCaseId": "0ce2a3efa79db58c34a5590015948f51", "fullName": "testcases.test_ella.unsupported_commands.test_hello_hello.TestEllaHelloHello#test_hello_hello", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_hello_hello"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_hello_hello"}]}