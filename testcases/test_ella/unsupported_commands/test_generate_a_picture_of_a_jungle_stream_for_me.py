"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaOpenPlayPoliticalNews(SimpleEllaTest):
    """Ella打开Generate a picture of a jungle stream for me测试类"""

    @allure.title("测试Generate a picture of a jungle stream for me")
    @allure.description("测试Generate a picture of a jungle stream for me指令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_generate_a_picture_of_a_jungle_stream_for_me(self, ella_app):
        """测试Generate a picture of a jungle stream for me命令"""
        command = "Generate a picture of a jungle stream for me"
        expected_text = ['The following images are generated for you.','Generated by AI, for reference only','reached the image generation limit for today']
        ''

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应包含期望内容"):
            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"


        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
