{"name": "测试add the lucy's number in this picture", "status": "passed", "description": "测试Ask Screen功能: add the lucy's number in this picture", "steps": [{"name": "准备测试数据", "status": "passed", "start": 1756793494581, "stop": 1756793502758}, {"name": "执行Ask Screen命令: add the lucy's number in this picture", "status": "passed", "steps": [{"name": "执行浮窗命令: add the lucy's number in this picture", "status": "passed", "steps": [{"name": "执行命令: add the lucy's number in this picture", "status": "passed", "start": 1756793502759, "stop": 1756793511816}, {"name": "等待并获取AI响应", "status": "passed", "start": 1756793511816, "stop": 1756793528408}, {"name": "验证响应内容", "status": "passed", "attachments": [{"name": "关键词验证结果", "source": "492a9306-f168-4bb1-844e-986971bbf3b5-attachment.txt", "type": "text/plain"}], "start": 1756793528408, "stop": 1756793528410}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "浮窗测试总结", "source": "39f6ad7e-edd8-4611-9444-43b51357bec7-attachment.txt", "type": "text/plain"}, {"name": "AI响应内容", "source": "86735be3-52ff-4e6a-b3f4-4502c657c1d0-attachment.txt", "type": "text/plain"}], "start": 1756793528410, "stop": 1756793528412}], "start": 1756793502758, "stop": 1756793528412}, {"name": "截图记录测试完成状态", "status": "passed", "attachments": [{"name": "floating_test_completed", "source": "70e0b4a9-9e6b-4544-b5b6-16e722fa83b3-attachment.png", "type": "image/png"}], "start": 1756793528412, "stop": 1756793528623}], "start": 1756793502758, "stop": 1756793529816}, {"name": "验证测试结果", "status": "passed", "start": 1756793529816, "stop": 1756793529817}], "attachments": [{"name": "stdout", "source": "33765d94-8dea-46c5-bec5-e48405228692-attachment.txt", "type": "text/plain"}], "start": 1756793494581, "stop": 1756793529819, "uuid": "a92027ae-a373-4803-bbea-2a298b9493a7", "historyId": "3d11cdc9d7b9f1e332cba29afd4889a7", "testCaseId": "3d11cdc9d7b9f1e332cba29afd4889a7", "fullName": "testcases.test_ella.test_ask_screen.contact.test_add_the_lucy_s_number_in_this_picture.TestAskScreenAddLucySNumberPicture#test_add_the_lucy_s_number_in_this_picture", "labels": [{"name": "epic", "value": "Ella浮窗测试"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ask Screen功能"}, {"name": "story", "value": "联系人相关"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.test_ask_screen.contact"}, {"name": "suite", "value": "test_add_the_lucy_s_number_in_this_picture"}, {"name": "subSuite", "value": "TestAskScreenAddLucySNumberPicture"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_ask_screen.contact.test_add_the_lucy_s_number_in_this_picture"}]}