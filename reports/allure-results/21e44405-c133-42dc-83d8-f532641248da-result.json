{"name": "测试set app notifications返回正确的不支持响应", "status": "passed", "description": "验证set app notifications指令返回预期的不支持响应", "steps": [{"name": "执行命令: set app notifications", "status": "passed", "steps": [{"name": "执行命令: set app notifications", "status": "passed", "start": 1756801427628, "stop": 1756801459008}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "86e4723f-54bd-42d4-a820-b39b4dc35bc9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "daaaae68-a04b-482b-a46b-830e2e2c3a6e-attachment.png", "type": "image/png"}], "start": 1756801459008, "stop": 1756801459268}], "start": 1756801427628, "stop": 1756801459268}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756801459268, "stop": 1756801459269}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f64eb1ca-7c27-42eb-9e4c-db99f938a764-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a4bd7078-af28-40e3-b648-b2aedfbb1287-attachment.png", "type": "image/png"}], "start": 1756801459269, "stop": 1756801459475}], "attachments": [{"name": "stdout", "source": "a2519d8d-14c4-4ef5-ba50-def6fb8d584e-attachment.txt", "type": "text/plain"}], "start": 1756801427628, "stop": 1756801459475, "uuid": "b2263f81-fc88-4090-9aad-616d156bdf2a", "historyId": "9458f45d3c37d9141658da9964a470f5", "testCaseId": "9458f45d3c37d9141658da9964a470f5", "fullName": "testcases.test_ella.unsupported_commands.test_set_app_notifications.TestEllaSetAppNotifications#test_set_app_notifications", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_app_notifications"}, {"name": "subSuite", "value": "TestEllaSetAppNotifications"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_app_notifications"}]}