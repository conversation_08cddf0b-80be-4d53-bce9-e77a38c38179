{"name": "测试switch to default mode能正常执行", "status": "passed", "description": "switch to default mode", "steps": [{"name": "执行命令: switch to default mode", "status": "passed", "steps": [{"name": "执行命令: switch to default mode", "status": "passed", "start": 1756791567663, "stop": 1756791595461}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1e18e891-51a9-476f-88dc-81ec028a9f0b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b0953024-b714-4ff1-9a63-72d7f928a0ef-attachment.png", "type": "image/png"}], "start": 1756791595461, "stop": 1756791595734}], "start": 1756791567663, "stop": 1756791595734}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1756791595734, "stop": 1756791595736}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "cfa892c8-e32f-45c8-a65c-8901199bbc3a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d3d45504-320d-4150-8382-ce711be5e3f6-attachment.png", "type": "image/png"}], "start": 1756791595736, "stop": 1756791595953}], "attachments": [{"name": "stdout", "source": "4227cbc4-095b-4b51-8ceb-31e6fc70b98f-attachment.txt", "type": "text/plain"}], "start": 1756791567663, "stop": 1756791595954, "uuid": "171b5825-29cb-4321-bdd0-685f4457ddf1", "historyId": "d2d9aa669417404f06e84a7a4387c55a", "testCaseId": "d2d9aa669417404f06e84a7a4387c55a", "fullName": "testcases.test_ella.system_coupling.test_switch_to_default_mode.TestEllaSwitchToDefaultMode#test_switch_to_default_mode", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_default_mode"}, {"name": "subSuite", "value": "TestEllaSwitchToDefaultMode"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_default_mode"}]}