{"name": "测试set my alarm volume to 50%", "status": "passed", "description": "测试set my alarm volume to 50%指令", "steps": [{"name": "执行命令: set my alarm volume to 50%", "status": "passed", "steps": [{"name": "执行命令: set my alarm volume to 50%", "status": "passed", "start": 1756790844484, "stop": 1756790865255}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8bfbb4c0-242b-47a1-929d-68f86fec0573-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c7c116e8-fd9c-4750-bf3c-45efbb7905ae-attachment.png", "type": "image/png"}], "start": 1756790865255, "stop": 1756790865445}], "start": 1756790844484, "stop": 1756790865446}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790865446, "stop": 1756790865447}, {"status": "passed", "start": 1756790865447, "stop": 1756790865447}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3839ea18-c72c-45c6-bad0-d519b1ba2b3e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c920900e-676c-4905-966a-e4a0934122fd-attachment.png", "type": "image/png"}], "start": 1756790865447, "stop": 1756790865644}], "attachments": [{"name": "stdout", "source": "8d87cf5e-ecda-44a8-8f6d-9282603e7d54-attachment.txt", "type": "text/plain"}], "start": 1756790844483, "stop": 1756790865644, "uuid": "203f03cf-63a8-43dd-8578-07721833413c", "historyId": "489e5c631d3a3ce20f77bce4c7c6632a", "testCaseId": "489e5c631d3a3ce20f77bce4c7c6632a", "fullName": "testcases.test_ella.system_coupling.test_set_my_alarm_volume_to.TestEllaOpenClock#test_set_my_alarm_volume_to", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_my_alarm_volume_to"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_my_alarm_volume_to"}]}