{"name": "测试Where is the place on the screen?", "status": "passed", "description": "测试Ask Screen功能: Where is the place on the screen?", "steps": [{"name": "准备测试数据", "status": "passed", "start": 1756794149922, "stop": 1756794158869}, {"name": "执行Ask Screen命令: Where is the place on the screen?", "status": "passed", "steps": [{"name": "执行浮窗命令: Where is the place on the screen?", "status": "passed", "steps": [{"name": "执行命令: Where is the place on the screen?", "status": "passed", "start": 1756794158870, "stop": 1756794167826}, {"name": "等待并获取AI响应", "status": "passed", "start": 1756794167826, "stop": 1756794184126}, {"name": "验证响应内容", "status": "passed", "attachments": [{"name": "关键词验证结果", "source": "625125ca-c6c4-482b-842f-169d716c6491-attachment.txt", "type": "text/plain"}], "start": 1756794184126, "stop": 1756794184129}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "浮窗测试总结", "source": "2d97e5e2-ea8f-4989-8696-3c880b2465a8-attachment.txt", "type": "text/plain"}, {"name": "AI响应内容", "source": "a31584bc-8b13-4e0d-9620-5d610ec204d2-attachment.txt", "type": "text/plain"}], "start": 1756794184129, "stop": 1756794184131}], "start": 1756794158869, "stop": 1756794184131}, {"name": "截图记录测试完成状态", "status": "passed", "attachments": [{"name": "floating_test_completed", "source": "c1b3fc2c-0722-4fcb-8a7b-6ffb8687f52b-attachment.png", "type": "image/png"}], "start": 1756794184131, "stop": 1756794184365}], "start": 1756794158869, "stop": 1756794185613}, {"name": "验证测试结果", "status": "passed", "start": 1756794185613, "stop": 1756794185615}, {"name": "记录测试完成", "status": "passed", "start": 1756794185615, "stop": 1756794185615}], "attachments": [{"name": "stdout", "source": "973a89e1-87c4-43ac-9443-ae899427295d-attachment.txt", "type": "text/plain"}], "start": 1756794149922, "stop": 1756794185616, "uuid": "486002fa-69ef-4d79-b1ca-f7f15582921b", "historyId": "91f19453d2c7e6d363b99298706ba47d", "testCaseId": "91f19453d2c7e6d363b99298706ba47d", "fullName": "testcases.test_ella.test_ask_screen.scene_understanding.test_where_is_the_place_on_the_screen.TestAskScreenWhereIsPlaceScreen#test_where_is_the_place_on_the_screen", "labels": [{"name": "story", "value": "场景理解"}, {"name": "epic", "value": "Ella浮窗测试"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ask Screen功能"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.test_ask_screen.scene_understanding"}, {"name": "suite", "value": "test_where_is_the_place_on_the_screen"}, {"name": "subSuite", "value": "TestAskScreenWhereIsPlaceScreen"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_ask_screen.scene_understanding.test_where_is_the_place_on_the_screen"}]}