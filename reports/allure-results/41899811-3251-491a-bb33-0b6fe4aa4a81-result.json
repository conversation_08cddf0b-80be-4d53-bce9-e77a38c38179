{"name": "测试what is the weather today能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['today', 'The high is forecast as', '℃']，实际响应: '['what is the weather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_what_is_the_weather_today.TestEllaWhatIsWeatherToday object at 0x0000018BC66B7190>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC828FE50>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_what_is_the_weather_today(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['today', 'The high is forecast as', '℃']，实际响应: '['what is the weather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_what_is_the_weather_today.py:33: AssertionError"}, "description": "what is the weather today", "steps": [{"name": "执行命令: what is the weather today", "status": "passed", "steps": [{"name": "执行命令: what is the weather today", "status": "passed", "start": 1756804256271, "stop": 1756804287320}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "900b8896-a65e-44be-879a-196e781e7bf3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "213cdbcc-bcd3-4872-9dd0-389c0d779a3d-attachment.png", "type": "image/png"}], "start": 1756804287320, "stop": 1756804287552}], "start": 1756804256271, "stop": 1756804287553}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['today', 'The high is forecast as', '℃']，实际响应: '['what is the weather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_what_is_the_weather_today.py\", line 33, in test_what_is_the_weather_today\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756804287553, "stop": 1756804287556}], "attachments": [{"name": "stdout", "source": "5af120a0-5e8c-4847-a744-d1d0ec7ac031-attachment.txt", "type": "text/plain"}], "start": 1756804256271, "stop": 1756804287558, "uuid": "a8e7c0b4-97b2-430f-a1c5-bd120712251b", "historyId": "a297156fb1e7af53c032ac3c6277feee", "testCaseId": "a297156fb1e7af53c032ac3c6277feee", "fullName": "testcases.test_ella.unsupported_commands.test_what_is_the_weather_today.TestEllaWhatIsWeatherToday#test_what_is_the_weather_today", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_what_is_the_weather_today"}, {"name": "subSuite", "value": "TestEllaWhatIsWeatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_what_is_the_weather_today"}]}