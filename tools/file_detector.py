"""
文件检测模块
负责检查指定目录下文件是否存在，支持日期时间戳文件检测
"""
import argparse
import json
import os
import re
import sys
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

try:
    from core.logger import log
    from pages.base.detectors.detector_utils import DetectorUtils
except ImportError:
    # 如果导入失败，使用简单的日志输出
    class SimpleLogger:
        def info(self, msg): print(f"[INFO] {msg}")
        def error(self, msg): print(f"[ERROR] {msg}")
        def warning(self, msg): print(f"[WARNING] {msg}")
        def debug(self, msg): print(f"[DEBUG] {msg}")

    log = SimpleLogger()

    # 简单的ADB命令执行器
    class SimpleDetectorUtils:
        @staticmethod
        def execute_adb_command(command: List[str], timeout: int = 10) -> Tuple[bool, str]:
            try:
                import subprocess
                result = subprocess.run(
                    command,
                    capture_output=True,
                    text=True,
                    timeout=timeout,
                    encoding='utf-8',
                    errors='ignore'
                )
                success = result.returncode == 0
                output = result.stdout if success else result.stderr
                return success, output
            except subprocess.TimeoutExpired:
                return False, "命令超时"
            except Exception as e:
                return False, str(e)

    DetectorUtils = SimpleDetectorUtils


class FileDetector:
    """文件检测器类"""

    # 支持的图片格式
    IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif'}

    # 日期时间戳模式
    DATETIME_PATTERNS = {
        'IMG_YYYYMMDD_HHMMSS_SSS': r'IMG_(\d{8})_(\d{6})_(\d{3})\.(jpg|jpeg|png|gif|bmp|webp)',
        'YYYYMMDD_HHMMSS': r'(\d{8})_(\d{6})\.(jpg|jpeg|png|gif|bmp|webp)',
        'YYYY-MM-DD_HH-MM-SS': r'(\d{4}-\d{2}-\d{2})_(\d{2}-\d{2}-\d{2})\.(jpg|jpeg|png|gif|bmp|webp)',
        'Screenshot_YYYYMMDD-HHMMSS': r'Screenshot_(\d{8})-(\d{6})\.(jpg|jpeg|png|gif|bmp|webp)'
    }

    # Android常见目录路径
    ANDROID_COMMON_PATHS = {
        'camera': ['/sdcard/DCIM/Camera', '/storage/emulated/0/DCIM/Camera'],
        'screenshots': ['/sdcard/Pictures/Screenshot', '/sdcard/Pictures/Screenshots', '/storage/emulated/0/Pictures/Screenshot', '/storage/emulated/0/Pictures/Screenshots'],
        'screen_recordings': ['/sdcard/Movies/ScreenRecord', '/sdcard/Movies', '/sdcard/DCIM/ScreenRecord', '/storage/emulated/0/Movies/ScreenRecord', '/storage/emulated/0/Movies'],
        'pictures': ['/sdcard/Pictures', '/storage/emulated/0/Pictures'],
        'downloads': ['/sdcard/Download', '/storage/emulated/0/Download'],
        'dcim': ['/sdcard/DCIM', '/storage/emulated/0/DCIM']
    }

    def __init__(self, base_path: str = "", use_adb: bool = False, device_id: str = ""):
        """
        初始化文件检测器

        Args:
            base_path: 基础路径，如果为空则使用当前工作目录
            use_adb: 是否使用ADB检查Android设备文件
            device_id: Android设备ID，为空时使用默认设备
        """
        self.base_path = base_path or os.getcwd()
        self.use_adb = use_adb
        self.device_id = device_id

        if self.use_adb:
            log.info(f"文件检测器初始化，Android设备模式 (设备: {device_id or 'default'})")
            self._check_adb_connection()
        else:
            log.info(f"文件检测器初始化，本地文件系统模式，基础路径: {self.base_path}")

    def _check_adb_connection(self) -> bool:
        """
        检查ADB连接状态

        Returns:
            bool: 连接是否正常
        """
        try:
            cmd = ["adb", "devices"]
            success, output = DetectorUtils.execute_adb_command(cmd, timeout=5)

            if not success:
                log.error(f"ADB连接检查失败: {output}")
                return False

            # 检查是否有设备连接
            lines = output.strip().split('\n')[1:]  # 跳过标题行
            connected_devices = []

            for line in lines:
                if line.strip() and '\t' in line:
                    parts = line.split('\t')
                    if len(parts) >= 2 and parts[1].strip() == 'device':
                        connected_devices.append(parts[0].strip())

            if not connected_devices:
                log.warning("未发现连接的Android设备")
                return False

            if self.device_id and self.device_id not in connected_devices:
                log.warning(f"指定的设备 {self.device_id} 未连接")
                return False

            log.info(f"ADB连接正常，发现 {len(connected_devices)} 个设备: {connected_devices}")
            return True

        except Exception as e:
            log.error(f"检查ADB连接时发生错误: {e}")
            return False
    
    def check_file_exists(self, file_path: str) -> bool:
        """
        检查单个文件是否存在

        Args:
            file_path: 文件路径（相对或绝对路径）

        Returns:
            bool: 文件是否存在
        """
        try:
            if self.use_adb:
                return self._check_android_file_exists(file_path)
            else:
                return self._check_local_file_exists(file_path)
        except Exception as e:
            log.error(f"检查文件时发生错误: {e}")
            return False

    def _check_local_file_exists(self, file_path: str) -> bool:
        """
        检查本地文件是否存在

        Args:
            file_path: 文件路径

        Returns:
            bool: 文件是否存在
        """
        # 如果是相对路径，则基于base_path构建完整路径
        if not os.path.isabs(file_path):
            full_path = os.path.join(self.base_path, file_path)
        else:
            full_path = file_path

        exists = os.path.isfile(full_path)
        log.debug(f"检查本地文件: {full_path} -> {'存在' if exists else '不存在'}")
        return exists

    def _check_android_file_exists(self, file_path: str) -> bool:
        """
        检查Android设备上的文件是否存在

        Args:
            file_path: Android设备上的文件路径

        Returns:
            bool: 文件是否存在
        """
        try:
            # 构建ADB命令
            cmd = ["adb"]
            if self.device_id:
                cmd.extend(["-s", self.device_id])

            # 使用test命令检查文件是否存在
            cmd.extend(["shell", "test", "-f", file_path, "&&", "echo", "EXISTS"])

            success, output = DetectorUtils.execute_adb_command(cmd, timeout=10)

            if success and "EXISTS" in output:
                log.debug(f"检查Android文件: {file_path} -> 存在")
                return True
            else:
                log.debug(f"检查Android文件: {file_path} -> 不存在")
                return False

        except Exception as e:
            log.error(f"检查Android文件时发生错误: {e}")
            return False
    
    def scan_directory(self, directory: str, recursive: bool = False) -> List[Dict[str, Any]]:
        """
        扫描目录下的所有文件

        Args:
            directory: 目录路径
            recursive: 是否递归扫描子目录

        Returns:
            List[Dict]: 文件信息列表
        """
        try:
            if self.use_adb:
                return self._scan_android_directory(directory, recursive)
            else:
                return self._scan_local_directory(directory, recursive)
        except Exception as e:
            log.error(f"扫描目录时发生错误: {e}")
            return []

    def _scan_local_directory(self, directory: str, recursive: bool = False) -> List[Dict[str, Any]]:
        """
        扫描本地目录

        Args:
            directory: 目录路径
            recursive: 是否递归扫描

        Returns:
            List[Dict]: 文件信息列表
        """
        # 构建完整目录路径
        if not os.path.isabs(directory):
            full_dir = os.path.join(self.base_path, directory)
        else:
            full_dir = directory

        if not os.path.exists(full_dir):
            log.warning(f"本地目录不存在: {full_dir}")
            return []

        files_info = []

        if recursive:
            # 递归扫描
            for root, dirs, files in os.walk(full_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    files_info.append(self._get_file_info(file_path))
        else:
            # 只扫描当前目录
            for item in os.listdir(full_dir):
                item_path = os.path.join(full_dir, item)
                if os.path.isfile(item_path):
                    files_info.append(self._get_file_info(item_path))

        log.info(f"扫描本地目录 {full_dir} 完成，找到 {len(files_info)} 个文件")
        return files_info

    def _scan_android_directory(self, directory: str, recursive: bool = False) -> List[Dict[str, Any]]:
        """
        扫描Android设备目录

        Args:
            directory: Android设备上的目录路径
            recursive: 是否递归扫描

        Returns:
            List[Dict]: 文件信息列表
        """
        try:
            # 构建ADB命令
            cmd = ["adb"]
            if self.device_id:
                cmd.extend(["-s", self.device_id])

            # 使用ls命令列出文件
            if recursive:
                cmd.extend(["shell", "find", directory, "-type", "f", "2>/dev/null"])
            else:
                cmd.extend(["shell", "ls", "-la", directory, "2>/dev/null"])

            success, output = DetectorUtils.execute_adb_command(cmd, timeout=30)

            if not success:
                log.warning(f"扫描Android目录失败: {directory}")
                return []

            files_info = []

            if recursive:
                # 处理find命令的输出
                lines = output.strip().split('\n')
                for line in lines:
                    if line.strip() and not line.startswith('find:'):
                        file_path = line.strip()
                        file_info = self._get_android_file_info(file_path)
                        if file_info:
                            files_info.append(file_info)
            else:
                # 处理ls -la命令的输出
                lines = output.strip().split('\n')
                for line in lines:
                    if line.strip() and not line.startswith('total') and not line.startswith('ls:'):
                        file_info = self._parse_android_ls_line(line, directory)
                        if file_info:
                            files_info.append(file_info)

            log.info(f"扫描Android目录 {directory} 完成，找到 {len(files_info)} 个文件")
            return files_info

        except Exception as e:
            log.error(f"扫描Android目录时发生错误: {e}")
            return []
    
    def _get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件详细信息

        Args:
            file_path: 文件路径

        Returns:
            Dict: 文件信息字典
        """
        try:
            if self.use_adb:
                return self._get_android_file_info(file_path)
            else:
                return self._get_local_file_info(file_path)
        except Exception as e:
            log.error(f"获取文件信息失败: {e}")
            return {'path': file_path, 'error': str(e)}

    def _get_local_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取本地文件详细信息

        Args:
            file_path: 文件路径

        Returns:
            Dict: 文件信息字典
        """
        try:
            stat = os.stat(file_path)
            file_info = {
                'path': file_path,
                'name': os.path.basename(file_path),
                'size': stat.st_size,
                'created_time': datetime.fromtimestamp(stat.st_ctime).strftime('%Y-%m-%d %H:%M:%S'),
                'modified_time': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                'extension': os.path.splitext(file_path)[1].lower(),
                'is_image': os.path.splitext(file_path)[1].lower() in self.IMAGE_EXTENSIONS
            }
            return file_info
        except Exception as e:
            log.error(f"获取本地文件信息失败: {e}")
            return {'path': file_path, 'error': str(e)}

    def _get_android_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取Android设备文件详细信息

        Args:
            file_path: Android设备上的文件路径

        Returns:
            Dict: 文件信息字典
        """
        try:
            # 构建ADB命令获取文件详细信息
            cmd = ["adb"]
            if self.device_id:
                cmd.extend(["-s", self.device_id])

            cmd.extend(["shell", "ls", "-la", file_path, "2>/dev/null"])

            success, output = DetectorUtils.execute_adb_command(cmd, timeout=10)

            if not success or not output.strip():
                return {'path': file_path, 'error': 'File not found or access denied'}

            # 解析ls -la输出
            line = output.strip().split('\n')[0]  # 取第一行
            file_info = self._parse_android_ls_line(line, os.path.dirname(file_path))

            if file_info:
                return file_info
            else:
                # 如果解析失败，返回基本信息
                return {
                    'path': file_path,
                    'name': os.path.basename(file_path),
                    'size': 0,
                    'created_time': 'Unknown',
                    'modified_time': 'Unknown',
                    'extension': os.path.splitext(file_path)[1].lower(),
                    'is_image': os.path.splitext(file_path)[1].lower() in self.IMAGE_EXTENSIONS
                }

        except Exception as e:
            log.error(f"获取Android文件信息失败: {e}")
            return {'path': file_path, 'error': str(e)}

    def _parse_android_ls_line(self, line: str, directory: str) -> Optional[Dict[str, Any]]:
        """
        解析Android ls -la命令的输出行

        Args:
            line: ls -la输出的一行
            directory: 目录路径

        Returns:
            Optional[Dict]: 文件信息字典，解析失败返回None
        """
        try:
            # ls -la输出格式: -rw-rw---- 1 <USER> <GROUP> 1234567 2025-08-04 12:34 filename.jpg
            parts = line.strip().split()

            if len(parts) < 8:  # 修改最小部分数量
                return None

            # 检查是否为文件（第一个字符为'-'）
            if not parts[0].startswith('-'):
                return None

            # 提取文件信息
            permissions = parts[0]
            size = int(parts[4]) if parts[4].isdigit() else 0

            # 日期和时间（可能是两个部分）
            date_part = parts[5]
            time_part = parts[6]

            # 文件名（可能包含空格，所以取剩余部分）
            filename = ' '.join(parts[7:])

            # 构建完整路径
            full_path = os.path.join(directory, filename).replace('\\', '/')

            # 构建修改时间
            try:
                # 尝试解析日期时间
                if ':' in time_part:
                    # 当年的文件，格式: 2025-08-04 11:23
                    modified_time = f"{date_part} {time_part}:00"
                else:
                    # 往年的文件，格式: 2024-08-04 2024
                    modified_time = f"{time_part}-{date_part} 00:00:00"
            except:
                modified_time = f"{date_part} {time_part}"

            file_info = {
                'path': full_path,
                'name': filename,
                'size': size,
                'created_time': modified_time,  # Android ls不提供创建时间，使用修改时间
                'modified_time': modified_time,
                'extension': os.path.splitext(filename)[1].lower(),
                'is_image': os.path.splitext(filename)[1].lower() in self.IMAGE_EXTENSIONS,
                'permissions': permissions
            }

            return file_info

        except Exception as e:
            log.debug(f"解析Android ls输出失败: {e}, line: {line}")
            return None
    
    def find_datetime_files(self, directory: str, pattern_name: str = 'IMG_YYYYMMDD_HHMMSS_SSS', 
                           target_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        查找符合日期时间戳模式的文件
        
        Args:
            directory: 搜索目录
            pattern_name: 模式名称
            target_date: 目标日期 (YYYYMMDD格式)，如果为None则使用当前日期
            
        Returns:
            List[Dict]: 匹配的文件信息列表
        """
        try:
            if pattern_name not in self.DATETIME_PATTERNS:
                log.error(f"不支持的模式: {pattern_name}")
                return []
            
            pattern = self.DATETIME_PATTERNS[pattern_name]
            target_date = target_date or datetime.now().strftime('%Y%m%d')
            
            # 扫描目录
            all_files = self.scan_directory(directory, recursive=False)
            log.info(f"扫描目录 {directory} 完成，找到 {len(all_files)} 个文件")
            matched_files = []
            
            for file_info in all_files:
                if 'error' in file_info:
                    continue
                
                filename = file_info['name']
                match = re.match(pattern, filename, re.IGNORECASE)
                
                if match:
                    # 提取日期信息
                    file_date = None
                    file_time = None
                    file_ms = None
                    file_ext = None

                    if pattern_name == 'IMG_YYYYMMDD_HHMMSS_SSS':
                        file_date = match.group(1)
                        file_time = match.group(2)
                        file_ms = match.group(3)
                        file_ext = match.group(4)
                    elif pattern_name == 'YYYYMMDD_HHMMSS':
                        file_date = match.group(1)
                        file_time = match.group(2)
                        file_ext = match.group(3)
                        file_ms = None
                    elif pattern_name == 'YYYY-MM-DD_HH-MM-SS':
                        file_date = match.group(1).replace('-', '')  # 转换为YYYYMMDD格式
                        file_time = match.group(2).replace('-', '')  # 转换为HHMMSS格式
                        file_ext = match.group(3)
                        file_ms = None
                    elif pattern_name == 'Screenshot_YYYYMMDD-HHMMSS':
                        file_date = match.group(1)
                        file_time = match.group(2)
                        file_ext = match.group(3)
                        file_ms = None

                    # 检查日期是否匹配
                    if file_date and file_date == target_date:
                        file_info.update({
                            'pattern_matched': pattern_name,
                            'extracted_date': file_date,
                            'extracted_time': file_time,
                            'extracted_ms': file_ms,
                            'file_extension': file_ext
                        })
                        matched_files.append(file_info)
            
            log.info(f"在目录 {directory} 中找到 {len(matched_files)} 个匹配日期 {target_date} 的文件")
            return matched_files
            
        except Exception as e:
            log.error(f"查找日期时间戳文件时发生错误: {e}")
            return []
    
    def check_camera_images(self, camera_dir: str = "",
                           target_date: Optional[str] = None) -> Dict[str, Any]:
        """
        检查相机目录下的图片文件

        Args:
            camera_dir: 相机目录路径，为空时使用默认路径
            target_date: 目标日期 (YYYYMMDD格式)

        Returns:
            Dict: 检测结果
        """
        try:
            target_date = target_date or datetime.now().strftime('%Y%m%d')

            # 确定相机目录路径
            if not camera_dir:
                if self.use_adb:
                    camera_dir = "/sdcard/DCIM/Camera"  # Android默认相机目录
                else:
                    camera_dir = r"\DCIM\Camera"  # Windows默认相机目录

            log.info(f"检查相机目录 {camera_dir} 中日期为 {target_date} 的图片")

            # 构建完整路径
            if self.use_adb:
                full_camera_dir = camera_dir
                directory_exists = self._check_android_directory_exists(camera_dir)
            else:
                if not os.path.isabs(camera_dir):
                    full_camera_dir = os.path.join(self.base_path, camera_dir.lstrip('\\').lstrip('/'))
                else:
                    full_camera_dir = camera_dir
                directory_exists = os.path.exists(full_camera_dir)

            result = {
                'directory': full_camera_dir,
                'target_date': target_date,
                'directory_exists': directory_exists,
                'total_files': 0,
                'matched_files': [],
                'patterns_checked': list(self.DATETIME_PATTERNS.keys()),
                'check_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'device_mode': 'Android ADB' if self.use_adb else 'Local FileSystem'
            }

            if not result['directory_exists']:
                log.warning(f"相机目录不存在: {full_camera_dir}")
                # 如果是Android设备，尝试其他常见路径
                if self.use_adb:
                    alternative_paths = self.ANDROID_COMMON_PATHS.get('camera', [])
                    for alt_path in alternative_paths:
                        if alt_path != camera_dir and self._check_android_directory_exists(alt_path):
                            log.info(f"找到替代相机目录: {alt_path}")
                            result['directory'] = alt_path
                            result['directory_exists'] = True
                            full_camera_dir = alt_path
                            break

                if not result['directory_exists']:
                    return result

            # 检查各种模式
            for pattern_name in self.DATETIME_PATTERNS.keys():
                matched = self.find_datetime_files(full_camera_dir, pattern_name, target_date)
                result['matched_files'].extend(matched)

            # 统计总文件数
            all_files = self.scan_directory(full_camera_dir, recursive=False)
            result['total_files'] = len(all_files)

            log.info(f"检查完成，共找到 {len(result['matched_files'])} 个匹配的图片文件")
            return result

        except Exception as e:
            log.error(f"检查相机图片时发生错误: {e}")
            return {'error': str(e)}

    def check_recent_camera_image(self, camera_dir: str = r"\DCIM\Camera",
                                 time_threshold: int = 30) -> bool:
        """
        检查相机目录下是否存在以当前日期+时间戳命名且在指定时间内生成的图片

        Args:
            camera_dir: 相机目录路径，默认为 \DCIM\Camera
            time_threshold: 时间阈值（秒），默认30秒

        Returns:
            bool: 存在符合条件的图片返回True，否则返回False
        """
        try:
            current_time = datetime.now()
            current_date = current_time.strftime('%Y%m%d')

            # 确定相机目录路径
            if self.use_adb:
                if camera_dir == r"\DCIM\Camera":
                    camera_dir = "/sdcard/DCIM/Camera"  # Android默认相机目录
                full_camera_dir = camera_dir
                directory_exists = self._check_android_directory_exists(camera_dir)
            else:
                if not os.path.isabs(camera_dir):
                    full_camera_dir = os.path.join(self.base_path, camera_dir.lstrip('\\').lstrip('/'))
                else:
                    full_camera_dir = camera_dir
                directory_exists = os.path.exists(full_camera_dir)

            log.info(f"检查目录 {full_camera_dir} 中 {time_threshold} 秒内生成的当前日期图片")

            if not directory_exists:
                log.warning(f"相机目录不存在: {full_camera_dir}")
                # 如果是Android设备，尝试其他常见路径
                if self.use_adb:
                    alternative_paths = self.ANDROID_COMMON_PATHS.get('camera', [])
                    for alt_path in alternative_paths:
                        if alt_path != camera_dir and self._check_android_directory_exists(alt_path):
                            log.info(f"找到替代相机目录: {alt_path}")
                            full_camera_dir = alt_path
                            directory_exists = True
                            break

                if not directory_exists:
                    return False

            # 扫描目录获取所有文件
            all_files = self.scan_directory(full_camera_dir, recursive=False)

            # 检查每个文件是否符合条件
            for file_info in all_files:
                if 'error' in file_info:
                    continue

                filename = file_info['name']
                log.debug(f"检查文件: {filename}")
                # 检查文件名是否匹配IMG_YYYYMMDD_HHMMSS_SSS.jpg格式
                import re
                pattern = r'IMG_(\d{8})_(\d{6})_(\d{3})\.(jpg|jpeg|png|gif|bmp|webp)'
                match = re.match(pattern, filename, re.IGNORECASE)

                if match:
                    file_date = match.group(1)
                    file_time = match.group(2)
                    file_ms = match.group(3)
                    file_ext = match.group(4)

                    # 检查日期是否为当前日期
                    if file_date == current_date:
                        # 解析文件时间戳
                        try:
                            file_datetime_str = f"{file_date}_{file_time}"
                            file_datetime = datetime.strptime(file_datetime_str, '%Y%m%d_%H%M%S')

                            # 计算时间差
                            time_diff = (current_time - file_datetime).total_seconds()

                            log.debug(f"检查文件: {filename}, 时间差: {time_diff:.1f}秒")

                            # 检查是否在时间阈值内
                            if 0 <= time_diff <= time_threshold:
                                log.info(f"找到符合条件的图片: {filename} (生成于 {time_diff:.1f} 秒前)")
                                return True

                        except ValueError as e:
                            log.debug(f"解析文件时间失败: {filename}, 错误: {e}")
                            continue

            log.info(f"未找到 {time_threshold} 秒内生成的当前日期图片")
            return False

        except Exception as e:
            log.error(f"检查最近相机图片时发生错误: {e}")
            return False

    def check_recent_screenshot_image(self, screenshot_dir: str = r"\sdcard\Pictures\Screenshot",
                                     time_threshold: int = 30) -> bool:
        """
        检查截图目录下是否存在以当前日期+时间戳命名且在指定时间内生成的截图

        Args:
            screenshot_dir: 截图目录路径，默认为 \sdcard\Pictures\Screenshot
            time_threshold: 时间阈值（秒），默认30秒

        Returns:
            bool: 存在符合条件的截图返回True，否则返回False
        """
        try:
            current_time = datetime.now()
            current_date = current_time.strftime('%Y%m%d')

            # 确定截图目录路径
            if self.use_adb:
                if screenshot_dir == r"\sdcard\Pictures\Screenshot":
                    screenshot_dir = "/sdcard/Pictures/Screenshot"  # Android默认截图目录
                full_screenshot_dir = screenshot_dir
                directory_exists = self._check_android_directory_exists(screenshot_dir)
            else:
                if not os.path.isabs(screenshot_dir):
                    full_screenshot_dir = os.path.join(self.base_path, screenshot_dir.lstrip('\\').lstrip('/'))
                else:
                    full_screenshot_dir = screenshot_dir
                directory_exists = os.path.exists(full_screenshot_dir)

            log.info(f"检查目录 {full_screenshot_dir} 中 {time_threshold} 秒内生成的当前日期截图")

            if not directory_exists:
                log.warning(f"截图目录不存在: {full_screenshot_dir}")
                # 如果是Android设备，尝试其他常见路径
                if self.use_adb:
                    alternative_paths = self.ANDROID_COMMON_PATHS.get('screenshots', [])
                    for alt_path in alternative_paths:
                        if alt_path != screenshot_dir and self._check_android_directory_exists(alt_path):
                            log.info(f"找到替代截图目录: {alt_path}")
                            full_screenshot_dir = alt_path
                            directory_exists = True
                            break

                if not directory_exists:
                    return False

            # 扫描目录获取所有文件
            all_files = self.scan_directory(full_screenshot_dir, recursive=False)

            # 检查每个文件是否符合条件
            for file_info in all_files:
                if 'error' in file_info:
                    continue

                filename = file_info['name']

                # 检查文件名是否匹配Screenshot_YYYYMMDD-HHMMSS.jpg格式
                import re
                pattern = r'Screenshot_(\d{8})-(\d{6})\.(jpg|jpeg|png|gif|bmp|webp)'
                match = re.match(pattern, filename, re.IGNORECASE)

                if match:
                    file_date = match.group(1)
                    file_time = match.group(2)
                    file_ext = match.group(3)

                    # 检查日期是否为当前日期
                    if file_date == current_date:
                        # 解析文件时间戳
                        try:
                            file_datetime_str = f"{file_date}_{file_time}"
                            file_datetime = datetime.strptime(file_datetime_str, '%Y%m%d_%H%M%S')

                            # 计算时间差
                            time_diff = (current_time - file_datetime).total_seconds()

                            log.debug(f"检查截图文件: {filename}, 时间差: {time_diff:.1f}秒")

                            # 检查是否在时间阈值内
                            if 0 <= time_diff <= time_threshold:
                                log.info(f"找到符合条件的截图: {filename} (生成于 {time_diff:.1f} 秒前)")
                                return True

                        except ValueError as e:
                            log.debug(f"解析截图文件时间失败: {filename}, 错误: {e}")
                            continue

            log.info(f"未找到 {time_threshold} 秒内生成的当前日期截图")
            return False

        except Exception as e:
            log.error(f"检查最近截图时发生错误: {e}")
            return False

    def check_recent_screen_recording(self, recording_dir: str = r"\Movies\ScreenRecord",
                                     time_threshold: int = 30) -> bool:
        """
        检查屏幕录制目录下是否存在以当前日期+时间戳命名且在指定时间内生成的MP4文件

        Args:
            recording_dir: 屏幕录制目录路径，默认为 \Movies\ScreenRecord
            time_threshold: 时间阈值（秒），默认30秒

        Returns:
            bool: 存在符合条件的屏幕录制文件返回True，否则返回False
        """
        try:
            current_time = datetime.now()
            current_date = current_time.strftime('%Y%m%d')

            # 确定屏幕录制目录路径
            if self.use_adb:
                if recording_dir == r"\Movies\ScreenRecord":
                    recording_dir = "/sdcard/Movies/ScreenRecord"  # Android默认屏幕录制目录
                full_recording_dir = recording_dir
                directory_exists = self._check_android_directory_exists(recording_dir)
            else:
                if not os.path.isabs(recording_dir):
                    full_recording_dir = os.path.join(self.base_path, recording_dir.lstrip('\\').lstrip('/'))
                else:
                    full_recording_dir = recording_dir
                directory_exists = os.path.exists(full_recording_dir)

            log.info(f"检查目录 {full_recording_dir} 中 {time_threshold} 秒内生成的当前日期屏幕录制文件")

            if not directory_exists:
                log.warning(f"屏幕录制目录不存在: {full_recording_dir}")
                # 如果是Android设备，尝试其他常见路径
                if self.use_adb:
                    alternative_paths = [
                        "/sdcard/Movies",
                        "/sdcard/DCIM/ScreenRecord",
                        "/storage/emulated/0/Movies/ScreenRecord",
                        "/storage/emulated/0/Movies",
                        "/sdcard/Pictures/ScreenRecord"
                    ]
                    for alt_path in alternative_paths:
                        if alt_path != recording_dir and self._check_android_directory_exists(alt_path):
                            log.info(f"找到替代屏幕录制目录: {alt_path}")
                            full_recording_dir = alt_path
                            directory_exists = True
                            break

                if not directory_exists:
                    return False

            # 扫描目录获取所有文件
            all_files = self.scan_directory(full_recording_dir, recursive=False)

            # 检查每个文件是否符合条件
            for file_info in all_files:
                if 'error' in file_info:
                    continue

                filename = file_info['name']

                # 检查文件名是否匹配Screen_Recording_YYYYMMDD_HHMMSS.mp4格式
                import re
                pattern = r'Screen_Recording_(\d{8})_(\d{6})\.(mp4|MP4)'
                match = re.match(pattern, filename, re.IGNORECASE)

                if match:
                    file_date = match.group(1)
                    file_time = match.group(2)
                    file_ext = match.group(3)

                    # 检查日期是否为当前日期
                    if file_date == current_date:
                        # 解析文件时间戳
                        try:
                            file_datetime_str = f"{file_date}_{file_time}"
                            file_datetime = datetime.strptime(file_datetime_str, '%Y%m%d_%H%M%S')

                            # 计算时间差
                            time_diff = (current_time - file_datetime).total_seconds()

                            log.debug(f"检查屏幕录制文件: {filename}, 时间差: {time_diff:.1f}秒")

                            # 检查是否在时间阈值内
                            if 0 <= time_diff <= time_threshold:
                                log.info(f"找到符合条件的屏幕录制文件: {filename} (生成于 {time_diff:.1f} 秒前)")
                                return True

                        except ValueError as e:
                            log.debug(f"解析屏幕录制文件时间失败: {filename}, 错误: {e}")
                            continue

            log.info(f"未找到 {time_threshold} 秒内生成的当前日期屏幕录制文件")
            return False

        except Exception as e:
            log.error(f"检查最近屏幕录制文件时发生错误: {e}")
            return False

    def _check_android_directory_exists(self, directory: str) -> bool:
        """
        检查Android设备上的目录是否存在

        Args:
            directory: 目录路径

        Returns:
            bool: 目录是否存在
        """
        try:
            cmd = ["adb"]
            if self.device_id:
                cmd.extend(["-s", self.device_id])

            cmd.extend(["shell", "test", "-d", directory, "&&", "echo", "EXISTS"])

            success, output = DetectorUtils.execute_adb_command(cmd, timeout=10)
            return success and "EXISTS" in output

        except Exception as e:
            log.debug(f"检查Android目录时发生错误: {e}")
            return False

    def get_android_common_directories(self) -> Dict[str, List[str]]:
        """
        获取Android设备上的常见目录

        Returns:
            Dict: 常见目录字典
        """
        if not self.use_adb:
            log.warning("当前不是Android设备模式")
            return {}

        existing_dirs = {}

        for category, paths in self.ANDROID_COMMON_PATHS.items():
            existing_dirs[category] = []
            for path in paths:
                if self._check_android_directory_exists(path):
                    existing_dirs[category].append(path)

        return existing_dirs
    
    def export_results(self, results: Dict[str, Any], output_file: str = None) -> str:
        """
        导出检测结果到JSON文件
        
        Args:
            results: 检测结果
            output_file: 输出文件路径
            
        Returns:
            str: 输出文件路径
        """
        try:
            if output_file is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_file = f"tools/output/file_detection_{timestamp}.json"
            
            # 确保输出目录存在
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
            
            # 写入JSON文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            log.info(f"检测结果已导出到: {output_file}")
            return output_file
            
        except Exception as e:
            log.error(f"导出结果时发生错误: {e}")
            return ""


def main():
    """命令行主函数"""
    parser = argparse.ArgumentParser(description='文件检测工具 - 支持本地文件系统和Android设备')
    parser.add_argument('--directory', '-d', default='',
                       help='要检查的目录路径 (默认: 本地模式使用\\DCIM\\Camera，Android模式使用/sdcard/DCIM/Camera)')
    parser.add_argument('--date', '-t', help='目标日期 (YYYYMMDD格式，默认为当前日期)')
    parser.add_argument('--base-path', '-b', help='基础路径 (仅本地模式有效)')
    parser.add_argument('--output', '-o', help='输出文件路径')
    parser.add_argument('--pattern', '-p', default='IMG_YYYYMMDD_HHMMSS_SSS',
                       choices=list(FileDetector.DATETIME_PATTERNS.keys()),
                       help='文件名模式')

    # Android设备相关参数
    parser.add_argument('--android', '-a', action='store_true',
                       help='使用Android设备模式 (通过ADB检查设备文件)')
    parser.add_argument('--device-id', '-i', help='Android设备ID (为空时使用默认设备)')
    parser.add_argument('--list-devices', action='store_true',
                       help='列出连接的Android设备')
    parser.add_argument('--common-dirs', action='store_true',
                       help='显示Android设备上的常见目录')

    # 最近图片检查参数
    parser.add_argument('--check-recent', action='store_true',
                       help='检查是否存在最近生成的相机图片（返回True/False）')
    parser.add_argument('--check-recent-screenshot', action='store_true',
                       help='检查是否存在最近生成的截图（返回True/False）')
    parser.add_argument('--check-recent-recording', action='store_true',
                       help='检查是否存在最近生成的屏幕录制文件（返回True/False）')
    parser.add_argument('--time-threshold', '-T', type=int, default=30,
                       help='时间阈值（秒），默认30秒')

    args = parser.parse_args()

    # 列出设备
    if args.list_devices:
        try:
            cmd = ["adb", "devices"]
            success, output = DetectorUtils.execute_adb_command(cmd, timeout=5)
            if success:
                print("连接的Android设备:")
                lines = output.strip().split('\n')[1:]  # 跳过标题行
                for line in lines:
                    if line.strip() and '\t' in line:
                        parts = line.split('\t')
                        if len(parts) >= 2 and parts[1].strip() == 'device':
                            print(f"  - {parts[0].strip()}")
            else:
                print("无法获取设备列表")
        except Exception as e:
            print(f"获取设备列表失败: {e}")
        return

    # 创建文件检测器
    detector = FileDetector(
        base_path=args.base_path,
        use_adb=args.android,
        device_id=args.device_id
    )

    # 显示常见目录
    if args.common_dirs:
        if args.android:
            common_dirs = detector.get_android_common_directories()
            print("Android设备上的常见目录:")
            for category, paths in common_dirs.items():
                print(f"\n{category.upper()}:")
                for path in paths:
                    print(f"  - {path}")
        else:
            print("常见目录功能仅在Android模式下可用，请使用 --android 参数")
        return

    # 检查最近图片
    if args.check_recent:
        directory = args.directory
        if not directory:
            if args.android:
                directory = "/sdcard/DCIM/Camera"
            else:
                directory = r"\DCIM\Camera"

        result = detector.check_recent_camera_image(directory, args.time_threshold)

        print(f"\n=== 最近图片检查结果 ===")
        print(f"模式: {'Android ADB' if args.android else 'Local FileSystem'}")
        print(f"目录: {directory}")
        print(f"时间阈值: {args.time_threshold} 秒")
        print(f"结果: {'✅ True (找到最近图片)' if result else '❌ False (未找到最近图片)'}")

        if result:
            print(f"说明: 在 {args.time_threshold} 秒内找到了以当前日期+时间戳命名的图片")
        else:
            print(f"说明: 在 {args.time_threshold} 秒内未找到以当前日期+时间戳命名的图片")

        return

    # 检查最近截图
    if args.check_recent_screenshot:
        directory = args.directory
        if not directory:
            if args.android:
                directory = "/sdcard/Pictures/Screenshot"
            else:
                directory = r"\Pictures\Screenshots"

        result = detector.check_recent_screenshot_image(directory, args.time_threshold)

        print(f"\n=== 最近截图检查结果 ===")
        print(f"模式: {'Android ADB' if args.android else 'Local FileSystem'}")
        print(f"目录: {directory}")
        print(f"时间阈值: {args.time_threshold} 秒")
        print(f"结果: {'✅ True (找到最近截图)' if result else '❌ False (未找到最近截图)'}")

        if result:
            print(f"说明: 在 {args.time_threshold} 秒内找到了以当前日期+时间戳命名的截图")
        else:
            print(f"说明: 在 {args.time_threshold} 秒内未找到以当前日期+时间戳命名的截图")

        return

    # 检查最近屏幕录制
    if args.check_recent_recording:
        directory = args.directory
        if not directory:
            if args.android:
                directory = "/sdcard/Movies/ScreenRecord"
            else:
                directory = r"\Movies\ScreenRecord"

        result = detector.check_recent_screen_recording(directory, args.time_threshold)

        print(f"\n=== 最近屏幕录制检查结果 ===")
        print(f"模式: {'Android ADB' if args.android else 'Local FileSystem'}")
        print(f"目录: {directory}")
        print(f"时间阈值: {args.time_threshold} 秒")
        print(f"结果: {'✅ True (找到最近录制文件)' if result else '❌ False (未找到最近录制文件)'}")

        if result:
            print(f"说明: 在 {args.time_threshold} 秒内找到了以当前日期+时间戳命名的屏幕录制文件")
        else:
            print(f"说明: 在 {args.time_threshold} 秒内未找到以当前日期+时间戳命名的屏幕录制文件")

        return

    # 确定检查目录
    directory = args.directory
    if not directory:
        if args.android:
            directory = "/sdcard/DCIM/Camera"
        else:
            directory = r"\DCIM\Camera"

    # 执行检测
    if directory.lower().endswith('camera') or 'dcim' in directory.lower():
        # 相机目录检测
        results = detector.check_camera_images(directory, args.date)
    else:
        # 普通目录检测 - 构建结果格式与相机目录检测一致
        matched_files = detector.find_datetime_files(directory, args.pattern, args.date)

        if args.android:
            directory_exists = detector._check_android_directory_exists(directory)
        else:
            full_dir = os.path.join(detector.base_path, directory) if not os.path.isabs(directory) else directory
            directory_exists = os.path.exists(full_dir)

        results = {
            'directory': directory,
            'target_date': args.date or datetime.now().strftime('%Y%m%d'),
            'directory_exists': directory_exists,
            'total_files': len(detector.scan_directory(directory, recursive=False)),
            'matched_files': matched_files,
            'patterns_checked': [args.pattern],
            'check_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'device_mode': 'Android ADB' if args.android else 'Local FileSystem'
        }

    # 导出结果
    output_file = detector.export_results(results, args.output)

    # 打印摘要
    if isinstance(results, dict) and 'matched_files' in results:
        print(f"\n=== 检测摘要 ===")
        print(f"模式: {results.get('device_mode', '未知')}")
        print(f"目录: {results.get('directory', directory)}")
        print(f"目标日期: {results.get('target_date', args.date or '当前日期')}")
        print(f"目录存在: {'✅' if results.get('directory_exists') else '❌'}")
        print(f"总文件数: {results.get('total_files', 0)}")
        print(f"匹配文件数: {len(results['matched_files'])}")
        print(f"结果文件: {output_file}")

        if results['matched_files']:
            print(f"\n匹配的文件:")
            for file_info in results['matched_files'][:5]:  # 只显示前5个
                print(f"  - {file_info['name']}")
            if len(results['matched_files']) > 5:
                print(f"  ... 还有 {len(results['matched_files']) - 5} 个文件")
    else:
        print(f"\n=== 检测摘要 ===")
        print(f"模式: {'Android ADB' if args.android else 'Local FileSystem'}")
        print(f"目录: {directory}")
        print(f"目标日期: {args.date or '当前日期'}")
        print(f"匹配文件数: {len(results) if isinstance(results, list) else 0}")
        print(f"结果文件: {output_file}")


if __name__ == "__main__":
    # main()
    detector = FileDetector(use_adb=True)
    # today = datetime.now().strftime('%Y%m%d')
    # results = detector.check_camera_images("/sdcard/DCIM/Camera", today)
    # print(f"今天拍摄了 {len(results['matched_files'])} 张照片")

    results = detector.check_recent_camera_image("/sdcard/DCIM/Camera")
    # results = detector.check_recent_screenshot_image()
    # results = detector.check_recent_screen_recording()
    print(results)