{"name": "测试help me generate a picture of blue and gold landscape", "status": "passed", "description": "测试help me generate a picture of blue and gold landscape指令", "steps": [{"name": "执行命令: help me generate a picture of blue and gold landscape", "status": "passed", "steps": [{"name": "执行命令: help me generate a picture of blue and gold landscape", "status": "passed", "start": 1756798253741, "stop": 1756798276575}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "25831581-5f47-466b-821b-8ccbf89a7077-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7244d2e3-ad1c-4875-993a-995ff4c3c0cd-attachment.png", "type": "image/png"}], "start": 1756798276575, "stop": 1756798276784}], "start": 1756798253741, "stop": 1756798276785}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756798276785, "stop": 1756798276786}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1bf70834-0c57-4293-8a86-0f92152e253c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "82baa8a7-c2ac-41ae-a34d-d389707c308a-attachment.png", "type": "image/png"}], "start": 1756798276786, "stop": 1756798277015}], "attachments": [{"name": "stdout", "source": "54ddb38b-f482-406b-9c5e-4f18b73fe57b-attachment.txt", "type": "text/plain"}], "start": 1756798253741, "stop": 1756798277015, "uuid": "87e6988d-b44f-4e74-b785-cf908a136ee4", "historyId": "eadc304b3069d4918c06805d847a62d7", "testCaseId": "eadc304b3069d4918c06805d847a62d7", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_blue_and_gold_landscape.TestEllaOpenPlayPoliticalNews#test_help_me_generate_a_picture_of_blue_and_gold_landscape", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_generate_a_picture_of_blue_and_gold_landscape"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_blue_and_gold_landscape"}]}