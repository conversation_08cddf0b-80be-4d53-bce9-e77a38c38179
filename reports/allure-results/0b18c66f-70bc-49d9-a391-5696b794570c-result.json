{"name": "测试decrease the volume to the minimun能正常执行", "status": "passed", "description": "decrease the volume to the minimun", "steps": [{"name": "执行命令: decrease the volume to the minimun", "status": "passed", "steps": [{"name": "执行命令: decrease the volume to the minimun", "status": "passed", "start": 1756789363383, "stop": 1756789386141}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a2f7b45f-b34f-4ff4-a84e-32a7c9d9d84c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b8fc8be5-d79a-417b-9529-0ba56eba9b69-attachment.png", "type": "image/png"}], "start": 1756789386141, "stop": 1756789386370}], "start": 1756789363383, "stop": 1756789386370}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756789386370, "stop": 1756789386371}, {"name": "验证应用已打开", "status": "passed", "start": 1756789386371, "stop": 1756789386371}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "82952b3e-60bd-4ea8-a41a-91a6f7d858ff-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ffaae111-fc7e-47eb-b0f8-9a9e6d57ad5d-attachment.png", "type": "image/png"}], "start": 1756789386371, "stop": 1756789386595}], "attachments": [{"name": "stdout", "source": "cffdb06d-86be-4b22-8f21-a56848922f9a-attachment.txt", "type": "text/plain"}], "start": 1756789363382, "stop": 1756789386595, "uuid": "81a54220-3603-4bcd-864c-82aa33cc5764", "historyId": "4aad505422b0d8c87c499477cd89ff5d", "testCaseId": "4aad505422b0d8c87c499477cd89ff5d", "fullName": "testcases.test_ella.system_coupling.test_decrease_the_volume_to_the_minimun.TestEllaDecreaseVolumeMinimun#test_decrease_the_volume_to_the_minimun", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_decrease_the_volume_to_the_minimun"}, {"name": "subSuite", "value": "TestEllaDecreaseVolumeMinimun"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_decrease_the_volume_to_the_minimun"}]}