{"name": "测试what time is it in china能正常执行", "status": "passed", "description": "what time is it in china", "steps": [{"name": "执行命令: what time is it in china", "status": "passed", "steps": [{"name": "执行命令: what time is it in china", "status": "passed", "start": 1756804433091, "stop": 1756804457150}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "10e14d51-9883-4f25-bd5d-1638612b89e5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "74c2507d-3706-490b-8a4d-dd5a90cb8637-attachment.png", "type": "image/png"}], "start": 1756804457150, "stop": 1756804457387}], "start": 1756804433090, "stop": 1756804457387}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756804457387, "stop": 1756804457389}, {"name": "验证应用已打开", "status": "passed", "start": 1756804457389, "stop": 1756804457389}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "81a5d118-accf-4307-a790-aa318a6fb161-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5b04d3ec-122f-41e3-973c-eb9e13f62307-attachment.png", "type": "image/png"}], "start": 1756804457389, "stop": 1756804457671}], "attachments": [{"name": "stdout", "source": "35da31fd-c7ef-4080-8167-cf219d56dd73-attachment.txt", "type": "text/plain"}], "start": 1756804433090, "stop": 1756804457673, "uuid": "2df584b6-2426-46de-8e03-1282ed62b5e8", "historyId": "7ebb09688c661659cc2b4a26d54a347f", "testCaseId": "7ebb09688c661659cc2b4a26d54a347f", "fullName": "testcases.test_ella.unsupported_commands.test_what_time_is_it_in_china.TestEllaWhatTimeIsItChina#test_what_time_is_it_in_china", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_what_time_is_it_in_china"}, {"name": "subSuite", "value": "TestEllaWhatTimeIsItChina"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_what_time_is_it_in_china"}]}