{"name": "测试play football video by youtube", "status": "passed", "description": "测试play football video by youtube指令", "steps": [{"name": "执行命令: play football video by youtube", "status": "passed", "steps": [{"name": "执行命令: play football video by youtube", "status": "passed", "start": 1756800435359, "stop": 1756800463913}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a7bcd6b3-636e-44a0-ab0c-0c91f93297bf-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5e606726-ca68-49de-a2b1-5db030d40b89-attachment.png", "type": "image/png"}], "start": 1756800463913, "stop": 1756800464192}], "start": 1756800435359, "stop": 1756800464192}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756800464192, "stop": 1756800464194}, {"name": "验证youtube已打开", "status": "passed", "start": 1756800464194, "stop": 1756800464194}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "534b25df-3af4-4ed1-a716-abcfce55bd73-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "82b36b17-3890-46ee-916d-21a0ac8d02bd-attachment.png", "type": "image/png"}], "start": 1756800464194, "stop": 1756800464432}], "attachments": [{"name": "stdout", "source": "82db548b-20a6-444e-adf1-3193968de65a-attachment.txt", "type": "text/plain"}], "start": 1756800435359, "stop": 1756800464433, "uuid": "2ecb214a-5d1a-4e3f-87cb-d816b4a6d9f2", "historyId": "ed94d8d97b63a623a1f6438b57774ff1", "testCaseId": "ed94d8d97b63a623a1f6438b57774ff1", "fullName": "testcases.test_ella.unsupported_commands.test_play_football_video_by_youtube.TestEllaOpenPlayPoliticalNews#test_play_football_video_by_youtube", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_football_video_by_youtube"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_football_video_by_youtube"}]}