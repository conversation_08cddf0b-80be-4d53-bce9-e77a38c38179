"""
设备感知的pytest配置
支持多设备并行测试
"""
import os
import sys
import pytest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logger import log


def pytest_addoption(parser):
    """添加pytest命令行选项"""
    parser.addoption(
        "--device-id",
        action="store",
        default=None,
        help="指定测试设备ID"
    )


@pytest.fixture(scope="function")
def ella_app():
    """设备感知的Ella应用fixture"""
    # 获取设备ID
    device_id = os.environ.get("PYTEST_DEVICE_ID")
    
    if device_id:
        log.info(f"🔧 使用指定设备: {device_id}")
        # 使用多设备版本
        from temp.multi_device_base_test import MultiDeviceSimpleEllaTest
        test_instance = MultiDeviceSimpleEllaTest(device_id=device_id)
        ella_app_generator = test_instance.ella_app()
        ella_app = next(ella_app_generator)
        
        try:
            yield ella_app
        finally:
            try:
                next(ella_app_generator)
            except StopIteration:
                pass
    else:
        log.info("🔧 使用默认设备")
        # 使用原始版本
        from pages.apps.ella.dialogue_page import EllaDialoguePage
        from testcases.test_ella.base_ella_test import BaseEllaTest
        
        base_test = BaseEllaTest()
        base_test.setup_batch_test_screen()
        
        ella_page = EllaDialoguePage()
        
        try:
            base_test.clear_all_running_processes()
            assert ella_page.start_app(), "Ella应用启动失败"
            assert ella_page.wait_for_page_load(timeout=15), "Ella页面加载失败"
            
            log.info("✅ Ella应用启动成功")
            yield ella_page
            
        except Exception as e:
            log.error(f"❌ Ella应用启动异常: {e}")
            pytest.fail(f"Ella应用启动异常: {e}")
        finally:
            try:
                ella_page.stop_app()
            except Exception as e:
                log.warning(f"⚠️ 停止应用异常: {e}")
