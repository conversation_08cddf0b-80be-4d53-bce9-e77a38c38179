{"name": "测试set lockscreen passwords返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['sorry']，实际响应: '['set lockscreen passwords', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Password & Security | Lockscreen Passwords | Slide to Unlock | Fingerprint | Unenrolled | Face Unlock | Unenrolled | Mobile Anti-Theft | System Security | More Settings']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_set_lockscreen_passwords.TestEllaSetLockscreenPasswords object at 0x0000018BC6622E60>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC9DCF430>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_lockscreen_passwords(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['sorry']，实际响应: '['set lockscreen passwords', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Password & Security | Lockscreen Passwords | Slide to Unlock | Fingerprint | Unenrolled | Face Unlock | Unenrolled | Mobile Anti-Theft | System Security | More Settings']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_lockscreen_passwords.py:34: AssertionError"}, "description": "验证set lockscreen passwords指令返回预期的不支持响应", "steps": [{"name": "执行命令: set lockscreen passwords", "status": "passed", "steps": [{"name": "执行命令: set lockscreen passwords", "status": "passed", "start": 1756802124977, "stop": 1756802153556}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "abf745fb-acc8-464d-a886-fc683528e6e9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8213ed8c-7b76-4457-8636-c20ffc0dc4d0-attachment.png", "type": "image/png"}], "start": 1756802153556, "stop": 1756802153791}], "start": 1756802124977, "stop": 1756802153791}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['sorry']，实际响应: '['set lockscreen passwords', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Password & Security | Lockscreen Passwords | Slide to Unlock | Fingerprint | Unenrolled | Face Unlock | Unenrolled | Mobile Anti-Theft | System Security | More Settings']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_lockscreen_passwords.py\", line 34, in test_set_lockscreen_passwords\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756802153791, "stop": 1756802153793}], "attachments": [{"name": "stdout", "source": "7838e268-7717-4f4f-9a5d-a3678f37bc9e-attachment.txt", "type": "text/plain"}], "start": 1756802124977, "stop": 1756802153794, "uuid": "899610fb-dc03-4e97-bee8-190405aefab6", "historyId": "89b134ac1374e88187e793daf9f8fcab", "testCaseId": "89b134ac1374e88187e793daf9f8fcab", "fullName": "testcases.test_ella.unsupported_commands.test_set_lockscreen_passwords.TestEllaSetLockscreenPasswords#test_set_lockscreen_passwords", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_lockscreen_passwords"}, {"name": "subSuite", "value": "TestEllaSetLockscreenPasswords"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_lockscreen_passwords"}]}