{"name": "测试Switch to davido voice能正常执行", "status": "passed", "description": "Switch to davido voice", "steps": [{"name": "执行命令: Switch to davido voice", "status": "passed", "steps": [{"name": "执行命令: Switch to davido voice", "status": "passed", "start": 1756803326734, "stop": 1756803351008}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a077106f-db23-4c5b-b9de-d8599d21330d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3df4f231-e396-4335-b26c-fed8839d063c-attachment.png", "type": "image/png"}], "start": 1756803351008, "stop": 1756803351254}], "start": 1756803326734, "stop": 1756803351254}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756803351254, "stop": 1756803351255}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "be960517-b1b7-4d62-b66a-4d3f16564189-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4487addc-0825-4895-969e-ee4aadd551f9-attachment.png", "type": "image/png"}], "start": 1756803351255, "stop": 1756803351497}], "attachments": [{"name": "stdout", "source": "d173ce24-7318-41a1-8aed-5921b116bc40-attachment.txt", "type": "text/plain"}], "start": 1756803326734, "stop": 1756803351498, "uuid": "77a63b6f-70c6-4aff-80d9-54f9e3f65ea0", "historyId": "cce948f7c988b6f22bd4e8d08ca74deb", "testCaseId": "cce948f7c988b6f22bd4e8d08ca74deb", "fullName": "testcases.test_ella.unsupported_commands.test_switch_to_davido_voice.TestEllaSwitchDavidoVoice#test_switch_to_davido_voice", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_switch_to_davido_voice"}, {"name": "subSuite", "value": "TestEllaSwitchDavidoVoice"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_switch_to_davido_voice"}]}