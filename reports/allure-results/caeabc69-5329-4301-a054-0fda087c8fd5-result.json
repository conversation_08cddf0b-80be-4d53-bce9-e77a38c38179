{"name": "测试play music by visha", "status": "passed", "description": "测试play music by visha指令", "steps": [{"name": "执行命令: play music by visha", "status": "passed", "steps": [{"name": "执行命令: play music by visha", "status": "passed", "start": 1756786197619, "stop": 1756786230606}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "41429f08-f9a0-428b-b46d-f0af28c2ae65-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4b4cd646-1c86-42f3-9b7a-63b022563513-attachment.png", "type": "image/png"}], "start": 1756786230606, "stop": 1756786230808}], "start": 1756786197618, "stop": 1756786230808}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756786230808, "stop": 1756786230810}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8412ccd3-b413-4909-9689-294e12ab1828-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "dc7bfb33-6135-4b6d-a07b-9ded460e84b8-attachment.png", "type": "image/png"}], "start": 1756786230810, "stop": 1756786230984}], "attachments": [{"name": "stdout", "source": "777bca39-4c63-4d2c-9f64-4029978b9e5c-attachment.txt", "type": "text/plain"}], "start": 1756786197618, "stop": 1756786230984, "uuid": "9a8a5be5-d885-4e2a-8ec0-8a0e878e176e", "historyId": "85e8e199dba3ac2c9d89e132805a4404", "testCaseId": "85e8e199dba3ac2c9d89e132805a4404", "fullName": "testcases.test_ella.dialogue.test_play_music_by_visha.TestEllaOpenPlayPoliticalNews#test_play_music_by_visha", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_music_by_visha"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_music_by_visha"}]}