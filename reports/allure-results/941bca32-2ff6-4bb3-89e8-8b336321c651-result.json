{"name": "测试turn on location services能正常执行", "status": "passed", "description": "turn on location services", "steps": [{"name": "执行命令: turn on location services", "status": "passed", "steps": [{"name": "执行命令: turn on location services", "status": "passed", "start": 1756792818230, "stop": 1756792839614}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dc0f03b4-2bfa-40c8-ae36-1ad4687ee779-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f69207d6-34f8-46f1-804d-10e130c07122-attachment.png", "type": "image/png"}], "start": 1756792839614, "stop": 1756792839818}], "start": 1756792818230, "stop": 1756792839819}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756792839819, "stop": 1756792839820}, {"name": "验证应用已打开", "status": "passed", "start": 1756792839820, "stop": 1756792839820}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "09041bcb-545e-4f32-8fba-cb4067df2e22-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "394a0f50-eda2-49fc-b7a7-e91c05d29906-attachment.png", "type": "image/png"}], "start": 1756792839820, "stop": 1756792840011}], "attachments": [{"name": "stdout", "source": "1dd86bc3-1367-49d3-82d6-be19f0de7ebd-attachment.txt", "type": "text/plain"}], "start": 1756792818230, "stop": 1756792840011, "uuid": "0aa393a8-300e-4a5e-bb33-855ec1c4b7e2", "historyId": "8a87a1d0f534afc4770901f3d1dfa316", "testCaseId": "8a87a1d0f534afc4770901f3d1dfa316", "fullName": "testcases.test_ella.system_coupling.test_turn_on_location_services.TestEllaTurnLocationServices#test_turn_on_location_services", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_location_services"}, {"name": "subSuite", "value": "TestEllaTurnLocationServices"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_location_services"}]}