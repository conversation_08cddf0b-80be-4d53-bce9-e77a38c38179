{"name": "测试Translate the content written on the picture into French", "status": "failed", "statusDetails": {"message": "AssertionError: 命令执行失败: Translate the content written on the picture into French", "trace": "self = <testcases.test_ella.test_ask_screen.translation.test_translate_the_content_written_on_the_picture_into_french.TestAskScreenTranslateContentWrittenPictureIntoFrench object at 0x0000018BC6302380>\nella_floating_page = <pages.apps.ella.floating_page.EllaFloatingPage object at 0x0000018BC828D210>\n\n    @allure.title(\"测试Translate the content written on the picture into French\")\n    @allure.description(\"测试Ask Screen功能: Translate the content written on the picture into French\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_translate_the_content_written_on_the_picture_into_french(self, ella_floating_page):\n        \"\"\"测试Translate the content written on the picture into French命令\"\"\"\n        command = \"Translate the content written on the picture into French\"\n        expected_keywords = ['Comment allez-vous', 'Unable to parse this content']\n    \n        # 数据准备\n        with allure.step(\"准备测试数据\"):\n            from tools.gallery_cleaner import cleaner\n            # 删除已有图片\n            cleaner.quick_clear_all()\n    \n            from tools.file_pusher import file_pusher\n            # 推送测试图片到设备\n            push_result = file_pusher.push_ask_screen_image(\"Translate_the_content_written_on_the_picture_into_French\")\n            assert push_result, f\"推送图片失败: Translate_the_content_written_on_the_picture_into_French\"\n    \n            # 打开图库并选择图片\n            photos_page = AiGalleryPhotosPage()\n            result = photos_page.start_app()\n            if result:\n                photos_page.wait_for_page_load()\n                photos_page.click_photo()\n    \n        # 执行命令并验证\n        with allure.step(f\"执行Ask Screen命令: {command}\"):\n>           success, response_texts, verification_result = self.simple_floating_command_test(\n                ella_floating_page, command, expected_keywords, verify_response=True\n            )\n\ntestcases\\test_ella\\test_ask_screen\\translation\\test_translate_the_content_written_on_the_picture_into_french.py:46: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\ntestcases\\test_ella\\test_ask_screen\\base_ask_screen_test.py:358: in simple_floating_command_test\n    success, response_texts, verification_result = self.execute_floating_command_and_verify(\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <testcases.test_ella.test_ask_screen.translation.test_translate_the_content_written_on_the_picture_into_french.TestAskScreenTranslateContentWrittenPictureIntoFrench object at 0x0000018BC6302380>\nfloating_page = <pages.apps.ella.floating_page.EllaFloatingPage object at 0x0000018BC828D210>, command = 'Translate the content written on the picture into French'\nexpected_keywords = ['Comment allez-vous', 'Unable to parse this content'], verify_response = True, response_timeout = None\n\n    def execute_floating_command_and_verify(self, floating_page: EllaFloatingPage, command: str,\n                                            expected_keywords: Optional[List[str]] = None,\n                                            verify_response: bool = True,\n                                            response_timeout: int = None) -> tuple:\n        \"\"\"\n        在浮窗中执行命令并验证结果的核心方法\n    \n        Args:\n            floating_page: Ella浮窗页面实例\n            command: 要执行的命令\n            expected_keywords: 期望在响应中包含的关键词列表\n            verify_response: 是否验证响应内容\n            response_timeout: 响应等待超时时间，None使用默认值\n    \n        Returns:\n            tuple: (success, response_texts, verification_result)\n        \"\"\"\n        try:\n            log.info(f\"在浮窗中执行命令并验证: {command}\")\n    \n            # # 1. 确保浮窗就绪\n            # with allure.step(\"确保浮窗就绪\"):\n            #     if not self.ensure_floating_window_ready(floating_page):\n            #         raise AssertionError(\"浮窗未就绪\")\n            #     log.info(\"✅ 浮窗已就绪\")\n    \n            # 2. 执行命令\n            with allure.step(f\"执行命令: {command}\"):\n                success = self._execute_floating_command_with_retry(floating_page, command)\n                if not success:\n>                   raise AssertionError(f\"命令执行失败: {command}\")\nE                   AssertionError: 命令执行失败: Translate the content written on the picture into French\n\ntestcases\\test_ella\\test_ask_screen\\base_ask_screen_test.py:118: AssertionError"}, "description": "测试Ask Screen功能: Translate the content written on the picture into French", "steps": [{"name": "准备测试数据", "status": "passed", "start": 1756794190854, "stop": 1756794199928}, {"name": "执行Ask Screen命令: Translate the content written on the picture into French", "status": "failed", "statusDetails": {"message": "AssertionError: 命令执行失败: Translate the content written on the picture into French\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\test_ask_screen\\translation\\test_translate_the_content_written_on_the_picture_into_french.py\", line 46, in test_translate_the_content_written_on_the_picture_into_french\n    success, response_texts, verification_result = self.simple_floating_command_test(\n  File \"D:\\app_test\\testcases\\test_ella\\test_ask_screen\\base_ask_screen_test.py\", line 358, in simple_floating_command_test\n    success, response_texts, verification_result = self.execute_floating_command_and_verify(\n  File \"D:\\app_test\\testcases\\test_ella\\test_ask_screen\\base_ask_screen_test.py\", line 118, in execute_floating_command_and_verify\n    raise AssertionError(f\"命令执行失败: {command}\")\n"}, "steps": [{"name": "执行浮窗命令: Translate the content written on the picture into French", "status": "failed", "statusDetails": {"message": "AssertionError: 命令执行失败: Translate the content written on the picture into French\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\test_ask_screen\\base_ask_screen_test.py\", line 358, in simple_floating_command_test\n    success, response_texts, verification_result = self.execute_floating_command_and_verify(\n  File \"D:\\app_test\\testcases\\test_ella\\test_ask_screen\\base_ask_screen_test.py\", line 118, in execute_floating_command_and_verify\n    raise AssertionError(f\"命令执行失败: {command}\")\n"}, "steps": [{"name": "执行命令: Translate the content written on the picture into French", "status": "failed", "statusDetails": {"message": "AssertionError: 命令执行失败: Translate the content written on the picture into French\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\test_ask_screen\\base_ask_screen_test.py\", line 118, in execute_floating_command_and_verify\n    raise AssertionError(f\"命令执行失败: {command}\")\n"}, "start": 1756794199929, "stop": 1756794232707}], "attachments": [{"name": "floating_command_error", "source": "efc06c15-e89e-42bd-897d-66c77cd2bb62-attachment.png", "type": "image/png"}], "start": 1756794199929, "stop": 1756794232925}], "start": 1756794199929, "stop": 1756794232925}], "attachments": [{"name": "stdout", "source": "f4b5b1b2-a2de-4f6b-a5cb-bf5b43a954b9-attachment.txt", "type": "text/plain"}], "start": 1756794190854, "stop": 1756794232925, "uuid": "c5ea0126-92b4-446c-a7ba-05a528916c94", "historyId": "bd0fc59491cba87a8192cc6066cdbab0", "testCaseId": "bd0fc59491cba87a8192cc6066cdbab0", "fullName": "testcases.test_ella.test_ask_screen.translation.test_translate_the_content_written_on_the_picture_into_french.TestAskScreenTranslateContentWrittenPictureIntoFrench#test_translate_the_content_written_on_the_picture_into_french", "labels": [{"name": "epic", "value": "Ella浮窗测试"}, {"name": "story", "value": "翻译功能"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ask Screen功能"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.test_ask_screen.translation"}, {"name": "suite", "value": "test_translate_the_content_written_on_the_picture_into_french"}, {"name": "subSuite", "value": "TestAskScreenTranslateContentWrittenPictureIntoFrench"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_ask_screen.translation.test_translate_the_content_written_on_the_picture_into_french"}]}