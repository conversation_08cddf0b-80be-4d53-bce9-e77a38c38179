{"name": "测试switching charging speed能正常执行", "status": "passed", "description": "switching charging speed", "steps": [{"name": "执行命令: switching charging speed", "status": "passed", "steps": [{"name": "执行命令: switching charging speed", "status": "passed", "start": 1756803500113, "stop": 1756803528791}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d733df04-6a20-4468-9adc-5125036e6a05-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b77fb151-6176-42fa-b2d2-99c3405ad106-attachment.png", "type": "image/png"}], "start": 1756803528791, "stop": 1756803529041}], "start": 1756803500113, "stop": 1756803529042}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756803529042, "stop": 1756803529044}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d15fadf7-ad88-4435-87b0-1e83e9c6453e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c97124fb-cfbb-46f3-a3a8-bfcae6aeeaeb-attachment.png", "type": "image/png"}], "start": 1756803529044, "stop": 1756803529300}], "attachments": [{"name": "stdout", "source": "78e1d5a6-8be0-4fbc-9218-a43b337a0bd6-attachment.txt", "type": "text/plain"}], "start": 1756803500113, "stop": 1756803529301, "uuid": "1a4a3443-6f30-43a4-ab6c-6c445de2229a", "historyId": "e8c3c7bb72cf538a9e89a7b790c5e689", "testCaseId": "e8c3c7bb72cf538a9e89a7b790c5e689", "fullName": "testcases.test_ella.unsupported_commands.test_switching_charging_speed.TestEllaSwitchingChargingSpeed#test_switching_charging_speed", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_switching_charging_speed"}, {"name": "subSuite", "value": "TestEllaSwitchingChargingSpeed"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_switching_charging_speed"}]}