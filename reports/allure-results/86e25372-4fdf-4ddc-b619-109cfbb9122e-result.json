{"name": "测试increase the volume to the maximun能正常执行", "status": "passed", "description": "increase the volume to the maximun", "steps": [{"name": "执行命令: increase the volume to the maximun", "status": "passed", "steps": [{"name": "执行命令: increase the volume to the maximun", "status": "passed", "start": 1756789752104, "stop": 1756789772367}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "193e782b-2ca2-4f00-94ee-bdceec685cbf-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3845c74c-7a43-48da-8f23-bb03c77f8979-attachment.png", "type": "image/png"}], "start": 1756789772367, "stop": 1756789772571}], "start": 1756789752104, "stop": 1756789772571}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756789772571, "stop": 1756789772572}, {"name": "验证应用已打开", "status": "passed", "start": 1756789772572, "stop": 1756789772572}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6f3be7f8-4ae1-4db3-a191-b3d2f4ad53a3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "bfaf7b52-79b7-4abf-92a7-17905aa106d4-attachment.png", "type": "image/png"}], "start": 1756789772572, "stop": 1756789772809}], "attachments": [{"name": "stdout", "source": "66a0d4f1-60e0-46c3-9402-182da68bbf1c-attachment.txt", "type": "text/plain"}], "start": 1756789752104, "stop": 1756789772809, "uuid": "c7b0778c-912a-42d4-a788-767bfa9b4370", "historyId": "563b9c2c8d2e1d68901eeac733e12913", "testCaseId": "563b9c2c8d2e1d68901eeac733e12913", "fullName": "testcases.test_ella.system_coupling.test_increase_the_volume_to_the_maximun.TestEllaIncreaseVolumeMaximun#test_increase_the_volume_to_the_maximun", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_increase_the_volume_to_the_maximun"}, {"name": "subSuite", "value": "TestEllaIncreaseVolumeMaximun"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_increase_the_volume_to_the_maximun"}]}