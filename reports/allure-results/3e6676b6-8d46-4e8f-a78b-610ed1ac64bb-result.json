{"name": "测试set customized cover screen返回正确的不支持响应", "status": "passed", "description": "验证set customized cover screen指令返回预期的不支持响应", "steps": [{"name": "执行命令: set customized cover screen", "status": "passed", "steps": [{"name": "执行命令: set customized cover screen", "status": "passed", "start": 1756801688828, "stop": 1756801711001}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d0d8f09c-68a4-40b0-aaca-226097c08038-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a5e049fc-3600-4ebf-9e26-e01a4dcbcf59-attachment.png", "type": "image/png"}], "start": 1756801711002, "stop": 1756801711236}], "start": 1756801688828, "stop": 1756801711236}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756801711236, "stop": 1756801711236}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "54d9828f-d611-490e-b4f5-24145d025abf-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c41fb390-1193-4b34-8ff8-d522fecf2a5b-attachment.png", "type": "image/png"}], "start": 1756801711236, "stop": 1756801711478}], "attachments": [{"name": "stdout", "source": "90b96d15-0f67-431b-ab8d-9b279d7cebac-attachment.txt", "type": "text/plain"}], "start": 1756801688821, "stop": 1756801711480, "uuid": "3cf166fd-d4b6-441d-8a5a-dd41b79b0d3c", "historyId": "104cf8a7ef102b6850b6d14f4cb14052", "testCaseId": "104cf8a7ef102b6850b6d14f4cb14052", "fullName": "testcases.test_ella.unsupported_commands.test_set_customized_cover_screen.TestEllaSetCustomizedCoverScreen#test_set_customized_cover_screen", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_customized_cover_screen"}, {"name": "subSuite", "value": "TestEllaSetCustomizedCoverScreen"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_customized_cover_screen"}]}