{"name": "测试open contact命令 - 简洁版本", "status": "passed", "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open app", "status": "passed", "steps": [{"name": "执行命令: open app", "status": "passed", "start": 1756786039977, "stop": 1756786062958}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4aeb6d73-b0fb-4605-8152-93687fc42d79-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7bf7ec2a-2794-45a6-8475-532beb8ed0b1-attachment.png", "type": "image/png"}], "start": 1756786062958, "stop": 1756786063175}], "start": 1756786039977, "stop": 1756786063175}, {"name": "验证响应包含Done", "status": "passed", "start": 1756786063175, "stop": 1756786063177}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a3ec53cd-a799-4bc5-840b-14014f2fe601-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "036c714b-75ed-472a-ba38-9d063a529cb6-attachment.png", "type": "image/png"}], "start": 1756786063177, "stop": 1756786063371}], "attachments": [{"name": "stdout", "source": "7d4904cb-b40c-4830-b73a-3951853005d5-attachment.txt", "type": "text/plain"}], "start": 1756786039977, "stop": 1756786063372, "uuid": "ec9664e1-d03b-44f8-a8ee-020b12645b12", "historyId": "f5346ff0fa4cb76e4b6ceea6116693ee", "testCaseId": "f5346ff0fa4cb76e4b6ceea6116693ee", "fullName": "testcases.test_ella.dialogue.test_open_app.TestEllaCommandConcise#test_open_app", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "打开"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_open_app"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_open_app"}]}