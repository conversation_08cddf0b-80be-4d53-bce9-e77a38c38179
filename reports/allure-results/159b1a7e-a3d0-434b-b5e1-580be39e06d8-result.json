{"name": "测试play the album", "status": "passed", "description": "测试play the album指令", "steps": [{"name": "执行命令: play the album", "status": "passed", "steps": [{"name": "执行命令: play the album", "status": "passed", "start": 1756800661721, "stop": 1756800696282}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "646ca358-a870-4f50-bd76-781bc6c6fc5f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "378bd083-e3dc-4961-ae28-b1466ce97f66-attachment.png", "type": "image/png"}], "start": 1756800696282, "stop": 1756800696518}], "start": 1756800661721, "stop": 1756800696518}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756800696518, "stop": 1756800696520}, {"name": "验证youtube已打开", "status": "passed", "start": 1756800696520, "stop": 1756800696520}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "aac3627f-6e49-411e-87fc-************-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0de88765-db49-40d7-9974-56127ea8fb09-attachment.png", "type": "image/png"}], "start": 1756800696520, "stop": 1756800696736}], "attachments": [{"name": "stdout", "source": "66258198-928d-4ea8-b3b3-7c71d73e3a44-attachment.txt", "type": "text/plain"}], "start": 1756800661721, "stop": 1756800696736, "uuid": "8933d343-058a-48e6-9ac1-d99e3cd60a9b", "historyId": "ab0169efb30d26689cbce230ff455598", "testCaseId": "ab0169efb30d26689cbce230ff455598", "fullName": "testcases.test_ella.unsupported_commands.test_play_the_album.TestEllaOpenPlayPoliticalNews#test_play_the_album", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_the_album"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_the_album"}]}