{"name": "测试disable touch optimization返回正确的不支持响应", "status": "passed", "description": "验证disable touch optimization指令返回预期的不支持响应", "steps": [{"name": "执行命令: disable touch optimization", "status": "passed", "steps": [{"name": "执行命令: disable touch optimization", "status": "passed", "start": 1756796618926, "stop": 1756796640952}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "249ef707-7a5a-4dd9-b7d4-cd48867649aa-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3bf47bce-d9b1-40b2-a5d4-54fe52924140-attachment.png", "type": "image/png"}], "start": 1756796640952, "stop": 1756796641170}], "start": 1756796618926, "stop": 1756796641170}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756796641170, "stop": 1756796641171}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9adfb89e-034b-45e0-9b59-6ec36babc551-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "45cce560-70fb-48fa-ae42-b89d79558fe1-attachment.png", "type": "image/png"}], "start": 1756796641171, "stop": 1756796641360}], "attachments": [{"name": "stdout", "source": "7403ab4e-b27b-4b22-81e5-293dadb78c59-attachment.txt", "type": "text/plain"}], "start": 1756796618926, "stop": 1756796641361, "uuid": "97333de8-b8af-453e-ba97-2a6cdb018294", "historyId": "0b659537bc9c9b47c2c23f702fadd56b", "testCaseId": "0b659537bc9c9b47c2c23f702fadd56b", "fullName": "testcases.test_ella.unsupported_commands.test_disable_touch_optimization.TestEllaDisableTouchOptimization#test_disable_touch_optimization", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_touch_optimization"}, {"name": "subSuite", "value": "TestEllaDisableTouchOptimization"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_touch_optimization"}]}