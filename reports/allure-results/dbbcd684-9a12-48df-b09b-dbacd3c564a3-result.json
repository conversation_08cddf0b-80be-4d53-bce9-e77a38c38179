{"name": "测试set screen to maximum brightness能正常执行", "status": "passed", "description": "set screen to maximum brightness", "steps": [{"name": "执行命令: set screen to maximum brightness", "status": "passed", "steps": [{"name": "执行命令: set screen to maximum brightness", "status": "passed", "start": 1756790957327, "stop": 1756790979097}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a8c75acc-7494-4105-8bcf-9f3534b12634-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "91457351-30f6-410e-ba5d-460b3bd8ac17-attachment.png", "type": "image/png"}], "start": 1756790979097, "stop": 1756790979314}], "start": 1756790957327, "stop": 1756790979314}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790979314, "stop": 1756790979315}, {"name": "验证应用已打开", "status": "passed", "start": 1756790979316, "stop": 1756790979316}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "43b849b8-cbbf-4a93-9cf3-0cbbed437004-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8a9f9c8a-4c20-4106-be68-0df3caf65569-attachment.png", "type": "image/png"}], "start": 1756790979316, "stop": 1756790979496}], "attachments": [{"name": "stdout", "source": "1899fbfc-0567-4797-bd53-900b22e203dd-attachment.txt", "type": "text/plain"}], "start": 1756790957327, "stop": 1756790979496, "uuid": "79b40bde-c9a0-45a0-84ec-f0b1308773e0", "historyId": "59f89eb284ef9300c926c5e2f1d3fd26", "testCaseId": "59f89eb284ef9300c926c5e2f1d3fd26", "fullName": "testcases.test_ella.system_coupling.test_set_screen_to_maximum_brightness.TestEllaSetScreenMaximumBrightness#test_set_screen_to_maximum_brightness", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_screen_to_maximum_brightness"}, {"name": "subSuite", "value": "TestEllaSetScreenMaximumBrightness"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_screen_to_maximum_brightness"}]}