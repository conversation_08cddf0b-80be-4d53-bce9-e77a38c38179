{"name": "测试play jay chou's music", "status": "passed", "description": "测试play jay chou's music指令", "steps": [{"name": "执行命令: play jay chou's music", "status": "passed", "steps": [{"name": "执行命令: play jay chou's music", "status": "passed", "start": 1756783801827, "stop": 1756783832162}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "01812a38-cb25-4d76-8de3-fd9c85bf8bf1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9439b60e-e322-42fc-a3fd-f58383db03b7-attachment.png", "type": "image/png"}], "start": 1756783832162, "stop": 1756783832340}], "start": 1756783801827, "stop": 1756783832340}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756783832340, "stop": 1756783832342}, {"name": "验证music已打开", "status": "passed", "start": 1756783832342, "stop": 1756783832342}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6b89c0b5-0cf0-42e8-9ef1-b4aa614d25d2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "75752b31-a403-455a-899a-7aa6464c92c3-attachment.png", "type": "image/png"}], "start": 1756783832342, "stop": 1756783832525}], "attachments": [{"name": "stdout", "source": "b2f1ab27-6e89-466d-9c4a-96d0b1fd31d8-attachment.txt", "type": "text/plain"}], "start": 1756783801827, "stop": 1756783832526, "uuid": "30e50b19-6b34-400c-a5b6-eba804feae56", "historyId": "b4e75f584d82368436f820de28f92cfd", "testCaseId": "b4e75f584d82368436f820de28f92cfd", "fullName": "testcases.test_ella.component_coupling.test_play_jay_chou_s_music.TestEllaOpenMusic#test_play_jay_chou_s_music", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_jay_chou_s_music"}, {"name": "subSuite", "value": "TestEllaOpenMusic"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_jay_chou_s_music"}]}