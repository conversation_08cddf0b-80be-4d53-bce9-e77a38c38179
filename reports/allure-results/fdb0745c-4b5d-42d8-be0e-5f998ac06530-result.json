{"name": "测试set languages返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['set languages', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_set_languages.TestEllaSetLanguages object at 0x0000018BC6620FD0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6DF10F0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_languages(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['set languages', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_languages.py:34: AssertionError"}, "description": "验证set languages指令返回预期的不支持响应", "steps": [{"name": "执行命令: set languages", "status": "passed", "steps": [{"name": "执行命令: set languages", "status": "passed", "start": 1756802083091, "stop": 1756802107209}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0b5988a7-79c2-40b3-98fe-29c0b4f9a976-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "429cbc41-8893-4d4c-bc22-ae051a595373-attachment.png", "type": "image/png"}], "start": 1756802107209, "stop": 1756802107455}], "start": 1756802083091, "stop": 1756802107455}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['set languages', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_languages.py\", line 34, in test_set_languages\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756802107455, "stop": 1756802107458}], "attachments": [{"name": "stdout", "source": "d8264056-c8f5-49aa-8b7e-369f2c87a06e-attachment.txt", "type": "text/plain"}], "start": 1756802083090, "stop": 1756802107460, "uuid": "a2a804a4-3e56-4697-ab01-bd801bafe47a", "historyId": "00e9182de9c9d3297d90ff42d6771a57", "testCaseId": "00e9182de9c9d3297d90ff42d6771a57", "fullName": "testcases.test_ella.unsupported_commands.test_set_languages.TestEllaSetLanguages#test_set_languages", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_languages"}, {"name": "subSuite", "value": "TestEllaSetLanguages"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_languages"}]}