{"uid": "562439685ca70ccb", "name": "测试Voice setting page返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_voice_setting_page.TestEllaVoiceSettingPage#test_voice_setting_page", "historyId": "9c301cfc137fb94f119957b5f74291ec", "time": {"start": 1756804161648, "stop": 1756804196229, "duration": 34581}, "description": "验证Voice setting page指令返回预期的不支持响应", "descriptionHtml": "<p>验证Voice setting page指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['sorry']，实际响应: '['Voice setting page', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Accessibility | Downloaded apps | Ella | Hi Translate | ScrollCaptureAccessibilityService | Select to Speak | Hear selected text | Smart Translate | TalkBack | Speak items on screen | Display | Text and Display | Extra dim | Dim screen beyond your phone’s minimum brightness | Magnification | Zoom in on the screen | Interaction controls | Accessibility Menu']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_voice_setting_page.TestEllaVoiceSettingPage object at 0x0000018BC66B79D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC9D7FF10>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_voice_setting_page(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['sorry']，实际响应: '['Voice setting page', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Accessibility | Downloaded apps | Ella | Hi Translate | ScrollCaptureAccessibilityService | Select to Speak | Hear selected text | Smart Translate | TalkBack | Speak items on screen | Display | Text and Display | Extra dim | Dim screen beyond your phone’s minimum brightness | Magnification | Zoom in on the screen | Interaction controls | Accessibility Menu']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_voice_setting_page.py:34: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1756782750014, "stop": 1756782750440, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1756804146350, "stop": 1756804161646, "duration": 15296}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1756804161647, "stop": 1756804161647, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证Voice setting page指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['sorry']，实际响应: '['Voice setting page', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Accessibility | Downloaded apps | Ella | Hi Translate | ScrollCaptureAccessibilityService | Select to Speak | Hear selected text | Smart Translate | TalkBack | Speak items on screen | Display | Text and Display | Extra dim | Dim screen beyond your phone’s minimum brightness | Magnification | Zoom in on the screen | Interaction controls | Accessibility Menu']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_voice_setting_page.TestEllaVoiceSettingPage object at 0x0000018BC66B79D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC9D7FF10>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_voice_setting_page(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['sorry']，实际响应: '['Voice setting page', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Accessibility | Downloaded apps | Ella | Hi Translate | ScrollCaptureAccessibilityService | Select to Speak | Hear selected text | Smart Translate | TalkBack | Speak items on screen | Display | Text and Display | Extra dim | Dim screen beyond your phone’s minimum brightness | Magnification | Zoom in on the screen | Interaction controls | Accessibility Menu']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_voice_setting_page.py:34: AssertionError", "steps": [{"name": "执行命令: Voice setting page", "time": {"start": 1756804161649, "stop": 1756804196225, "duration": 34576}, "status": "passed", "steps": [{"name": "执行命令: Voice setting page", "time": {"start": 1756804161649, "stop": 1756804195919, "duration": 34270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1756804195920, "stop": 1756804196224, "duration": 304}, "status": "passed", "steps": [], "attachments": [{"uid": "4e84e9a11c175337", "name": "测试总结", "source": "4e84e9a11c175337.txt", "type": "text/plain", "size": 671}, {"uid": "a41b39fcded59c49", "name": "test_completed", "source": "a41b39fcded59c49.png", "type": "image/png", "size": 479945}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1756804196225, "stop": 1756804196228, "duration": 3}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['sorry']，实际响应: '['Voice setting page', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.android.settings页面内容] Accessibility | Downloaded apps | Ella | Hi Translate | ScrollCaptureAccessibilityService | Select to Speak | Hear selected text | Smart Translate | TalkBack | Speak items on screen | Display | Text and Display | Extra dim | Dim screen beyond your phone’s minimum brightness | Magnification | Zoom in on the screen | Interaction controls | Accessibility Menu']'\nassert False\n", "statusTrace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_voice_setting_page.py\", line 34, in test_voice_setting_page\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": true, "attachmentsCount": 0, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "e428a74ec724141c", "name": "stdout", "source": "e428a74ec724141c.txt", "type": "text/plain", "size": 17086}], "parameters": [], "shouldDisplayMessage": true, "attachmentsCount": 3, "stepsCount": 4, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1756804196234, "stop": 1756804196462, "duration": 228}, "status": "passed", "steps": [], "attachments": [{"uid": "4b74f6a07a4ebbee", "name": "失败截图-TestEllaVoiceSettingPage", "source": "4b74f6a07a4ebbee.png", "type": "image/png", "size": 479975}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "stepsCount": 0, "hasContent": true, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1756804196464, "stop": 1756804198034, "duration": 1570}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1756804622359, "stop": 1756804622360, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_voice_setting_page"}, {"name": "subSuite", "value": "TestEllaVoiceSettingPage"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_voice_setting_page"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "562439685ca70ccb.json", "parameterValues": []}