{"name": "测试pls open whatsapp", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'I need to download whatsapp', 'Generated by AI, for reference only']，实际响应: '['pls open whatsapp', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[android页面内容] Dual App | Enable dual app, you can use dual WhatsApp simultaneously.']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_pls_open_whatsapp.TestEllaOpenWhatsapp object at 0x0000018BC65CA290>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC94F7760>\n\n    @allure.title(\"测试pls open whatsapp\")\n    @allure.description(\"测试pls open whatsapp指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_pls_open_whatsapp(self, ella_app):\n        \"\"\"测试pls open whatsapp命令\"\"\"\n        command = \"pls open whatsapp\"\n        # expected_text = ['Done']\n        expected_text =  ['Sorry','Oops','out of my reach',\n                          'I need to download whatsapp',\n                          'Generated by AI, for reference only']\n        app_name = 'whatsapp'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n    \n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'I need to download whatsapp', 'Generated by AI, for reference only']，实际响应: '['pls open whatsapp', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[android页面内容] Dual App | Enable dual app, you can use dual WhatsApp simultaneously.']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_pls_open_whatsapp.py:35: AssertionError"}, "description": "测试pls open whatsapp指令", "steps": [{"name": "执行命令: pls open whatsapp", "status": "passed", "steps": [{"name": "执行命令: pls open whatsapp", "status": "passed", "start": 1756800858685, "stop": 1756800886784}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "231ebfcf-fc84-4910-9b34-6dc93ea280c4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d827446a-0b15-4587-bf58-8d329616273d-attachment.png", "type": "image/png"}], "start": 1756800886784, "stop": 1756800886992}], "start": 1756800858685, "stop": 1756800886993}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'I need to download whatsapp', 'Generated by AI, for reference only']，实际响应: '['pls open whatsapp', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[android页面内容] Dual App | Enable dual app, you can use dual WhatsApp simultaneously.']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_pls_open_whatsapp.py\", line 35, in test_pls_open_whatsapp\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756800886993, "stop": 1756800886996}], "attachments": [{"name": "stdout", "source": "d0eb63c3-6e84-49e1-b54e-bd712f9eb350-attachment.txt", "type": "text/plain"}], "start": 1756800858685, "stop": 1756800886997, "uuid": "717e82c8-cd79-4140-8292-541729ed1d8c", "historyId": "abcb20b3882e9c0dbf1add7f63082581", "testCaseId": "abcb20b3882e9c0dbf1add7f63082581", "fullName": "testcases.test_ella.unsupported_commands.test_pls_open_whatsapp.TestEllaOpenWhatsapp#test_pls_open_whatsapp", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_pls_open_whatsapp"}, {"name": "subSuite", "value": "TestEllaOpenWhatsapp"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_pls_open_whatsapp"}]}