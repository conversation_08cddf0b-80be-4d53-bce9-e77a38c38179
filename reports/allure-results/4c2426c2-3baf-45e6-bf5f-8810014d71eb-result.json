{"name": "测试turn off show battery percentage返回正确的不支持响应", "status": "passed", "description": "验证turn off show battery percentage指令返回预期的不支持响应", "steps": [{"name": "执行命令: turn off show battery percentage", "status": "passed", "steps": [{"name": "执行命令: turn off show battery percentage", "status": "passed", "start": 1756803914233, "stop": 1756803937882}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "aec10880-506e-497b-837e-9ed137b2096b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "32ea9f4f-954f-40f3-9d90-963b6e4360f6-attachment.png", "type": "image/png"}], "start": 1756803937882, "stop": 1756803938121}], "start": 1756803914233, "stop": 1756803938122}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756803938122, "stop": 1756803938123}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "bcdd22ee-f0ed-4922-b127-ac6b764ca116-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6e274e0f-8773-483e-96fc-07c2538e1881-attachment.png", "type": "image/png"}], "start": 1756803938123, "stop": 1756803938378}], "attachments": [{"name": "stdout", "source": "fd482916-7081-4899-b8db-1e1ac140b529-attachment.txt", "type": "text/plain"}], "start": 1756803914233, "stop": 1756803938378, "uuid": "d7894988-1f34-4c34-aab2-7256b71104ad", "historyId": "4bda544c08fa4bc5494c7dda01d4cc77", "testCaseId": "4bda544c08fa4bc5494c7dda01d4cc77", "fullName": "testcases.test_ella.unsupported_commands.test_turn_off_show_battery_percentage.TestEllaTurnOffShowBatteryPercentage#test_turn_off_show_battery_percentage", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_off_show_battery_percentage"}, {"name": "subSuite", "value": "TestEllaTurnOffShowBatteryPercentage"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_off_show_battery_percentage"}]}