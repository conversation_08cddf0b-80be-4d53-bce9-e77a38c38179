{"name": "测试Generate a cartoon-style puppy image for me with a 4:3 aspect ratio", "status": "passed", "description": "测试Generate a cartoon-style puppy image for me with a 4:3 aspect ratio指令", "steps": [{"name": "执行命令: Generate a cartoon-style puppy image for me with a 4:3 aspect ratio", "status": "passed", "steps": [{"name": "执行命令: Generate a cartoon-style puppy image for me with a 4:3 aspect ratio", "status": "passed", "start": 1756797437721, "stop": 1756797459797}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b6a3497b-0a7e-42c4-b79c-c4636b07d81b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c085643f-a7b0-41c4-a854-cb07addb1839-attachment.png", "type": "image/png"}], "start": 1756797459797, "stop": 1756797460046}], "start": 1756797437721, "stop": 1756797460047}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756797460047, "stop": 1756797460048}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "96dd63da-40fa-431d-b5f3-c5cf2cbcd42b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "14f5901c-5769-4a0d-bffb-576eeb4047c2-attachment.png", "type": "image/png"}], "start": 1756797460048, "stop": 1756797460250}], "attachments": [{"name": "stdout", "source": "a92de08a-ebd6-4b9f-a798-524302cf5690-attachment.txt", "type": "text/plain"}], "start": 1756797437720, "stop": 1756797460250, "uuid": "a1eee115-8519-41b1-ac33-************", "historyId": "693bce6b8bfdefa98827246122355bf1", "testCaseId": "693bce6b8bfdefa98827246122355bf1", "fullName": "testcases.test_ella.unsupported_commands.test_generate_a_cartoon_style_puppy_image_for_me_with_a_4_3_aspect_ratio.TestEllaOpenPlayPoliticalNews#test_generate_a_cartoon_style_puppy_image_for_me_with_a_4_3_aspect_ratio", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_generate_a_cartoon_style_puppy_image_for_me_with_a_4_3_aspect_ratio"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_generate_a_cartoon_style_puppy_image_for_me_with_a_4_3_aspect_ratio"}]}