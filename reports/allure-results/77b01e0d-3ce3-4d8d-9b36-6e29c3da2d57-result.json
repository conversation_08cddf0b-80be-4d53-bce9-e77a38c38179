{"name": "测试turn on wifi能正常执行", "status": "passed", "description": "turn on wifi", "steps": [{"name": "执行命令: turn on wifi", "status": "passed", "steps": [{"name": "执行命令: turn on wifi", "status": "passed", "start": 1756793113203, "stop": 1756793135844}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "af7763f7-e180-42d8-ac46-864c242adf3e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "db78e366-2c6e-4492-ad81-1cb47c879176-attachment.png", "type": "image/png"}], "start": 1756793135844, "stop": 1756793136053}], "start": 1756793113203, "stop": 1756793136053}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756793136053, "stop": 1756793136054}, {"name": "验证应用已打开", "status": "passed", "start": 1756793136054, "stop": 1756793136054}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c2b06628-d470-4896-ae11-6627315b470d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d5c49289-c6a2-4130-90f0-82167a7b827b-attachment.png", "type": "image/png"}], "start": 1756793136054, "stop": 1756793136246}], "attachments": [{"name": "stdout", "source": "70b2edcc-983e-4e6f-8cd6-66f721854230-attachment.txt", "type": "text/plain"}], "start": 1756793113203, "stop": 1756793136246, "uuid": "92e07d68-d035-4110-b087-acde4f794fd9", "historyId": "d18ea588937139bb162adb1092a66013", "testCaseId": "d18ea588937139bb162adb1092a66013", "fullName": "testcases.test_ella.system_coupling.test_turn_on_wifi.TestEllaTurnWifi#test_turn_on_wifi", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_wifi"}, {"name": "subSuite", "value": "TestEllaTurnWifi"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_wifi"}]}