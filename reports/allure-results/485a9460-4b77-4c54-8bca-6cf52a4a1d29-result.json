{"name": "测试What movie is on the screen?", "status": "passed", "description": "测试Ask Screen功能: What movie is on the screen?", "steps": [{"name": "准备测试数据", "status": "passed", "start": 1756794045342, "stop": 1756794054428}, {"name": "执行Ask Screen命令: What movie is on the screen?", "status": "passed", "steps": [{"name": "执行浮窗命令: What movie is on the screen?", "status": "passed", "steps": [{"name": "执行命令: What movie is on the screen?", "status": "passed", "start": 1756794054429, "stop": 1756794063365}, {"name": "等待并获取AI响应", "status": "passed", "start": 1756794063365, "stop": 1756794078855}, {"name": "验证响应内容", "status": "passed", "attachments": [{"name": "关键词验证结果", "source": "e7a0b255-69e7-487a-bcf3-bde339851015-attachment.txt", "type": "text/plain"}], "start": 1756794078855, "stop": 1756794078857}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "浮窗测试总结", "source": "08383328-73dc-4a3a-90e5-9cf02a7e99f8-attachment.txt", "type": "text/plain"}, {"name": "AI响应内容", "source": "a2ee050e-0121-4175-a08d-7942b878c007-attachment.txt", "type": "text/plain"}], "start": 1756794078857, "stop": 1756794078871}], "start": 1756794054428, "stop": 1756794078871}, {"name": "截图记录测试完成状态", "status": "passed", "attachments": [{"name": "floating_test_completed", "source": "6e240913-96ad-4551-bc95-3090ce53c7d2-attachment.png", "type": "image/png"}], "start": 1756794078871, "stop": 1756794079168}], "start": 1756794054428, "stop": 1756794080413}, {"name": "验证测试结果", "status": "passed", "start": 1756794080413, "stop": 1756794080413}, {"name": "记录测试完成", "status": "passed", "start": 1756794080413, "stop": 1756794080414}], "attachments": [{"name": "stdout", "source": "dd4d6b3a-6c86-463a-a2b8-80f18f2bcf32-attachment.txt", "type": "text/plain"}], "start": 1756794045342, "stop": 1756794080415, "uuid": "0c77396a-ffd7-4233-8e92-dd3665b34930", "historyId": "629e60713979f085f1805341fb36622f", "testCaseId": "629e60713979f085f1805341fb36622f", "fullName": "testcases.test_ella.test_ask_screen.object_detection.test_what_movie_is_on_the_screen.TestAskScreenWhatMovieIsScreen#test_what_movie_is_on_the_screen", "labels": [{"name": "story", "value": "物体检测"}, {"name": "epic", "value": "Ella浮窗测试"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ask Screen功能"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.test_ask_screen.object_detection"}, {"name": "suite", "value": "test_what_movie_is_on_the_screen"}, {"name": "subSuite", "value": "TestAskScreenWhatMovieIsScreen"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_ask_screen.object_detection.test_what_movie_is_on_the_screen"}]}