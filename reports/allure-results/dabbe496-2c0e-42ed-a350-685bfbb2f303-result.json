{"name": "测试help me take a screenshot能正常执行", "status": "passed", "description": "help me take a screenshot", "steps": [{"name": "执行命令: help me take a screenshot", "status": "passed", "steps": [{"name": "执行命令: help me take a screenshot", "status": "passed", "start": 1756789598757, "stop": 1756789621700}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8c4ff842-7041-4854-ac2f-20338a17e6b4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "457be3dc-6271-49b5-b714-6dc6001b9260-attachment.png", "type": "image/png"}], "start": 1756789621700, "stop": 1756789621887}], "start": 1756789598757, "stop": 1756789621888}, {"name": "验证文件存在", "status": "passed", "start": 1756789621888, "stop": 1756789621888}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7e78f282-**************-27c4d4d1da64-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "daeeb8cf-9a7c-41dc-8a92-5fcc3ed4c42d-attachment.png", "type": "image/png"}], "start": 1756789621888, "stop": 1756789622085}], "attachments": [{"name": "stdout", "source": "a24da33f-6a46-431e-b637-d242c803c470-attachment.txt", "type": "text/plain"}], "start": 1756789598757, "stop": 1756789622085, "uuid": "59866c2b-897b-46a1-956a-19b9c0d1f446", "historyId": "459c099a876d1129ddcb7cb28663b756", "testCaseId": "459c099a876d1129ddcb7cb28663b756", "fullName": "testcases.test_ella.system_coupling.test_help_me_take_a_screenshot.TestEllaHelpMeTakeScreenshot#test_help_me_take_a_screenshot", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_help_me_take_a_screenshot"}, {"name": "subSuite", "value": "TestEllaHelpMeTakeScreenshot"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_help_me_take_a_screenshot"}]}