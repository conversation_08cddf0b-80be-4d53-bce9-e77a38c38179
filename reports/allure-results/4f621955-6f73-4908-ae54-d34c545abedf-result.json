{"name": "测试set alarm volume 50", "status": "passed", "description": "测试set alarm volume 50指令", "steps": [{"name": "执行命令: set alarm volume 50", "status": "passed", "steps": [{"name": "执行命令: set alarm volume 50", "status": "passed", "start": 1756790686825, "stop": 1756790708118}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "15f81433-0665-4647-b91d-e25d67092431-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "87681080-d643-4bf9-ad44-22483f35a468-attachment.png", "type": "image/png"}], "start": 1756790708118, "stop": 1756790708310}], "start": 1756790686825, "stop": 1756790708310}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790708310, "stop": 1756790708311}, {"name": "验证alarm_volume已打开", "status": "passed", "start": 1756790708311, "stop": 1756790708311}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dd858d10-ac5b-4c14-8624-499e5271ace1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "bf93fb62-c265-4ff9-91d7-dbbcf10bc196-attachment.png", "type": "image/png"}], "start": 1756790708311, "stop": 1756790708486}], "attachments": [{"name": "stdout", "source": "474efbcb-5941-44f1-a66e-98a8e93a2220-attachment.txt", "type": "text/plain"}], "start": 1756790686825, "stop": 1756790708486, "uuid": "f2a3dadc-3976-4acf-b429-8e7b9b1033e1", "historyId": "3238a485ed8e76dc2866c0b0bcd4930e", "testCaseId": "3238a485ed8e76dc2866c0b0bcd4930e", "fullName": "testcases.test_ella.system_coupling.test_set_alarm_volume.TestEllaOpenAlarmVolume#test_set_alarm_volume", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_alarm_volume"}, {"name": "subSuite", "value": "TestEllaOpenAlarmVolume"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_alarm_volume"}]}