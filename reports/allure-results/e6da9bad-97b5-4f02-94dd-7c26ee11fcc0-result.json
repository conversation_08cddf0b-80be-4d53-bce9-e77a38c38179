{"name": "测试turn on adaptive brightness能正常执行", "status": "passed", "description": "turn on adaptive brightness", "steps": [{"name": "执行命令: turn on adaptive brightness", "status": "passed", "steps": [{"name": "执行命令: turn on adaptive brightness", "status": "passed", "start": 1756792548655, "stop": 1756792569945}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "00e83a0a-543f-4d26-9c5b-e95363794b9b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b7867ccf-ab04-48a0-aa53-2ef182d1c1a8-attachment.png", "type": "image/png"}], "start": 1756792569945, "stop": 1756792570169}], "start": 1756792548655, "stop": 1756792570170}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756792570170, "stop": 1756792570171}, {"name": "验证应用已打开", "status": "passed", "start": 1756792570171, "stop": 1756792570171}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "083ff376-5cfe-4055-96e9-a1614fc01d8b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1c9ac62d-93d5-417f-8a73-3780b810bcb5-attachment.png", "type": "image/png"}], "start": 1756792570171, "stop": 1756792570364}], "attachments": [{"name": "stdout", "source": "489b1a72-10d2-435f-afc4-e1932f5204f4-attachment.txt", "type": "text/plain"}], "start": 1756792548655, "stop": 1756792570365, "uuid": "b43331c4-9313-4078-b42f-a2ab675426b8", "historyId": "123d65cc114fff79e459e14acfbcd445", "testCaseId": "123d65cc114fff79e459e14acfbcd445", "fullName": "testcases.test_ella.system_coupling.test_turn_on_adaptive_brightness.TestEllaTurnAdaptiveBrightness#test_turn_on_adaptive_brightness", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_adaptive_brightness"}, {"name": "subSuite", "value": "TestEllaTurnAdaptiveBrightness"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_adaptive_brightness"}]}