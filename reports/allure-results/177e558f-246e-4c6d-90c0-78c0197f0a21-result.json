{"name": "测试A furry little monkey", "status": "passed", "description": "测试A furry little monkey指令", "steps": [{"name": "执行命令: A furry little monkey", "status": "passed", "steps": [{"name": "执行命令: A furry little monkey", "status": "passed", "start": 1756797932403, "stop": 1756797956106}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7dcb930d-b0ce-4455-ab03-852357abca08-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8fa46819-15c9-4937-b1c2-a80eeac4fbbd-attachment.png", "type": "image/png"}], "start": 1756797956106, "stop": 1756797956325}], "start": 1756797932403, "stop": 1756797956325}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756797956325, "stop": 1756797956326}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "711fc2b8-398f-42e5-8599-7f1f2d675cf6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "895fd976-0840-4b44-8d58-3336cef8e6bb-attachment.png", "type": "image/png"}], "start": 1756797956326, "stop": 1756797956530}], "attachments": [{"name": "stdout", "source": "89f61bdb-33d5-40e4-b731-c099262b229f-attachment.txt", "type": "text/plain"}], "start": 1756797932403, "stop": 1756797956531, "uuid": "b968cc05-0824-4bb4-a8cb-3fd9b52a8bb0", "historyId": "6980dbcce9a72cd9dea6dee04c6891de", "testCaseId": "6980dbcce9a72cd9dea6dee04c6891de", "fullName": "testcases.test_ella.unsupported_commands.test_help_generate_a_picture_of_ancient_city.TestEllaOpenPlayPoliticalNews#test_help_generate_a_picture_of_ancient_city", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_generate_a_picture_of_ancient_city"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_generate_a_picture_of_ancient_city"}]}