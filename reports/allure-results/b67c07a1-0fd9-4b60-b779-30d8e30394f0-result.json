{"name": "测试max notifications volume能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=0, 最终=0, 响应='['max notifications volume', 'Notification volume has been set to the maximum.', '', '', '', '', '', '', '', '', '']'\nassert 0 == 15", "trace": "self = <testcases.test_ella.system_coupling.test_max_notifications_volume.TestEllaMaxNotificationsVolume object at 0x0000018BC61665F0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC3E54FD0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_max_notifications_volume(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证应用已打开\"):\n>           assert final_status==15, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=0, 最终=0, 响应='['max notifications volume', 'Notification volume has been set to the maximum.', '', '', '', '', '', '', '', '', '']'\nE           assert 0 == 15\n\ntestcases\\test_ella\\system_coupling\\test_max_notifications_volume.py:36: AssertionError"}, "description": "max notifications volume", "steps": [{"name": "执行命令: max notifications volume", "status": "passed", "steps": [{"name": "执行命令: max notifications volume", "status": "passed", "start": 1756789939434, "stop": 1756789960750}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e02efce3-eff2-436c-a3de-55ee32d298de-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c33e61dc-5865-4393-8693-1299ff4624fc-attachment.png", "type": "image/png"}], "start": 1756789960750, "stop": 1756789960945}], "start": 1756789939434, "stop": 1756789960946}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756789960946, "stop": 1756789960947}, {"name": "验证应用已打开", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=0, 最终=0, 响应='['max notifications volume', 'Notification volume has been set to the maximum.', '', '', '', '', '', '', '', '', '']'\nassert 0 == 15\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\system_coupling\\test_max_notifications_volume.py\", line 36, in test_max_notifications_volume\n    assert final_status==15, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1756789960947, "stop": 1756789960947}], "attachments": [{"name": "stdout", "source": "cda7d2a6-de7d-40d1-8fc8-43ba7cb2700e-attachment.txt", "type": "text/plain"}], "start": 1756789939434, "stop": 1756789960948, "uuid": "f4af7d10-90dc-48ee-a6b4-73949b9c9e4a", "historyId": "a5e15bb05795c5949d67f33200f4d69b", "testCaseId": "a5e15bb05795c5949d67f33200f4d69b", "fullName": "testcases.test_ella.system_coupling.test_max_notifications_volume.TestEllaMaxNotificationsVolume#test_max_notifications_volume", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_max_notifications_volume"}, {"name": "subSuite", "value": "TestEllaMaxNotificationsVolume"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_max_notifications_volume"}]}