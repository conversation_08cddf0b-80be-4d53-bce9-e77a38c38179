{"name": "测试set personal hotspot返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Multiple settings options found']，实际响应: '['set personal hotspot', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.settings.wifi页面内容] Personal Hotspot | Portable Hotspot | Personal Hotspot | Hotspot Settings | Share Hotspot | Connection Management | Turn Off Hotspot Automatically | If no device or network is connected within a certain period, the hotspot will be turned off automatically. | AI Decides']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_set_personal_hotspot.TestEllaSetPersonalHotspot object at 0x0000018BC66212D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6DBE920>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_personal_hotspot(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Multiple settings options found']，实际响应: '['set personal hotspot', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.settings.wifi页面内容] Personal Hotspot | Portable Hotspot | Personal Hotspot | Hotspot Settings | Share Hotspot | Connection Management | Turn Off Hotspot Automatically | If no device or network is connected within a certain period, the hotspot will be turned off automatically. | AI Decides']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_personal_hotspot.py:34: AssertionError"}, "description": "验证set personal hotspot指令返回预期的不支持响应", "steps": [{"name": "执行命令: set personal hotspot", "status": "passed", "steps": [{"name": "执行命令: set personal hotspot", "status": "passed", "start": 1756802393667, "stop": 1756802428578}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ae2a2410-8d3b-4369-a955-c977af264daa-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7e79b41d-efc7-455c-8bc5-7b315d9f2a6e-attachment.png", "type": "image/png"}], "start": 1756802428578, "stop": 1756802428855}], "start": 1756802393667, "stop": 1756802428855}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Multiple settings options found']，实际响应: '['set personal hotspot', 'Done!', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', '[com.transsion.settings.wifi页面内容] Personal Hotspot | Portable Hotspot | Personal Hotspot | Hotspot Settings | Share Hotspot | Connection Management | Turn Off Hotspot Automatically | If no device or network is connected within a certain period, the hotspot will be turned off automatically. | AI Decides']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_personal_hotspot.py\", line 34, in test_set_personal_hotspot\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756802428855, "stop": 1756802428862}], "attachments": [{"name": "stdout", "source": "733acd57-b38a-4915-b9bb-e80ba04b1979-attachment.txt", "type": "text/plain"}], "start": 1756802393667, "stop": 1756802428863, "uuid": "a8267c85-e179-44a1-b52c-073a7d2c0171", "historyId": "4f538fc772535a0c0811ad87d3aa9494", "testCaseId": "4f538fc772535a0c0811ad87d3aa9494", "fullName": "testcases.test_ella.unsupported_commands.test_set_personal_hotspot.TestEllaSetPersonalHotspot#test_set_personal_hotspot", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_personal_hotspot"}, {"name": "subSuite", "value": "TestEllaSetPersonalHotspot"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_personal_hotspot"}]}