{"name": "测试happy new year能正常执行", "status": "passed", "description": "happy new year", "steps": [{"name": "执行命令: happy new year", "status": "passed", "steps": [{"name": "执行命令: happy new year", "status": "passed", "start": 1756797844758, "stop": 1756797870666}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "74cc3cb6-2483-4db1-876e-67a413f01f5b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2c130219-c669-41e8-9554-cede0209e3c2-attachment.png", "type": "image/png"}], "start": 1756797870666, "stop": 1756797870899}], "start": 1756797844758, "stop": 1756797870899}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756797870899, "stop": 1756797870901}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6a458eff-43ac-4603-8831-dda80e64f335-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c14fca97-79dd-4233-a38b-a409443f382d-attachment.png", "type": "image/png"}], "start": 1756797870901, "stop": 1756797871104}], "attachments": [{"name": "stdout", "source": "8c870099-73e0-4459-92c7-a12c16f5976a-attachment.txt", "type": "text/plain"}], "start": 1756797844758, "stop": 1756797871105, "uuid": "4cc4505d-07f7-4bd8-ae6d-d4ff1194ef68", "historyId": "20bd2e7c8179ca1901bc63c9a02b9ee1", "testCaseId": "20bd2e7c8179ca1901bc63c9a02b9ee1", "fullName": "testcases.test_ella.unsupported_commands.test_happy_new_year.TestEllaHappyNewYear#test_happy_new_year", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_happy_new_year"}, {"name": "subSuite", "value": "TestEllaHappyNewYear"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_happy_new_year"}]}