{"name": "测试set edge mistouch prevention返回正确的不支持响应", "status": "passed", "description": "验证set edge mistouch prevention指令返回预期的不支持响应", "steps": [{"name": "执行命令: set edge mistouch prevention", "status": "passed", "steps": [{"name": "执行命令: set edge mistouch prevention", "status": "passed", "start": 1756801769845, "stop": 1756801800897}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6a01017d-5e8f-44a7-9b7e-ad04c319ae3e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5a6ad641-65a9-4daa-922e-b77f7d721c7f-attachment.png", "type": "image/png"}], "start": 1756801800897, "stop": 1756801801243}], "start": 1756801769844, "stop": 1756801801243}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756801801244, "stop": 1756801801245}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "37ad81cf-b7b7-4d0f-916a-21562d222dc6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a8d18171-5581-43c0-b805-0f6e03370fbc-attachment.png", "type": "image/png"}], "start": 1756801801245, "stop": 1756801801516}], "attachments": [{"name": "stdout", "source": "48169ea0-48e8-4c35-b644-9f8c82c5c011-attachment.txt", "type": "text/plain"}], "start": 1756801769844, "stop": 1756801801517, "uuid": "878ec768-b9f8-40dd-9994-3fdc4d9a6784", "historyId": "2b2dcc407b5c428f968f62d94fe8025c", "testCaseId": "2b2dcc407b5c428f968f62d94fe8025c", "fullName": "testcases.test_ella.unsupported_commands.test_set_edge_mistouch_prevention.TestEllaSetEdgeMistouchPrevention#test_set_edge_mistouch_prevention", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_edge_mistouch_prevention"}, {"name": "subSuite", "value": "TestEllaSetEdgeMistouchPrevention"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_edge_mistouch_prevention"}]}