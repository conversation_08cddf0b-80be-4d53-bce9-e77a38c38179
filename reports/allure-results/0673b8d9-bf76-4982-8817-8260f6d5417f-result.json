{"name": "测试call number by whatsapp能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['call number by whatsapp', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_call_number_by_whatsapp.TestEllaCallNumberWhatsapp object at 0x0000018BC640F280>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC3E556F0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_call_number_by_whatsapp(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response_advanced(expected_text, response_text,match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['call number by whatsapp', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_call_number_by_whatsapp.py:35: AssertionError"}, "description": "call number by whatsapp", "steps": [{"name": "执行命令: call number by whatsapp", "status": "passed", "steps": [{"name": "执行命令: call number by whatsapp", "status": "passed", "start": 1756795369884, "stop": 1756795394704}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "355e3a80-f05e-478e-8ff2-10fdc4a5e6ad-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7da2fa15-58ac-4a72-aff5-dad64c8b0b1f-attachment.png", "type": "image/png"}], "start": 1756795394704, "stop": 1756795394942}], "start": 1756795369884, "stop": 1756795394942}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['WhatsApp is not installed yet', 'I need to download WhatsApp']，实际响应: '['call number by whatsapp', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_call_number_by_whatsapp.py\", line 35, in test_call_number_by_whatsapp\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756795394942, "stop": 1756795394944}], "attachments": [{"name": "stdout", "source": "16974eaa-7f49-4c51-8f43-18b17f01551f-attachment.txt", "type": "text/plain"}], "start": 1756795369882, "stop": 1756795394944, "uuid": "5dff8567-1c58-493a-9059-6bcd47aedaf9", "historyId": "4fd50eb7a7e49fc09f612442a33e3010", "testCaseId": "4fd50eb7a7e49fc09f612442a33e3010", "fullName": "testcases.test_ella.unsupported_commands.test_call_number_by_whatsapp.TestEllaCallNumberWhatsapp#test_call_number_by_whatsapp", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_call_number_by_whatsapp"}, {"name": "subSuite", "value": "TestEllaCallNumberWhatsapp"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_call_number_by_whatsapp"}]}