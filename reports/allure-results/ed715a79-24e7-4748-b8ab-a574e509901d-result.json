{"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "status": "passed", "description": "验证jump to lock screen notification and display settings指令返回预期的不支持响应", "steps": [{"name": "执行命令: jump to lock screen notification and display settings", "status": "passed", "steps": [{"name": "执行命令: jump to lock screen notification and display settings", "status": "passed", "start": 1756799255078, "stop": 1756799284537}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6c13c7a7-e2d0-4bce-a6cd-dc4f92681dbe-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "442a4405-bbe7-4fe4-a951-359e7cfc30a5-attachment.png", "type": "image/png"}], "start": 1756799284537, "stop": 1756799284785}], "start": 1756799255078, "stop": 1756799284785}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756799284785, "stop": 1756799284787}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f07059df-d227-4a2e-9932-ce968f9f0e3b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d19c2fa2-56a1-470e-b639-3438b6a311d5-attachment.png", "type": "image/png"}], "start": 1756799284787, "stop": 1756799285020}], "attachments": [{"name": "stdout", "source": "155f77e1-6e76-4087-b1fb-8475d4953d8c-attachment.txt", "type": "text/plain"}], "start": 1756799255078, "stop": 1756799285020, "uuid": "1765c5a2-857c-470e-80a6-441080cae44d", "historyId": "fca782bf64e9cf595a09003471d4cc31", "testCaseId": "fca782bf64e9cf595a09003471d4cc31", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_lock_screen_notification_and_display_settings.TestEllaOpenSettings#test_jump_to_lock_screen_notification_and_display_settings", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_lock_screen_notification_and_display_settings"}, {"name": "subSuite", "value": "TestEllaOpenSettings"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_lock_screen_notification_and_display_settings"}]}