{"name": "测试set screen relay返回正确的不支持响应", "status": "passed", "description": "验证set screen relay指令返回预期的不支持响应", "steps": [{"name": "执行命令: set screen relay", "status": "passed", "steps": [{"name": "执行命令: set screen relay", "status": "passed", "start": 1756802645518, "stop": 1756802667295}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a1d850dd-8690-45fb-b564-9c3699107c70-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "88a9cd82-0e4c-45b1-853a-0b10ccc88303-attachment.png", "type": "image/png"}], "start": 1756802667295, "stop": 1756802667511}], "start": 1756802645518, "stop": 1756802667511}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756802667511, "stop": 1756802667514}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0606afa7-8adf-417b-bb4e-af37f5f2ede3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9842698a-106c-493b-9f6b-1448d52b2c68-attachment.png", "type": "image/png"}], "start": 1756802667514, "stop": 1756802667744}], "attachments": [{"name": "stdout", "source": "5ffb894b-252c-40a0-a2c8-5d30779a0d33-attachment.txt", "type": "text/plain"}], "start": 1756802645518, "stop": 1756802667746, "uuid": "4146f179-c1af-4d26-8694-045c694f60f9", "historyId": "7ba4a9d343c0f63e9f654ce03ea4fa51", "testCaseId": "7ba4a9d343c0f63e9f654ce03ea4fa51", "fullName": "testcases.test_ella.unsupported_commands.test_set_screen_relay.TestEllaSetScreenRelay#test_set_screen_relay", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_screen_relay"}, {"name": "subSuite", "value": "TestEllaSetScreenRelay"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_screen_relay"}]}