{"name": "测试Scan the QR code in the image", "status": "passed", "description": "测试Ask Screen功能: <PERSON>an the QR code in the image", "steps": [{"name": "准备测试数据", "status": "passed", "start": 1756793815234, "stop": 1756793824210}, {"name": "执行Ask Screen命令: <PERSON>an the QR code in the image", "status": "passed", "steps": [{"name": "执行浮窗命令: <PERSON>an the QR code in the image", "status": "passed", "steps": [{"name": "执行命令: Scan the QR code in the image", "status": "passed", "start": 1756793824211, "stop": 1756793833549}, {"name": "等待并获取AI响应", "status": "passed", "start": 1756793833549, "stop": 1756793848970}, {"name": "验证响应内容", "status": "passed", "attachments": [{"name": "关键词验证结果", "source": "68cb9a45-4cbe-45db-a2e2-e3be26f1d6b7-attachment.txt", "type": "text/plain"}], "start": 1756793848970, "stop": 1756793848972}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "浮窗测试总结", "source": "afd1b3f1-c31e-4d13-92b6-a95862f58422-attachment.txt", "type": "text/plain"}, {"name": "AI响应内容", "source": "16b5241e-93bd-43c7-b3ae-ba6831a5d3cc-attachment.txt", "type": "text/plain"}], "start": 1756793848972, "stop": 1756793848974}], "start": 1756793824210, "stop": 1756793848974}, {"name": "截图记录测试完成状态", "status": "passed", "attachments": [{"name": "floating_test_completed", "source": "cc051434-b537-4dc9-9f1a-1b0a5d16e5b3-attachment.png", "type": "image/png"}], "start": 1756793848974, "stop": 1756793849180}], "start": 1756793824210, "stop": 1756793850352}, {"name": "验证测试结果", "status": "passed", "start": 1756793850352, "stop": 1756793850353}, {"name": "记录测试完成", "status": "passed", "start": 1756793850353, "stop": 1756793850353}], "attachments": [{"name": "stdout", "source": "3487877e-410c-4cde-9620-e171dbff7cef-attachment.txt", "type": "text/plain"}], "start": 1756793815234, "stop": 1756793850354, "uuid": "55ae1d40-3bd6-4752-a932-66a8340de744", "historyId": "9631a765ba57ecef91f8ebeb45e7807b", "testCaseId": "9631a765ba57ecef91f8ebeb45e7807b", "fullName": "testcases.test_ella.test_ask_screen.image_analysis.test_scan_the_qr_code_in_the_image.TestAskScreenScanQrCodeImage#test_scan_the_qr_code_in_the_image", "labels": [{"name": "epic", "value": "Ella浮窗测试"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "图片分析"}, {"name": "feature", "value": "Ask Screen功能"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.test_ask_screen.image_analysis"}, {"name": "suite", "value": "test_scan_the_qr_code_in_the_image"}, {"name": "subSuite", "value": "TestAskScreenScanQrCodeImage"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_ask_screen.image_analysis.test_scan_the_qr_code_in_the_image"}]}