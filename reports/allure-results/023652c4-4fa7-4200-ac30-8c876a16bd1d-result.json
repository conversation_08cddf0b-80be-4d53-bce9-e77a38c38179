{"name": "测试continue music能正常执行", "status": "passed", "description": "continue music", "steps": [{"name": "执行命令: continue music", "status": "passed", "steps": [{"name": "执行命令: continue music", "status": "passed", "start": 1756799884996, "stop": 1756799906919}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4443482a-0a69-47f4-8ab2-53aa0d8731ef-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3d8f0d37-b11c-460b-b6b7-1946b3247a26-attachment.png", "type": "image/png"}], "start": 1756799906919, "stop": 1756799907075}], "start": 1756799884996, "stop": 1756799907076}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756799907076, "stop": 1756799907077}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f8d1fb51-e8bc-481f-a97d-2e494fda9c47-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e0a022d1-3e6c-49f2-bd86-22fabf242312-attachment.png", "type": "image/png"}], "start": 1756799907077, "stop": 1756799907253}], "attachments": [{"name": "stdout", "source": "a12ff2a7-890d-45e2-a41c-8e937ee8cb85-attachment.txt", "type": "text/plain"}], "start": 1756799884996, "stop": 1756799907254, "uuid": "45ae8634-354f-4d6e-a634-04b4b5906b2a", "historyId": "87f3dc53ab72c729262e053c16a3dbcb", "testCaseId": "87f3dc53ab72c729262e053c16a3dbcb", "fullName": "testcases.test_ella.component_coupling.test_continue_music.TestEllaContinueMusic#test_continue_music", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_continue_music"}, {"name": "subSuite", "value": "TestEllaContinueMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "30444-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_continue_music"}]}