{"name": "测试A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance", "status": "passed", "description": "测试A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance指令", "steps": [{"name": "执行命令: A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance", "status": "passed", "steps": [{"name": "执行命令: A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance", "status": "passed", "start": 1756795185235, "stop": 1756795213242}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c659943f-e0f9-4200-97bc-ca63e05c7dbf-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a2e9ae9d-84f6-4044-a686-f6ec04ecb432-attachment.png", "type": "image/png"}], "start": 1756795213242, "stop": 1756795213463}], "start": 1756795185235, "stop": 1756795213463}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756795213463, "stop": 1756795213465}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0ecdb1f8-2c42-4da5-a8bd-f8df4632aad7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "94005ba4-f61d-4e86-a83a-8379f19b1215-attachment.png", "type": "image/png"}], "start": 1756795213465, "stop": 1756795213717}], "attachments": [{"name": "stdout", "source": "ac19d0db-7eb5-421c-ac64-fd559b03d723-attachment.txt", "type": "text/plain"}], "start": 1756795185235, "stop": 1756795213718, "uuid": "8caee712-09ff-4f2b-ab8e-9f87061d4fbd", "historyId": "51c103053dd0c5596d9f4d9f178c3d8d", "testCaseId": "51c103053dd0c5596d9f4d9f178c3d8d", "fullName": "testcases.test_ella.unsupported_commands.test_a_little_raccoon_is_walking_on_the_forest_meadow_surrounded_by_a_row_of_green_bamboo_groves_with_layers_of_high_mountains_shrouded_in_clouds_and_mist_in_the_distance.TestEllaOpenPlayPoliticalNews#test_a_little_raccoon_is_walking_on_the_forest_meadow_surrounded_by_a_row_of_green_bamboo_groves_with_layers_of_high_mountains_shrouded_in_clouds_and_mist_in_the_distance", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_a_little_raccoon_is_walking_on_the_forest_meadow_surrounded_by_a_row_of_green_bamboo_groves_with_layers_of_high_mountains_shrouded_in_clouds_and_mist_in_the_distance"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_a_little_raccoon_is_walking_on_the_forest_meadow_surrounded_by_a_row_of_green_bamboo_groves_with_layers_of_high_mountains_shrouded_in_clouds_and_mist_in_the_distance"}]}