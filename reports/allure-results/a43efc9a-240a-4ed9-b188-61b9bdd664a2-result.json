{"name": "测试check my balance of sim1返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['The carrier for SIM 1 is not supported']，实际响应: '['check my balance of sim1', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_check_my_balance_of_sim.TestEllaCheckMyBalanceSim object at 0x0000018BC6432110>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC952FB80>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_check_my_balance_of_sim(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['The carrier for SIM 1 is not supported']，实际响应: '['check my balance of sim1', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_check_my_balance_of_sim.py:33: AssertionError"}, "description": "验证check my balance of sim1指令返回预期的不支持响应", "steps": [{"name": "执行命令: check my balance of sim1", "status": "passed", "steps": [{"name": "执行命令: check my balance of sim1", "status": "passed", "start": 1756795816023, "stop": 1756795838583}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "48a2504c-69de-4866-9ff1-e5fd102826e9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "60bbf89c-7b0e-439d-8f26-87fa23085e05-attachment.png", "type": "image/png"}], "start": 1756795838583, "stop": 1756795838806}], "start": 1756795816023, "stop": 1756795838807}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['The carrier for SIM 1 is not supported']，实际响应: '['check my balance of sim1', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_check_my_balance_of_sim.py\", line 33, in test_check_my_balance_of_sim\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756795838807, "stop": 1756795838810}], "attachments": [{"name": "stdout", "source": "f9f2ecfd-b025-4d12-9f9f-6dfc93a325a1-attachment.txt", "type": "text/plain"}], "start": 1756795816023, "stop": 1756795838812, "uuid": "a82185dc-f684-4dbd-95b1-6e12a1e66b7d", "historyId": "8bcc4c0c2b314e79a7177168f7d787b8", "testCaseId": "8bcc4c0c2b314e79a7177168f7d787b8", "fullName": "testcases.test_ella.unsupported_commands.test_check_my_balance_of_sim.TestEllaCheckMyBalanceSim#test_check_my_balance_of_sim", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_my_balance_of_sim"}, {"name": "subSuite", "value": "TestEllaCheckMyBalanceSim"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_my_balance_of_sim"}]}