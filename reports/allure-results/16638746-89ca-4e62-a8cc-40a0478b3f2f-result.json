{"name": "stop  screen recording能正常执行", "status": "passed", "description": "stop  screen recording", "steps": [{"name": "执行命令: stop recording", "status": "passed", "steps": [{"name": "执行命令: start recording", "status": "passed", "start": 1756791352832, "stop": 1756791380299}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f25d4c0e-0c56-4f45-bc55-23120629b1f9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f8e846fc-7009-4a47-84ae-66a4edeccc59-attachment.png", "type": "image/png"}], "start": 1756791380299, "stop": 1756791380527}], "start": 1756791352832, "stop": 1756791380527}, {"name": "执行命令: stop recording", "status": "passed", "steps": [{"name": "执行命令: stop recording", "status": "passed", "start": 1756791380527, "stop": 1756791402530}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "06d9cafe-263a-403a-852c-e4b80efa3abc-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "20ae9f7c-39b7-4f0e-921c-bcd37e1d361d-attachment.png", "type": "image/png"}], "start": 1756791402530, "stop": 1756791402781}], "start": 1756791380527, "stop": 1756791402782}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756791402782, "stop": 1756791402784}, {"name": "验证已打开", "status": "passed", "start": 1756791402784, "stop": 1756791402784}, {"name": "验证文件存在", "status": "passed", "start": 1756791402784, "stop": 1756791402784}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4cbac8c4-85ff-411b-ac00-d3282dd6cdc4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f8e3c3bd-15c5-4e56-925f-e048e0803b50-attachment.png", "type": "image/png"}], "start": 1756791402784, "stop": 1756791403013}], "attachments": [{"name": "stdout", "source": "af03652c-9a64-4f50-9a24-c9fa88cdc128-attachment.txt", "type": "text/plain"}], "start": 1756791352832, "stop": 1756791403014, "uuid": "a49f426f-0843-481a-8acb-6acb49de00a0", "historyId": "3bafa6d8eb5b49bc5b77f1784275285e", "testCaseId": "3bafa6d8eb5b49bc5b77f1784275285e", "fullName": "testcases.test_ella.system_coupling.test_stop_recording.TestEllaTurnScreenRecord#test_stop_recording", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_stop_recording"}, {"name": "subSuite", "value": "TestEllaTurnScreenRecord"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_stop_recording"}]}