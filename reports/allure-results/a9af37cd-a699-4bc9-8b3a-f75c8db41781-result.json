{"name": "测试set phantom v pen返回正确的不支持响应", "status": "passed", "description": "验证set phantom v pen指令返回预期的不支持响应", "steps": [{"name": "执行命令: set phantom v pen", "status": "passed", "steps": [{"name": "执行命令: set phantom v pen", "status": "passed", "start": 1756802446840, "stop": 1756802470337}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3b525b7e-4803-4c57-9605-e1c444cfa00b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "eab304bd-49d9-4e7d-9d01-9cf6be0c4b0d-attachment.png", "type": "image/png"}], "start": 1756802470338, "stop": 1756802470631}], "start": 1756802446840, "stop": 1756802470631}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756802470631, "stop": 1756802470634}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "23ee340b-737d-4a3f-8214-bae1a3dc6315-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0639fe25-47f9-442b-ae39-9d20014b0a56-attachment.png", "type": "image/png"}], "start": 1756802470634, "stop": 1756802470866}], "attachments": [{"name": "stdout", "source": "5f7fd641-443f-4424-8b9d-218c5b9c739d-attachment.txt", "type": "text/plain"}], "start": 1756802446839, "stop": 1756802470866, "uuid": "b2ed02f1-f966-4c30-ba28-71494fd3b1e3", "historyId": "ca9dd7f70b2888aafceb94247d7986f0", "testCaseId": "ca9dd7f70b2888aafceb94247d7986f0", "fullName": "testcases.test_ella.unsupported_commands.test_set_phantom_v_pen.TestEllaSetPhantomVPen#test_set_phantom_v_pen", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_phantom_v_pen"}, {"name": "subSuite", "value": "TestEllaSetPhantomVPen"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_phantom_v_pen"}]}