{"name": "测试jump to nfc settings", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['jump to nfc settings', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_jump_to_nfc_settings.TestEllaOpenPlayPoliticalNews object at 0x0000018BC6526110>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6DBD960>\n\n    @allure.title(\"测试jump to nfc settings\")\n    @allure.description(\"测试jump to nfc settings指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_jump_to_nfc_settings(self, ella_app):\n        \"\"\"测试jump to nfc settings命令\"\"\"\n        command = \"jump to nfc settings\"\n        app_name = 'settings'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = ['Done']\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['jump to nfc settings', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_jump_to_nfc_settings.py:31: AssertionError"}, "description": "测试jump to nfc settings指令", "steps": [{"name": "执行命令: jump to nfc settings", "status": "passed", "steps": [{"name": "执行命令: jump to nfc settings", "status": "passed", "start": 1756799302294, "stop": 1756799326822}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "02e2602f-eaee-4c9a-af12-decd546658dc-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "84684ebc-ba26-4d34-a233-6928992b3a4f-attachment.png", "type": "image/png"}], "start": 1756799326822, "stop": 1756799327070}], "start": 1756799302294, "stop": 1756799327070}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['jump to nfc settings', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_jump_to_nfc_settings.py\", line 31, in test_jump_to_nfc_settings\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756799327070, "stop": 1756799327073}], "attachments": [{"name": "stdout", "source": "bb9e3d98-ade6-4a22-b6d9-4ef474e563a9-attachment.txt", "type": "text/plain"}], "start": 1756799302293, "stop": 1756799327074, "uuid": "e139dc61-c893-45de-b9d7-f0ae609da767", "historyId": "88a6294df5f2ab484e181b1f196ff253", "testCaseId": "88a6294df5f2ab484e181b1f196ff253", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_nfc_settings.TestEllaOpenPlayPoliticalNews#test_jump_to_nfc_settings", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_nfc_settings"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_nfc_settings"}]}