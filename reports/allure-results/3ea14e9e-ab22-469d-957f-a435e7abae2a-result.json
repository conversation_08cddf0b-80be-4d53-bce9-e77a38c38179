{"name": "测试pause music能正常执行", "status": "passed", "description": "pause music", "steps": [{"name": "执行命令: pause music", "status": "passed", "steps": [{"name": "执行命令: pause music", "status": "passed", "start": 1756783631531, "stop": 1756783651858}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "36bd13cf-4d37-4f91-92d1-1888b51391db-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e9f9681c-ef81-4d04-adc7-4ba407ad0a33-attachment.png", "type": "image/png"}], "start": 1756783651858, "stop": 1756783652075}], "start": 1756783631531, "stop": 1756783652075}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756783652075, "stop": 1756783652077}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e5d8a1ad-b550-4e2d-9794-5dc07c9f83e5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "49bd8e04-87c8-41ed-9b50-7a1fe5447a23-attachment.png", "type": "image/png"}], "start": 1756783652077, "stop": 1756783652295}], "attachments": [{"name": "stdout", "source": "304291ac-6c80-4df5-b923-************-attachment.txt", "type": "text/plain"}], "start": 1756783631531, "stop": 1756783652296, "uuid": "7a71bcdf-bb35-4a42-ad53-b468902e10c2", "historyId": "464c8ea2a15f7ff86b8e0a347a821945", "testCaseId": "464c8ea2a15f7ff86b8e0a347a821945", "fullName": "testcases.test_ella.component_coupling.test_pause_music.TestEllaPauseMusic#test_pause_music", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_pause_music"}, {"name": "subSuite", "value": "TestEllaPauseMusic"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_pause_music"}]}