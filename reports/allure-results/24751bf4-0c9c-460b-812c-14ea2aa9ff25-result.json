{"name": "测试view recent alarms能正常执行", "status": "passed", "description": "view recent alarms", "steps": [{"name": "执行命令:  delete all the alarms", "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "status": "passed", "start": 1756787382267, "stop": 1756787404211}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7ba1a389-e6ad-4e42-974d-c94df207b063-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2c19e8c1-975a-4613-a905-d8e152ce8f03-attachment.png", "type": "image/png"}], "start": 1756787404211, "stop": 1756787404436}], "start": 1756787382267, "stop": 1756787404436}, {"name": "执行命令:  Set an alarm at 10 am tomorrow", "status": "passed", "steps": [{"name": "执行命令: Set an alarm at 10 am tomorrow", "status": "passed", "start": 1756787404436, "stop": 1756787427712}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ed3bf5b6-259c-4666-8b3c-d4341478e31e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ae1babb3-c40e-4091-8d75-3efdddb3999d-attachment.png", "type": "image/png"}], "start": 1756787427712, "stop": 1756787427909}], "start": 1756787404436, "stop": 1756787427909}, {"name": "执行命令: view recent alarms", "status": "passed", "steps": [{"name": "执行命令: view recent alarms", "status": "passed", "start": 1756787427909, "stop": 1756787449868}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a485c247-baf7-4900-ad41-e656da4fbeef-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ad642bae-bae1-4d75-aa5e-9e3deed1fc55-attachment.png", "type": "image/png"}], "start": 1756787449868, "stop": 1756787450055}], "start": 1756787427909, "stop": 1756787450055}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756787450056, "stop": 1756787450057}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1f86a7df-a2c9-4268-899a-72434956cdb7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "07cfb3cd-dd91-4714-8b44-adaeda4488e9-attachment.png", "type": "image/png"}], "start": 1756787450057, "stop": 1756787450233}], "attachments": [{"name": "stdout", "source": "f35ee645-28d2-48a5-9b39-995935ab8b89-attachment.txt", "type": "text/plain"}], "start": 1756787382267, "stop": 1756787450233, "uuid": "24425078-a232-4c1b-93f6-02279b0486de", "historyId": "d960192ea83ce0c13a534ec13ca1700e", "testCaseId": "d960192ea83ce0c13a534ec13ca1700e", "fullName": "testcases.test_ella.dialogue.test_view_recent_alarms.TestEllaHowIsWeatherToday#test_view_recent_alarms", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_view_recent_alarms"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_view_recent_alarms"}]}