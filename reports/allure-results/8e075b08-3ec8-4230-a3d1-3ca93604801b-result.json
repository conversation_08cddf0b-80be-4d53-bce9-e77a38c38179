{"name": "测试close flashlight能正常执行", "status": "passed", "description": "close flashlight", "steps": [{"name": "执行命令: close flashlight", "status": "passed", "steps": [{"name": "执行命令: close flashlight", "status": "passed", "start": 1756789188448, "stop": 1756789211602}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "db3f4f48-62a0-4c74-a1eb-4f65c1e6eb0c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "74771e9b-ad5a-40ae-89f8-9a406f819e56-attachment.png", "type": "image/png"}], "start": 1756789211602, "stop": 1756789211818}], "start": 1756789188448, "stop": 1756789211819}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756789211819, "stop": 1756789211820}, {"name": "验证应用已打开", "status": "passed", "start": 1756789211820, "stop": 1756789211820}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d2272663-2ef0-4ac0-aebb-abe34bfa3794-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cc63da87-62aa-4550-be7a-d01742376c6c-attachment.png", "type": "image/png"}], "start": 1756789211820, "stop": 1756789212009}], "attachments": [{"name": "stdout", "source": "35cf3b35-d5ef-45c7-bd94-e1e58a2ab465-attachment.txt", "type": "text/plain"}], "start": 1756789188448, "stop": 1756789212009, "uuid": "3c09f400-4fe7-42f4-8975-cfc6dc20bed0", "historyId": "d18ed3013dc7867440fb611fb474ca05", "testCaseId": "d18ed3013dc7867440fb611fb474ca05", "fullName": "testcases.test_ella.system_coupling.test_close_flashlight.TestEllaCloseFlashlight#test_close_flashlight", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_close_flashlight"}, {"name": "subSuite", "value": "TestEllaCloseFlashlight"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_close_flashlight"}]}