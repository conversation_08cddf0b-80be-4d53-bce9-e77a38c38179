{"name": "测试please show me where i am能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Your current location']，实际响应: '['please show me where i am', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats 04:13 pm Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh How treat poison ivy rashes in the wild? <PERSON>, Rolex Headline 2025 Watch Moments Open Camera please show me where i am Getting location failed. DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_please_show_me_where_i_am.TestEllaPleaseShowMeWhereIAm object at 0x0000018BC65C9C30>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC9555F00>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_please_show_me_where_i_am(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Your current location']，实际响应: '['please show me where i am', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats 04:13 pm Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh How treat poison ivy rashes in the wild? Taylor Swift, Rolex Headline 2025 Watch Moments Open Camera please show me where i am Getting location failed. DeepSeek-R1 Feel free to ask me any questions…\"]'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_please_show_me_where_i_am.py:35: AssertionError"}, "description": "please show me where i am", "steps": [{"name": "执行命令: please show me where i am", "status": "passed", "steps": [{"name": "执行命令: please show me where i am", "status": "passed", "start": 1756800814926, "stop": 1756800839977}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f4106d26-eec6-4e17-9e9f-0674203ca843-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "dde4409d-3894-41c6-8f2a-ee4aae267cbd-attachment.png", "type": "image/png"}], "start": 1756800839977, "stop": 1756800840262}], "start": 1756800814926, "stop": 1756800840262}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Your current location']，实际响应: '['please show me where i am', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats 04:13 pm Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh How treat poison ivy rashes in the wild? <PERSON>, Rolex Headline 2025 Watch Moments Open Camera please show me where i am Getting location failed. DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_please_show_me_where_i_am.py\", line 35, in test_please_show_me_where_i_am\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756800840262, "stop": 1756800840267}], "attachments": [{"name": "stdout", "source": "c46783fe-19bb-4a90-bde6-0aa4ca04465c-attachment.txt", "type": "text/plain"}], "start": 1756800814925, "stop": 1756800840269, "uuid": "2d37f229-f576-448d-b972-c4fa2c247a71", "historyId": "fbd0b78d6e1c10d8dc70e2bd96aa5bdc", "testCaseId": "fbd0b78d6e1c10d8dc70e2bd96aa5bdc", "fullName": "testcases.test_ella.unsupported_commands.test_please_show_me_where_i_am.TestEllaPleaseShowMeWhereIAm#test_please_show_me_where_i_am", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_please_show_me_where_i_am"}, {"name": "subSuite", "value": "TestEllaPleaseShowMeWhereIAm"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_please_show_me_where_i_am"}]}