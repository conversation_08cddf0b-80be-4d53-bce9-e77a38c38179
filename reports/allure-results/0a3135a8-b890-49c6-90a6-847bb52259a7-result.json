{"name": "测试pause music能正常执行", "status": "passed", "description": "pause music", "steps": [{"name": "执行命令: pause music", "status": "passed", "steps": [{"name": "执行命令: pause music", "status": "passed", "start": 1756786079262, "stop": 1756786101049}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4e44a579-a161-42e6-b545-884a4fe197bc-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "14138699-3deb-4952-a588-a6896a96bba4-attachment.png", "type": "image/png"}], "start": 1756786101050, "stop": 1756786101243}], "start": 1756786079262, "stop": 1756786101244}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756786101244, "stop": 1756786101245}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e6dab8df-a897-4212-8c8a-f3c9b4e860dc-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "374a07ed-1b8b-44e8-aba3-7f8cafeb1fb5-attachment.png", "type": "image/png"}], "start": 1756786101245, "stop": 1756786101474}], "attachments": [{"name": "stdout", "source": "d7e2e5fa-7958-41a6-8674-f8ca2be9a87b-attachment.txt", "type": "text/plain"}], "start": 1756786079262, "stop": 1756786101475, "uuid": "002bc2ff-0000-473e-a270-c72e9db9cd68", "historyId": "b7bfa1ba094155307273abc83c43a0d5", "testCaseId": "b7bfa1ba094155307273abc83c43a0d5", "fullName": "testcases.test_ella.dialogue.test_pause_music.TestEllaHowIsWeatherToday#test_pause_music", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_pause_music"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_pause_music"}]}