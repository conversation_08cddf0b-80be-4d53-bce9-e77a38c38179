{"name": "测试download in play store", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['download in play store', 'Which app should I download?', '', '', '', '', '', '', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_download_in_play_store.TestEllaOpenGooglePlaystore object at 0x0000018BC646D6F0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC952F340>\n\n    @allure.title(\"测试download in play store\")\n    @allure.description(\"测试download in play store指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_download_in_play_store(self, ella_app):\n        \"\"\"测试download in play store命令\"\"\"\n        command = \"download in play store\"\n        expected_text = ['Done']\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['download in play store', 'Which app should I download?', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_download_in_play_store.py:30: AssertionError"}, "description": "测试download in play store指令", "steps": [{"name": "执行命令: download in play store", "status": "passed", "steps": [{"name": "执行命令: download in play store", "status": "passed", "start": 1756796740692, "stop": 1756796764329}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e0fa338a-7067-40d7-84e0-6ac8f9c49df7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "be35c635-6b36-48fa-abee-799b16a92bb8-attachment.png", "type": "image/png"}], "start": 1756796764329, "stop": 1756796764556}], "start": 1756796740692, "stop": 1756796764556}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['download in play store', 'Which app should I download?', '', '', '', '', '', '', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_download_in_play_store.py\", line 30, in test_download_in_play_store\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756796764556, "stop": 1756796764558}], "attachments": [{"name": "stdout", "source": "7022819e-de20-4195-903b-6d59360bad92-attachment.txt", "type": "text/plain"}], "start": 1756796740692, "stop": 1756796764560, "uuid": "5a39e86b-ae51-419d-9227-da312a21e0f7", "historyId": "e1f3b35ff3714fe0225699eb84cc1b87", "testCaseId": "e1f3b35ff3714fe0225699eb84cc1b87", "fullName": "testcases.test_ella.unsupported_commands.test_download_in_play_store.TestEllaOpenGooglePlaystore#test_download_in_play_store", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_download_in_play_store"}, {"name": "subSuite", "value": "TestEllaOpenGooglePlaystore"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_download_in_play_store"}]}