{"name": "测试turn on auto rotate screen能正常执行", "status": "passed", "description": "turn on auto rotate screen", "steps": [{"name": "执行命令: turn on auto rotate screen", "status": "passed", "steps": [{"name": "执行命令: turn on auto rotate screen", "status": "passed", "start": 1756792586798, "stop": 1756792608183}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "daba7e19-2846-4b2f-bdae-cedfa02cf3f8-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f3fce55f-7564-441b-ad2a-09062511881e-attachment.png", "type": "image/png"}], "start": 1756792608183, "stop": 1756792608390}], "start": 1756792586798, "stop": 1756792608390}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756792608390, "stop": 1756792608391}, {"name": "验证应用已打开", "status": "passed", "start": 1756792608391, "stop": 1756792608391}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5c0e2010-38b1-49f8-afa1-d2404e4c3429-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "92fd9a72-33fb-4fc0-9ced-61dc11edf049-attachment.png", "type": "image/png"}], "start": 1756792608391, "stop": 1756792608581}], "attachments": [{"name": "stdout", "source": "22dbfbee-3abf-46b3-a065-71c0f4be9efa-attachment.txt", "type": "text/plain"}], "start": 1756792586797, "stop": 1756792608581, "uuid": "16f85f1e-74f0-47c5-85f6-5a6c8185a43b", "historyId": "0f4d3881c287fa46a9fdaf099fc19f8d", "testCaseId": "0f4d3881c287fa46a9fdaf099fc19f8d", "fullName": "testcases.test_ella.system_coupling.test_turn_on_auto_rotate_screen.TestEllaTurnAutoRotateScreen#test_turn_on_auto_rotate_screen", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_auto_rotate_screen"}, {"name": "subSuite", "value": "TestEllaTurnAutoRotateScreen"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_auto_rotate_screen"}]}