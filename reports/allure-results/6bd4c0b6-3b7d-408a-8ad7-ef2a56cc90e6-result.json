{"name": "测试where`s my car能正常执行", "status": "passed", "description": "where`s my car", "steps": [{"name": "执行命令: where`s my car", "status": "passed", "steps": [{"name": "执行命令: where`s my car", "status": "passed", "start": 1756804557413, "stop": 1756804579890}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c489a50c-6ce4-46d8-abad-7a47c9492f88-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cd3c58c8-d4aa-4187-bc87-d525aa319918-attachment.png", "type": "image/png"}], "start": 1756804579892, "stop": 1756804580225}], "start": 1756804557413, "stop": 1756804580226}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756804580226, "stop": 1756804580227}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "393f9441-c5e1-41c5-a76f-21aac574f408-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "be901b96-701f-4f20-8f79-9db9c71081e5-attachment.png", "type": "image/png"}], "start": 1756804580227, "stop": 1756804580478}], "attachments": [{"name": "stdout", "source": "b4c03906-7059-42ed-bc6b-75c40ebca531-attachment.txt", "type": "text/plain"}], "start": 1756804557413, "stop": 1756804580478, "uuid": "da53819c-5321-4d69-b685-d03448ce120b", "historyId": "776dca6a65836abb5a943543ee2b9f12", "testCaseId": "776dca6a65836abb5a943543ee2b9f12", "fullName": "testcases.test_ella.unsupported_commands.test_where_s_my_car.TestEllaWhereSMyCar#test_where_s_my_car", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_where_s_my_car"}, {"name": "subSuite", "value": "TestEllaWhereSMyCar"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_where_s_my_car"}]}