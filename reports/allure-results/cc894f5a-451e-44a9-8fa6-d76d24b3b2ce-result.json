{"name": "测试i want to make a call能正常执行", "status": "passed", "description": "i want to make a call", "steps": [{"name": "执行命令: i want to make a call", "status": "passed", "steps": [{"name": "执行命令: i want to make a call", "status": "passed", "start": 1756785650766, "stop": 1756785672419}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2effff94-1d24-4a10-8dbe-4f9651246257-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "44d0b2f8-f2ca-4d5b-b391-554b3964c348-attachment.png", "type": "image/png"}], "start": 1756785672419, "stop": 1756785672630}], "start": 1756785650766, "stop": 1756785672631}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756785672631, "stop": 1756785672632}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1eec25f7-3c81-4040-86c6-3d435967af3f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f7445a16-b309-4980-9d0a-d816d31f901a-attachment.png", "type": "image/png"}], "start": 1756785672632, "stop": 1756785672845}], "attachments": [{"name": "stdout", "source": "8ef3acff-eeee-4dcb-aaad-f4eed9ed8480-attachment.txt", "type": "text/plain"}], "start": 1756785650765, "stop": 1756785672845, "uuid": "12f15f9d-2899-45fd-9537-4ab801943b40", "historyId": "f4da532f5d62abff197a05947efc027a", "testCaseId": "f4da532f5d62abff197a05947efc027a", "fullName": "testcases.test_ella.dialogue.test_i_want_to_make_a_call.TestEllaIWantMakeCall#test_i_want_to_make_a_call", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_i_want_to_make_a_call"}, {"name": "subSuite", "value": "TestEllaIWantMakeCall"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_i_want_to_make_a_call"}]}