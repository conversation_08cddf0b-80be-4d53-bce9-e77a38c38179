{"name": "测试remove alarms能正常执行", "status": "passed", "description": "remove alarms", "steps": [{"name": "执行命令: set an alarm at 8 am", "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "status": "passed", "start": 1756786495456, "stop": 1756786516674}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b9136b3a-db8c-46e4-bf8d-8a357055aa3e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cdc16895-f6ba-463e-9402-b3b5c51a3fa4-attachment.png", "type": "image/png"}], "start": 1756786516674, "stop": 1756786516857}], "start": 1756786495456, "stop": 1756786516857}, {"name": "执行命令: remove alarms", "status": "passed", "steps": [{"name": "执行命令: remove alarms", "status": "passed", "start": 1756786516857, "stop": 1756786538183}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "056c6c68-5f49-4bfd-af54-0b8b5796e0a4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c82f771d-e83a-472d-8184-75436ae34eca-attachment.png", "type": "image/png"}], "start": 1756786538183, "stop": 1756786538409}], "start": 1756786516857, "stop": 1756786538410}, {"name": "执行命令: get all the alarms", "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "status": "passed", "start": 1756786538410, "stop": 1756786559466}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6f068ef8-66f6-46d0-8f70-427dbf92ac32-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "daf9207d-78cf-46b4-87ab-4f4e595893dd-attachment.png", "type": "image/png"}], "start": 1756786559466, "stop": 1756786559695}], "start": 1756786538410, "stop": 1756786559695}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756786559695, "stop": 1756786559697}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3d36df66-8d8f-41d8-a282-4d6ca6ff5a31-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d6bcdb2e-a62b-48b2-9277-32ec6cb1d0ca-attachment.png", "type": "image/png"}], "start": 1756786559697, "stop": 1756786559921}], "attachments": [{"name": "stdout", "source": "08f62290-ca81-4ece-b821-64415eb13b36-attachment.txt", "type": "text/plain"}], "start": 1756786495456, "stop": 1756786559922, "uuid": "c55fbcd4-4191-4131-94f8-ede1987f964b", "historyId": "d28e2beb1494e42c051e9b99add608fb", "testCaseId": "d28e2beb1494e42c051e9b99add608fb", "fullName": "testcases.test_ella.dialogue.test_remove_alarms.TestEllaHowIsWeatherToday#test_remove_alarms", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_remove_alarms"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_remove_alarms"}]}