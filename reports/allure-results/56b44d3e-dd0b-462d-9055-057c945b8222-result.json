{"name": "测试close phonemaster能正常执行", "status": "passed", "description": "close phonemaster", "steps": [{"name": "执行命令: close phonemaster", "status": "passed", "steps": [{"name": "执行命令: close phonemaster", "status": "passed", "start": 1756799850527, "stop": 1756799872473}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "39fcd349-ea3d-4fdc-ad0e-ee575f30f219-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e5202558-b5e9-4fc9-9ece-3fdc6e05ec57-attachment.png", "type": "image/png"}], "start": 1756799872473, "stop": 1756799872649}], "start": 1756799850527, "stop": 1756799872649}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756799872649, "stop": 1756799872651}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c723b611-0489-41c4-b4b8-5f27fd03aaf7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "246f8372-3d2b-45db-8fa9-edb26a77d753-attachment.png", "type": "image/png"}], "start": 1756799872651, "stop": 1756799872838}], "attachments": [{"name": "stdout", "source": "417eba60-ee8b-4643-ae87-35803979baef-attachment.txt", "type": "text/plain"}], "start": 1756799850527, "stop": 1756799872838, "uuid": "4cad665b-b95e-4e9d-aca9-3a4c5926c3da", "historyId": "bc062eca91b16841cac5c9865921b5c1", "testCaseId": "bc062eca91b16841cac5c9865921b5c1", "fullName": "testcases.test_ella.component_coupling.test_close_phonemaster.TestEllaClosePhonemaster#test_close_phonemaster", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_phonemaster"}, {"name": "subSuite", "value": "TestEllaClosePhonemaster"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "30444-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_phonemaster"}]}