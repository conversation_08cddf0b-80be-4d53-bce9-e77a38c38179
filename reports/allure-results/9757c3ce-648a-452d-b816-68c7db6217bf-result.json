{"name": "测试turn off flashlight能正常执行", "status": "passed", "description": "turn off flashlight", "steps": [{"name": "执行命令: turn off flashlight", "status": "passed", "steps": [{"name": "执行命令: turn off flashlight", "status": "passed", "start": 1756792299137, "stop": 1756792320038}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "14e0c756-2edc-41b8-bc39-fd0806ff0458-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ff13bb47-e3e8-42ea-bf2b-472aa73a1dc1-attachment.png", "type": "image/png"}], "start": 1756792320038, "stop": 1756792320255}], "start": 1756792299137, "stop": 1756792320255}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756792320255, "stop": 1756792320256}, {"name": "验证应用已打开", "status": "passed", "start": 1756792320256, "stop": 1756792320256}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "702d074c-**************-076a2f61ecd1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d8c8ec86-c25b-4f52-bb5d-868a1a7c2e86-attachment.png", "type": "image/png"}], "start": 1756792320256, "stop": 1756792320453}], "attachments": [{"name": "stdout", "source": "*************-44d3-bc84-1948a05bf978-attachment.txt", "type": "text/plain"}], "start": 1756792299137, "stop": 1756792320454, "uuid": "a03e86b3-6c51-43cf-bf8b-16de073d4140", "historyId": "c190fe929896ea57ed1e33f8bc5bf113", "testCaseId": "c190fe929896ea57ed1e33f8bc5bf113", "fullName": "testcases.test_ella.system_coupling.test_turn_off_flashlight.TestEllaTurnOffFlashlight#test_turn_off_flashlight", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_off_flashlight"}, {"name": "subSuite", "value": "TestEllaTurnOffFlashlight"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_off_flashlight"}]}