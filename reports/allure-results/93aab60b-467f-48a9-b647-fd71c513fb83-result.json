{"name": "测试increase the brightness能正常执行", "status": "passed", "description": "increase the brightness", "steps": [{"name": "执行命令: increase the brightness", "status": "passed", "steps": [{"name": "执行命令: increase the brightness", "status": "passed", "start": 1756789713895, "stop": 1756789735982}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f4cf1989-f771-409a-bcd7-d10ece01f187-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "65a0090f-15c3-4d9f-8464-b8d2f1bd4069-attachment.png", "type": "image/png"}], "start": 1756789735982, "stop": 1756789736195}], "start": 1756789713895, "stop": 1756789736195}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756789736195, "stop": 1756789736196}, {"name": "验证应用已打开", "status": "passed", "start": 1756789736196, "stop": 1756789736196}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5d1e784c-436b-47ac-90e2-1984d3a92b6d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "14452d9d-2745-4eb6-9bfe-0395b80793f9-attachment.png", "type": "image/png"}], "start": 1756789736196, "stop": 1756789736384}], "attachments": [{"name": "stdout", "source": "73bdd6f0-e628-48e1-83b9-630967b7b619-attachment.txt", "type": "text/plain"}], "start": 1756789713894, "stop": 1756789736385, "uuid": "4f76904b-756e-4be9-9a70-5282f415dd72", "historyId": "e78aa9affdc78502b8ad3b712ecf28b9", "testCaseId": "e78aa9affdc78502b8ad3b712ecf28b9", "fullName": "testcases.test_ella.system_coupling.test_increase_the_brightness.TestEllaIncreaseBrightness#test_increase_the_brightness", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_increase_the_brightness"}, {"name": "subSuite", "value": "TestEllaIncreaseBrightness"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_increase_the_brightness"}]}