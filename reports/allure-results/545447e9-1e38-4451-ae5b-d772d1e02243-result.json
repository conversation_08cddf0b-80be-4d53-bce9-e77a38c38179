{"name": "测试play video by youtube", "status": "passed", "description": "测试play video by youtube指令", "steps": [{"name": "执行命令: play video by youtube", "status": "passed", "steps": [{"name": "执行命令: play video by youtube", "status": "passed", "start": 1756800768012, "stop": 1756800796272}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e09eebfd-e58f-496a-ae6d-44eb5a346eb2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "baea10e2-0449-48bf-b83c-efbaa166632b-attachment.png", "type": "image/png"}], "start": 1756800796273, "stop": 1756800796502}], "start": 1756800768012, "stop": 1756800796502}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756800796502, "stop": 1756800796503}, {"name": "验证youtube已打开", "status": "passed", "start": 1756800796503, "stop": 1756800796503}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "647b6387-a318-47d0-bf2a-2e64dbf46640-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c047035b-4027-47e7-8ab9-56b391c120b0-attachment.png", "type": "image/png"}], "start": 1756800796503, "stop": 1756800796715}], "attachments": [{"name": "stdout", "source": "f3f69e7e-841b-44c4-bc18-a0416cf46713-attachment.txt", "type": "text/plain"}], "start": 1756800768012, "stop": 1756800796716, "uuid": "bd76e90e-5fa5-4ec9-9469-2ec99f86a01d", "historyId": "0dc117f78053bbddaf3ffcf69df0af9d", "testCaseId": "0dc117f78053bbddaf3ffcf69df0af9d", "fullName": "testcases.test_ella.unsupported_commands.test_play_video_by_youtube.TestEllaOpenPlayPoliticalNews#test_play_video_by_youtube", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_video_by_youtube"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_video_by_youtube"}]}