{"name": "测试download in playstore", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['download in playstore', 'Which app should I download?', '', '', '', '', '', '', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_download_in_playstore.TestEllaOpenGooglePlaystore object at 0x0000018BC646DDE0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC95556F0>\n\n    @allure.title(\"测试download in playstore\")\n    @allure.description(\"测试download in playstore指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_download_in_playstore(self, ella_app):\n        \"\"\"测试download in playstore命令\"\"\"\n        command = \"download in playstore\"\n        expected_text = ['Done']\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['download in playstore', 'Which app should I download?', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_download_in_playstore.py:30: AssertionError"}, "description": "测试download in playstore指令", "steps": [{"name": "执行命令: download in playstore", "status": "passed", "steps": [{"name": "执行命令: download in playstore", "status": "passed", "start": 1756796781550, "stop": 1756796805885}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ac400777-35f1-4e39-bd77-e63f893199eb-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9a581410-2103-4712-a887-8788e353dda7-attachment.png", "type": "image/png"}], "start": 1756796805885, "stop": 1756796806081}], "start": 1756796781550, "stop": 1756796806081}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['download in playstore', 'Which app should I download?', '', '', '', '', '', '', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_download_in_playstore.py\", line 30, in test_download_in_playstore\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756796806081, "stop": 1756796806084}], "attachments": [{"name": "stdout", "source": "a071fed9-dbd1-46b6-89f3-64dea9e4236d-attachment.txt", "type": "text/plain"}], "start": 1756796781550, "stop": 1756796806084, "uuid": "d64bb03a-f7e4-4458-8203-1ccff7a4e222", "historyId": "c2ecb960f7f893feeaa2f24a34c9d77e", "testCaseId": "c2ecb960f7f893feeaa2f24a34c9d77e", "fullName": "testcases.test_ella.unsupported_commands.test_download_in_playstore.TestEllaOpenGooglePlaystore#test_download_in_playstore", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_download_in_playstore"}, {"name": "subSuite", "value": "TestEllaOpenGooglePlaystore"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_download_in_playstore"}]}