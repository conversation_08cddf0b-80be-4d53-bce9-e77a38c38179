{"name": "测试help me generate a picture of a puppy", "status": "passed", "description": "测试help me generate a picture of a puppy指令", "steps": [{"name": "执行命令: help me generate a picture of a puppy", "status": "passed", "steps": [{"name": "执行命令: help me generate a picture of a puppy", "status": "passed", "start": 1756798094434, "stop": 1756798116437}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "489c9594-0418-4d81-9f98-22b666067a9e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9783ee04-6ec2-4e1c-b67c-ee703c93f6bf-attachment.png", "type": "image/png"}], "start": 1756798116437, "stop": 1756798116642}], "start": 1756798094434, "stop": 1756798116643}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756798116643, "stop": 1756798116644}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7c45e0d4-bfe8-4d18-ac47-e3c34b5b7c78-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b8eb7726-55f8-4541-aec4-9a38fdfe4951-attachment.png", "type": "image/png"}], "start": 1756798116644, "stop": 1756798116864}], "attachments": [{"name": "stdout", "source": "5a6cd32e-d560-42a4-bf77-3fd4c23c9bca-attachment.txt", "type": "text/plain"}], "start": 1756798094434, "stop": 1756798116864, "uuid": "00022701-d732-408c-baed-17a99c237a03", "historyId": "89e187687dfa3317845107c44a62f287", "testCaseId": "89e187687dfa3317845107c44a62f287", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_a_puppy.TestEllaOpenPlayPoliticalNews#test_help_me_generate_a_picture_of_a_puppy", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_generate_a_picture_of_a_puppy"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_a_puppy"}]}