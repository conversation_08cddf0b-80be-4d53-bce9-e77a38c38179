{"name": "测试play news", "status": "passed", "description": "测试play news指令", "steps": [{"name": "执行命令: play news", "status": "passed", "steps": [{"name": "执行命令: play news", "status": "passed", "start": 1756786374822, "stop": 1756786399523}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "620dd596-94c1-400a-bd95-0f97c5e55748-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c06468fd-bbad-4f56-b527-1304410f2fea-attachment.png", "type": "image/png"}], "start": 1756786399523, "stop": 1756786399772}], "start": 1756786374822, "stop": 1756786399772}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756786399772, "stop": 1756786399773}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "38137cc7-f73a-46f8-a534-d94183c651eb-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1065a537-9cbe-46e7-9121-464792bf115f-attachment.png", "type": "image/png"}], "start": 1756786399773, "stop": 1756786399999}], "attachments": [{"name": "stdout", "source": "4c834ff0-5e07-487e-9d83-da167a955a87-attachment.txt", "type": "text/plain"}], "start": 1756786374822, "stop": 1756786399999, "uuid": "7eac388c-f882-48ec-b404-62878496ddef", "historyId": "fee3033814a8b17ff8c8abe6bbcdc839", "testCaseId": "fee3033814a8b17ff8c8abe6bbcdc839", "fullName": "testcases.test_ella.dialogue.test_play_news.TestEllaOpenPlayNews#test_play_news", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_news"}, {"name": "subSuite", "value": "TestEllaOpenPlayNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_news"}]}