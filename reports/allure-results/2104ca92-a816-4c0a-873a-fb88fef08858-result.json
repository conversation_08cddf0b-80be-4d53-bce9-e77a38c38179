{"name": "测试boost phone能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore <PERSON>wipe down to view earlier chats 12:56 pm Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh What are the restaurants nearby？ How to use Ask About Screen Liverpool Tops Premier League After Arsenal Win DeepSeek-R1 Feel free to ask me any questions…\", '[com.transsion.launcher3页面内容] Calendar | File Manager | Ella | Themes | Visha Player | Weather | Notepad | Freezer | HiOS Family | Tools | Chrome | Hot Apps | Hot Games | Maps | Facebook | WhatsApp | Phone | Messages | Camera']'\nassert False", "trace": "self = <testcases.test_ella.system_coupling.test_boost_phone.TestEllaBoostPhone object at 0x0000018BC611E470>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6976770>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_boost_phone(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats 12:56 pm Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh What are the restaurants nearby？ How to use Ask About Screen Liverpool Tops Premier League After Arsenal Win DeepSeek-R1 Feel free to ask me any questions…\", '[com.transsion.launcher3页面内容] Calendar | File Manager | Ella | Themes | Visha Player | Weather | Notepad | Freezer | HiOS Family | Tools | Chrome | Hot Apps | Hot Games | Maps | Facebook | WhatsApp | Phone | Messages | Camera']'\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_boost_phone.py:33: AssertionError"}, "description": "boost phone", "steps": [{"name": "执行命令: boost phone", "status": "passed", "steps": [{"name": "执行命令: boost phone", "status": "passed", "start": 1756788961195, "stop": 1756788993880}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9baaf151-b5ae-4982-9ec2-2a30abb08dd4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "79574e51-f099-4105-951e-f26bf2aff8f5-attachment.png", "type": "image/png"}], "start": 1756788993880, "stop": 1756788994076}], "start": 1756788961195, "stop": 1756788994077}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore <PERSON>wipe down to view earlier chats 12:56 pm Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh What are the restaurants nearby？ How to use Ask About Screen Liverpool Tops Premier League After Arsenal Win DeepSeek-R1 Feel free to ask me any questions…\", '[com.transsion.launcher3页面内容] Calendar | File Manager | Ella | Themes | Visha Player | Weather | Notepad | Freezer | HiOS Family | Tools | Chrome | Hot Apps | Hot Games | Maps | Facebook | WhatsApp | Phone | Messages | Camera']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\system_coupling\\test_boost_phone.py\", line 33, in test_boost_phone\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756788994077, "stop": 1756788994078}], "attachments": [{"name": "stdout", "source": "6faeedd8-ae1f-4357-b4e1-1904ee6acecf-attachment.txt", "type": "text/plain"}], "start": 1756788961194, "stop": 1756788994079, "uuid": "93aca219-e271-42b4-a323-b50140df4c37", "historyId": "29390733aaf67e070f7c061b70bad8a5", "testCaseId": "29390733aaf67e070f7c061b70bad8a5", "fullName": "testcases.test_ella.system_coupling.test_boost_phone.TestEllaBoostPhone#test_boost_phone", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_boost_phone"}, {"name": "subSuite", "value": "TestEllaBoostPhone"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_boost_phone"}]}