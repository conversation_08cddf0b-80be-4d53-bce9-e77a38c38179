{"name": "测试set nfc tag", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['set nfc tag', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_set_nfc_tag.TestEllaOpenPlayPoliticalNews object at 0x0000018BC66211E0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC92CD960>\n\n    @allure.title(\"测试set nfc tag\")\n    @allure.description(\"测试set nfc tag指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_set_nfc_tag(self, ella_app):\n        \"\"\"测试set nfc tag命令\"\"\"\n        command = \"set nfc tag\"\n        app_name = 'settings'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = ['Done']\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['set nfc tag', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_nfc_tag.py:31: AssertionError"}, "description": "测试set nfc tag指令", "steps": [{"name": "执行命令: set nfc tag", "status": "passed", "steps": [{"name": "执行命令: set nfc tag", "status": "passed", "start": 1756802264583, "stop": 1756802288674}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6d89e911-250d-43a3-93bc-6402063390a6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "70299d1a-07c5-4ebd-88aa-0b20d6c94172-attachment.png", "type": "image/png"}], "start": 1756802288674, "stop": 1756802288973}], "start": 1756802264583, "stop": 1756802288973}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['set nfc tag', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_nfc_tag.py\", line 31, in test_set_nfc_tag\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756802288973, "stop": 1756802288976}], "attachments": [{"name": "stdout", "source": "368be0d1-50c6-4cab-8234-28b0dae26e45-attachment.txt", "type": "text/plain"}], "start": 1756802264583, "stop": 1756802288977, "uuid": "1f3a9eb8-2c2a-47c5-9cfd-79938fb91abe", "historyId": "7d8296483e3dfc0902b42ccfe6759e59", "testCaseId": "7d8296483e3dfc0902b42ccfe6759e59", "fullName": "testcases.test_ella.unsupported_commands.test_set_nfc_tag.TestEllaOpenPlayPoliticalNews#test_set_nfc_tag", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_nfc_tag"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_nfc_tag"}]}