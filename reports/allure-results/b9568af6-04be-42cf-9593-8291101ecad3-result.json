{"name": "测试download qq能正常执行", "status": "passed", "description": "download qq", "steps": [{"name": "执行命令: download qq", "status": "passed", "steps": [{"name": "执行命令: download qq", "status": "passed", "start": 1756794344227, "stop": 1756794371267}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2379ef68-9a48-4866-b42e-72dd6980b454-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fdd5b5a4-a132-4f33-b565-e92b4329d1f4-attachment.png", "type": "image/png"}], "start": 1756794371267, "stop": 1756794371476}], "start": 1756794344227, "stop": 1756794371476}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756794371476, "stop": 1756794371477}, {"name": "验证应用已打开", "status": "passed", "start": 1756794371477, "stop": 1756794371477}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e5e0887b-a74e-4421-a447-68f3ad54bdf0-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "802a4c51-1b9a-4694-af2a-870f5f319765-attachment.png", "type": "image/png"}], "start": 1756794371477, "stop": 1756794371700}], "attachments": [{"name": "stdout", "source": "fa9bfd59-c79f-457a-9bdb-b0ed8d3f4865-attachment.txt", "type": "text/plain"}], "start": 1756794344227, "stop": 1756794371702, "uuid": "af7f960a-3cb7-4e23-b84e-2326cf63710c", "historyId": "b89775573784e6ef95769309baebeae4", "testCaseId": "b89775573784e6ef95769309baebeae4", "fullName": "testcases.test_ella.third_coupling.test_download_qq.TestEllaDownloadQq#test_download_qq", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_download_qq"}, {"name": "subSuite", "value": "TestEllaDownloadQq"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_download_qq"}]}