{"name": "测试close aivana能正常执行", "status": "passed", "description": "close aivana", "steps": [{"name": "执行命令: close aivana", "status": "passed", "steps": [{"name": "执行命令: close aivana", "status": "passed", "start": 1756782764531, "stop": 1756782796261}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0a644852-8773-4db7-b284-cd81f089ef1c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a4149e55-1c40-4935-b27e-80e5fe8e2a2c-attachment.png", "type": "image/png"}], "start": 1756782796261, "stop": 1756782797111}], "start": 1756782764531, "stop": 1756782797112}, {"name": "验证已打开", "status": "passed", "start": 1756782797112, "stop": 1756782797112}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7272f076-8fdd-4b48-8c8e-10ffa556403c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5048fac5-c5d0-4936-972d-7b790c098e5f-attachment.png", "type": "image/png"}], "start": 1756782797112, "stop": 1756782797315}], "attachments": [{"name": "stdout", "source": "8dc89f25-f59e-4dfe-b0ba-1a0185b78b51-attachment.txt", "type": "text/plain"}], "start": 1756782764531, "stop": 1756782797316, "uuid": "d0318990-9939-4936-a062-0c59e03ac989", "historyId": "d9f01ef1af79559082ce9e9b2e40295f", "testCaseId": "d9f01ef1af79559082ce9e9b2e40295f", "fullName": "testcases.test_ella.component_coupling.test_close_aivana.TestEllaCloseAivana#test_close_aivana", "labels": [{"name": "feature", "value": "设备信息"}, {"name": "story", "value": "设备型号: TECNO CN5"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_aivana"}, {"name": "subSuite", "value": "TestEllaCloseAivana"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_aivana"}]}