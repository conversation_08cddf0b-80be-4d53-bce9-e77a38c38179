{"name": "测试set notifications volume to 50能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=0, 最终=0, 响应='['set notifications volume to 50', 'Notification volume has been set to 50.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert 0 == 7", "trace": "self = <testcases.test_ella.system_coupling.test_set_notifications_volume_to.TestEllaSetNotificationsVolume object at 0x0000018BC61AAAD0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC617FF70>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_set_notifications_volume_to(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证应用已打开\"):\n>           assert final_status==7, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=0, 最终=0, 响应='['set notifications volume to 50', 'Notification volume has been set to 50.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert 0 == 7\n\ntestcases\\test_ella\\system_coupling\\test_set_notifications_volume_to.py:36: AssertionError"}, "description": "set notifications volume to 50", "steps": [{"name": "执行命令: set notifications volume to 50", "status": "passed", "steps": [{"name": "执行命令: set notifications volume to 50", "status": "passed", "start": 1756790881821, "stop": 1756790902819}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "54a6f7cb-f483-450a-ba69-5cc19a73c087-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "550d0814-65d7-4a4b-8efe-a2b2149b9e16-attachment.png", "type": "image/png"}], "start": 1756790902819, "stop": 1756790903048}], "start": 1756790881821, "stop": 1756790903049}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790903049, "stop": 1756790903050}, {"name": "验证应用已打开", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=0, 最终=0, 响应='['set notifications volume to 50', 'Notification volume has been set to 50.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert 0 == 7\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\system_coupling\\test_set_notifications_volume_to.py\", line 36, in test_set_notifications_volume_to\n    assert final_status==7, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1756790903050, "stop": 1756790903050}], "attachments": [{"name": "stdout", "source": "5a240c99-2e9f-4005-ba52-62a00fdf4178-attachment.txt", "type": "text/plain"}], "start": 1756790881821, "stop": 1756790903051, "uuid": "1c6210f1-af08-4715-ab25-15400b0399b4", "historyId": "13916c423237bf06a74b0b109aad0d66", "testCaseId": "13916c423237bf06a74b0b109aad0d66", "fullName": "testcases.test_ella.system_coupling.test_set_notifications_volume_to.TestEllaSetNotificationsVolume#test_set_notifications_volume_to", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_notifications_volume_to"}, {"name": "subSuite", "value": "TestEllaSetNotificationsVolume"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_notifications_volume_to"}]}