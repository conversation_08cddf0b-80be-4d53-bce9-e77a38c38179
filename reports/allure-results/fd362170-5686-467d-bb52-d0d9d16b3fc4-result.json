{"name": "测试set screen timeout返回正确的不支持响应", "status": "passed", "description": "验证set screen timeout指令返回预期的不支持响应", "steps": [{"name": "执行命令: set screen timeout", "status": "passed", "steps": [{"name": "执行命令: set screen timeout", "status": "passed", "start": 1756802685385, "stop": 1756802717573}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "911c2abc-54fc-4732-926b-5ef24a6ebe9e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "68e679fd-43a6-468e-8c97-84decb8d3fc4-attachment.png", "type": "image/png"}], "start": 1756802717573, "stop": 1756802717820}], "start": 1756802685385, "stop": 1756802717820}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756802717820, "stop": 1756802717822}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "426bb79e-280d-4020-8fdf-c8dcea7032d1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "832a972b-3b3f-4dfb-b244-0ed5fb3268f5-attachment.png", "type": "image/png"}], "start": 1756802717822, "stop": 1756802718089}], "attachments": [{"name": "stdout", "source": "bc89dd78-f29e-425c-9b8d-4354320713e7-attachment.txt", "type": "text/plain"}], "start": 1756802685384, "stop": 1756802718089, "uuid": "83c4bb2d-ebc7-4e88-938b-7a319b55625f", "historyId": "e76af38ac3a594aa2b7d7173d57e98ad", "testCaseId": "e76af38ac3a594aa2b7d7173d57e98ad", "fullName": "testcases.test_ella.unsupported_commands.test_set_screen_timeout.TestEllaSetScreenTimeout#test_set_screen_timeout", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_screen_timeout"}, {"name": "subSuite", "value": "TestEllaSetScreenTimeout"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_screen_timeout"}]}