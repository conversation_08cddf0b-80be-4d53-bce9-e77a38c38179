{"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "status": "passed", "description": "验证set scheduled power on/off and restart指令返回预期的不支持响应", "steps": [{"name": "执行命令: set scheduled power on/off and restart", "status": "passed", "steps": [{"name": "执行命令: set scheduled power on/off and restart", "status": "passed", "start": 1756802547405, "stop": 1756802576956}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "cab9eeab-1031-468d-84ae-d95c3e36dd57-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "56fc5bd9-9d0a-42c3-9c56-ecdbd9617a4a-attachment.png", "type": "image/png"}], "start": 1756802576957, "stop": 1756802577177}], "start": 1756802547405, "stop": 1756802577178}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756802577178, "stop": 1756802577179}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "62df679a-6ec1-4432-8e75-1d25e4eb4be1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a8f55119-4952-4b70-a53f-ac41bc3b0e26-attachment.png", "type": "image/png"}], "start": 1756802577179, "stop": 1756802577453}], "attachments": [{"name": "stdout", "source": "e115c4d5-5e24-4992-a417-ba50fab18a78-attachment.txt", "type": "text/plain"}], "start": 1756802547405, "stop": 1756802577454, "uuid": "7adacf7a-3eee-4876-815c-17dea2698ad3", "historyId": "e21c5dda6a9f09862a68c3a0bcda554a", "testCaseId": "e21c5dda6a9f09862a68c3a0bcda554a", "fullName": "testcases.test_ella.unsupported_commands.test_set_scheduled_power_on_off_and_restart.TestEllaSetScheduledPowerOffRestart#test_set_scheduled_power_on_off_and_restart", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_scheduled_power_on_off_and_restart"}, {"name": "subSuite", "value": "TestEllaSetScheduledPowerOffRestart"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_scheduled_power_on_off_and_restart"}]}