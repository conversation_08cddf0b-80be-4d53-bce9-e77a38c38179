{"name": "测试power saving能正常执行", "status": "passed", "description": "power saving", "steps": [{"name": "执行命令: power saving", "status": "passed", "steps": [{"name": "执行命令: power saving", "status": "passed", "start": 1756790449356, "stop": 1756790477851}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "44d3f1b7-36ea-46c3-9270-3b1f063d7f68-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "96bb8173-15b7-4a61-b4f6-bff6669a0c96-attachment.png", "type": "image/png"}], "start": 1756790477852, "stop": 1756790478037}], "start": 1756790449355, "stop": 1756790478037}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790478037, "stop": 1756790478039}, {"name": "验证PhoneMaster应用已打开", "status": "passed", "start": 1756790478039, "stop": 1756790478039}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e9e86d0b-f922-4506-a094-0467131aee50-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7877fa25-a0a2-4ce8-bf6e-82abd5e047f3-attachment.png", "type": "image/png"}], "start": 1756790478039, "stop": 1756790478237}], "attachments": [{"name": "stdout", "source": "b2ec68f3-1cad-4e4c-8e98-e5ade3f838d3-attachment.txt", "type": "text/plain"}], "start": 1756790449355, "stop": 1756790478237, "uuid": "4cb72049-40e8-4716-ac20-a5a117093af1", "historyId": "8acd3c85f9c9d0b7f252da4466c049e6", "testCaseId": "8acd3c85f9c9d0b7f252da4466c049e6", "fullName": "testcases.test_ella.system_coupling.test_power_saving.TestEllaPowerSaving#test_power_saving", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_power_saving"}, {"name": "subSuite", "value": "TestEllaPowerSaving"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_power_saving"}]}