{"name": "测试stop playing", "status": "passed", "description": "测试stop playing指令", "steps": [{"name": "执行命令: stop playing", "status": "passed", "steps": [{"name": "执行命令: stop playing", "status": "passed", "start": 1756784219175, "stop": 1756784241277}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c1209816-00e5-49ae-8d70-2acdba5ab20e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7d6b1ed8-bb08-4244-bf0d-d0beb91093a2-attachment.png", "type": "image/png"}], "start": 1756784241277, "stop": 1756784241495}], "start": 1756784219175, "stop": 1756784241495}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756784241495, "stop": 1756784241497}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6ffe3db5-9a15-4f0e-ac07-db4c91360270-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e330e794-b4eb-4705-ab0d-0451aabd1567-attachment.png", "type": "image/png"}], "start": 1756784241497, "stop": 1756784241712}], "attachments": [{"name": "stdout", "source": "6633b21e-7e11-4f0e-999c-15be2308c6cb-attachment.txt", "type": "text/plain"}], "start": 1756784219175, "stop": 1756784241712, "uuid": "a324f090-6dc4-431d-929b-7362a3ee9606", "historyId": "329fa4b06eb0b0d769c2c418ed03dab7", "testCaseId": "329fa4b06eb0b0d769c2c418ed03dab7", "fullName": "testcases.test_ella.component_coupling.test_stop_playing.TestEllaOpenYoutube#test_stop_playing", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_stop_playing"}, {"name": "subSuite", "value": "TestEllaOpenYoutube"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_stop_playing"}]}