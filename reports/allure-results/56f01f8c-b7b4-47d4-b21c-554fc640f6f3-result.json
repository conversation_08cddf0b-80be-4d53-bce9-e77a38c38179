{"name": "测试set font size返回正确的不支持响应", "status": "passed", "description": "验证set font size指令返回预期的不支持响应", "steps": [{"name": "执行命令: set font size", "status": "passed", "steps": [{"name": "执行命令: set font size", "status": "passed", "start": 1756801984651, "stop": 1756802014311}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8a5457ba-cf0d-4b78-bee1-a01ed16a5cb3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "bd371cbb-2355-478b-909e-4e938732a4f9-attachment.png", "type": "image/png"}], "start": 1756802014311, "stop": 1756802014560}], "start": 1756801984651, "stop": 1756802014561}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756802014561, "stop": 1756802014564}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "49316361-8c49-430e-bdd8-f643cbf14b7a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "586ae8f3-ab55-42eb-9e12-053de26cc1c9-attachment.png", "type": "image/png"}], "start": 1756802014564, "stop": 1756802014804}], "attachments": [{"name": "stdout", "source": "76bac9ce-b7ad-4a8d-8d21-2364e2dbe23b-attachment.txt", "type": "text/plain"}], "start": 1756801984651, "stop": 1756802014805, "uuid": "84e22b70-2d73-4057-b0b6-0ef4474a9da7", "historyId": "6c315a350a546e1382e435255d28245b", "testCaseId": "6c315a350a546e1382e435255d28245b", "fullName": "testcases.test_ella.unsupported_commands.test_set_font_size.TestEllaSetFontSize#test_set_font_size", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_font_size"}, {"name": "subSuite", "value": "TestEllaSetFontSize"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_font_size"}]}