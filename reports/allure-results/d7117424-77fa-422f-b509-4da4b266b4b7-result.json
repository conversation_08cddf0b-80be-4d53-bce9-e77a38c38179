{"name": "测试help me generate a picture of green trees in shade and distant mountains in a hazy state", "status": "passed", "description": "测试help me generate a picture of green trees in shade and distant mountains in a hazy state指令", "steps": [{"name": "执行命令: help me generate a picture of green trees in shade and distant mountains in a hazy state", "status": "passed", "steps": [{"name": "执行命令: help me generate a picture of green trees in shade and distant mountains in a hazy state", "status": "passed", "start": 1756798294174, "stop": 1756798316758}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2ea3f3a1-8724-47ae-8a68-310addf3b39e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "38f29148-e5b5-4d7c-a66a-81919709b8ab-attachment.png", "type": "image/png"}], "start": 1756798316758, "stop": 1756798316985}], "start": 1756798294174, "stop": 1756798316985}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756798316985, "stop": 1756798316988}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "da0b499b-2e4c-438e-b585-d7aa3b8f8f3c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5454af28-8b76-4ea3-9801-c1323320283d-attachment.png", "type": "image/png"}], "start": 1756798316988, "stop": 1756798317218}], "attachments": [{"name": "stdout", "source": "41a8e45a-f0fd-4523-bdb2-bc909abc4b72-attachment.txt", "type": "text/plain"}], "start": 1756798294174, "stop": 1756798317218, "uuid": "c2cbb2c1-fbef-49f3-a653-7f69a5747d43", "historyId": "5f09c86ae320138eb73de73a25f73607", "testCaseId": "5f09c86ae320138eb73de73a25f73607", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_green_trees_in_shade_and_distant_mountains_in_a_hazy_state.TestEllaOpenPlayPoliticalNews#test_help_me_generate_a_picture_of_green_trees_in_shade_and_distant_mountains_in_a_hazy_state", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_generate_a_picture_of_green_trees_in_shade_and_distant_mountains_in_a_hazy_state"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_green_trees_in_shade_and_distant_mountains_in_a_hazy_state"}]}