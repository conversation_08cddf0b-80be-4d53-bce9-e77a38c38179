{"name": "测试play music by Audiomack", "status": "passed", "description": "测试play music by Audiomack指令", "steps": [{"name": "执行命令: play music by Audiomack", "status": "passed", "steps": [{"name": "执行命令: play music by Audiomack", "status": "passed", "start": 1756800545394, "stop": 1756800583855}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1b67dacc-c079-46bc-98ab-ecbe68c6aca9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "08a34916-f6e0-4142-a203-0034b6683762-attachment.png", "type": "image/png"}], "start": 1756800583855, "stop": 1756800584119}], "start": 1756800545394, "stop": 1756800584119}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756800584119, "stop": 1756800584121}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "effe33d5-58d1-4d0d-91ef-acbff176e635-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1caeea67-4446-43c0-a261-b3426f6f683f-attachment.png", "type": "image/png"}], "start": 1756800584121, "stop": 1756800584373}], "attachments": [{"name": "stdout", "source": "7f1e662d-aeb7-45fa-a41b-a27e52638322-attachment.txt", "type": "text/plain"}], "start": 1756800545394, "stop": 1756800584374, "uuid": "00ccfefa-1fc7-4c35-ae47-490c722fcf10", "historyId": "e9039096aff36ba6e4fae58e40eb8539", "testCaseId": "e9039096aff36ba6e4fae58e40eb8539", "fullName": "testcases.test_ella.unsupported_commands.test_play_music_by_Audiomack.TestEllaOpenPlayPoliticalNews#test_play_music_by_Audiomack", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_music_by_Audiomack"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_music_by_Audiomack"}]}