{"name": "测试open folax能正常执行", "status": "passed", "description": "open folax", "steps": [{"name": "执行命令: open folax", "status": "passed", "steps": [{"name": "执行命令: open folax", "status": "passed", "start": 1756783510373, "stop": 1756783531362}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2aea58cc-7425-47a0-8747-b40ec38d00a6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ed3106cd-86f2-4da8-8f37-58aed1a6f331-attachment.png", "type": "image/png"}], "start": 1756783531362, "stop": 1756783531577}], "start": 1756783510373, "stop": 1756783531577}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1756783531577, "stop": 1756783531579}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1874a3d0-23eb-4f5a-bd87-a9e4a2865ac6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "970cd6e8-e6ea-4e54-bd56-e53bd0f75479-attachment.png", "type": "image/png"}], "start": 1756783531579, "stop": 1756783531793}], "attachments": [{"name": "stdout", "source": "20a164a3-e4bb-4834-a1bb-200b57f8ca8e-attachment.txt", "type": "text/plain"}], "start": 1756783510373, "stop": 1756783531794, "uuid": "532ad59d-c5b4-4b2b-9ac3-4fe7769155e2", "historyId": "9da64d3434f91a12d693ed9c71b62e87", "testCaseId": "9da64d3434f91a12d693ed9c71b62e87", "fullName": "testcases.test_ella.component_coupling.test_open_folax.TestEllaCommandConcise#test_open_folax", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_folax"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_folax"}]}