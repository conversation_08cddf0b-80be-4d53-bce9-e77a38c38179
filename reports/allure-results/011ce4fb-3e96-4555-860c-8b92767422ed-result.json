{"name": "测试set sim1 ringtone返回正确的不支持响应", "status": "passed", "description": "验证set sim1 ringtone指令返回预期的不支持响应", "steps": [{"name": "执行命令: set sim1 ringtone", "status": "passed", "steps": [{"name": "执行命令: set sim1 ringtone", "status": "passed", "start": 1756802787670, "stop": 1756802818824}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "30614e87-d717-4b29-809b-cdf28d5ece5b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c47fd2b9-c2f0-40cd-ab48-3dcffa35019d-attachment.png", "type": "image/png"}], "start": 1756802818824, "stop": 1756802819082}], "start": 1756802787670, "stop": 1756802819082}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756802819082, "stop": 1756802819083}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "746b3831-2e6f-44cf-acc2-ab929f33447b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "962e62c6-0bd5-4928-b6cc-e22fd4341b33-attachment.png", "type": "image/png"}], "start": 1756802819083, "stop": 1756802819309}], "attachments": [{"name": "stdout", "source": "2ae9153f-8f46-4b1b-806a-7373e9c4a214-attachment.txt", "type": "text/plain"}], "start": 1756802787670, "stop": 1756802819311, "uuid": "d999a328-30d0-4086-9857-5501399dc7b8", "historyId": "a5a3cc08eb97e600c97acb65a7439ec0", "testCaseId": "a5a3cc08eb97e600c97acb65a7439ec0", "fullName": "testcases.test_ella.unsupported_commands.test_set_sim_ringtone.TestEllaSetSimRingtone#test_set_sim_ringtone", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_sim_ringtone"}, {"name": "subSuite", "value": "TestEllaSetSimRingtone"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_sim_ringtone"}]}