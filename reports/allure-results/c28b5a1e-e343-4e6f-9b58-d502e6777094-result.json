{"name": "测试go home能正常执行", "status": "passed", "description": "go home", "steps": [{"name": "执行命令: go home", "status": "passed", "steps": [{"name": "执行命令: go home", "status": "passed", "start": 1756797677323, "stop": 1756797700463}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "cf74c574-3089-4187-8af9-d766783e515b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "470456d4-6a7f-427c-b531-1ec6fb2f7f22-attachment.png", "type": "image/png"}], "start": 1756797700463, "stop": 1756797700659}], "start": 1756797677323, "stop": 1756797700660}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756797700660, "stop": 1756797700661}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "cbf4a09c-5adf-4058-9f82-5854bd7d2063-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0c72b271-a093-46a0-8858-a43052152b2f-attachment.png", "type": "image/png"}], "start": 1756797700661, "stop": 1756797700880}], "attachments": [{"name": "stdout", "source": "8365f07a-df33-42a6-86cb-4e5749b18f99-attachment.txt", "type": "text/plain"}], "start": 1756797677323, "stop": 1756797700881, "uuid": "5483d0d3-91b6-4f59-be08-afed49717e27", "historyId": "c612b04b455e8fbc03acd18c2fc89827", "testCaseId": "c612b04b455e8fbc03acd18c2fc89827", "fullName": "testcases.test_ella.unsupported_commands.test_go_home.TestEllaGoHome#test_go_home", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_go_home"}, {"name": "subSuite", "value": "TestEllaGoHome"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_go_home"}]}