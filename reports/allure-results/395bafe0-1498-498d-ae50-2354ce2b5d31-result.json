{"name": "测试puppy", "status": "passed", "description": "测试puppy指令", "steps": [{"name": "执行命令: puppy", "status": "passed", "steps": [{"name": "执行命令: puppy", "status": "passed", "start": 1756800955169, "stop": 1756800979611}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5af3c088-1c1a-452f-b398-4880093480f3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8377c481-0c88-43ef-9f11-7e152be74100-attachment.png", "type": "image/png"}], "start": 1756800979611, "stop": 1756800979837}], "start": 1756800955169, "stop": 1756800979837}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756800979837, "stop": 1756800979840}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "85c1450e-950a-45ec-bd90-dc69ef1f7636-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1ddd1593-fa0c-4328-93ea-5b6dcba68d39-attachment.png", "type": "image/png"}], "start": 1756800979840, "stop": 1756800980118}], "attachments": [{"name": "stdout", "source": "8b2c3dce-79d6-4562-b209-5a13338d88fe-attachment.txt", "type": "text/plain"}], "start": 1756800955168, "stop": 1756800980119, "uuid": "362aa6de-7a93-4a23-bb0b-17221756a536", "historyId": "6b4c2fb43e48aa6ef45b7a33c8b2d9ef", "testCaseId": "6b4c2fb43e48aa6ef45b7a33c8b2d9ef", "fullName": "testcases.test_ella.unsupported_commands.test_puppy.TestEllaOpenPlayPoliticalNews#test_puppy", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_puppy"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_puppy"}]}