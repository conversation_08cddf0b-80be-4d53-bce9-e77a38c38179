{"name": "测试disable running lock返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['disable running lock', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_disable_running_lock.TestEllaDisableRunningLock object at 0x0000018BC646DF00>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC9557B50>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_disable_running_lock(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['disable running lock', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_disable_running_lock.py:34: AssertionError"}, "description": "验证disable running lock指令返回预期的不支持响应", "steps": [{"name": "执行命令: disable running lock", "status": "passed", "steps": [{"name": "执行命令: disable running lock", "status": "passed", "start": 1756796574959, "stop": 1756796601465}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "bfe84f18-9936-4b50-b3dd-fe260f4b21c6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "aac65d46-7dfb-4a4e-b787-c7222a4f348f-attachment.png", "type": "image/png"}], "start": 1756796601465, "stop": 1756796601701}], "start": 1756796574959, "stop": 1756796601701}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['disable running lock', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_disable_running_lock.py\", line 34, in test_disable_running_lock\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756796601701, "stop": 1756796601704}], "attachments": [{"name": "stdout", "source": "30bb4a19-9a51-4420-b73f-1a9f0db2634e-attachment.txt", "type": "text/plain"}], "start": 1756796574959, "stop": 1756796601705, "uuid": "d2d14a96-d46c-4d6f-9566-7274cf409b24", "historyId": "bb51fd67dac102de95e755be72996bd1", "testCaseId": "bb51fd67dac102de95e755be72996bd1", "fullName": "testcases.test_ella.unsupported_commands.test_disable_running_lock.TestEllaDisableRunningLock#test_disable_running_lock", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_running_lock"}, {"name": "subSuite", "value": "TestEllaDisableRunningLock"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_running_lock"}]}