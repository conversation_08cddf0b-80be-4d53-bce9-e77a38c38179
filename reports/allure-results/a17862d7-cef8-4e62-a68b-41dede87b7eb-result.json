{"name": "测试max alarm clock volume", "status": "passed", "description": "测试max alarm clock volume指令", "steps": [{"name": "执行命令: max alarm clock volume", "status": "passed", "steps": [{"name": "执行命令: max alarm clock volume", "status": "passed", "start": 1756789864163, "stop": 1756789885290}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "66c66d4b-f9ca-41a7-a58a-138f744ad361-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3b36d6f5-89bd-4441-a2d4-568bdac9c381-attachment.png", "type": "image/png"}], "start": 1756789885290, "stop": 1756789885512}], "start": 1756789864163, "stop": 1756789885513}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756789885513, "stop": 1756789885514}, {"name": "验证clock已打开", "status": "passed", "start": 1756789885514, "stop": 1756789885514}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "127e0243-0b30-4edd-aef6-019b2a0a9aea-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "98fa9c82-aa09-4db0-9c87-7b25e1665d7e-attachment.png", "type": "image/png"}], "start": 1756789885514, "stop": 1756789885704}], "attachments": [{"name": "stdout", "source": "b36dbc8e-a5dc-4995-9fc9-a6fa33b038b8-attachment.txt", "type": "text/plain"}], "start": 1756789864162, "stop": 1756789885705, "uuid": "462a079c-1941-4570-9e2c-e51391b0957c", "historyId": "8f69a86b2d665eb6925fa007d973040e", "testCaseId": "8f69a86b2d665eb6925fa007d973040e", "fullName": "testcases.test_ella.system_coupling.test_max_alarm_clock_volume.TestEllaOpenClock#test_max_alarm_clock_volume", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_max_alarm_clock_volume"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_max_alarm_clock_volume"}]}