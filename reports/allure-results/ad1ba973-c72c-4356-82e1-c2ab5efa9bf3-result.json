{"name": "测试a clear glass cup", "status": "passed", "description": "测试a clear glass cup指令", "steps": [{"name": "执行命令: a clear glass cup", "status": "passed", "steps": [{"name": "执行命令: a clear glass cup", "status": "passed", "start": 1756795014969, "stop": 1756795040366}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d6577ef3-d0ca-431b-98f2-171a43083fe0-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2f8d1c24-3a84-4d10-bb6c-17c4723b6d08-attachment.png", "type": "image/png"}], "start": 1756795040366, "stop": 1756795040611}], "start": 1756795014969, "stop": 1756795040612}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756795040612, "stop": 1756795040613}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "368205fc-b055-458f-8c19-93e56d6d6d13-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "61e6435f-46b3-41bc-a04f-59e89b3ac95d-attachment.png", "type": "image/png"}], "start": 1756795040613, "stop": 1756795040839}], "attachments": [{"name": "stdout", "source": "81fc8805-1b10-421e-8e43-b492a7ac3dde-attachment.txt", "type": "text/plain"}], "start": 1756795014969, "stop": 1756795040839, "uuid": "1e7b9a57-9b67-4201-994b-845b9906bc31", "historyId": "728822fc623e888cd9efa450f4737787", "testCaseId": "728822fc623e888cd9efa450f4737787", "fullName": "testcases.test_ella.unsupported_commands.test_a_clear_glass_cup.TestEllaOpenPlayPoliticalNews#test_a_clear_glass_cup", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_a_clear_glass_cup"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_a_clear_glass_cup"}]}