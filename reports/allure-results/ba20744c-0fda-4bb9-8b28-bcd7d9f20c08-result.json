{"name": "测试find a restaurant near me能正常执行", "status": "passed", "description": "find a restaurant near me", "steps": [{"name": "执行命令: find a restaurant near me", "status": "passed", "steps": [{"name": "执行命令: find a restaurant near me", "status": "passed", "start": 1756794387998, "stop": 1756794420060}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dbb36a61-00f6-4e9b-baf6-c05c14d7aa0a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "51df8824-d647-4da8-9cba-8d6f652e0538-attachment.png", "type": "image/png"}], "start": 1756794420060, "stop": 1756794420266}], "start": 1756794387998, "stop": 1756794420266}, {"name": "验证已打开", "status": "passed", "start": 1756794420266, "stop": 1756794420266}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d55d516b-fd25-4d7e-a855-b4c1494578e3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f095e947-5e45-4f2d-8eb2-d52c66c989b1-attachment.png", "type": "image/png"}], "start": 1756794420266, "stop": 1756794420527}], "attachments": [{"name": "stdout", "source": "3207d35c-74d7-49a8-8011-63c428ee55c4-attachment.txt", "type": "text/plain"}], "start": 1756794387998, "stop": 1756794420528, "uuid": "444cc8d4-757d-48f5-979a-ee1ae1cf58a8", "historyId": "918c52f1eb9803594ff76c724b43d5f8", "testCaseId": "918c52f1eb9803594ff76c724b43d5f8", "fullName": "testcases.test_ella.third_coupling.test_find_a_restaurant_near_me.TestEllaFindRestaurantNearMe#test_find_a_restaurant_near_me", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_find_a_restaurant_near_me"}, {"name": "subSuite", "value": "TestEllaFindRestaurantNearMe"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_find_a_restaurant_near_me"}]}