{"name": "测试my phone is too slow能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore <PERSON>wi<PERSON> down to view earlier chats 11:19 am <PERSON>, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh USA Collapses in AmeriCup Semis Design a fun coding game Send my recent photo to mom on WhatsApp DeepSeek-R1 Feel free to ask me any questions…\", '[com.transsion.phonemaster页面] 未获取到文本内容']'\nassert False", "trace": "self = <testcases.test_ella.component_coupling.test_my_phone_is_too_slow.TestEllaMyPhoneIsTooSlow object at 0x0000018BC4EE6E60>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC67700D0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_my_phone_is_too_slow(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats 11:19 am Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh USA Collapses in AmeriCup Semis Design a fun coding game Send my recent photo to mom on WhatsApp DeepSeek-R1 Feel free to ask me any questions…\", '[com.transsion.phonemaster页面] 未获取到文本内容']'\nE           assert False\n\ntestcases\\test_ella\\component_coupling\\test_my_phone_is_too_slow.py:33: AssertionError"}, "description": "my phone is too slow", "steps": [{"name": "执行命令: my phone is too slow", "status": "passed", "steps": [{"name": "执行命令: my phone is too slow", "status": "passed", "start": 1756783156637, "stop": 1756783184364}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "097e228a-0290-4550-835e-5282e29fd3e2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e89ce9f6-b9c7-4cdf-9b2e-a641a63a6266-attachment.png", "type": "image/png"}], "start": 1756783184364, "stop": 1756783184587}], "start": 1756783156637, "stop": 1756783184587}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore <PERSON>wi<PERSON> down to view earlier chats 11:19 am <PERSON>, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh USA Collapses in AmeriCup Semis Design a fun coding game Send my recent photo to mom on WhatsApp DeepSeek-R1 Feel free to ask me any questions…\", '[com.transsion.phonemaster页面] 未获取到文本内容']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\component_coupling\\test_my_phone_is_too_slow.py\", line 33, in test_my_phone_is_too_slow\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756783184587, "stop": 1756783184598}], "attachments": [{"name": "stdout", "source": "96b08147-a145-4fb4-abfe-07645ff8c88a-attachment.txt", "type": "text/plain"}], "start": 1756783156637, "stop": 1756783184599, "uuid": "ed6db9f6-c35c-443f-a2e1-af13206fa092", "historyId": "0a0a3640b2ba4adce516043bd9362070", "testCaseId": "0a0a3640b2ba4adce516043bd9362070", "fullName": "testcases.test_ella.component_coupling.test_my_phone_is_too_slow.TestEllaMyPhoneIsTooSlow#test_my_phone_is_too_slow", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_my_phone_is_too_slow"}, {"name": "subSuite", "value": "TestEllaMyPhoneIsTooSlow"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_my_phone_is_too_slow"}]}