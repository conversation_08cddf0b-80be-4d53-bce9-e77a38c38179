{"name": "测试Scan the QR code in the image 能正常执行", "status": "passed", "description": "Scan the QR code in the image ", "steps": [{"name": "执行命令: Scan the QR code in the image ", "status": "passed", "steps": [{"name": "执行命令: Scan the QR code in the image ", "status": "passed", "start": 1756788505152, "stop": 1756788614968}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "353eb2ef-af05-479a-898a-8ece40df5aca-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "970df158-20fc-4c47-b2f8-7d482bad3648-attachment.png", "type": "image/png"}], "start": 1756788614968, "stop": 1756788615196}], "start": 1756788505152, "stop": 1756788615196}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756788615196, "stop": 1756788615198}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "22ceaced-d384-4dd7-8a00-0edcac1d7c61-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c1e124f8-a3da-46a3-9337-53edc2899122-attachment.png", "type": "image/png"}], "start": 1756788615198, "stop": 1756788615428}], "attachments": [{"name": "stdout", "source": "a006d3d8-a2ab-428c-836f-1f8e08feb7fa-attachment.txt", "type": "text/plain"}], "start": 1756788505152, "stop": 1756788615429, "uuid": "e2f24c67-3996-432e-9200-84eb6c8767f8", "historyId": "1498285b0d63f23df3a22c9c5262f7f3", "testCaseId": "1498285b0d63f23df3a22c9c5262f7f3", "fullName": "testcases.test_ella.self_function.test_scan_the_qr_code_in_the_image.TestEllaScanQrCodeImage#test_scan_the_qr_code_in_the_image", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.self_function"}, {"name": "suite", "value": "test_scan_the_qr_code_in_the_image"}, {"name": "subSuite", "value": "TestEllaScanQrCodeImage"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.self_function.test_scan_the_qr_code_in_the_image"}]}