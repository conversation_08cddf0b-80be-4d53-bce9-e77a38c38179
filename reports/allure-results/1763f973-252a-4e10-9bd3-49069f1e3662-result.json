{"name": "测试Generate a picture in the night forest for me", "status": "passed", "description": "测试Generate a picture in the night forest for me指令", "steps": [{"name": "执行命令: Generate a picture in the night forest for me", "status": "passed", "steps": [{"name": "执行命令: Generate a picture in the night forest for me", "status": "passed", "start": 1756797556295, "stop": 1756797579808}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c07507b9-76c7-4516-9fe0-94f32303f77d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9b277b1f-6690-4faf-9f24-ee82883573b9-attachment.png", "type": "image/png"}], "start": 1756797579808, "stop": 1756797580051}], "start": 1756797556295, "stop": 1756797580051}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756797580051, "stop": 1756797580052}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "77545828-196e-4afb-bb90-65fe54155bc4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "930a551f-7900-43b3-912f-569fda5fc7b5-attachment.png", "type": "image/png"}], "start": 1756797580052, "stop": 1756797580267}], "attachments": [{"name": "stdout", "source": "c06a85c2-4827-4a02-98b7-4582d36c128b-attachment.txt", "type": "text/plain"}], "start": 1756797556295, "stop": 1756797580267, "uuid": "fa0ef567-460f-4755-84d2-d54e69da8fed", "historyId": "5d0a6cda1787168fa2fdaaae1dee86f3", "testCaseId": "5d0a6cda1787168fa2fdaaae1dee86f3", "fullName": "testcases.test_ella.unsupported_commands.test_generate_a_picture_in_the_night_forest_for_me.TestEllaOpenPlayPoliticalNews#test_generate_a_picture_in_the_night_forest_for_me", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_generate_a_picture_in_the_night_forest_for_me"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_generate_a_picture_in_the_night_forest_for_me"}]}