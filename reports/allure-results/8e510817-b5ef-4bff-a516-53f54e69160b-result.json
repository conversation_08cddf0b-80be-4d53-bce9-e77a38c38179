{"name": "测试Switch to Barrage Notification能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['Switch to Barrage Notification', 'This feature is temporarily not supported.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.system_coupling.test_switch_to_barrage_notification.TestEllaSwitchBarrageNotification object at 0x0000018BC61C3BE0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC7CC9A80>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_switch_to_barrage_notification(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['Switch to Barrage Notification', 'This feature is temporarily not supported.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_switch_to_barrage_notification.py:34: AssertionError"}, "description": "Switch to Barrage Notification", "steps": [{"name": "执行命令: Switch to Barrage Notification", "status": "passed", "steps": [{"name": "执行命令: Switch to Barrage Notification", "status": "passed", "start": 1756791530804, "stop": 1756791551272}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "bbd1c9b1-b38d-4cca-a6be-33218c208660-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "13649f2b-3de6-4ee9-aad0-f249e4012f33-attachment.png", "type": "image/png"}], "start": 1756791551272, "stop": 1756791551508}], "start": 1756791530804, "stop": 1756791551508}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['Switch to Barrage Notification', 'This feature is temporarily not supported.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\system_coupling\\test_switch_to_barrage_notification.py\", line 34, in test_switch_to_barrage_notification\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756791551508, "stop": 1756791551511}], "attachments": [{"name": "stdout", "source": "7d6de927-fc98-4659-8774-93e92f90963b-attachment.txt", "type": "text/plain"}], "start": 1756791530803, "stop": 1756791551512, "uuid": "d1d1c831-dd8b-4a38-9390-41096cc3d2fb", "historyId": "79b68fd9ac84793c5f55250aad03649a", "testCaseId": "79b68fd9ac84793c5f55250aad03649a", "fullName": "testcases.test_ella.system_coupling.test_switch_to_barrage_notification.TestEllaSwitchBarrageNotification#test_switch_to_barrage_notification", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_barrage_notification"}, {"name": "subSuite", "value": "TestEllaSwitchBarrageNotification"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_barrage_notification"}]}