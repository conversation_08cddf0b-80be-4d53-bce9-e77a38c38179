{"name": "测试set color style返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['set color style', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_set_color_style.TestEllaSetColorStyle object at 0x0000018BC6604760>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC9D7C0A0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_color_style(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['set color style', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_color_style.py:34: AssertionError"}, "description": "验证set color style指令返回预期的不支持响应", "steps": [{"name": "执行命令: set color style", "status": "passed", "steps": [{"name": "执行命令: set color style", "status": "passed", "start": 1756801565050, "stop": 1756801589698}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "46414b79-3a22-4996-a584-33c27b07ab98-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3a92274b-622d-4a49-833e-63c5fdc60270-attachment.png", "type": "image/png"}], "start": 1756801589698, "stop": 1756801589973}], "start": 1756801565050, "stop": 1756801589973}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['set color style', 'Multiple settings options found. Which one would you like to access?', '', '', '', '', '', '', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_color_style.py\", line 34, in test_set_color_style\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756801589973, "stop": 1756801589977}], "attachments": [{"name": "stdout", "source": "0c7dc071-9c0e-4cc5-9d22-ce21190923f8-attachment.txt", "type": "text/plain"}], "start": 1756801565050, "stop": 1756801589979, "uuid": "1b7c699b-f764-4843-9ce0-54014f3206c7", "historyId": "e2beda2a0bda4155b33d47f14bdcb9ed", "testCaseId": "e2beda2a0bda4155b33d47f14bdcb9ed", "fullName": "testcases.test_ella.unsupported_commands.test_set_color_style.TestEllaSetColorStyle#test_set_color_style", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_color_style"}, {"name": "subSuite", "value": "TestEllaSetColorStyle"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_color_style"}]}