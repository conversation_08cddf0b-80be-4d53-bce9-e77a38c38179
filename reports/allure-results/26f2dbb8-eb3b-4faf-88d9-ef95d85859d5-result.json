{"name": "测试create a metting schedule at tomorrow能正常执行", "status": "passed", "description": "create a metting schedule at tomorrow", "steps": [{"name": "执行命令: create a metting schedule at tomorrow", "status": "passed", "steps": [{"name": "执行命令: create a metting schedule at tomorrow", "status": "passed", "start": 1756799918582, "stop": 1756799941535}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b874eb10-a897-44a9-b0aa-b2b7781dde16-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9a38f9e7-e96c-4f9c-8341-10403a5e3d2a-attachment.png", "type": "image/png"}], "start": 1756799941535, "stop": 1756799941713}], "start": 1756799918582, "stop": 1756799941713}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756799941713, "stop": 1756799941715}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0cb1d14e-d264-4160-8539-dfdf1c5585d4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0c5770d3-0e93-4e9c-b138-46022a2353ae-attachment.png", "type": "image/png"}], "start": 1756799941715, "stop": 1756799941885}], "attachments": [{"name": "stdout", "source": "1fbb23f9-f2b5-4d6e-83ed-************-attachment.txt", "type": "text/plain"}], "start": 1756799918582, "stop": 1756799941886, "uuid": "8ba17cd2-26da-4211-ad26-e6252eaebc51", "historyId": "ea48444c2b0789e59a64850ccfab3722", "testCaseId": "ea48444c2b0789e59a64850ccfab3722", "fullName": "testcases.test_ella.component_coupling.test_create_a_metting_schedule_at_tomorrow.TestEllaCreateMettingScheduleTomorrow#test_create_a_metting_schedule_at_tomorrow", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_create_a_metting_schedule_at_tomorrow"}, {"name": "subSuite", "value": "TestEllaCreateMettingScheduleTomorrow"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "30444-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_create_a_metting_schedule_at_tomorrow"}]}