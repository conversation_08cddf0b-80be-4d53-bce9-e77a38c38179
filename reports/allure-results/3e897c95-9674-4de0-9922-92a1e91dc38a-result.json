{"name": "stop  screen recording能正常执行", "status": "passed", "description": "stop  screen recording", "steps": [{"name": "执行命令: stop screen recording", "status": "passed", "steps": [{"name": "执行命令: stop screen recording", "status": "passed", "start": 1756791287423, "stop": 1756791336248}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "81ffaacf-9512-4053-bf4a-aec5e531702a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "053c59ad-0db0-4317-b9a6-4736283aff77-attachment.png", "type": "image/png"}], "start": 1756791336249, "stop": 1756791336459}], "start": 1756791287423, "stop": 1756791336459}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756791336459, "stop": 1756791336460}, {"name": "验证已打开", "status": "passed", "start": 1756791336460, "stop": 1756791336460}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7db9e5ed-c20b-42d2-8b04-3cfefd92f962-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "27c68d8a-cf6a-421e-8381-803fb599cbf7-attachment.png", "type": "image/png"}], "start": 1756791336460, "stop": 1756791336659}], "attachments": [{"name": "stdout", "source": "fb854673-6617-4e8e-9f81-33e150c1d943-attachment.txt", "type": "text/plain"}], "start": 1756791287423, "stop": 1756791336659, "uuid": "eeb904e9-b254-4b5d-ae33-1760942be056", "historyId": "cda905ef365af8bbcc5fba28f6bde9ea", "testCaseId": "cda905ef365af8bbcc5fba28f6bde9ea", "fullName": "testcases.test_ella.system_coupling.test_start_screen_recording.TestEllaStartScreenRecording#test_stop_screen_recording", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_start_screen_recording"}, {"name": "subSuite", "value": "TestEllaStartScreenRecording"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_start_screen_recording"}]}