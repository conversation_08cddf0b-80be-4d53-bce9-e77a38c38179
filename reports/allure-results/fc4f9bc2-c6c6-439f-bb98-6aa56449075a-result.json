{"name": "测试open camera能正常执行", "status": "passed", "description": "open camera", "steps": [{"name": "执行命令: open camera", "status": "passed", "steps": [{"name": "执行命令: open camera", "status": "passed", "start": 1756783237328, "stop": 1756783269825}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "23876126-d446-4c55-8f27-0c5dc426ea0e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e31553ef-ad23-457f-9af2-9da63faf9309-attachment.png", "type": "image/png"}], "start": 1756783269825, "stop": 1756783270030}], "start": 1756783237328, "stop": 1756783270030}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1756783270030, "stop": 1756783270031}, {"name": "验证已打开", "status": "passed", "start": 1756783270032, "stop": 1756783270032}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d262a080-56bc-4021-8e3e-33c3217b49fe-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f99504ae-e621-4728-97ec-50c47b239383-attachment.png", "type": "image/png"}], "start": 1756783270032, "stop": 1756783270237}], "attachments": [{"name": "stdout", "source": "7590c53d-1369-494b-95b1-916da2eef0bf-attachment.txt", "type": "text/plain"}], "start": 1756783237327, "stop": 1756783270237, "uuid": "2e923b25-b596-4969-b37d-17c068aaec5e", "historyId": "ae0ee984c3712fd05ea04b52289e14fe", "testCaseId": "ae0ee984c3712fd05ea04b52289e14fe", "fullName": "testcases.test_ella.component_coupling.test_open_camera.TestEllaCommandConcise#test_open_camera", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_camera"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_camera"}]}