{"name": "测试turn on the 7AM alarm", "status": "passed", "description": "测试turn on the 7AM alarm指令", "steps": [{"name": "执行命令: delete all the alarms", "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "status": "passed", "start": 1756792933690, "stop": 1756792953637}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "010f74e6-dfc4-4183-a767-bd60720cbb56-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ab2381c3-bb83-4b01-9be0-30d8588439df-attachment.png", "type": "image/png"}], "start": 1756792953637, "stop": 1756792953864}], "start": 1756792933690, "stop": 1756792953865}, {"name": "执行命令: set an alarm at 7 am", "status": "passed", "steps": [{"name": "执行命令: set an alarm at 7 am", "status": "passed", "start": 1756792953865, "stop": 1756792973878}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "bf2f0214-c423-4629-aa40-12d7d34ab9cf-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "018c2e10-6d0a-4361-beff-b759586db332-attachment.png", "type": "image/png"}], "start": 1756792973878, "stop": 1756792974068}], "start": 1756792953865, "stop": 1756792974069}, {"name": "执行命令: turn on the 7AM alarm", "status": "passed", "steps": [{"name": "执行命令: turn on the 7AM alarm", "status": "passed", "start": 1756792974069, "stop": 1756792993826}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d180cf50-00d6-488c-b987-7247522146e2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "91022cb4-27a9-492f-afeb-00a57dbc903b-attachment.png", "type": "image/png"}], "start": 1756792993826, "stop": 1756792994050}], "start": 1756792974069, "stop": 1756792994050}, {"name": "执行命令: get all the alarms", "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "status": "passed", "start": 1756792994050, "stop": 1756793013846}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a4773674-0a72-43af-9222-c3a3bc125d89-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3335bc34-576e-4b58-b944-994c71346ea8-attachment.png", "type": "image/png"}], "start": 1756793013846, "stop": 1756793014060}], "start": 1756792994050, "stop": 1756793014060}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756793014061, "stop": 1756793014062}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c867e44f-886d-4874-a99f-191b255c23eb-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5a6ba288-e414-47cb-97e3-5443a80327cb-attachment.png", "type": "image/png"}], "start": 1756793014062, "stop": 1756793014272}], "attachments": [{"name": "stdout", "source": "6d0ef3f5-45b1-4347-9592-4b4f76bc3aea-attachment.txt", "type": "text/plain"}], "start": 1756792933689, "stop": 1756793014273, "uuid": "54d9039f-d853-4471-8871-b2fb24e6dd5f", "historyId": "d235f5ef7da67b833ad362b7c693c8f1", "testCaseId": "d235f5ef7da67b833ad362b7c693c8f1", "fullName": "testcases.test_ella.system_coupling.test_turn_on_the_7am_alarm.TestEllaOpenClock#test_turn_on_the_am_alarm", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_the_7am_alarm"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_the_7am_alarm"}]}