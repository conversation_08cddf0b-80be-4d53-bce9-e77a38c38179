{"name": "测试What languages do you support能正常执行", "status": "passed", "description": "What languages do you support", "steps": [{"name": "执行命令: What languages do you support", "status": "passed", "steps": [{"name": "执行命令: What languages do you support", "status": "passed", "start": 1756787507248, "stop": 1756787528688}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "acbfd26c-cef4-4ef8-aab8-d43003424588-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1a3e75ac-7425-41de-ac6f-155570abd655-attachment.png", "type": "image/png"}], "start": 1756787528688, "stop": 1756787528921}], "start": 1756787507248, "stop": 1756787528921}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756787528921, "stop": 1756787528923}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a0a2499f-b6cf-456e-9889-b487c07a2bea-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "411cfb1c-7cdc-4a3d-8f65-f460583efa17-attachment.png", "type": "image/png"}], "start": 1756787528923, "stop": 1756787529170}], "attachments": [{"name": "stdout", "source": "c9d82bd8-753d-4883-9ee8-2fffd16152bb-attachment.txt", "type": "text/plain"}], "start": 1756787507248, "stop": 1756787529170, "uuid": "9f30f58a-739a-4dd5-b910-3250c5e65f6e", "historyId": "0c44c94f08feed70addcec44e96bda5a", "testCaseId": "0c44c94f08feed70addcec44e96bda5a", "fullName": "testcases.test_ella.dialogue.test_what_languages_do_you_support.TestEllaWhatLanguagesDoYouSupport#test_what_languages_do_you_support", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_languages_do_you_support"}, {"name": "subSuite", "value": "TestEllaWhatLanguagesDoYouSupport"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_languages_do_you_support"}]}