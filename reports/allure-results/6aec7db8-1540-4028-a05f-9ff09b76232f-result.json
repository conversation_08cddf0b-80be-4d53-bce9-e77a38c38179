{"name": "测试install whatsapp", "status": "passed", "description": "测试install whatsapp指令", "steps": [{"name": "执行命令: install whatsapp", "status": "passed", "steps": [{"name": "执行命令: install whatsapp", "status": "passed", "start": 1756798816144, "stop": 1756798844462}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "101707b6-620a-4a45-8820-65e23a025495-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cec083bb-7d46-4ddc-8fc2-798b7775769f-attachment.png", "type": "image/png"}], "start": 1756798844462, "stop": 1756798844730}], "start": 1756798816144, "stop": 1756798844731}, {"name": "验证google_playstore已打开", "status": "passed", "start": 1756798844731, "stop": 1756798844731}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9c3071da-37bb-4186-aa7a-afde6308b243-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fcf30387-45b5-44b2-8172-2c9ba6c2de1f-attachment.png", "type": "image/png"}], "start": 1756798844731, "stop": 1756798844972}], "attachments": [{"name": "stdout", "source": "714fcebd-c982-4136-baff-9e1e972e4896-attachment.txt", "type": "text/plain"}], "start": 1756798816144, "stop": 1756798844973, "uuid": "40c63b55-76d0-439f-8f5a-339eb81527e8", "historyId": "434b905bf8be3ce2ee79606468e155db", "testCaseId": "434b905bf8be3ce2ee79606468e155db", "fullName": "testcases.test_ella.unsupported_commands.test_install_whatsapp.TestEllaOpenPlayPoliticalNews#test_install_whatsapp", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_install_whatsapp"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_install_whatsapp"}]}