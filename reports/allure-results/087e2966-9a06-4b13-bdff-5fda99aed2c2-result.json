{"name": "测试set my themes返回正确的不支持响应", "status": "passed", "description": "验证set my themes指令返回预期的不支持响应", "steps": [{"name": "执行命令: set my themes", "status": "passed", "steps": [{"name": "执行命令: set my themes", "status": "passed", "start": 1756802219210, "stop": 1756802246775}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "877c6971-d9f4-42d2-860a-c636315c4f5f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5dcbe353-8605-47b5-91ba-44bfdd59f8a1-attachment.png", "type": "image/png"}], "start": 1756802246775, "stop": 1756802247123}], "start": 1756802219210, "stop": 1756802247123}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756802247123, "stop": 1756802247125}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "af2d6be1-de3b-4477-bcd6-d98355a24115-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3da7efa5-0b81-47cf-b0e9-56feb38d5102-attachment.png", "type": "image/png"}], "start": 1756802247125, "stop": 1756802247387}], "attachments": [{"name": "stdout", "source": "92d89cc7-6a93-4232-bb0d-92d0c0e5dc4c-attachment.txt", "type": "text/plain"}], "start": 1756802219209, "stop": 1756802247387, "uuid": "6f31fd01-c815-4248-9f7d-55df4ddb4397", "historyId": "084048a337d3081654dc4414f67fce70", "testCaseId": "084048a337d3081654dc4414f67fce70", "fullName": "testcases.test_ella.unsupported_commands.test_set_my_themes.TestEllaSetMyThemes#test_set_my_themes", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_my_themes"}, {"name": "subSuite", "value": "TestEllaSetMyThemes"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_my_themes"}]}