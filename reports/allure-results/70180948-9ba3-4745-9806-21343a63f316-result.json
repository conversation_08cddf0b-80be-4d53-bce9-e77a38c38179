{"name": "测试turn on the alarm at 8 am", "status": "passed", "description": "测试turn on the alarm at 8 am指令", "steps": [{"name": "执行命令: delete all the alarms", "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "status": "passed", "start": 1756784505779, "stop": 1756784527119}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "903a4679-cc64-4044-8219-4dfe993538b4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "24574407-0be2-4375-b62f-ad1ffa258813-attachment.png", "type": "image/png"}], "start": 1756784527119, "stop": 1756784527357}], "start": 1756784505779, "stop": 1756784527358}, {"name": "执行命令: set an alarm at 8 am", "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "status": "passed", "start": 1756784527358, "stop": 1756784549641}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "23423826-cc61-4619-b64f-b3648da90829-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "dc137f99-be09-4f16-a575-804db787a77b-attachment.png", "type": "image/png"}], "start": 1756784549641, "stop": 1756784549837}], "start": 1756784527358, "stop": 1756784549838}, {"name": "执行命令: turn on the alarm at 8 am", "status": "passed", "steps": [{"name": "执行命令: turn on the alarm at 8 am", "status": "passed", "start": 1756784549838, "stop": 1756784570406}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dd6e0420-63b8-40ba-b868-4b8815b0d987-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4fc12223-e145-4070-a3ed-0626cc91c829-attachment.png", "type": "image/png"}], "start": 1756784570406, "stop": 1756784570632}], "start": 1756784549838, "stop": 1756784570632}, {"name": "执行命令: get all the alarms", "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "status": "passed", "start": 1756784570632, "stop": 1756784591586}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a1c3aea6-f128-456a-8074-0c22014dae64-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "bae9255f-7b6c-48e2-986c-5cf28891c34d-attachment.png", "type": "image/png"}], "start": 1756784591586, "stop": 1756784591805}], "start": 1756784570632, "stop": 1756784591805}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756784591805, "stop": 1756784591807}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "cfffa50c-ccae-45d0-9653-7c06d41c58d9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e269b6f0-0c86-42ab-b507-b27ace5e5b70-attachment.png", "type": "image/png"}], "start": 1756784591807, "stop": 1756784592029}], "attachments": [{"name": "stdout", "source": "e496f656-fc59-4175-b82a-0115e05b954f-attachment.txt", "type": "text/plain"}], "start": 1756784505778, "stop": 1756784592029, "uuid": "b50cc8a7-5222-4072-9b3a-57d9aad63b62", "historyId": "b165a17be8ab35920a6af9be7611a2c9", "testCaseId": "b165a17be8ab35920a6af9be7611a2c9", "fullName": "testcases.test_ella.component_coupling.test_turn_on_the_alarm_at_8_am.TestEllaOpenClock#test_turn_on_the_alarm_at_8_am", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_turn_on_the_alarm_at_8_am"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_turn_on_the_alarm_at_8_am"}]}