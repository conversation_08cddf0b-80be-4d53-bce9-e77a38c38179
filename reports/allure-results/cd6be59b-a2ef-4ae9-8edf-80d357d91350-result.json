{"name": "测试set split-screen apps返回正确的不支持响应", "status": "passed", "description": "验证set split-screen apps指令返回预期的不支持响应", "steps": [{"name": "执行命令: set split-screen apps", "status": "passed", "steps": [{"name": "执行命令: set split-screen apps", "status": "passed", "start": 1756802972315, "stop": 1756803001858}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "12367507-3403-431e-a2dc-e6b1c567fa03-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cae26cce-625c-4777-88a6-6c7b38aeccc5-attachment.png", "type": "image/png"}], "start": 1756803001858, "stop": 1756803002099}], "start": 1756802972315, "stop": 1756803002099}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756803002099, "stop": 1756803002101}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9497e15c-b1be-4a5b-beac-dc16988c06ac-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e319e039-3b25-4ec8-b082-2ac4d33a1f90-attachment.png", "type": "image/png"}], "start": 1756803002101, "stop": 1756803002360}], "attachments": [{"name": "stdout", "source": "b9ee1bc5-552c-41dd-8ae3-25eda80cf78b-attachment.txt", "type": "text/plain"}], "start": 1756802972315, "stop": 1756803002362, "uuid": "09105392-dc53-4cf9-bf70-46f280097427", "historyId": "b5e1711cce3102fc710ff74e18bf9129", "testCaseId": "b5e1711cce3102fc710ff74e18bf9129", "fullName": "testcases.test_ella.unsupported_commands.test_set_split_screen_apps.TestEllaSetSplitScreenApps#test_set_split_screen_apps", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_split_screen_apps"}, {"name": "subSuite", "value": "TestEllaSetSplitScreenApps"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_split_screen_apps"}]}