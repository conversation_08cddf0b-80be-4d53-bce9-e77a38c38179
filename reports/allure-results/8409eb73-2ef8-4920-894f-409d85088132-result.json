{"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "status": "passed", "description": "验证jump to ai wallpaper generator settings指令返回预期的不支持响应", "steps": [{"name": "执行命令: jump to ai wallpaper generator settings", "status": "passed", "steps": [{"name": "执行命令: jump to ai wallpaper generator settings", "status": "passed", "start": 1756798992707, "stop": 1756799015459}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3cbaca5e-a637-4d5b-a32c-38776ecaa855-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c17faa5d-1a99-44e6-a278-ea57c3d4a76c-attachment.png", "type": "image/png"}], "start": 1756799015460, "stop": 1756799015774}], "start": 1756798992707, "stop": 1756799015775}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756799015775, "stop": 1756799015776}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d7271d8e-c696-46d4-a7aa-2c4633b510d1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f6553a74-48e1-4cf5-a169-d599528b9771-attachment.png", "type": "image/png"}], "start": 1756799015776, "stop": 1756799016028}], "attachments": [{"name": "stdout", "source": "1d624157-7caa-467f-a393-c558ddcc0ff1-attachment.txt", "type": "text/plain"}], "start": 1756798992707, "stop": 1756799016029, "uuid": "15ed0331-2913-45da-bed1-ca6d5518907b", "historyId": "f1bf796cd6804ca9b19a3e3f949a04ba", "testCaseId": "f1bf796cd6804ca9b19a3e3f949a04ba", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_ai_wallpaper_generator_settings.TestEllaJumpAiWallpaperGeneratorSettings#test_jump_to_ai_wallpaper_generator_settings", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_ai_wallpaper_generator_settings"}, {"name": "subSuite", "value": "TestEllaJumpAiWallpaperGeneratorSettings"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_ai_wallpaper_generator_settings"}]}