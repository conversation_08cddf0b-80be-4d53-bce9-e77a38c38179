{"name": "测试Enable Network Enhancement返回正确的不支持响应", "status": "passed", "description": "验证Enable Network Enhancement指令返回预期的不支持响应", "steps": [{"name": "执行命令: Enable Network Enhancement", "status": "passed", "steps": [{"name": "执行命令: Enable Network Enhancement", "status": "passed", "start": 1756797156706, "stop": 1756797178942}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "54a5419d-792e-44b6-b3d7-47d2e0817462-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e08b25ac-2351-4720-b4a3-5d56c3b5d176-attachment.png", "type": "image/png"}], "start": 1756797178942, "stop": 1756797179187}], "start": 1756797156705, "stop": 1756797179187}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756797179187, "stop": 1756797179189}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f4b3f7f2-6958-4598-bd13-e2a9d8585cbb-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8422f945-68cb-4326-9f61-cb6c73d26536-attachment.png", "type": "image/png"}], "start": 1756797179189, "stop": 1756797179424}], "attachments": [{"name": "stdout", "source": "dbf303ae-1c4d-44f8-b3f4-805f20ae6489-attachment.txt", "type": "text/plain"}], "start": 1756797156705, "stop": 1756797179424, "uuid": "c98fdeff-05f6-4259-9e8f-6adf7419e7c1", "historyId": "657acdf17dda1a11abf6946763f6ed52", "testCaseId": "657acdf17dda1a11abf6946763f6ed52", "fullName": "testcases.test_ella.unsupported_commands.test_enable_network_enhancement.TestEllaEnableNetworkEnhancement#test_enable_network_enhancement", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_network_enhancement"}, {"name": "subSuite", "value": "TestEllaEnableNetworkEnhancement"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_network_enhancement"}]}