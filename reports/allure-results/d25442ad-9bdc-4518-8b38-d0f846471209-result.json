{"name": "测试order a burger返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['I need to download Yandex Eats to continue']，实际响应: '['order a burger', \"O<PERSON>, out of my reach, ask me again after I've learned it\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_order_a_burger.TestEllaOrderBurger object at 0x0000018BC6599AE0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6DBC5B0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_order_a_burger(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['I need to download Yandex Eats to continue']，实际响应: '['order a burger', \"Oops, out of my reach, ask me again after I've learned it\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_order_a_burger.py:34: AssertionError"}, "description": "验证order a burger指令返回预期的不支持响应", "steps": [{"name": "执行命令: order a burger", "status": "passed", "steps": [{"name": "执行命令: order a burger", "status": "passed", "start": 1756800264288, "stop": 1756800286601}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3019d48a-ff7b-433a-aca7-444c31d18ac2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "721990dc-6107-4b91-8fb5-176a5395a94b-attachment.png", "type": "image/png"}], "start": 1756800286601, "stop": 1756800287023}], "start": 1756800264288, "stop": 1756800287024}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['I need to download Yandex Eats to continue']，实际响应: '['order a burger', \"O<PERSON>, out of my reach, ask me again after I've learned it\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_order_a_burger.py\", line 34, in test_order_a_burger\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756800287024, "stop": 1756800287026}], "attachments": [{"name": "stdout", "source": "b44c647d-48b7-4464-ae16-75116fe5fd5b-attachment.txt", "type": "text/plain"}], "start": 1756800264288, "stop": 1756800287028, "uuid": "7478db43-07b4-4e5c-8449-b67c0f7d303f", "historyId": "2b244b852ff1236f560ec792596ae556", "testCaseId": "2b244b852ff1236f560ec792596ae556", "fullName": "testcases.test_ella.unsupported_commands.test_order_a_burger.TestEllaOrderBurger#test_order_a_burger", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_order_a_burger"}, {"name": "subSuite", "value": "TestEllaOrderBurger"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_order_a_burger"}]}