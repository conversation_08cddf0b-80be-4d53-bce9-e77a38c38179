{"name": "测试set phone number返回正确的不支持响应", "status": "passed", "description": "验证set phone number指令返回预期的不支持响应", "steps": [{"name": "执行命令: set phone number", "status": "passed", "steps": [{"name": "执行命令: set phone number", "status": "passed", "start": 1756802490642, "stop": 1756802529498}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "bcf00084-1b00-4c0c-9ee7-1f39268c9701-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ea1b7dc7-44e0-47f2-8505-c1ff9258a662-attachment.png", "type": "image/png"}], "start": 1756802529498, "stop": 1756802529714}], "start": 1756802490642, "stop": 1756802529715}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756802529715, "stop": 1756802529716}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d1cd379b-11cf-43b9-a0fe-0cc96ee422c7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "50400be7-be9a-4020-a3fd-066d7665da4d-attachment.png", "type": "image/png"}], "start": 1756802529716, "stop": 1756802529938}], "attachments": [{"name": "stdout", "source": "0c85109b-45cc-4a9a-aa20-ee3889a796dc-attachment.txt", "type": "text/plain"}], "start": 1756802490641, "stop": 1756802529939, "uuid": "10a6c581-4d6e-41ff-9bcb-5aa69a060825", "historyId": "8e367b8da758818b9a0fe21deca7ec48", "testCaseId": "8e367b8da758818b9a0fe21deca7ec48", "fullName": "testcases.test_ella.unsupported_commands.test_set_phone_number.TestEllaSetPhoneNumber#test_set_phone_number", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_phone_number"}, {"name": "subSuite", "value": "TestEllaSetPhoneNumber"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_phone_number"}]}