{"name": "测试what's the weather today?能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '[\"what's the weather today?\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.dialogue.test_whats_the_weather_today.TestEllaWhatsWeatherToday object at 0x0000018BC608CA60>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC675DBA0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_whats_the_weather_today(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '[\"what's the weather today?\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_whats_the_weather_today.py:36: AssertionError"}, "description": "what's the weather today?", "steps": [{"name": "执行命令: what's the weather today?", "status": "passed", "steps": [{"name": "执行命令: what's the weather today?", "status": "passed", "start": 1756787755960, "stop": 1756787785517}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5720a11b-ecfc-4355-b7aa-cd06f4822a75-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5711c6f1-6415-4269-9f65-69e824d16c58-attachment.png", "type": "image/png"}], "start": 1756787785517, "stop": 1756787785769}], "start": 1756787755960, "stop": 1756787785770}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '[\"what's the weather today?\", 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\dialogue\\test_whats_the_weather_today.py\", line 36, in test_whats_the_weather_today\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756787785770, "stop": 1756787785773}], "attachments": [{"name": "stdout", "source": "dca641f4-e5ca-4d77-b533-fad3e2377f25-attachment.txt", "type": "text/plain"}], "start": 1756787755960, "stop": 1756787785774, "uuid": "e786f895-0754-4427-83b3-5a25d653b49c", "historyId": "acca7d0b06a28ad8e6ccfe3c35828ce1", "testCaseId": "acca7d0b06a28ad8e6ccfe3c35828ce1", "fullName": "testcases.test_ella.dialogue.test_whats_the_weather_today.TestEllaWhatsWeatherToday#test_whats_the_weather_today", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_whats_the_weather_today"}, {"name": "subSuite", "value": "TestEllaWhatsWeatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_whats_the_weather_today"}]}