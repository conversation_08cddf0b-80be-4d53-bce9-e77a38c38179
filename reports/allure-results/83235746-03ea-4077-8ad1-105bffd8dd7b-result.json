{"name": "测试open contact命令", "status": "passed", "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open phone", "status": "passed", "steps": [{"name": "执行命令: open phone", "status": "passed", "start": 1756783548048, "stop": 1756783578406}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d43f6e71-0a12-453b-966f-16a1dcaac636-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9a6056ab-8cd7-4d5b-aa0e-c898c86832e2-attachment.png", "type": "image/png"}], "start": 1756783578406, "stop": 1756783578635}], "start": 1756783548048, "stop": 1756783578636}, {"name": "验证响应包含Done", "status": "passed", "start": 1756783578636, "stop": 1756783578637}, {"name": "验证Dalier应用已打开", "status": "passed", "start": 1756783578637, "stop": 1756783578637}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "38b93970-cce1-4a80-8231-b31577933e58-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d3649962-5cae-4c34-8a45-ab1aa3abe329-attachment.png", "type": "image/png"}], "start": 1756783578637, "stop": 1756783578841}], "attachments": [{"name": "stdout", "source": "7753323c-f7a9-4284-bfb5-97448e8b2e31-attachment.txt", "type": "text/plain"}], "start": 1756783548048, "stop": 1756783578842, "uuid": "517cdb13-c827-4ff1-bf11-8188a52f5264", "historyId": "acdb323f998d6127fbebbc545f6e8a59", "testCaseId": "acdb323f998d6127fbebbc545f6e8a59", "fullName": "testcases.test_ella.component_coupling.test_open_phone.TestEllaContactCommandConcise#test_open_phone", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "联系人控制命令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_phone"}, {"name": "subSuite", "value": "TestEllaContactCommandConcise"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_phone"}]}