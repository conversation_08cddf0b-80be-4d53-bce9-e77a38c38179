{"name": "测试pause screen recording能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=False, 最终=False, 响应='['pause screen recording', 'Screen recording on hold.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.system_coupling.test_start_screen_recording.TestEllaStartScreenRecording object at 0x0000018BC61C13F0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC70A7DC0>\n\n    @allure.title(f\"测试pause screen recording能正常执行\")\n    @allure.description(f\"pause screen recording\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_pause_screen_recording(self, ella_app):\n        command = \"pause screen recording\"\n        expected_text = ['Screen recording on hold']\n        f\"\"\"{command}\"\"\"\n    \n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证已打开\"):\n>           assert final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=False, 最终=False, 响应='['pause screen recording', 'Screen recording on hold.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_start_screen_recording.py:62: AssertionError"}, "description": "pause screen recording", "steps": [{"name": "执行命令: pause screen recording", "status": "passed", "steps": [{"name": "执行命令: pause screen recording", "status": "passed", "start": 1756791198152, "stop": 1756791225799}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "36524c17-8804-4686-a57f-4857409af42f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f8c0cad0-59c3-48bd-a740-6256b80d28c6-attachment.png", "type": "image/png"}], "start": 1756791225799, "stop": 1756791226016}], "start": 1756791198152, "stop": 1756791226017}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756791226017, "stop": 1756791226018}, {"name": "验证已打开", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=False, 最终=False, 响应='['pause screen recording', 'Screen recording on hold.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\system_coupling\\test_start_screen_recording.py\", line 62, in test_pause_screen_recording\n    assert final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1756791226018, "stop": 1756791226018}], "attachments": [{"name": "stdout", "source": "b033962c-9213-4eca-9ab7-2495a3db2df5-attachment.txt", "type": "text/plain"}], "start": 1756791198151, "stop": 1756791226019, "uuid": "ab65b07a-bf7d-4e0e-beef-304b6d33162d", "historyId": "1ab60ce22774c460e557aaa3b3f9120a", "testCaseId": "1ab60ce22774c460e557aaa3b3f9120a", "fullName": "testcases.test_ella.system_coupling.test_start_screen_recording.TestEllaStartScreenRecording#test_pause_screen_recording", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_start_screen_recording"}, {"name": "subSuite", "value": "TestEllaStartScreenRecording"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_start_screen_recording"}]}