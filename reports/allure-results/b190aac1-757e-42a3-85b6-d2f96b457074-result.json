{"name": "测试I think the screen is a bit dark now. Could you please help me brighten it up?能正常执行", "status": "passed", "description": "I think the screen is a bit dark now. Could you please help me brighten it up?", "steps": [{"name": "执行命令: I think the screen is a bit dark now. Could you please help me brighten it up?", "status": "passed", "steps": [{"name": "执行命令: I think the screen is a bit dark now. Could you please help me brighten it up?", "status": "passed", "start": 1756798600767, "stop": 1756798625834}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a494a00d-e646-4370-a54f-75abf42ae1a3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0d315f5b-4d2d-4e42-ae2f-3a5183f4d578-attachment.png", "type": "image/png"}], "start": 1756798625834, "stop": 1756798626096}], "start": 1756798600767, "stop": 1756798626096}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756798626096, "stop": 1756798626097}, {"name": "验证应用已打开", "status": "passed", "start": 1756798626097, "stop": 1756798626097}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9a921e7d-0d7d-4a2c-8147-cd25214b6153-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2c76aebe-f3da-4c08-a50a-b2eee96b8626-attachment.png", "type": "image/png"}], "start": 1756798626097, "stop": 1756798626353}], "attachments": [{"name": "stdout", "source": "68fcc2aa-4687-4d68-af95-bde77f0795c6-attachment.txt", "type": "text/plain"}], "start": 1756798600767, "stop": 1756798626354, "uuid": "390c9585-b6d2-424f-b53e-c6180ee6f1bd", "historyId": "3f0254c16b7bc20d18094d502f49138a", "testCaseId": "3f0254c16b7bc20d18094d502f49138a", "fullName": "testcases.test_ella.unsupported_commands.test_i_think_the_screen_is_a_bit_dark_now_could_you_please_help_me_brighten_it_up.TestEllaIThinkScreenIsBitDarkNowCouldYouPleaseHelpMeBrightenItUp#test_i_think_the_screen_is_a_bit_dark_now_could_you_please_help_me_brighten_it_up", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_i_think_the_screen_is_a_bit_dark_now_could_you_please_help_me_brighten_it_up"}, {"name": "subSuite", "value": "TestEllaIThinkScreenIsBitDarkNowCouldYouPleaseHelpMeBrightenItUp"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_i_think_the_screen_is_a_bit_dark_now_could_you_please_help_me_brighten_it_up"}]}