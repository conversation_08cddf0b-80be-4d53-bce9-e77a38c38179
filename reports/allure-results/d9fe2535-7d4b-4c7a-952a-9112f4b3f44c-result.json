{"name": "测试open settings", "status": "passed", "description": "测试open settings指令", "steps": [{"name": "执行命令: open settings", "status": "passed", "steps": [{"name": "执行命令: open settings", "status": "passed", "start": 1756800123255, "stop": 1756800153348}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0b186fab-896e-46e1-9098-8cccacb34514-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2c3e5a5e-b52d-4517-be72-e78a0df265e6-attachment.png", "type": "image/png"}], "start": 1756800153348, "stop": 1756800153619}], "start": 1756800123255, "stop": 1756800153620}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756800153620, "stop": 1756800153622}, {"name": "验证settings已打开", "status": "passed", "start": 1756800153622, "stop": 1756800153622}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a9286d44-d770-41e2-8b09-76cbc193a77b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7f6c3bda-dbe6-4a5a-99bb-37215c0f30bf-attachment.png", "type": "image/png"}], "start": 1756800153622, "stop": 1756800153842}], "attachments": [{"name": "stdout", "source": "64b50128-a4be-4b67-ba49-8509b7554281-attachment.txt", "type": "text/plain"}], "start": 1756800123255, "stop": 1756800153842, "uuid": "c7cfa776-4aff-490f-8b46-2de442fae7a9", "historyId": "af89bd1d18cd6a175678a8fe1f43ee33", "testCaseId": "af89bd1d18cd6a175678a8fe1f43ee33", "fullName": "testcases.test_ella.unsupported_commands.test_open_settings.TestEllaOpenPlayPoliticalNews#test_open_settings", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_open_settings"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_open_settings"}]}