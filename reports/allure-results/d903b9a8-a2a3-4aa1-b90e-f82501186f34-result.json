{"name": "测试Switch to Low-Temp Charge能正常执行", "status": "passed", "description": "Switch to Low-Temp Charge", "steps": [{"name": "执行命令: Switch to Low-Temp Charge", "status": "passed", "steps": [{"name": "执行命令: Switch to Low-Temp Charge", "status": "passed", "start": 1756791732887, "stop": 1756791752730}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4cd5150a-7429-4fbe-be16-ff7aed123006-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "310779e4-722b-45e4-ab9f-482bbd6a7ec7-attachment.png", "type": "image/png"}], "start": 1756791752730, "stop": 1756791752920}], "start": 1756791732887, "stop": 1756791752920}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1756791752920, "stop": 1756791752922}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a715887c-5b88-4cea-b911-cc223170c70d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4a4b4df3-8ba7-476a-8744-0ef3c4786cbb-attachment.png", "type": "image/png"}], "start": 1756791752922, "stop": 1756791753117}], "attachments": [{"name": "stdout", "source": "2bbfe4c7-d9e8-403a-b098-16527c9e5c57-attachment.txt", "type": "text/plain"}], "start": 1756791732887, "stop": 1756791753117, "uuid": "5801c119-b470-4b52-b347-8b2c191ebb07", "historyId": "753ba105235625e906d023bd3aaa0821", "testCaseId": "753ba105235625e906d023bd3aaa0821", "fullName": "testcases.test_ella.system_coupling.test_switch_to_low_temp_charge.TestEllaSwitchToLowtempCharge#test_switch_to_low_temp_charge", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_low_temp_charge"}, {"name": "subSuite", "value": "TestEllaSwitchToLowtempCharge"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_low_temp_charge"}]}