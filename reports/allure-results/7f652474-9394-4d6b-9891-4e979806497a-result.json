{"name": "测试turn off smart reminder能正常执行", "status": "passed", "description": "turn off smart reminder", "steps": [{"name": "执行命令: turn off smart reminder", "status": "passed", "steps": [{"name": "执行命令: turn off smart reminder", "status": "passed", "start": 1756792412890, "stop": 1756792432164}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4a097a20-69a6-4ef6-93e4-a02acd8a85e3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a0562b20-997b-4be6-9c68-88b8eafc9e23-attachment.png", "type": "image/png"}], "start": 1756792432165, "stop": 1756792432402}], "start": 1756792412890, "stop": 1756792432402}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756792432402, "stop": 1756792432404}, {"name": "验证应用已打开", "status": "passed", "start": 1756792432404, "stop": 1756792432404}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b9674870-ac82-405c-b4a1-8e3b5e0692a1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "10d55dab-e079-4f99-bccd-61ffdb7affff-attachment.png", "type": "image/png"}], "start": 1756792432404, "stop": 1756792432626}], "attachments": [{"name": "stdout", "source": "83c81fd0-3ba6-4d69-a5e3-aa84c32bc201-attachment.txt", "type": "text/plain"}], "start": 1756792412890, "stop": 1756792432626, "uuid": "f5b77407-0b93-4e5c-8457-17e70d1dfcaa", "historyId": "e8f9ac327bc5f166a4c4a14509f365bf", "testCaseId": "e8f9ac327bc5f166a4c4a14509f365bf", "fullName": "testcases.test_ella.system_coupling.test_turn_off_smart_reminder.TestEllaTurnOffSmartReminder#test_turn_off_smart_reminder", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_off_smart_reminder"}, {"name": "subSuite", "value": "TestEllaTurnOffSmartReminder"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_off_smart_reminder"}]}