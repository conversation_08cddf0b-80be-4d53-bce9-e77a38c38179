{"name": "测试disable brightness locking返回正确的不支持响应", "status": "passed", "description": "验证disable brightness locking指令返回预期的不支持响应", "steps": [{"name": "执行命令: disable brightness locking", "status": "passed", "steps": [{"name": "执行命令: disable brightness locking", "status": "passed", "start": 1756796374854, "stop": 1756796399402}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "42a8ae08-939d-48a4-aeb7-1ad1eb6d897c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e0599cba-7877-45ec-9fe3-435aad66b02a-attachment.png", "type": "image/png"}], "start": 1756796399402, "stop": 1756796399616}], "start": 1756796374854, "stop": 1756796399617}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756796399617, "stop": 1756796399617}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dbddd88d-19fa-474f-8267-d52569eaa23f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "706d2800-60f8-4fcb-9907-1f011eca7b5d-attachment.png", "type": "image/png"}], "start": 1756796399617, "stop": 1756796399833}], "attachments": [{"name": "stdout", "source": "0cee3d4d-a20e-4e87-8f88-8a7b22b15589-attachment.txt", "type": "text/plain"}], "start": 1756796374854, "stop": 1756796399833, "uuid": "85d35b99-05c9-426c-9368-d8bbee2d9f67", "historyId": "edbb2ef8b0440e0325be2bfae4eb0bee", "testCaseId": "edbb2ef8b0440e0325be2bfae4eb0bee", "fullName": "testcases.test_ella.unsupported_commands.test_disable_brightness_locking.TestEllaDisableBrightnessLocking#test_disable_brightness_locking", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_brightness_locking"}, {"name": "subSuite", "value": "TestEllaDisableBrightnessLocking"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_brightness_locking"}]}