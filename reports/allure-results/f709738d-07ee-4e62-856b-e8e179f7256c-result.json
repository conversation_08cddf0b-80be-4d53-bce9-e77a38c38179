{"name": "测试set smart panel返回正确的不支持响应", "status": "passed", "description": "验证set smart panel指令返回预期的不支持响应", "steps": [{"name": "执行命令: set smart panel", "status": "passed", "steps": [{"name": "执行命令: set smart panel", "status": "passed", "start": 1756802878812, "stop": 1756802909717}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "571370fc-29ba-460b-b965-eca6875b3f95-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f3866f0c-1c73-439f-bf09-1e9474f655a9-attachment.png", "type": "image/png"}], "start": 1756802909717, "stop": 1756802909981}], "start": 1756802878812, "stop": 1756802909982}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756802909982, "stop": 1756802909985}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7a18cdfd-3aa6-411f-9db2-28263432a079-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "20208ed0-919c-4d36-83ec-d4d5c2e25879-attachment.png", "type": "image/png"}], "start": 1756802909985, "stop": 1756802910225}], "attachments": [{"name": "stdout", "source": "dc24d509-**************-d800bf52b45f-attachment.txt", "type": "text/plain"}], "start": 1756802878812, "stop": 1756802910226, "uuid": "4817c13e-fced-479b-be9e-14b72aa5d712", "historyId": "5fc780d1e7f790011f0e4a521e125a16", "testCaseId": "5fc780d1e7f790011f0e4a521e125a16", "fullName": "testcases.test_ella.unsupported_commands.test_set_smart_panel.TestEllaSetSmartPanel#test_set_smart_panel", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_smart_panel"}, {"name": "subSuite", "value": "TestEllaSetSmartPanel"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_smart_panel"}]}