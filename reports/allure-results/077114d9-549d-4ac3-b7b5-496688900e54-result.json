{"name": "测试close whatsapp能正常执行", "status": "passed", "description": "close whatsapp", "steps": [{"name": "执行命令: close whatsapp", "status": "passed", "steps": [{"name": "执行命令: close whatsapp", "status": "passed", "start": 1756784895037, "stop": 1756784917198}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "addfb9c3-2895-44d7-92f4-d40ff47f8742-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1c971ad7-322c-43e0-94ea-e989a9e0e685-attachment.png", "type": "image/png"}], "start": 1756784917198, "stop": 1756784917403}], "start": 1756784895037, "stop": 1756784917403}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756784917404, "stop": 1756784917405}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4d05e734-0ca1-4eea-816e-73f8f9681b2c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d76d39f0-c1ef-49c4-9526-44ddef5a7df7-attachment.png", "type": "image/png"}], "start": 1756784917405, "stop": 1756784917606}], "attachments": [{"name": "stdout", "source": "dc13bc81-6aec-4751-97e1-5d2d98ff6b08-attachment.txt", "type": "text/plain"}], "start": 1756784895037, "stop": 1756784917606, "uuid": "3ff390aa-6104-4cb7-a677-86f1dbd0a249", "historyId": "876e77318cece5d1079b726f0c97bc45", "testCaseId": "876e77318cece5d1079b726f0c97bc45", "fullName": "testcases.test_ella.dialogue.test_close_whatsapp.TestEllaCloseWhatsapp#test_close_whatsapp", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_close_whatsapp"}, {"name": "subSuite", "value": "TestEllaCloseWhatsapp"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_close_whatsapp"}]}