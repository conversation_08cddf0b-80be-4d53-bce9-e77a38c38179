{"name": "测试A cute little boy is skiing 能正常执行", "status": "passed", "description": "A cute little boy is skiing ", "steps": [{"name": "执行命令: A cute little boy is skiing ", "status": "passed", "steps": [{"name": "执行命令: A cute little boy is skiing ", "status": "passed", "start": 1756787964251, "stop": 1756788050994}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "939d17cf-66cd-4214-9f72-9a14b3d0b8d0-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c53422ca-6412-4ccf-920f-f373cefed4cf-attachment.png", "type": "image/png"}], "start": 1756788050994, "stop": 1756788051224}], "start": 1756787964251, "stop": 1756788051224}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756788051224, "stop": 1756788051226}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c1743a41-8eb3-489a-8bd3-ffc07f41a006-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6c8dcf4a-b663-4333-8d21-3890a0d18663-attachment.png", "type": "image/png"}], "start": 1756788051226, "stop": 1756788051465}], "attachments": [{"name": "stdout", "source": "0d1dfdfd-7e45-4c7e-b021-7e8a48fabfb3-attachment.txt", "type": "text/plain"}], "start": 1756787964251, "stop": 1756788051466, "uuid": "0c820fb9-dd0f-46bf-bc04-ff86fdbccb92", "historyId": "da0740a44317121ce1879d022e95ae23", "testCaseId": "da0740a44317121ce1879d022e95ae23", "fullName": "testcases.test_ella.self_function.test_a_cute_little_boy_is_skiing.TestEllaCuteLittleBoyIsSkiing#test_a_cute_little_boy_is_skiing", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.self_function"}, {"name": "suite", "value": "test_a_cute_little_boy_is_skiing"}, {"name": "subSuite", "value": "TestEllaCuteLittleBoyIsSkiing"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.self_function.test_a_cute_little_boy_is_skiing"}]}