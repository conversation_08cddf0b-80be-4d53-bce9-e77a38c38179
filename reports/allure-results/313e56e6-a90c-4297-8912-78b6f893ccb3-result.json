{"name": "测试set flip case feature返回正确的不支持响应", "status": "passed", "description": "验证set flip case feature指令返回预期的不支持响应", "steps": [{"name": "执行命令: set flip case feature", "status": "passed", "steps": [{"name": "执行命令: set flip case feature", "status": "passed", "start": 1756801860479, "stop": 1756801884041}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0d05daaf-89e2-4d01-9f56-4d0a02c7a1aa-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7a9cc130-6da4-40ac-b6db-0e5f786c4248-attachment.png", "type": "image/png"}], "start": 1756801884041, "stop": 1756801884270}], "start": 1756801860479, "stop": 1756801884270}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756801884271, "stop": 1756801884271}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d3f3a6f4-6f44-4db9-a677-39d2eec71b74-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "178f327e-d255-478e-8ede-9ca8940f345f-attachment.png", "type": "image/png"}], "start": 1756801884271, "stop": 1756801884476}], "attachments": [{"name": "stdout", "source": "35bf0de4-71f3-4eef-af78-17f1e658cb45-attachment.txt", "type": "text/plain"}], "start": 1756801860479, "stop": 1756801884477, "uuid": "9d7dd50c-ea1a-4519-8ae3-92bc48308c81", "historyId": "3a27fef360a79f638f96f0461df262da", "testCaseId": "3a27fef360a79f638f96f0461df262da", "fullName": "testcases.test_ella.unsupported_commands.test_set_flip_case_feature.TestEllaSetFlipCaseFeature#test_set_flip_case_feature", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_flip_case_feature"}, {"name": "subSuite", "value": "TestEllaSetFlipCaseFeature"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_flip_case_feature"}]}