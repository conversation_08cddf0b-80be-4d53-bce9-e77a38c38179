{"name": "测试i want to hear a joke能正常执行", "status": "passed", "description": "i want to hear a joke", "steps": [{"name": "执行命令: i want to hear a joke", "status": "passed", "steps": [{"name": "执行命令: i want to hear a joke", "status": "passed", "start": 1756798734456, "stop": 1756798758780}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d1f04173-c502-4fe8-a095-659dc4533813-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1361598f-ce30-438e-9fa0-8e932b5b2cde-attachment.png", "type": "image/png"}], "start": 1756798758780, "stop": 1756798759009}], "start": 1756798734456, "stop": 1756798759009}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756798759009, "stop": 1756798759012}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "15f90619-2d05-4b87-836a-8ba96b1f7059-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "76e2f748-509e-47b4-8262-2c15a3f84b78-attachment.png", "type": "image/png"}], "start": 1756798759012, "stop": 1756798759218}], "attachments": [{"name": "stdout", "source": "83330d97-204e-45de-83aa-a352dc55cd9a-attachment.txt", "type": "text/plain"}], "start": 1756798734456, "stop": 1756798759218, "uuid": "828979d5-2ba5-4840-8610-251510746d0c", "historyId": "5dbd6c476e40c9de0215f0509dd43986", "testCaseId": "5dbd6c476e40c9de0215f0509dd43986", "fullName": "testcases.test_ella.unsupported_commands.test_i_want_to_hear_a_joke.TestEllaIWantHearJoke#test_i_want_to_hear_a_joke", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_i_want_to_hear_a_joke"}, {"name": "subSuite", "value": "TestEllaIWantHearJoke"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_i_want_to_hear_a_joke"}]}