{"name": "测试close aivana能正常执行", "status": "passed", "description": "close aivana", "steps": [{"name": "执行命令: close aivana", "status": "passed", "steps": [{"name": "执行命令: close aivana", "status": "passed", "start": 1756799722532, "stop": 1756799753982}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4f1592d0-ae7f-4701-a789-6b85854fe4b5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "705993e5-819e-438d-afcf-5456ffa0d295-attachment.png", "type": "image/png"}], "start": 1756799753983, "stop": 1756799754426}], "start": 1756799722532, "stop": 1756799754426}, {"name": "验证已打开", "status": "passed", "start": 1756799754426, "stop": 1756799754426}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4678a49c-6b56-46d6-a5a7-45da69f77405-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2fae12cd-3a80-4e82-8900-a5c432f30e3f-attachment.png", "type": "image/png"}], "start": 1756799754426, "stop": 1756799754612}], "attachments": [{"name": "stdout", "source": "6cd2bac5-b3e9-485f-8504-8f8ba3d33c54-attachment.txt", "type": "text/plain"}], "start": 1756799722532, "stop": 1756799754613, "uuid": "96de817a-a8e9-4d86-b01e-6265302efe1b", "historyId": "d9f01ef1af79559082ce9e9b2e40295f", "testCaseId": "d9f01ef1af79559082ce9e9b2e40295f", "fullName": "testcases.test_ella.component_coupling.test_close_aivana.TestEllaCloseAivana#test_close_aivana", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_aivana"}, {"name": "subSuite", "value": "TestEllaCloseAivana"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "30444-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_aivana"}]}