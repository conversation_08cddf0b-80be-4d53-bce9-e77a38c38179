{"name": "测试turn off adaptive brightness能正常执行", "status": "passed", "description": "turn off adaptive brightness", "steps": [{"name": "执行命令: turn off adaptive brightness", "status": "passed", "steps": [{"name": "执行命令: turn off adaptive brightness", "status": "passed", "start": 1756792225762, "stop": 1756792246019}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "993b60fb-9a69-42ed-a495-d553aa425bb6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e0a5d5dc-e5d3-4fc9-bed4-0cd3410e5aaf-attachment.png", "type": "image/png"}], "start": 1756792246019, "stop": 1756792246207}], "start": 1756792225762, "stop": 1756792246208}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756792246208, "stop": 1756792246209}, {"name": "验证应用已打开", "status": "passed", "start": 1756792246209, "stop": 1756792246209}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b8062deb-df64-404a-bb93-381a63790863-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "96ee1b91-bdf1-49ac-8011-410910f69012-attachment.png", "type": "image/png"}], "start": 1756792246209, "stop": 1756792246393}], "attachments": [{"name": "stdout", "source": "4061771b-1b72-4375-a6d2-4ecb1aba0d83-attachment.txt", "type": "text/plain"}], "start": 1756792225762, "stop": 1756792246393, "uuid": "8a648a8f-a885-4e95-9370-2875a3e5d1d8", "historyId": "ebd2c9b6cd7c07b69e348328a207e18b", "testCaseId": "ebd2c9b6cd7c07b69e348328a207e18b", "fullName": "testcases.test_ella.system_coupling.test_turn_off_adaptive_brightness.TestEllaTurnOffAdaptiveBrightness#test_turn_off_adaptive_brightness", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_off_adaptive_brightness"}, {"name": "subSuite", "value": "TestEllaTurnOffAdaptiveBrightness"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_off_adaptive_brightness"}]}