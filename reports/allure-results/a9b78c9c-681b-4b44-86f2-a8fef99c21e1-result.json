{"name": "测试screen record能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=False, 最终=False, 响应='['screen record', 'Screen recording started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.system_coupling.test_screen_record.TestEllaScreenRecord object at 0x0000018BC617FD30>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC4DF27D0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_screen_record(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证已打开\"):\n>           assert final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=False, 最终=False, 响应='['screen record', 'Screen recording started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_screen_record.py:36: AssertionError"}, "description": "screen record", "steps": [{"name": "执行命令: screen record", "status": "passed", "steps": [{"name": "执行命令: screen record", "status": "passed", "start": 1756790494540, "stop": 1756790522705}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "49cc9e33-5a8a-4562-83f9-f65bed39f53f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a8dd420f-b763-4cb9-8fe5-335329492364-attachment.png", "type": "image/png"}], "start": 1756790522705, "stop": 1756790522941}], "start": 1756790494540, "stop": 1756790522942}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790522942, "stop": 1756790522943}, {"name": "验证已打开", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=False, 最终=False, 响应='['screen record', 'Screen recording started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\system_coupling\\test_screen_record.py\", line 36, in test_screen_record\n    assert final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1756790522943, "stop": 1756790522943}], "attachments": [{"name": "stdout", "source": "f52df2a6-**************-cbdd49f2d145-attachment.txt", "type": "text/plain"}], "start": 1756790494539, "stop": 1756790522944, "uuid": "aa915e85-5edb-49f7-8295-9410d2f87b36", "historyId": "18fb8c43c609a9825fe52e528761fd1b", "testCaseId": "18fb8c43c609a9825fe52e528761fd1b", "fullName": "testcases.test_ella.system_coupling.test_screen_record.TestEllaScreenRecord#test_screen_record", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_screen_record"}, {"name": "subSuite", "value": "TestEllaScreenRecord"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_screen_record"}]}