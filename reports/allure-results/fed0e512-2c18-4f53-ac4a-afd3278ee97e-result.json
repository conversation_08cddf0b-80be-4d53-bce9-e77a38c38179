{"name": "测试open bluetooth", "status": "passed", "description": "测试open bluetooth指令", "steps": [{"name": "执行命令: open bluetooth", "status": "passed", "steps": [{"name": "执行命令: open bluetooth", "status": "passed", "start": 1756790293889, "stop": 1756790317272}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "51e294fe-dacc-4cda-824c-13b6a54a61ef-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8c2c4146-86cd-46ee-b504-510b1fab550a-attachment.png", "type": "image/png"}], "start": 1756790317272, "stop": 1756790317500}], "start": 1756790293889, "stop": 1756790317500}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790317500, "stop": 1756790317501}, {"name": "验证bluetooth已打开", "status": "passed", "start": 1756790317501, "stop": 1756790317501}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "62496a5a-e230-433f-be31-64bf8223e21c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d5ea15e7-bd68-4b43-934d-18b3c11a2766-attachment.png", "type": "image/png"}], "start": 1756790317501, "stop": 1756790317680}], "attachments": [{"name": "stdout", "source": "247ac0df-600c-4dca-8908-7cf169f11f15-attachment.txt", "type": "text/plain"}], "start": 1756790293889, "stop": 1756790317680, "uuid": "60d6fd79-cd30-42f9-b5da-a6af17e8b80f", "historyId": "733cc57b9e666f7c16017a85f41c410d", "testCaseId": "733cc57b9e666f7c16017a85f41c410d", "fullName": "testcases.test_ella.system_coupling.test_open_bluetooth.TestEllaOpenBluetooth#test_open_bluetooth", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_open_bluetooth"}, {"name": "subSuite", "value": "TestEllaOpenBluetooth"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_open_bluetooth"}]}