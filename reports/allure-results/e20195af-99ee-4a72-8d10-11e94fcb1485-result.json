{"name": "测试how is the weather today能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the weather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.dialogue.test_how_is_the_weather_today.TestEllaHowIsWeatherToday object at 0x0000018BC4CDB2B0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC4CDAE90>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_how_is_the_weather_today(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the weather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_how_is_the_weather_today.py:39: AssertionError"}, "description": "how is the weather today", "steps": [{"name": "执行命令: how is the weather today", "status": "passed", "steps": [{"name": "执行命令: how is the weather today", "status": "passed", "start": 1756785316739, "stop": 1756785345100}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b809b1c0-dada-41f6-b096-8a0989ca8774-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e906837c-1f93-4421-9756-4dc013746b48-attachment.png", "type": "image/png"}], "start": 1756785345100, "stop": 1756785345315}], "start": 1756785316739, "stop": 1756785345316}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['how is the weather today', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\dialogue\\test_how_is_the_weather_today.py\", line 39, in test_how_is_the_weather_today\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756785345316, "stop": 1756785345319}], "attachments": [{"name": "stdout", "source": "d47e33dd-da5b-4c9c-8c7a-512b7b4e9c90-attachment.txt", "type": "text/plain"}], "start": 1756785316739, "stop": 1756785345320, "uuid": "06bd04e7-83b0-4dd7-a448-862a2b7e1254", "historyId": "3004e41c81a7ebd857f79d043aaf59df", "testCaseId": "3004e41c81a7ebd857f79d043aaf59df", "fullName": "testcases.test_ella.dialogue.test_how_is_the_weather_today.TestEllaHowIsWeatherToday#test_how_is_the_weather_today", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_is_the_weather_today"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_is_the_weather_today"}]}