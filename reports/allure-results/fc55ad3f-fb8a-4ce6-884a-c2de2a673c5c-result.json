{"name": "测试increase notification volume能正常执行", "status": "passed", "description": "increase notification volume", "steps": [{"name": "执行命令: increase notification volume", "status": "passed", "steps": [{"name": "执行命令: increase notification volume", "status": "passed", "start": 1756789637579, "stop": 1756789659303}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0cf7358c-df1c-4eb4-beb2-5d6d18063b9f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "57e83361-3d50-480c-a126-65f0c67c5566-attachment.png", "type": "image/png"}], "start": 1756789659303, "stop": 1756789659528}], "start": 1756789637579, "stop": 1756789659529}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756789659529, "stop": 1756789659530}, {"name": "验证应用已打开", "status": "passed", "start": 1756789659530, "stop": 1756789659530}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "caf7b557-7d83-4966-b52d-6c1e4761dbb1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "20fbbcc5-f1d1-4ff1-83f4-653a74d1c89e-attachment.png", "type": "image/png"}], "start": 1756789659530, "stop": 1756789659712}], "attachments": [{"name": "stdout", "source": "75ad1e97-ea52-4633-b607-3cabc2a95e64-attachment.txt", "type": "text/plain"}], "start": 1756789637579, "stop": 1756789659713, "uuid": "a337b475-1831-4412-93dc-b7c9506d067f", "historyId": "e5dc64184617a9497ec52a3b74103b55", "testCaseId": "e5dc64184617a9497ec52a3b74103b55", "fullName": "testcases.test_ella.system_coupling.test_increase_notification_volume.TestEllaIncreaseNotificationVolume#test_increase_notification_volume", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_increase_notification_volume"}, {"name": "subSuite", "value": "TestEllaIncreaseNotificationVolume"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_increase_notification_volume"}]}