{"name": "测试enable accelerate dialogue返回正确的不支持响应", "status": "passed", "description": "验证enable accelerate dialogue指令返回预期的不支持响应", "steps": [{"name": "执行命令: enable accelerate dialogue", "status": "passed", "steps": [{"name": "执行命令: enable accelerate dialogue", "status": "passed", "start": 1756796908436, "stop": 1756796937665}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e3e5d58d-abf3-433d-9061-f41701a0c180-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6fcd56a6-cb8b-40e4-98bc-487776748753-attachment.png", "type": "image/png"}], "start": 1756796937665, "stop": 1756796937884}], "start": 1756796908436, "stop": 1756796937885}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756796937885, "stop": 1756796937886}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9041e689-0411-4f74-a985-90c4ee95ff94-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5a4feeac-f8fd-448e-9b60-86dc250b2985-attachment.png", "type": "image/png"}], "start": 1756796937886, "stop": 1756796938077}], "attachments": [{"name": "stdout", "source": "0ffb77fc-f82e-4bf8-b730-4c0dd6f94fb3-attachment.txt", "type": "text/plain"}], "start": 1756796908436, "stop": 1756796938077, "uuid": "a4c21de3-503c-4bcc-b24b-a4462de8585b", "historyId": "0e5513d569c7270e4332e484218ae36b", "testCaseId": "0e5513d569c7270e4332e484218ae36b", "fullName": "testcases.test_ella.unsupported_commands.test_enable_accelerate_dialogue.TestEllaEnableAccelerateDialogue#test_enable_accelerate_dialogue", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_accelerate_dialogue"}, {"name": "subSuite", "value": "TestEllaEnableAccelerateDialogue"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_accelerate_dialogue"}]}