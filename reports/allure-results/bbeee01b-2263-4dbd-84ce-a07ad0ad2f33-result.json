{"name": "测试what's the date today", "status": "passed", "description": "测试what's the date today指令", "steps": [{"name": "执行命令: what's the date today", "status": "passed", "steps": [{"name": "执行命令: what's the date today", "status": "passed", "start": 1756804304899, "stop": 1756804329036}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4656bb93-57dc-425a-a49d-6c7bd40bad9b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b2638c12-df39-4280-93a4-033efb868731-attachment.png", "type": "image/png"}], "start": 1756804329036, "stop": 1756804329323}], "start": 1756804304899, "stop": 1756804329323}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756804329323, "stop": 1756804329324}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "87d1bccb-61b7-4766-aae4-0950b74003d1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0de83879-b629-487b-a72b-5c99bd1418f7-attachment.png", "type": "image/png"}], "start": 1756804329324, "stop": 1756804329552}], "attachments": [{"name": "stdout", "source": "bc6cb742-2206-4d00-bee1-15e4a71546ad-attachment.txt", "type": "text/plain"}], "start": 1756804304899, "stop": 1756804329553, "uuid": "5a02e126-1f69-4271-be81-83509675054f", "historyId": "4a00cb3818a7086991fb9f7a4d2a3bb5", "testCaseId": "4a00cb3818a7086991fb9f7a4d2a3bb5", "fullName": "testcases.test_ella.unsupported_commands.test_what_s_the_date_today.TestEllaOpenPlayPoliticalNews#test_what_s_the_date_today", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_what_s_the_date_today"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_what_s_the_date_today"}]}