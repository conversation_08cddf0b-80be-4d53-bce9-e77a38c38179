{"name": "测试what·s the weather today？能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['what·s the weather today？', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.dialogue.test_what_s_the_weather_today.TestEllaWhatSWeatherToday object at 0x0000018BC606ECE0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC4EE5120>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_what_s_the_weather_today(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['what·s the weather today？', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_what_s_the_weather_today.py:35: AssertionError"}, "description": "what·s the weather today？", "steps": [{"name": "执行命令: what·s the weather today？", "status": "passed", "steps": [{"name": "执行命令: what·s the weather today？", "status": "passed", "start": 1756787591318, "stop": 1756787619629}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "387b6126-6518-41eb-b4dd-ae4c2120282d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "840fdde6-83b9-48e8-ae8e-a6c171b1c9cc-attachment.png", "type": "image/png"}], "start": 1756787619629, "stop": 1756787619863}], "start": 1756787591318, "stop": 1756787619863}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['is', 'today', 'The high is forecast as', 'and the low as', '℃']，实际响应: '['what·s the weather today？', 'Sorry, weather data error, try later.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\dialogue\\test_what_s_the_weather_today.py\", line 35, in test_what_s_the_weather_today\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756787619863, "stop": 1756787619867}], "attachments": [{"name": "stdout", "source": "c0bb2560-cf64-47ee-aac7-69446ba70264-attachment.txt", "type": "text/plain"}], "start": 1756787591318, "stop": 1756787619867, "uuid": "5107a74c-fb32-4ff8-9c67-7aad9b529913", "historyId": "8d12bedb52d3f000f4269afc25f3fe30", "testCaseId": "8d12bedb52d3f000f4269afc25f3fe30", "fullName": "testcases.test_ella.dialogue.test_what_s_the_weather_today.TestEllaWhatSWeatherToday#test_what_s_the_weather_today", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_s_the_weather_today"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_s_the_weather_today"}]}