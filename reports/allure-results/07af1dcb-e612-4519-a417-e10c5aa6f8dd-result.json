{"name": "测试turn up notifications volume能正常执行", "status": "passed", "description": "turn up notifications volume", "steps": [{"name": "执行命令: turn up notifications volume", "status": "passed", "steps": [{"name": "执行命令: turn up notifications volume", "status": "passed", "start": 1756793192543, "stop": 1756793215139}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b423036d-f8a3-4455-9c6e-b75f4ca0df6c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7d26d755-659c-4cbe-8b12-2111c7eccbff-attachment.png", "type": "image/png"}], "start": 1756793215139, "stop": 1756793215357}], "start": 1756793192543, "stop": 1756793215357}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756793215357, "stop": 1756793215360}, {"name": "验证应用已打开", "status": "passed", "start": 1756793215360, "stop": 1756793215360}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "93c33cd3-4853-45e7-890e-b20fbffa0e08-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "868da83c-da85-4123-8205-16df5361fedb-attachment.png", "type": "image/png"}], "start": 1756793215360, "stop": 1756793215553}], "attachments": [{"name": "stdout", "source": "5cb74643-78b7-4f96-b5e0-3c57e621a06f-attachment.txt", "type": "text/plain"}], "start": 1756793192543, "stop": 1756793215554, "uuid": "0b4d0301-6e59-4aa7-9e53-fb8810092d9d", "historyId": "fe7a0f349fbd2027990e72ab5a6650f2", "testCaseId": "fe7a0f349fbd2027990e72ab5a6650f2", "fullName": "testcases.test_ella.system_coupling.test_turn_up_notifications_volume.TestEllaTurnUpNotificationsVolume#test_turn_up_notifications_volume", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_up_notifications_volume"}, {"name": "subSuite", "value": "TestEllaTurnUpNotificationsVolume"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_up_notifications_volume"}]}