{"name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "status": "passed", "description": "navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai", "steps": [{"name": "执行命令: navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai", "status": "passed", "steps": [{"name": "执行命令: navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai", "status": "passed", "start": 1756794437497, "stop": 1756794470387}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "51f0345a-bb5a-4a13-8371-4de355927c8e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c78da4df-de85-4b4f-82ed-38953bfa1054-attachment.png", "type": "image/png"}], "start": 1756794470387, "stop": 1756794470612}], "start": 1756794437497, "stop": 1756794470613}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756794470613, "stop": 1756794470614}, {"name": "验证应用已打开", "status": "passed", "start": 1756794470614, "stop": 1756794470614}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "57b7e425-fa5e-4a17-817b-274c4a2166cd-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b9780284-c70d-4e1e-8e4d-10a5598d7ef4-attachment.png", "type": "image/png"}], "start": 1756794470614, "stop": 1756794470828}], "attachments": [{"name": "stdout", "source": "7dc3b8ef-510e-4b3b-8de2-************-attachment.txt", "type": "text/plain"}], "start": 1756794437497, "stop": 1756794470828, "uuid": "53b59c2b-7467-438d-8fb0-89c7d766abfd", "historyId": "f622c7c4831272dc58cb99e6af8d9943", "testCaseId": "f622c7c4831272dc58cb99e6af8d9943", "fullName": "testcases.test_ella.third_coupling.test_navigate_from_beijing_to_shanghai.TestEllaNavigateFromBeijingShanghai#test_navigate_from_beijing_to_shanghai", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_navigate_from_beijing_to_shanghai"}, {"name": "subSuite", "value": "TestEllaNavigateFromBeijingShanghai"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_navigate_from_beijing_to_shanghai"}]}