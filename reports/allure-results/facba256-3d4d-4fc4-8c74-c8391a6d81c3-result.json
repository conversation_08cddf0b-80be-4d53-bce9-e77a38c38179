{"name": "测试decrease the brightness能正常执行", "status": "passed", "description": "decrease the brightness", "steps": [{"name": "执行命令: decrease the brightness", "status": "passed", "steps": [{"name": "执行命令: turn on brightness to 80", "status": "passed", "start": 1756789299776, "stop": 1756789323057}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1bc0ae39-1647-479f-97d1-62d39e789e19-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "07aa5ff4-428a-4366-b861-d0b25d944727-attachment.png", "type": "image/png"}], "start": 1756789323057, "stop": 1756789323271}], "start": 1756789299776, "stop": 1756789323271}, {"name": "执行命令: decrease the brightness", "status": "passed", "steps": [{"name": "执行命令: decrease the brightness", "status": "passed", "start": 1756789323272, "stop": 1756789346123}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "46b733fe-b8d2-46fd-a433-dd4579ac408b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "befe7087-3c71-42d1-a0f7-5aea1f3ab4cd-attachment.png", "type": "image/png"}], "start": 1756789346123, "stop": 1756789346351}], "start": 1756789323272, "stop": 1756789346351}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756789346351, "stop": 1756789346352}, {"name": "验证亮度降低", "status": "passed", "start": 1756789346352, "stop": 1756789346352}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7d6bfbd1-a4f8-43a0-b17f-b80d318c6e90-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "92d19017-7bfb-4f3a-9bea-e677008c0aae-attachment.png", "type": "image/png"}], "start": 1756789346352, "stop": 1756789346566}], "attachments": [{"name": "stdout", "source": "162e3739-d39a-4a04-adb4-472ce9058747-attachment.txt", "type": "text/plain"}], "start": 1756789299776, "stop": 1756789346567, "uuid": "30629dfa-2c2e-4f12-b49c-79bc5283f98b", "historyId": "b68db9e7b007fe641926babf537afa6c", "testCaseId": "b68db9e7b007fe641926babf537afa6c", "fullName": "testcases.test_ella.system_coupling.test_decrease_the_brightness.TestEllaDecreaseBrightness#test_decrease_the_brightness", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_decrease_the_brightness"}, {"name": "subSuite", "value": "TestEllaDecreaseBrightness"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_decrease_the_brightness"}]}