{"name": "测试check mobile data balance of sim2返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'SIM 2 not detected.']，实际响应: '['check mobile data balance of sim2', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_check_mobile_data_balance_of_sim.TestEllaCheckMobileDataBalanceSim object at 0x0000018BC64313F0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC92CEE00>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_check_mobile_data_balance_of_sim(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'SIM 2 not detected.']，实际响应: '['check mobile data balance of sim2', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_check_mobile_data_balance_of_sim.py:33: AssertionError"}, "description": "验证check mobile data balance of sim2指令返回预期的不支持响应", "steps": [{"name": "执行命令: check mobile data balance of sim2", "status": "passed", "steps": [{"name": "执行命令: check mobile data balance of sim2", "status": "passed", "start": 1756795734918, "stop": 1756795758508}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7e39c8a3-6ef5-495a-86eb-d2409639773f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "429ef192-a054-4043-92f5-7c402d72798f-attachment.png", "type": "image/png"}], "start": 1756795758508, "stop": 1756795758737}], "start": 1756795734918, "stop": 1756795758737}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'SIM 2 not detected.']，实际响应: '['check mobile data balance of sim2', 'The question you sent may not be related to AI Image Generator. You can exit AI Image Generator and ask the question again.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_check_mobile_data_balance_of_sim.py\", line 33, in test_check_mobile_data_balance_of_sim\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756795758738, "stop": 1756795758740}], "attachments": [{"name": "stdout", "source": "3e5e0209-5d0d-4522-9a36-211e6de42c7b-attachment.txt", "type": "text/plain"}], "start": 1756795734918, "stop": 1756795758741, "uuid": "0906a80f-52b2-4444-aadf-56bb6ee0b88f", "historyId": "7c50481bc992b9ff109abbeeeece073a", "testCaseId": "7c50481bc992b9ff109abbeeeece073a", "fullName": "testcases.test_ella.unsupported_commands.test_check_mobile_data_balance_of_sim.TestEllaCheckMobileDataBalanceSim#test_check_mobile_data_balance_of_sim", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_mobile_data_balance_of_sim"}, {"name": "subSuite", "value": "TestEllaCheckMobileDataBalanceSim"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_mobile_data_balance_of_sim"}]}