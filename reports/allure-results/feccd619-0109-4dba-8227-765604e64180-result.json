{"name": "测试set battery saver settings返回正确的不支持响应", "status": "passed", "description": "验证set battery saver settings指令返回预期的不支持响应", "steps": [{"name": "执行命令: set battery saver settings", "status": "passed", "steps": [{"name": "执行命令: set battery saver settings", "status": "passed", "start": 1756801477262, "stop": 1756801506997}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fad8dbbb-cfb1-4343-ba58-7082838a601e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b1b5ea68-0c09-43f3-a9a1-d48b2fae3f9e-attachment.png", "type": "image/png"}], "start": 1756801506997, "stop": 1756801507226}], "start": 1756801477262, "stop": 1756801507226}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756801507226, "stop": 1756801507229}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "bb96604b-f692-4acd-8c53-23cacc7d4603-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "58ee8871-e43c-4266-a8ac-51676a569d96-attachment.png", "type": "image/png"}], "start": 1756801507229, "stop": 1756801507496}], "attachments": [{"name": "stdout", "source": "5535dcdf-1d95-4bba-a72c-9984ac3be700-attachment.txt", "type": "text/plain"}], "start": 1756801477260, "stop": 1756801507504, "uuid": "35d3f155-21a1-4f98-90b7-7a43b06950f5", "historyId": "e60dab4e55edacbecf632d4d22f368e6", "testCaseId": "e60dab4e55edacbecf632d4d22f368e6", "fullName": "testcases.test_ella.unsupported_commands.test_set_battery_saver_settings.TestEllaSetBatterySaverSettings#test_set_battery_saver_settings", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_battery_saver_settings"}, {"name": "subSuite", "value": "TestEllaSetBatterySaverSettings"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_battery_saver_settings"}]}