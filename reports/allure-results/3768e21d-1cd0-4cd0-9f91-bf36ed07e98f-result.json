{"name": "测试set an alarm at 8 am", "status": "passed", "description": "测试set an alarm at 8 am指令", "steps": [{"name": "执行命令: delete all the alarms", "status": "passed", "steps": [{"name": "执行命令: delete all the alarms", "status": "passed", "start": 1756784140777, "stop": 1756784160908}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7f35afd2-5307-4b65-a14b-7cf8a130613b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d84987d1-34df-4f58-a718-e4c4671441d7-attachment.png", "type": "image/png"}], "start": 1756784160908, "stop": 1756784161118}], "start": 1756784140777, "stop": 1756784161118}, {"name": "执行命令: set an alarm at 8 am", "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "status": "passed", "start": 1756784161118, "stop": 1756784181804}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "31c144cc-d2e4-4a2f-a2ea-6aa76b1450c8-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b6dd0e9b-4156-4718-aa2e-cf5c9b520094-attachment.png", "type": "image/png"}], "start": 1756784181804, "stop": 1756784182033}], "start": 1756784161118, "stop": 1756784182033}, {"name": "执行命令: get all the alarms", "status": "passed", "steps": [{"name": "执行命令: get all the alarms", "status": "passed", "start": 1756784182033, "stop": 1756784202716}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "de6c5c93-66ae-44d9-b957-7aabfcb28248-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3bdb8297-aa58-4ff4-9997-d94f03b97b95-attachment.png", "type": "image/png"}], "start": 1756784202716, "stop": 1756784202920}], "start": 1756784182033, "stop": 1756784202921}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756784202921, "stop": 1756784202922}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1ea7b977-64fb-479e-a24a-2f438d0a5db2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c7614970-3b4d-4212-8726-7f1348d241f8-attachment.png", "type": "image/png"}], "start": 1756784202922, "stop": 1756784203117}], "attachments": [{"name": "stdout", "source": "2b168f94-7550-4e28-8c00-94ae81224bfe-attachment.txt", "type": "text/plain"}], "start": 1756784140777, "stop": 1756784203117, "uuid": "d17ca810-43b8-46c1-be2e-cad988508c73", "historyId": "de0ed312f350c708e7a00bb74aeaac0f", "testCaseId": "de0ed312f350c708e7a00bb74aeaac0f", "fullName": "testcases.test_ella.component_coupling.test_set_an_alarm_at_8_am.TestEllaOpenClock#test_set_an_alarm_at_8_am", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_set_an_alarm_at_8_am"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_set_an_alarm_at_8_am"}]}