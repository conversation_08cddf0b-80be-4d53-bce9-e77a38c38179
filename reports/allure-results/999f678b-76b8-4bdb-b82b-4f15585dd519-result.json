{"name": "测试help me write an email能正常执行", "status": "passed", "description": "help me write an email", "steps": [{"name": "执行命令: help me write an email", "status": "passed", "steps": [{"name": "执行命令: help me write an email", "status": "passed", "start": 1756798375768, "stop": 1756798400432}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "df50070c-e136-446a-be81-6e4d98586c8e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "dcbd3e48-f6a0-4a06-a4db-9444364fb46b-attachment.png", "type": "image/png"}], "start": 1756798400432, "stop": 1756798400690}], "start": 1756798375768, "stop": 1756798400691}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756798400691, "stop": 1756798400692}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "190bae56-a37d-4f73-851d-d4a2b62d37a9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "527dcec3-4c22-48cb-bd88-e810cb739a4c-attachment.png", "type": "image/png"}], "start": 1756798400692, "stop": 1756798400932}], "attachments": [{"name": "stdout", "source": "0626b46f-46bb-4d11-8cc3-554e732942dc-attachment.txt", "type": "text/plain"}], "start": 1756798375768, "stop": 1756798400933, "uuid": "649255b4-1a5a-4536-bdc5-3f9e9eb596a0", "historyId": "70c9bd8c4aab57e96eb06acb93ca2223", "testCaseId": "70c9bd8c4aab57e96eb06acb93ca2223", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_write_an_email.TestEllaHelpMeWriteAnEmail#test_help_me_write_an_email", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_write_an_email"}, {"name": "subSuite", "value": "TestEllaHelpMeWriteAnEmail"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_write_an_email"}]}