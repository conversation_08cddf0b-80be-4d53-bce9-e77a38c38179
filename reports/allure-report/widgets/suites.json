{"total": 12, "items": [{"uid": "09cb3650ff0a2cc2af23d31dd3c975a2", "name": "testcases.test_ella.unsupported_commands", "statistic": {"failed": 42, "broken": 0, "skipped": 0, "passed": 181, "unknown": 0, "total": 223}}, {"uid": "4159dc35ce06d1422bb1b7c5665d834a", "name": "testcases.test_ella.system_coupling", "statistic": {"failed": 15, "broken": 0, "skipped": 0, "passed": 89, "unknown": 0, "total": 104}}, {"uid": "0a5f897bb744ec2f8b960fc5954cddf6", "name": "testcases.test_ella.dialogue", "statistic": {"failed": 11, "broken": 0, "skipped": 0, "passed": 66, "unknown": 0, "total": 77}}, {"uid": "5948c7c27387d214d4b5e1b876d4cb27", "name": "testcases.test_ella.component_coupling", "statistic": {"failed": 3, "broken": 0, "skipped": 0, "passed": 36, "unknown": 0, "total": 39}}, {"uid": "09775f95e182eb4ca75428a11ed7ae8d", "name": "testcases.test_ella.test_ask_screen.contact", "statistic": {"failed": 2, "broken": 0, "skipped": 0, "passed": 8, "unknown": 0, "total": 10}}, {"uid": "2660f6320a566ad526d6ea679fb2528f", "name": "testcases.test_ella.third_coupling", "statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 13, "unknown": 0, "total": 14}}, {"uid": "c47b7e54b8dcdeae7f34ae920768ce74", "name": "testcases.test_ella.test_ask_screen.translation", "statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}}, {"uid": "6dc11cb9bb372733a2ed6f167a12d612", "name": "testcases.test_ella.self_function", "statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 8, "unknown": 0, "total": 8}}, {"uid": "1a1f8f1f915bddf27e5f42469c81c088", "name": "testcases.test_ella.test_ask_screen.math_calculation", "statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 4, "unknown": 0, "total": 4}}, {"uid": "97006696402b61731abbf742b66dc15b", "name": "testcases.test_ella.test_ask_screen.scene_understanding", "statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 2, "unknown": 0, "total": 2}}]}