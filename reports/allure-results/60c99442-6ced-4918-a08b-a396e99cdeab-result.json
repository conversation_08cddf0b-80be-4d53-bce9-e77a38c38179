{"name": "测试switch to power saving mode能正常执行", "status": "passed", "description": "switch to power saving mode", "steps": [{"name": "执行命令: switch to power saving mode", "status": "passed", "steps": [{"name": "执行命令: switch to power saving mode", "status": "passed", "start": 1756791769103, "stop": 1756791790086}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2dee103c-6b02-443e-8718-ddc0a72a86c7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "dd4908f2-b66e-4129-92e1-716e8a5445d4-attachment.png", "type": "image/png"}], "start": 1756791790086, "stop": 1756791790310}], "start": 1756791769103, "stop": 1756791790311}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1756791790311, "stop": 1756791790312}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3e75d6a1-682e-4505-871d-afa0ae91524a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4184db74-3cbb-4517-a478-0158c4b44ffa-attachment.png", "type": "image/png"}], "start": 1756791790312, "stop": 1756791790536}], "attachments": [{"name": "stdout", "source": "5d55a6c3-aa75-4c80-bb93-d0d47a4ab0dd-attachment.txt", "type": "text/plain"}], "start": 1756791769103, "stop": 1756791790537, "uuid": "9ab38f85-01a3-41ac-9b4e-a7ade1b6f5fe", "historyId": "5dff8ffa0041df33df80919398086e48", "testCaseId": "5dff8ffa0041df33df80919398086e48", "fullName": "testcases.test_ella.system_coupling.test_switch_to_power_saving_mode.TestEllaSwitchToPowerSavingMode#test_switch_to_power_saving_mode", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_power_saving_mode"}, {"name": "subSuite", "value": "TestEllaSwitchToPowerSavingMode"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_power_saving_mode"}]}