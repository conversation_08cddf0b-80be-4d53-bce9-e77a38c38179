{"name": "测试close phonemaster能正常执行", "status": "passed", "description": "close phonemaster", "steps": [{"name": "执行命令: close phonemaster", "status": "passed", "steps": [{"name": "执行命令: close phonemaster", "status": "passed", "start": 1756782908521, "stop": 1756782928945}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1e1494a1-6699-4047-b255-b63987c85fab-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d74c5501-99d9-431f-80b3-7d550a34ca82-attachment.png", "type": "image/png"}], "start": 1756782928945, "stop": 1756782929150}], "start": 1756782908521, "stop": 1756782929151}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756782929151, "stop": 1756782929152}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d52c2208-54f5-455d-a9a1-a96a4deccf53-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "53e46ff5-4eac-4025-a519-6f9ec61a8a6b-attachment.png", "type": "image/png"}], "start": 1756782929152, "stop": 1756782929350}], "attachments": [{"name": "stdout", "source": "ca80e619-e7e9-4ee2-8bfa-9dfecd240c11-attachment.txt", "type": "text/plain"}], "start": 1756782908521, "stop": 1756782929351, "uuid": "4b5321aa-3f72-4e19-9366-000b52049724", "historyId": "bc062eca91b16841cac5c9865921b5c1", "testCaseId": "bc062eca91b16841cac5c9865921b5c1", "fullName": "testcases.test_ella.component_coupling.test_close_phonemaster.TestEllaClosePhonemaster#test_close_phonemaster", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_phonemaster"}, {"name": "subSuite", "value": "TestEllaClosePhonemaster"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_phonemaster"}]}