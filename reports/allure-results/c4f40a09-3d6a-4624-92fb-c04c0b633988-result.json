{"name": "测试play jay chou's music by spotify", "status": "passed", "description": "测试play jay chou's music by spotify指令", "steps": [{"name": "执行命令: play jay chou's music by spotify", "status": "passed", "steps": [{"name": "执行命令: play jay chou's music by spotify", "status": "passed", "start": 1756783847748, "stop": 1756783870186}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "91f45f3a-2e65-4d73-b231-3d61240a0139-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "313baeba-62ef-4898-8c3e-209a273efa69-attachment.png", "type": "image/png"}], "start": 1756783870186, "stop": 1756783870397}], "start": 1756783847748, "stop": 1756783870398}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756783870398, "stop": 1756783870399}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c7af2982-f9d2-442c-8340-770181b32fd3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "084b121f-a490-4d29-bb60-c8771eb18992-attachment.png", "type": "image/png"}], "start": 1756783870399, "stop": 1756783870606}], "attachments": [{"name": "stdout", "source": "5578a16d-b096-4870-89b6-101089e7b898-attachment.txt", "type": "text/plain"}], "start": 1756783847748, "stop": 1756783870607, "uuid": "2881823b-e317-42f7-b023-e65d9d9a89b3", "historyId": "cb21afc40e4aec32b847936756c8ba6e", "testCaseId": "cb21afc40e4aec32b847936756c8ba6e", "fullName": "testcases.test_ella.component_coupling.test_play_jay_chou_s_music_by_spotify.TestEllaOpenMusic#test_play_jay_chou_s_music_by_spotify", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_jay_chou_s_music_by_spotify"}, {"name": "subSuite", "value": "TestEllaOpenMusic"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_jay_chou_s_music_by_spotify"}]}