{"name": "测试Change the style of this image to 3D cartoon能正常执行", "status": "passed", "description": "Change the style of this image to 3D cartoon", "steps": [{"name": "执行命令: Change the style of this image to 3D cartoon", "status": "passed", "steps": [{"name": "执行命令: Change the style of this image to 3D cartoon", "status": "passed", "start": 1756788067685, "stop": 1756788177950}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7329bc23-13ae-43fa-a083-cba95ed4731e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5aab1a88-739e-4788-bbf9-edd180f3f3c9-attachment.png", "type": "image/png"}], "start": 1756788177950, "stop": 1756788178185}], "start": 1756788067685, "stop": 1756788178185}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756788178185, "stop": 1756788178186}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "030f9fc6-8e32-4ad1-9cdf-92e86e4fbfb2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "32470a9c-6fc2-4c05-8129-0b41c21a99c0-attachment.png", "type": "image/png"}], "start": 1756788178186, "stop": 1756788178402}], "attachments": [{"name": "stdout", "source": "2a21755e-11ca-4054-b836-38c73eb309d8-attachment.txt", "type": "text/plain"}], "start": 1756788067685, "stop": 1756788178402, "uuid": "1adcde55-b543-48dd-8981-a59f28f6a2cb", "historyId": "caccad19499f4bc8494953ac84d8d23c", "testCaseId": "caccad19499f4bc8494953ac84d8d23c", "fullName": "testcases.test_ella.self_function.test_change_the_style_of_this_image_to_d_cartoon.TestEllaChangeStyleThisImageDCartoon#test_change_the_style_of_this_image_to_d_cartoon", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.self_function"}, {"name": "suite", "value": "test_change_the_style_of_this_image_to_d_cartoon"}, {"name": "subSuite", "value": "TestEllaChangeStyleThisImageDCartoon"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.self_function.test_change_the_style_of_this_image_to_d_cartoon"}]}