{"name": "测试create a metting schedule at tomorrow能正常执行", "status": "passed", "description": "create a metting schedule at tomorrow", "steps": [{"name": "执行命令: create a metting schedule at tomorrow", "status": "passed", "steps": [{"name": "执行命令: create a metting schedule at tomorrow", "status": "passed", "start": 1756782981491, "stop": 1756783003096}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7fdccb34-1929-4f37-98aa-6faa03184497-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "29c06747-3b71-4149-96c9-0b52abb7794a-attachment.png", "type": "image/png"}], "start": 1756783003096, "stop": 1756783003287}], "start": 1756782981491, "stop": 1756783003287}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756783003287, "stop": 1756783003288}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5efc2270-ad6d-4410-91ba-e10930134d33-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8643319e-6a4f-4ef9-a020-0bae0525a7c6-attachment.png", "type": "image/png"}], "start": 1756783003288, "stop": 1756783003464}], "attachments": [{"name": "stdout", "source": "87573ec3-fd86-4e09-be64-638ed9bdbc4d-attachment.txt", "type": "text/plain"}], "start": 1756782981491, "stop": 1756783003465, "uuid": "0ea568ab-f73c-4a60-b540-f1d65596592c", "historyId": "ea48444c2b0789e59a64850ccfab3722", "testCaseId": "ea48444c2b0789e59a64850ccfab3722", "fullName": "testcases.test_ella.component_coupling.test_create_a_metting_schedule_at_tomorrow.TestEllaCreateMettingScheduleTomorrow#test_create_a_metting_schedule_at_tomorrow", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_create_a_metting_schedule_at_tomorrow"}, {"name": "subSuite", "value": "TestEllaCreateMettingScheduleTomorrow"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_create_a_metting_schedule_at_tomorrow"}]}