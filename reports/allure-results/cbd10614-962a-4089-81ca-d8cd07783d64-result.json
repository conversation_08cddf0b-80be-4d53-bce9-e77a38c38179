{"name": "测试Add this image to my notes", "status": "passed", "description": "测试Ask Screen功能: Add this image to my notes", "steps": [{"name": "准备测试数据", "status": "passed", "start": 1756793923383, "stop": 1756793932484}, {"name": "执行Ask Screen命令: Add this image to my notes", "status": "passed", "steps": [{"name": "执行浮窗命令: Add this image to my notes", "status": "passed", "steps": [{"name": "执行命令: Add this image to my notes", "status": "passed", "start": 1756793932485, "stop": 1756793940449}, {"name": "等待并获取AI响应", "status": "passed", "start": 1756793940449, "stop": 1756793957235}, {"name": "验证响应内容", "status": "passed", "attachments": [{"name": "关键词验证结果", "source": "9517512f-bbc2-494e-8967-6a035afcfb30-attachment.txt", "type": "text/plain"}], "start": 1756793957235, "stop": 1756793957237}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "浮窗测试总结", "source": "e7b497e5-e8a0-4d0e-b0e1-d211cf29dbab-attachment.txt", "type": "text/plain"}, {"name": "AI响应内容", "source": "30cad42d-a4e6-4a49-9ab0-40504704275c-attachment.txt", "type": "text/plain"}], "start": 1756793957237, "stop": 1756793957240}], "start": 1756793932484, "stop": 1756793957240}, {"name": "截图记录测试完成状态", "status": "passed", "attachments": [{"name": "floating_test_completed", "source": "181ff890-239a-4597-babe-2401d3e5c6df-attachment.png", "type": "image/png"}], "start": 1756793957240, "stop": 1756793957436}], "start": 1756793932484, "stop": 1756793958642}, {"name": "验证测试结果", "status": "passed", "start": 1756793958642, "stop": 1756793958643}, {"name": "记录测试完成", "status": "passed", "start": 1756793958643, "stop": 1756793958643}], "attachments": [{"name": "stdout", "source": "f88209ef-a249-4ec1-94e1-2e141e040b74-attachment.txt", "type": "text/plain"}], "start": 1756793923383, "stop": 1756793958643, "uuid": "51f8140d-f1d7-4f31-906c-1795040ba083", "historyId": "eba7a9e7bc39f5ded45d5d0bd68c834e", "testCaseId": "eba7a9e7bc39f5ded45d5d0bd68c834e", "fullName": "testcases.test_ella.test_ask_screen.math_calculation.test_add_this_image_to_my_notes.TestAskScreenAddImageMyNotes#test_add_this_image_to_my_notes", "labels": [{"name": "story", "value": "数学计算"}, {"name": "epic", "value": "Ella浮窗测试"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ask Screen功能"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.test_ask_screen.math_calculation"}, {"name": "suite", "value": "test_add_this_image_to_my_notes"}, {"name": "subSuite", "value": "TestAskScreenAddImageMyNotes"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_ask_screen.math_calculation.test_add_this_image_to_my_notes"}]}