{"name": "测试hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively.", "status": "passed", "description": "测试hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively.指令", "steps": [{"name": "执行命令: hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively.", "status": "passed", "steps": [{"name": "执行命令: hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively.", "status": "passed", "start": 1756797801547, "stop": 1756797827618}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "376dcfa2-1f0e-46b7-a891-c286498e8f48-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f4506a4f-dc98-4f6a-b179-c86cf64109d6-attachment.png", "type": "image/png"}], "start": 1756797827619, "stop": 1756797827854}], "start": 1756797801547, "stop": 1756797827854}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756797827854, "stop": 1756797827855}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1f0ea578-4ab6-4295-8337-c57ee6a339de-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "310ca190-e793-4fab-9f79-198f8979f4d8-attachment.png", "type": "image/png"}], "start": 1756797827855, "stop": 1756797828102}], "attachments": [{"name": "stdout", "source": "79a8d5c2-f1a5-441b-9f73-1d68bd511190-attachment.txt", "type": "text/plain"}], "start": 1756797801547, "stop": 1756797828102, "uuid": "6fcbb88b-3e3b-414d-adc1-4bf7de706123", "historyId": "00ae864f57f2146374ff8bf301b9b8af", "testCaseId": "00ae864f57f2146374ff8bf301b9b8af", "fullName": "testcases.test_ella.unsupported_commands.test_hamster_mascot.TestEllaOpenPlayPoliticalNews#test_hamster_mascot", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_hamster_mascot"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_hamster_mascot"}]}