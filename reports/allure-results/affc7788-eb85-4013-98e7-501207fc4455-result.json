{"name": "测试i want to listen to fm能正常执行", "status": "passed", "description": "i want to listen to fm", "steps": [{"name": "执行命令: i want to listen to fm", "status": "passed", "steps": [{"name": "执行命令: i want to listen to fm", "status": "passed", "start": 1756785612557, "stop": 1756785633953}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "34ad5692-731f-41ea-9dfa-3b43901e00ce-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "43745cd0-e20b-4e4b-aa98-52e90a53f909-attachment.png", "type": "image/png"}], "start": 1756785633953, "stop": 1756785634168}], "start": 1756785612557, "stop": 1756785634168}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756785634169, "stop": 1756785634170}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "573e855a-8aa2-4d17-99dc-39200e8443f7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d438a85e-92b6-471e-a2e5-9fd8805d2c14-attachment.png", "type": "image/png"}], "start": 1756785634170, "stop": 1756785634413}], "attachments": [{"name": "stdout", "source": "92a37fa0-7735-490c-89e0-5201f0e2435a-attachment.txt", "type": "text/plain"}], "start": 1756785612556, "stop": 1756785634414, "uuid": "c18a9b79-eb5f-4609-9bb6-58987c6fa85a", "historyId": "3d6cfc87445d1bd76dceee439d00b3d6", "testCaseId": "3d6cfc87445d1bd76dceee439d00b3d6", "fullName": "testcases.test_ella.dialogue.test_i_want_to_listen_to_fm.TestEllaHelloHello#test_i_want_to_listen_to_fm", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_i_want_to_listen_to_fm"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_i_want_to_listen_to_fm"}]}