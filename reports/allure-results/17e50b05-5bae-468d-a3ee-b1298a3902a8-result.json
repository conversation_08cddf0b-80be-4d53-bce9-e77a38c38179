{"name": "测试there is a colorful butterfly beside it", "status": "passed", "description": "测试there is a colorful butterfly beside it指令", "steps": [{"name": "执行命令: there is a colorful butterfly beside it", "status": "passed", "steps": [{"name": "执行命令: there is a colorful butterfly beside it", "status": "passed", "start": 1756803792587, "stop": 1756803816653}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1082cbb1-f82e-4021-8f53-5789c5e52dac-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2530a6ab-e5f7-4fce-bce5-f11851c9da7e-attachment.png", "type": "image/png"}], "start": 1756803816653, "stop": 1756803816890}], "start": 1756803792587, "stop": 1756803816890}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756803816890, "stop": 1756803816893}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2affe8b8-2e58-404e-99e2-be2bd7ccde9f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "16aaef8a-0257-43b4-b7a2-a28f1d3dcfa2-attachment.png", "type": "image/png"}], "start": 1756803816893, "stop": 1756803817133}], "attachments": [{"name": "stdout", "source": "fff02b0c-e041-4760-836d-08459f24bb6d-attachment.txt", "type": "text/plain"}], "start": 1756803792587, "stop": 1756803817133, "uuid": "c8bafe9a-b546-4afe-960e-d7564f7b2036", "historyId": "cc158f7c05891fbac271a86692bc57a6", "testCaseId": "cc158f7c05891fbac271a86692bc57a6", "fullName": "testcases.test_ella.unsupported_commands.test_there_is_a_colorful_butterfly_beside_it.TestEllaOpenPlayPoliticalNews#test_there_is_a_colorful_butterfly_beside_it", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_there_is_a_colorful_butterfly_beside_it"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_there_is_a_colorful_butterfly_beside_it"}]}