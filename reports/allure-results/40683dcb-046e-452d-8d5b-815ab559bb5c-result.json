{"name": "测试Switch Magic Voice to Grace能正常执行", "status": "passed", "description": "Switch Magic Voice to Grace", "steps": [{"name": "执行命令: Switch Magic Voice to Grace", "status": "passed", "steps": [{"name": "执行命令: Switch Magic Voice to Grace", "status": "passed", "start": 1756791455998, "stop": 1756791477040}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f98a4fff-e4f0-414a-ab0a-aeaa6a2fdbdc-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6a8e449e-2942-46b5-8f65-2a1fec90d00d-attachment.png", "type": "image/png"}], "start": 1756791477040, "stop": 1756791477265}], "start": 1756791455998, "stop": 1756791477265}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756791477265, "stop": 1756791477267}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "cf270fae-5b06-46b5-8f4c-a5ee0fc00207-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6819948d-6632-4b01-9369-74e1c9a5200f-attachment.png", "type": "image/png"}], "start": 1756791477267, "stop": 1756791477465}], "attachments": [{"name": "stdout", "source": "89fafcfa-6bce-4cdb-8457-683991c0b92c-attachment.txt", "type": "text/plain"}], "start": 1756791455998, "stop": 1756791477466, "uuid": "a5d3c28e-aede-4208-8cfb-a109c1577503", "historyId": "04cef4934fe29e90ca0248af7b395794", "testCaseId": "04cef4934fe29e90ca0248af7b395794", "fullName": "testcases.test_ella.system_coupling.test_switch_magic_voice_to_grace.TestEllaSwitchMagicVoiceGrace#test_switch_magic_voice_to_grace", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_magic_voice_to_grace"}, {"name": "subSuite", "value": "TestEllaSwitchMagicVoiceGrace"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_magic_voice_to_grace"}]}