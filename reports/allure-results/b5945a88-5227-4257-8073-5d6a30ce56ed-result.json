{"name": "测试view in notebook", "status": "passed", "description": "测试view in notebook指令", "steps": [{"name": "执行命令: view in notebook", "status": "passed", "steps": [{"name": "执行命令: view in notebook", "status": "passed", "start": 1756804115143, "stop": 1756804144224}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "294d8e0c-ca33-489e-833f-d6a8b01d348d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7a9dce2a-5c7a-4496-873f-49b0d55a3a95-attachment.png", "type": "image/png"}], "start": 1756804144224, "stop": 1756804144445}], "start": 1756804115143, "stop": 1756804144445}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756804144445, "stop": 1756804144447}, {"name": "验证notes已打开", "status": "passed", "start": 1756804144447, "stop": 1756804144447}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6bdf91b8-2345-444b-89dc-086bd530b19d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a3d90546-5439-4d71-8153-c3c55f1379fa-attachment.png", "type": "image/png"}], "start": 1756804144447, "stop": 1756804144721}], "attachments": [{"name": "stdout", "source": "1bf33348-2b94-4e30-9a47-6aacde04d7bc-attachment.txt", "type": "text/plain"}], "start": 1756804115142, "stop": 1756804144722, "uuid": "be9a4a45-9153-49e3-ba03-2c0662a5cacc", "historyId": "19df0c79ab8c9ce909771e1a9d21fed3", "testCaseId": "19df0c79ab8c9ce909771e1a9d21fed3", "fullName": "testcases.test_ella.unsupported_commands.test_view_in_notebook.TestEllaOpenPlayPoliticalNews#test_view_in_notebook", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_view_in_notebook"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_view_in_notebook"}]}