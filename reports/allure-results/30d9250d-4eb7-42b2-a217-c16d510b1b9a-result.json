{"name": "测试adjustment the brightness to minimun能正常执行", "status": "passed", "description": "adjustment the brightness to minimun", "steps": [{"name": "执行命令: adjustment the brightness to minimun", "status": "passed", "steps": [{"name": "执行命令: adjustment the brightness to minimun", "status": "passed", "start": 1756788921764, "stop": 1756788944605}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b1ba26c8-c939-4c92-bc82-3d33bf0d1f05-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9fc1dc03-ca1a-4f7f-a23b-489d4f84945b-attachment.png", "type": "image/png"}], "start": 1756788944606, "stop": 1756788944839}], "start": 1756788921764, "stop": 1756788944839}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756788944840, "stop": 1756788944841}, {"name": "验证应用已打开", "status": "passed", "start": 1756788944841, "stop": 1756788944841}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "48c7f3f3-69c9-403a-bbe5-954a6a9d2b0a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "bc5923b7-6d75-48a4-a4c5-a4b3ae9ce8ce-attachment.png", "type": "image/png"}], "start": 1756788944841, "stop": 1756788945055}], "attachments": [{"name": "stdout", "source": "7a4d0f91-c504-4cf0-b06e-675e66a6300b-attachment.txt", "type": "text/plain"}], "start": 1756788921764, "stop": 1756788945055, "uuid": "95c645fc-4135-429f-b51c-75d013a69d63", "historyId": "1a94a631f074dc4c0ba2bd39c9518123", "testCaseId": "1a94a631f074dc4c0ba2bd39c9518123", "fullName": "testcases.test_ella.system_coupling.test_adjustment_the_brightness_to_minimun.TestEllaAdjustmentBrightnessMinimun#test_adjustment_the_brightness_to_minimun", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_adjustment_the_brightness_to_minimun"}, {"name": "subSuite", "value": "TestEllaAdjustmentBrightnessMinimun"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_adjustment_the_brightness_to_minimun"}]}