{"name": "测试take a note on how to build a treehouse能正常执行", "status": "passed", "description": "take a note on how to build a treehouse", "steps": [{"name": "执行命令: take a note on how to build a treehouse", "status": "passed", "steps": [{"name": "执行命令: take a note on how to build a treehouse", "status": "passed", "start": 1756787148348, "stop": 1756787172057}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2c2f4f90-b018-4fd4-9a5f-963fa1259d57-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a4fd2486-7f7f-4642-8a74-f4cc5a948831-attachment.png", "type": "image/png"}], "start": 1756787172057, "stop": 1756787172271}], "start": 1756787148348, "stop": 1756787172271}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756787172271, "stop": 1756787172272}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7deb6680-1472-4f3d-9a46-27204bcd1114-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f0d139fe-367e-4433-bc17-a0151d6cc574-attachment.png", "type": "image/png"}], "start": 1756787172272, "stop": 1756787172498}], "attachments": [{"name": "stdout", "source": "976be8e8-08b3-47d8-a26d-b720b4326a35-attachment.txt", "type": "text/plain"}], "start": 1756787148348, "stop": 1756787172498, "uuid": "7df8c900-7a40-42fd-96cd-95bd24cd92a2", "historyId": "c45aa63628fe12b375ba7e65c39d93b1", "testCaseId": "c45aa63628fe12b375ba7e65c39d93b1", "fullName": "testcases.test_ella.dialogue.test_take_a_note_on_how_to_build_a_treehouse.TestEllaTakeNoteHowBuildTreehouse#test_take_a_note_on_how_to_build_a_treehouse", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_take_a_note_on_how_to_build_a_treehouse"}, {"name": "subSuite", "value": "TestEllaTakeNoteHowBuildTreehouse"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_take_a_note_on_how_to_build_a_treehouse"}]}