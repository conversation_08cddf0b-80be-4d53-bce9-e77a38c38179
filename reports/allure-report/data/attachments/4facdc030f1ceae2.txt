2025-09-02 12:52:24 | INFO | core.base_page:__init__:38 | 初始化页面: ella - dialogue_page
2025-09-02 12:52:24 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:299 | 🧹 开始清除手机上所有运行中的应用进程...
2025-09-02 12:52:24 | INFO | tools.adb_process_monitor:clear_all_running_processes:1955 | 🧹 开始清除手机上所有运行中的应用进程...
2025-09-02 12:52:24 | INFO | tools.adb_process_monitor:clear_all_running_processes:1971 | ⚡ 优先使用命令直接清理...
2025-09-02 12:52:26 | INFO | tools.adb_process_monitor:clear_all_running_processes:1977 | 💪 强制停止顽固应用...
2025-09-02 12:52:32 | INFO | tools.adb_process_monitor:clear_all_running_processes:1987 | 🎉 应用进程清理完成，共清理 30 个应用
2025-09-02 12:52:34 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:301 | ✅ 应用进程清理完成
2025-09-02 12:52:34 | INFO | pages.apps.ella.dialogue_page:start_app:215 | 启动Ella应用
2025-09-02 12:52:34 | INFO | pages.apps.ella.dialogue_page:start_app:223 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-09-02 12:52:38 | INFO | pages.apps.ella.dialogue_page:_check_app_started:281 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-09-02 12:52:38 | INFO | pages.apps.ella.dialogue_page:start_app:228 | ✅ Ella应用启动成功（指定Activity）
2025-09-02 12:52:38 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:302 | 等待Ella页面加载完成 (超时: 15秒)
2025-09-02 12:52:38 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:312 | ✅ 确认当前在Ella应用中
2025-09-02 12:52:38 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:321 | ✅ 主输入框已出现，页面加载完成
2025-09-02 12:52:38 | INFO | testcases.test_ella.base_ella_test:ella_app:333 | ✅ Ella应用启动成功
2025-09-02 12:52:38 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:371 | 初始状态None- 使用命令Summarize what I'm reading，状态: 
2025-09-02 12:52:38 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:770 | 确保在对话页面...
2025-09-02 12:52:38 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-09-02 12:52:39 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-09-02 12:52:39 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-09-02 12:52:39 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-09-02 12:52:39 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:781 | ✅ 已在对话页面
2025-09-02 12:52:39 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-09-02 12:52:39 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-09-02 12:52:39 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:379 | 🎯 检测到多模态指令: document
2025-09-02 12:52:39 | INFO | testcases.test_ella.base_ella_test:_execute_multimodal_operation:1234 | 🚀 开始执行多模态操作: document
2025-09-02 12:52:39 | INFO | pages.apps.ella.ella_multimodal_handler:execute_multimodal_function:49 | 执行多模态功能: document
2025-09-02 12:52:39 | INFO | pages.apps.ella.ella_multimodal_handler:_open_multimodal_entrance:75 | 点击多模态入口
2025-09-02 12:52:39 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [多模态入口], 超时时间: 5秒
2025-09-02 12:52:39 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 12:52:39 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [多模态入口]
2025-09-02 12:52:40 | INFO | core.base_element:click:231 | 点击元素成功 [多模态入口]
2025-09-02 12:52:43 | INFO | pages.apps.ella.ella_multimodal_handler:_open_multimodal_entrance:86 | ✅ 成功点击多模态入口
2025-09-02 12:52:43 | INFO | pages.apps.ella.ella_multimodal_handler:_handle_document_flow:102 | 执行文档功能流程
2025-09-02 12:52:43 | INFO | pages.apps.ella.ella_multimodal_handler:_prepare_test_documents:722 | 开始推送测试文档到设备
2025-09-02 12:52:43 | INFO | tools.file_pusher:check_device_connection:444 | 检测到 1 个连接的设备
2025-09-02 12:52:43 | INFO | tools.file_pusher:push_documents_to_device:62 | 开始推送文档文件到设备
2025-09-02 12:52:43 | INFO | tools.file_pusher:_push_single_file:420 | ✅ 文件推送成功: bcy_doc.txt
2025-09-02 12:52:43 | INFO | tools.file_pusher:_push_single_file:420 | ✅ 文件推送成功: bcy_doc2.txt
2025-09-02 12:52:44 | INFO | tools.file_pusher:_push_single_file:420 | ✅ 文件推送成功: bcy_doc.txt
2025-09-02 12:52:44 | INFO | tools.file_pusher:_push_single_file:420 | ✅ 文件推送成功: bcy_doc2.txt
2025-09-02 12:52:44 | INFO | tools.file_pusher:push_documents_to_device:88 | 文档推送完成: 4/4 个文件成功
2025-09-02 12:52:44 | INFO | pages.apps.ella.ella_multimodal_handler:_prepare_test_documents:732 | ✅ 测试文档推送成功
2025-09-02 12:52:46 | INFO | pages.apps.ella.ella_multimodal_handler:_prepare_test_documents:740 | 设备中的文件: ['AIMonkey', 'Translate_the_content_written_on_the_picture_into_French.jpg', 'bcy_doc.txt', 'bcy_doc2.txt']
2025-09-02 12:52:46 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [多模态-document], 超时时间: 5秒
2025-09-02 12:52:47 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 12:52:47 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [多模态-document]
2025-09-02 12:52:47 | INFO | core.base_element:click:231 | 点击元素成功 [多模态-document]
2025-09-02 12:52:49 | INFO | pages.apps.ella.ella_multimodal_handler:_handle_document_flow:116 | ✅ 点击document按钮成功
2025-09-02 12:52:49 | INFO | pages.apps.ella.ella_multimodal_handler:_select_file:255 | 尝试选择文件
2025-09-02 12:52:59 | INFO | pages.apps.ella.ella_multimodal_handler:_open_downloads_directory:447 | 尝试打开下载目录
2025-09-02 12:53:01 | INFO | pages.apps.ella.ella_multimodal_handler:_open_downloads_directory:457 | ✅ 点击菜单按钮成功
2025-09-02 12:53:04 | INFO | pages.apps.ella.ella_multimodal_handler:_open_downloads_directory:465 | ✅ 通过精确定位点击Downloads选项成功
2025-09-02 12:53:14 | INFO | pages.apps.ella.ella_multimodal_handler:_open_downloads_directory:488 | ✅ 下载目录打开成功
2025-09-02 12:53:14 | INFO | pages.apps.ella.ella_multimodal_handler:_select_specific_file:290 | 尝试选择特定文件: bcy_doc.txt
2025-09-02 12:53:17 | INFO | pages.apps.ella.ella_multimodal_handler:_select_specific_file:299 | ✅ 成功选择文件: bcy_doc.txt
2025-09-02 12:53:17 | INFO | pages.apps.ella.ella_multimodal_handler:_handle_document_flow:123 | ✅ 文档功能流程完成
2025-09-02 12:53:17 | INFO | testcases.test_ella.base_ella_test:_execute_multimodal_operation:1245 | ✅ 多模态功能执行成功: document
2025-09-02 12:53:19 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:770 | 确保在对话页面...
2025-09-02 12:53:19 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-09-02 12:53:19 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-09-02 12:53:19 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-09-02 12:53:19 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-09-02 12:53:19 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:781 | ✅ 已在对话页面
2025-09-02 12:53:19 | INFO | testcases.test_ella.base_ella_test:_execute_command:887 | 📝 执行文本命令: Summarize what I'm reading
2025-09-02 12:53:19 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: Summarize what I'm reading
2025-09-02 12:53:19 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-09-02 12:53:19 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-09-02 12:53:19 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: Summarize what I'm reading
2025-09-02 12:53:20 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-09-02 12:53:20 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 12:53:20 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-09-02 12:53:20 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-09-02 12:53:20 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-09-02 12:53:20 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 12:53:20 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-09-02 12:53:20 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: Summarize what I'm reading
2025-09-02 12:53:20 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-09-02 12:53:20 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-09-02 12:53:22 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-09-02 12:53:22 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-09-02 12:53:22 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-09-02 12:53:23 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-09-02 12:53:23 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-09-02 12:53:23 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-09-02 12:53:23 | INFO | testcases.test_ella.base_ella_test:_execute_command:891 | ✅ 成功执行命令: Summarize what I'm reading (语音模式: False)
2025-09-02 12:53:23 | INFO | testcases.test_ella.base_ella_test:_handle_popup_after_command:178 | handle_popup_after_command:处理弹窗
2025-09-02 12:53:23 | INFO | core.popup_tool:detect_and_close_popup_once:738 | 执行单次弹窗检测和关闭
2025-09-02 12:53:26 | INFO | core.popup_tool:detect_and_close_popup_once:742 | 未检测到弹窗，无需处理
2025-09-02 12:53:26 | INFO | testcases.test_ella.base_ella_test:_get_response_timeout:1281 | 🎯 多模态命令 (document) 使用专用超时时间: 5秒
2025-09-02 12:53:26 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:35 | 等待AI响应，超时时间: 5秒
2025-09-02 12:53:28 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:60 | ✅ 通过TTS按钮检测到响应
2025-09-02 12:53:31 | INFO | testcases.test_ella.base_ella_test:_get_final_status_with_page_info:445 | 状态检查时当前应用包名: com.transsion.aivoiceassistant
2025-09-02 12:53:31 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:563 | 状态检查完成，现在获取响应文本
2025-09-02 12:53:31 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:567 | 第1次尝试确保在Ella页面以获取响应
2025-09-02 12:53:31 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:770 | 确保在对话页面...
2025-09-02 12:53:31 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-09-02 12:53:32 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-09-02 12:53:32 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-09-02 12:53:32 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-09-02 12:53:32 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:781 | ✅ 已在对话页面
2025-09-02 12:53:32 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:571 | ✅ 已确认在Ella对话页面，可以获取响应
2025-09-02 12:53:32 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-09-02 12:53:32 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-09-02 12:53:32 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-09-02 12:53:32 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-09-02 12:53:32 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:1335 | 检查是否在Ella页面...
2025-09-02 12:53:32 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-09-02 12:53:33 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-09-02 12:53:33 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-09-02 12:53:33 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-09-02 12:53:33 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:1344 | ✅ 当前在Ella页面
2025-09-02 12:53:33 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:140 | 获取AI响应文本
2025-09-02 12:53:33 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:474 | ✅ 从asr_txt成功获取响应: Summarize what I'm reading
2025-09-02 12:53:35 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | robot_text节点不存在，已达到最大重试次数
2025-09-02 12:53:36 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | function_name节点不存在，已达到最大重试次数
2025-09-02 12:53:37 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | function_control节点不存在，已达到最大重试次数
2025-09-02 12:53:38 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:474 | ✅ 从tv_card_chat_gpt成功获取响应: Summary of AIGC Applications Across Industries

The document outlines various applications of AIGC (Artificial Intelligence Generated Content) across multiple industries.

Manufacturing : Haier's Shanghai appliance R&D center uses an AIGC-powered virtual designer, "Co-designer," to automate the design process, improving efficiency and accelerating digital transformation.
Fashion Design : D UNIVERSE integrates AIGC to generate customized designs, promoting digitalization and enabling creative freedom in the fashion industry.
Culture and Entertainment : Temple of Light and Mihoyo use AIGC to create immersive digital art experiences, enhancing user experience and innovating game content.
Healthcare : Nanopharm utilizes AIGC to develop local bioequiva
2025-09-02 12:53:39 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | tv_top节点不存在，已达到最大重试次数
2025-09-02 12:53:40 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | tv_banner节点不存在，已达到最大重试次数
2025-09-02 12:53:42 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | tv_text节点不存在，已达到最大重试次数
2025-09-02 12:53:43 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | alarm_time_tv节点不存在，已达到最大重试次数
2025-09-02 12:53:44 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:383 | alarm_day_tv节点不存在，已达到最大重试次数
2025-09-02 12:53:46 | WARNING | pages.apps.ella.ella_response_handler:_get_element_checked_with_retry:530 | alarm_switch节点不存在，已达到最大重试次数
2025-09-02 12:53:46 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:168 | 尝试获取其他有效的响应文本
2025-09-02 12:53:46 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:1049 | 从TextView元素获取响应
2025-09-02 12:53:46 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:1054 | 从TextView获取响应: Dialogue
2025-09-02 12:53:46 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:180 | ✅ 获取到响应文本: Dialogue
2025-09-02 12:53:46 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:1065 | 查找RecyclerView中的最新消息
2025-09-02 12:53:47 | INFO | pages.apps.ella.ella_response_handler:_extract_text_from_check_area_dump:897 | 从dump正则提取文本: Dialogue Explore Swipe down to view earlier chats Exit Summarization and Q&A DeepSeek-R1 Feel free to ask me any questions…
2025-09-02 12:53:47 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:180 | ✅ 获取到响应文本: Dialogue Explore Swipe down to view earlier chats Exit Summarization and Q&A DeepSeek-R1 Feel free to ask me any questions…
2025-09-02 12:53:47 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:185 | 未获取到有效的响应文本
2025-09-02 12:53:47 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:601 | 最终获取的AI响应: '["Summarize what I'm reading", '', '', '', 'Summary of AIGC Applications Across Industries\n\nThe document outlines various applications of AIGC (Artificial Intelligence Generated Content) across multiple industries.\n\nManufacturing : Haier\'s Shanghai appliance R&D center uses an AIGC-powered virtual designer, "Co-designer," to automate the design process, improving efficiency and accelerating digital transformation.\nFashion Design : D UNIVERSE integrates AIGC to generate customized designs, promoting digitalization and enabling creative freedom in the fashion industry.\nCulture and Entertainment : Temple of Light and Mihoyo use AIGC to create immersive digital art experiences, enhancing user experience and innovating game content.\nHealthcare : Nanopharm utilizes AIGC to develop local bioequiva', '', '', '', '', '', '', 'Dialogue', 'Dialogue Explore Swipe down to view earlier chats Exit Summarization and Q&A DeepSeek-R1 Feel free to ask me any questions…']'
2025-09-02 12:53:47 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:412 | ✅ 多模态操作执行完成: document
2025-09-02 12:53:47 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\app_test\reports/screenshots\TestEllaSummarizeWhatIMReading\test_completed.png
2025-09-02 12:53:47 | INFO | testcases.test_ella.base_ella_test:simple_command_test:1401 | 🎉 Summarize what I'm reading 测试完成 (多模态: document)
2025-09-02 12:53:47 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:1031 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ["Summarize what I'm reading", '', '', '', 'Summary of AIGC Applications Across Industries\n\nThe document outlines various applications of AIGC (Artificial Intelligence Generated Content) across multiple industries.\n\nManufacturing : Haier\'s Shanghai appliance R&D center uses an AIGC-powered virtual designer, "Co-designer," to automate the design process, improving efficiency and accelerating digital transformation.\nFashion Design : D UNIVERSE integrates AIGC to generate customized designs, promoting digitalization and enabling creative freedom in the fashion industry.\nCulture and Entertainment : Temple of Light and Mihoyo use AIGC to create immersive digital art experiences, enhancing user experience and innovating game content.\nHealthcare : Nanopharm utilizes AIGC to develop local bioequiva', '', '', '', '', '', '', 'Dialogue', 'Dialogue Explore Swipe down to view earlier chats Exit Summarization and Q&A DeepSeek-R1 Feel free to ask me any questions…']
2025-09-02 12:53:47 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:1075 | ✅ 响应包含期望内容: 'AIGC'
2025-09-02 12:53:47 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:1083 | 🎉 所有期望内容都已找到 (1/1)
2025-09-02 12:53:47 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\app_test\reports/screenshots\TestEllaSummarizeWhatIMReading\test_completed.png
2025-09-02 12:53:47 | INFO | pages.apps.ella.dialogue_page:stop_app:389 | 停止Ella应用
2025-09-02 12:53:49 | INFO | pages.apps.ella.dialogue_page:stop_app:400 | ✅ Ella应用已成功停止
