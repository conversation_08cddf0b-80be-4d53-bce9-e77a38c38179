{"name": "测试hello hello能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['How can I ']，实际响应: '['hello hello', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats 11:52 am <PERSON>, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh Help expand landscape photos What is the secret behind fireflies' synchronized flashing? G<PERSON><PERSON>y Brace Fires Dortmund to First Win hello hello <PERSON> is thinking… DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "trace": "self = <testcases.test_ella.dialogue.test_hello_hello.TestEllaHelloHello object at 0x0000018BC4CDA380>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC4D1F640>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_hello_hello(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['How can I ']，实际响应: '['hello hello', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats 11:52 am Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh Help expand landscape photos What is the secret behind fireflies' synchronized flashing? Guirassy Brace Fires Dortmund to First Win hello hello Ella is thinking… DeepSeek-R1 Feel free to ask me any questions…\"]'\nE           assert False\n\ntestcases\\test_ella\\dialogue\\test_hello_hello.py:35: AssertionError"}, "description": "hello hello", "steps": [{"name": "执行命令: hello hello", "status": "passed", "steps": [{"name": "执行命令: hello hello", "status": "passed", "start": 1756785173872, "stop": 1756785200048}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8aaff983-22e3-4cf4-a90a-9a26e670c4a6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4f14cf28-356e-4c80-866e-a491a399dc41-attachment.png", "type": "image/png"}], "start": 1756785200048, "stop": 1756785200261}], "start": 1756785173872, "stop": 1756785200262}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['How can I ']，实际响应: '['hello hello', '', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats 11:52 am <PERSON>, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh Help expand landscape photos What is the secret behind fireflies' synchronized flashing? G<PERSON><PERSON>y Brace Fires Dortmund to First Win hello hello <PERSON> is thinking… DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\dialogue\\test_hello_hello.py\", line 35, in test_hello_hello\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756785200262, "stop": 1756785200264}], "attachments": [{"name": "stdout", "source": "808951f1-a600-4fdf-b7e6-9a365d8ea16f-attachment.txt", "type": "text/plain"}], "start": 1756785173872, "stop": 1756785200265, "uuid": "74ebdf74-fcab-4ba7-b0a8-f2634ae9a24b", "historyId": "b3fa1d22b59def5f059cd9b0eefbe2b0", "testCaseId": "b3fa1d22b59def5f059cd9b0eefbe2b0", "fullName": "testcases.test_ella.dialogue.test_hello_hello.TestEllaHelloHello#test_hello_hello", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_hello_hello"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_hello_hello"}]}