{"name": "测试make a call能正常执行", "status": "passed", "description": "make a call", "steps": [{"name": "执行命令: make a call", "status": "passed", "steps": [{"name": "执行命令: make a call", "status": "passed", "start": 1756785848657, "stop": 1756785869834}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c935c7b4-b9fe-4544-ab43-f1995e8ebec5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e7589867-7bb0-47b7-acdd-2cbad3256ea7-attachment.png", "type": "image/png"}], "start": 1756785869834, "stop": 1756785870023}], "start": 1756785848657, "stop": 1756785870025}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756785870025, "stop": 1756785870026}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0cf416b2-c2d0-465a-9656-f0c39f845fda-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7f5abd6b-db4f-4452-ba99-7fc544b47839-attachment.png", "type": "image/png"}], "start": 1756785870026, "stop": 1756785870248}], "attachments": [{"name": "stdout", "source": "09e08008-c342-4209-bd5e-110a627d68b7-attachment.txt", "type": "text/plain"}], "start": 1756785848657, "stop": 1756785870248, "uuid": "b98d608f-a2cf-4777-9153-64699a26d388", "historyId": "2428ad915810150c12838b88ee13f49c", "testCaseId": "2428ad915810150c12838b88ee13f49c", "fullName": "testcases.test_ella.dialogue.test_make_a_call.TestEllaMakeCall#test_make_a_call", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_make_a_call"}, {"name": "subSuite", "value": "TestEllaMakeCall"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_make_a_call"}]}