{"name": "测试close airplane能正常执行", "status": "passed", "description": "close airplane", "steps": [{"name": "执行命令: close airplane", "status": "passed", "steps": [{"name": "执行命令: close airplane", "status": "passed", "start": 1756789110493, "stop": 1756789133297}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "93f6cab8-2a7d-4cf9-ab20-77afc9dcbacc-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "145a3044-4dff-4370-ad48-a7f0c38ecf8f-attachment.png", "type": "image/png"}], "start": 1756789133297, "stop": 1756789133508}], "start": 1756789110493, "stop": 1756789133508}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756789133508, "stop": 1756789133509}, {"name": "验证应用已打开", "status": "passed", "start": 1756789133509, "stop": 1756789133509}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c70ab798-ca59-4a8f-923b-3231d0f0332c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ba287d6e-4518-4aec-b834-7406b17f7e6c-attachment.png", "type": "image/png"}], "start": 1756789133509, "stop": 1756789133717}], "attachments": [{"name": "stdout", "source": "facc4777-6afc-4ebe-b328-c69fe6e62ea6-attachment.txt", "type": "text/plain"}], "start": 1756789110493, "stop": 1756789133717, "uuid": "f7b293d4-8256-4e65-9fb4-9e58b6bccf91", "historyId": "32ba476d46e86e963aa12ced39981955", "testCaseId": "32ba476d46e86e963aa12ced39981955", "fullName": "testcases.test_ella.system_coupling.test_close_airplane.TestEllaCloseAirplane#test_close_airplane", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_close_airplane"}, {"name": "subSuite", "value": "TestEllaCloseAirplane"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_close_airplane"}]}