{"name": "测试help me generate a picture of an elegant girl", "status": "passed", "description": "测试help me generate a picture of an elegant girl指令", "steps": [{"name": "执行命令: help me generate a picture of an elegant girl", "status": "passed", "steps": [{"name": "执行命令: help me generate a picture of an elegant girl", "status": "passed", "start": 1756798213878, "stop": 1756798236004}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8db3a070-997e-48b5-9201-b6e7afedc061-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8dbea08d-b019-43f5-b75e-650db8ddb0be-attachment.png", "type": "image/png"}], "start": 1756798236004, "stop": 1756798236216}], "start": 1756798213878, "stop": 1756798236217}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756798236217, "stop": 1756798236218}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "21e4ea38-ffb3-49b2-b947-932b34b7a54b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ba80b5b6-deac-4e63-86ec-814db438dc98-attachment.png", "type": "image/png"}], "start": 1756798236218, "stop": 1756798236445}], "attachments": [{"name": "stdout", "source": "da31d1e8-f6f4-40a6-90aa-405e05fe1146-attachment.txt", "type": "text/plain"}], "start": 1756798213877, "stop": 1756798236446, "uuid": "e222bbf5-eebb-41b0-9e64-520552233b77", "historyId": "50e811b93ce50e5ac8364a9fea07e234", "testCaseId": "50e811b93ce50e5ac8364a9fea07e234", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_an_elegant_girl.TestEllaOpenPlayPoliticalNews#test_help_me_generate_a_picture_of_an_elegant_girl", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_generate_a_picture_of_an_elegant_girl"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_generate_a_picture_of_an_elegant_girl"}]}