{"name": "测试show scores between livepool and manchester city能正常执行", "status": "passed", "description": "show scores between livepool and manchester city", "steps": [{"name": "执行命令: show scores between livepool and manchester city", "status": "passed", "steps": [{"name": "执行命令: show scores between livepool and manchester city", "status": "passed", "start": 1756786867722, "stop": 1756786892717}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8f69602d-0ecd-4737-b964-2a9cb5d089d4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "083d2c50-d06d-4d8f-9de2-bdc2cc2799c9-attachment.png", "type": "image/png"}], "start": 1756786892717, "stop": 1756786892993}], "start": 1756786867722, "stop": 1756786892993}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756786892993, "stop": 1756786892995}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "45788edc-8013-48d0-a943-1ce071610f59-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8f8b241f-64ae-44f6-b56f-2b0330900136-attachment.png", "type": "image/png"}], "start": 1756786892995, "stop": 1756786893217}], "attachments": [{"name": "stdout", "source": "afaf9c21-3dcc-49ed-9ecc-56a854eeaf04-attachment.txt", "type": "text/plain"}], "start": 1756786867722, "stop": 1756786893218, "uuid": "ee2f8147-9aa7-40a3-b82e-a8faa2947f86", "historyId": "83e3a1b41834e87017b680efd7c16b92", "testCaseId": "83e3a1b41834e87017b680efd7c16b92", "fullName": "testcases.test_ella.dialogue.test_show_scores_between_livepool_and_manchester_city.TestEllaShowScoresBetweenLivepoolManchesterCity#test_show_scores_between_livepool_and_manchester_city", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_show_scores_between_livepool_and_manchester_city"}, {"name": "subSuite", "value": "TestEllaShowScoresBetweenLivepoolManchesterCity"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_show_scores_between_livepool_and_manchester_city"}]}