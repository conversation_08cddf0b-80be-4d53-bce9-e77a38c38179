{"name": "测试Search for addresses on the screen能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only']，实际响应: '['Search for addresses on the screen', 'Unable to summarize the content on this page. You can try sending links or uploading documents.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_search_for_addresses_on_the_screen.TestEllaSearchAddressesScreen object at 0x0000018BC65E0E80>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC95E4190>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_search_for_addresses_on_the_screen(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only']，实际响应: '['Search for addresses on the screen', 'Unable to summarize the content on this page. You can try sending links or uploading documents.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_search_for_addresses_on_the_screen.py:34: AssertionError"}, "description": "Search for addresses on the screen", "steps": [{"name": "执行命令: Search for addresses on the screen", "status": "passed", "steps": [{"name": "执行命令: Search for addresses on the screen", "status": "passed", "start": 1756801168565, "stop": 1756801191854}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dc27e8e5-6f9f-4c56-8689-195f151bcab7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "99fb597a-9054-4506-b49f-50e87073f448-attachment.png", "type": "image/png"}], "start": 1756801191854, "stop": 1756801192108}], "start": 1756801168565, "stop": 1756801192108}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only']，实际响应: '['Search for addresses on the screen', 'Unable to summarize the content on this page. You can try sending links or uploading documents.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_search_for_addresses_on_the_screen.py\", line 34, in test_search_for_addresses_on_the_screen\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756801192108, "stop": 1756801192112}], "attachments": [{"name": "stdout", "source": "b75978b0-3c12-4033-a1ed-7189fcb24dbc-attachment.txt", "type": "text/plain"}], "start": 1756801168565, "stop": 1756801192112, "uuid": "a55f7a32-ffec-4119-920a-b4182fe79dd9", "historyId": "8814f1dafa698e785ee1f58faa6e745d", "testCaseId": "8814f1dafa698e785ee1f58faa6e745d", "fullName": "testcases.test_ella.unsupported_commands.test_search_for_addresses_on_the_screen.TestEllaSearchAddressesScreen#test_search_for_addresses_on_the_screen", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_search_for_addresses_on_the_screen"}, {"name": "subSuite", "value": "TestEllaSearchAddressesScreen"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_search_for_addresses_on_the_screen"}]}