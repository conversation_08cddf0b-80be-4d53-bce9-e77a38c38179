{"name": "测试turn on driving mode返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['turn on driving mode', \"This one stumped me, but I'll keep improving. Let's try again differently!\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_turn_on_driving_mode.TestEllaTurnDrivingMode object at 0x0000018BC66B4E20>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC953D990>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_turn_on_driving_mode(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry']，实际响应: '['turn on driving mode', \"This one stumped me, but I'll keep improving. Let's try again differently!\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_turn_on_driving_mode.py:33: AssertionError"}, "description": "验证turn on driving mode指令返回预期的不支持响应", "steps": [{"name": "执行命令: turn on driving mode", "status": "passed", "steps": [{"name": "执行命令: turn on driving mode", "status": "passed", "start": 1756803955233, "stop": 1756803978398}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "085bde4e-79bb-4668-860a-a51bee593505-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a28cc55c-47bc-4f32-83e7-8c2dc94bfab1-attachment.png", "type": "image/png"}], "start": 1756803978398, "stop": 1756803978671}], "start": 1756803955233, "stop": 1756803978671}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Sorry']，实际响应: '['turn on driving mode', \"This one stumped me, but I'll keep improving. Let's try again differently!\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_turn_on_driving_mode.py\", line 33, in test_turn_on_driving_mode\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756803978673, "stop": 1756803978675}], "attachments": [{"name": "stdout", "source": "837e7ac5-0b47-46d0-b1df-94736a1f09b7-attachment.txt", "type": "text/plain"}], "start": 1756803955233, "stop": 1756803978679, "uuid": "e33c0c6b-7863-4bdb-9ea2-83e4f2c393be", "historyId": "d8a3659601151a79f3b71ba4e47cafee", "testCaseId": "d8a3659601151a79f3b71ba4e47cafee", "fullName": "testcases.test_ella.unsupported_commands.test_turn_on_driving_mode.TestEllaTurnDrivingMode#test_turn_on_driving_mode", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_on_driving_mode"}, {"name": "subSuite", "value": "TestEllaTurnDrivingMode"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_on_driving_mode"}]}