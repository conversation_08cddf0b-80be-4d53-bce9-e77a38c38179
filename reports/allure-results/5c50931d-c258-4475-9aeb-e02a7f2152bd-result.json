{"name": "测试set gesture navigation返回正确的不支持响应", "status": "passed", "description": "验证set gesture navigation指令返回预期的不支持响应", "steps": [{"name": "执行命令: set gesture navigation", "status": "passed", "steps": [{"name": "执行命令: set gesture navigation", "status": "passed", "start": 1756802032514, "stop": 1756802065736}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "84d496d9-443c-4afa-9873-fb420d0899be-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c3847f51-baa1-4b80-b5a6-f926ac4f0af5-attachment.png", "type": "image/png"}], "start": 1756802065736, "stop": 1756802065963}], "start": 1756802032514, "stop": 1756802065964}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756802065965, "stop": 1756802065966}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e823a2f7-cf62-4d65-88fa-821075a13c54-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "14bc813b-5d82-43aa-a593-a67a167ad32c-attachment.png", "type": "image/png"}], "start": 1756802065966, "stop": 1756802066186}], "attachments": [{"name": "stdout", "source": "3338ca9d-779c-4378-a51d-1eebe9e935f3-attachment.txt", "type": "text/plain"}], "start": 1756802032514, "stop": 1756802066187, "uuid": "28415c4b-3920-44bb-bb85-21b6d7254973", "historyId": "e14cd5a605f26d24de7f5f63d4667c68", "testCaseId": "e14cd5a605f26d24de7f5f63d4667c68", "fullName": "testcases.test_ella.unsupported_commands.test_set_gesture_navigation.TestEllaSetGestureNavigation#test_set_gesture_navigation", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_gesture_navigation"}, {"name": "subSuite", "value": "TestEllaSetGestureNavigation"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_gesture_navigation"}]}