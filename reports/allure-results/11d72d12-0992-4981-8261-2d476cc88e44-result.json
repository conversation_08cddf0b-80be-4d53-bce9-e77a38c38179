{"name": "测试introduce yourself能正常执行", "status": "passed", "description": "introduce yourself", "steps": [{"name": "执行命令: introduce yourself", "status": "passed", "steps": [{"name": "执行命令: introduce yourself", "status": "passed", "start": 1756785730270, "stop": 1756785753863}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "403f7846-44b6-40ec-b04c-1d1b959371a2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ecd6de00-eb2d-4fea-b550-0416ad5eb85d-attachment.png", "type": "image/png"}], "start": 1756785753863, "stop": 1756785754097}], "start": 1756785730270, "stop": 1756785754098}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756785754098, "stop": 1756785754100}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c88cfef7-8ab8-4df8-a31f-c15fe90a4fae-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "dbb34d56-daab-417f-8abb-6d9ab629f249-attachment.png", "type": "image/png"}], "start": 1756785754100, "stop": 1756785754333}], "attachments": [{"name": "stdout", "source": "21ef4cdd-00ad-4102-a377-f806854f9710-attachment.txt", "type": "text/plain"}], "start": 1756785730270, "stop": 1756785754333, "uuid": "0a4ffcc4-5a40-4c83-9778-417795990ba1", "historyId": "a19924fb0a564cf26596907610c0f678", "testCaseId": "a19924fb0a564cf26596907610c0f678", "fullName": "testcases.test_ella.dialogue.test_introduce_yourself.TestEllaIntroduceYourself#test_introduce_yourself", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_introduce_yourself"}, {"name": "subSuite", "value": "TestEllaIntroduceYourself"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_introduce_yourself"}]}