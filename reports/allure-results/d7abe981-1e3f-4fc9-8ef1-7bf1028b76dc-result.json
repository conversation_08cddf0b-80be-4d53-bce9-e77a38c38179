{"name": "测试disable zonetouch master返回正确的不支持响应", "status": "passed", "description": "验证disable zonetouch master指令返回预期的不支持响应", "steps": [{"name": "执行命令: disable zonetouch master", "status": "passed", "steps": [{"name": "执行命令: disable zonetouch master", "status": "passed", "start": 1756796700410, "stop": 1756796723160}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7e199235-b48a-4f6e-8b22-8407d12f9861-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "de8340eb-c1d9-41d9-b91f-b29569b353c7-attachment.png", "type": "image/png"}], "start": 1756796723160, "stop": 1756796723378}], "start": 1756796700410, "stop": 1756796723378}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756796723378, "stop": 1756796723379}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d02975ba-7099-42b9-b3be-3cdfabd6e335-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "98a08989-6e27-4a18-a3af-d69eda567c20-attachment.png", "type": "image/png"}], "start": 1756796723379, "stop": 1756796723596}], "attachments": [{"name": "stdout", "source": "8aa7c98a-5c92-4267-b0d8-768e3ccc7b21-attachment.txt", "type": "text/plain"}], "start": 1756796700410, "stop": 1756796723597, "uuid": "0e350715-d60b-453c-b879-ff7b2656f04f", "historyId": "d094a0b21c0bd532e6db707dcbab5564", "testCaseId": "d094a0b21c0bd532e6db707dcbab5564", "fullName": "testcases.test_ella.unsupported_commands.test_disable_zonetouch_master.TestEllaDisableZonetouchMaster#test_disable_zonetouch_master", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_zonetouch_master"}, {"name": "subSuite", "value": "TestEllaDisableZonetouchMaster"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_zonetouch_master"}]}