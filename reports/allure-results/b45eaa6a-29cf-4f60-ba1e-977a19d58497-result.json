{"name": "测试pause fm能正常执行", "status": "passed", "description": "pause fm", "steps": [{"name": "执行命令: pause fm", "status": "passed", "steps": [{"name": "执行命令: pause fm", "status": "passed", "start": 1756783594695, "stop": 1756783615175}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "281f5c12-911b-4840-b7e2-62d9f70c2343-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "108d57a5-d293-4f10-80aa-f0d85e1fbe0a-attachment.png", "type": "image/png"}], "start": 1756783615175, "stop": 1756783615383}], "start": 1756783594695, "stop": 1756783615383}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756783615383, "stop": 1756783615384}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ea65089e-dacb-4400-86aa-df6c429f7706-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "477e7a8d-8fe9-4e05-9a46-9093a5ec281c-attachment.png", "type": "image/png"}], "start": 1756783615384, "stop": 1756783615603}], "attachments": [{"name": "stdout", "source": "85f758c1-9774-46d5-877f-977b0e2ed8fa-attachment.txt", "type": "text/plain"}], "start": 1756783594695, "stop": 1756783615603, "uuid": "633c93fd-0c2c-469d-8f77-eefb7c76f2f4", "historyId": "861aa58f9a3d0d9c9861d88316e784c5", "testCaseId": "861aa58f9a3d0d9c9861d88316e784c5", "fullName": "testcases.test_ella.component_coupling.test_pause_fm.TestEllaPauseFm#test_pause_fm", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_pause_fm"}, {"name": "subSuite", "value": "TestEllaPauseFm"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_pause_fm"}]}