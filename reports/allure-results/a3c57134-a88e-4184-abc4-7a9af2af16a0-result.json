{"name": "测试set date & time返回正确的不支持响应", "status": "passed", "description": "验证set date & time指令返回预期的不支持响应", "steps": [{"name": "执行命令: set date & time", "status": "passed", "steps": [{"name": "执行命令: set date & time", "status": "passed", "start": 1756801728777, "stop": 1756801752356}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "798aca53-a3d0-4614-bd00-b8c1b00ec01d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ec5294f4-254f-4d4c-917d-6a3122d14bf0-attachment.png", "type": "image/png"}], "start": 1756801752356, "stop": 1756801752609}], "start": 1756801728777, "stop": 1756801752611}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756801752611, "stop": 1756801752612}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2fae670c-ef7e-4139-bbbc-de170b398278-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3d2a18ed-3952-4fd6-913d-b19cc402f106-attachment.png", "type": "image/png"}], "start": 1756801752612, "stop": 1756801752858}], "attachments": [{"name": "stdout", "source": "30a06ecb-2d63-4b97-9ce0-2fc0069ef224-attachment.txt", "type": "text/plain"}], "start": 1756801728777, "stop": 1756801752858, "uuid": "757fe6e8-b76d-41da-9c13-a5b36b2d57d8", "historyId": "92e2909ea81e82011e43342b4fc06c3b", "testCaseId": "92e2909ea81e82011e43342b4fc06c3b", "fullName": "testcases.test_ella.unsupported_commands.test_set_date_time.TestEllaSetDateTime#test_set_date_time", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_date_time"}, {"name": "subSuite", "value": "TestEllaSetDateTime"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_date_time"}]}