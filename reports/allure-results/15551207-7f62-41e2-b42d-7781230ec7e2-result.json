{"name": "测试measure heart rate", "status": "passed", "description": "测试measure heart rate指令", "steps": [{"name": "执行命令: measure heart rate", "status": "passed", "steps": [{"name": "执行命令: measure heart rate", "status": "passed", "start": 1756785923733, "stop": 1756785944830}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d67e0d6d-8e93-4607-98bc-f83e45f882dd-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0e5a0361-edfa-4fbd-b297-cc94c31cbd33-attachment.png", "type": "image/png"}], "start": 1756785944830, "stop": 1756785945038}], "start": 1756785923733, "stop": 1756785945038}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756785945038, "stop": 1756785945039}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5ae3a8ba-c2fb-40eb-ae14-0610ab1753a2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8e7e878c-1a48-4d2d-9a32-807fbf028d2d-attachment.png", "type": "image/png"}], "start": 1756785945039, "stop": 1756785945259}], "attachments": [{"name": "stdout", "source": "c19b043f-a99e-4fb3-bb05-d5d7a614c7d7-attachment.txt", "type": "text/plain"}], "start": 1756785923733, "stop": 1756785945259, "uuid": "74aadea5-c804-4f13-bfa5-53137c821333", "historyId": "de524bccf252aabc016822f1a65de7f4", "testCaseId": "de524bccf252aabc016822f1a65de7f4", "fullName": "testcases.test_ella.dialogue.test_measure_heart_rate.TestEllaOpenPlayPoliticalNews#test_measure_heart_rate", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_measure_heart_rate"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_measure_heart_rate"}]}