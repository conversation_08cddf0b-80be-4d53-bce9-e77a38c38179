{"name": "测试set smart hub返回正确的不支持响应", "status": "passed", "description": "验证set smart hub指令返回预期的不支持响应", "steps": [{"name": "执行命令: set smart hub", "status": "passed", "steps": [{"name": "执行命令: set smart hub", "status": "passed", "start": 1756802837177, "stop": 1756802860573}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "87919505-17f4-47ed-9363-f3a2924c6975-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5f6f20ed-4c6f-4e2c-ba07-fff592c3d3e6-attachment.png", "type": "image/png"}], "start": 1756802860574, "stop": 1756802860860}], "start": 1756802837177, "stop": 1756802860861}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756802860862, "stop": 1756802860864}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fbae0b6f-922d-4753-b556-631b5697462b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0d3c6917-5661-4e44-996d-522f9fa429b9-attachment.png", "type": "image/png"}], "start": 1756802860864, "stop": 1756802861114}], "attachments": [{"name": "stdout", "source": "c91d394d-0e98-4dca-8fb3-4e525978f379-attachment.txt", "type": "text/plain"}], "start": 1756802837176, "stop": 1756802861114, "uuid": "1deb65a1-22d6-460e-901b-e02124b8b34e", "historyId": "61e923bb9b35a687b231b0c27b5ec620", "testCaseId": "61e923bb9b35a687b231b0c27b5ec620", "fullName": "testcases.test_ella.unsupported_commands.test_set_smart_hub.TestEllaSetSmartHub#test_set_smart_hub", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_smart_hub"}, {"name": "subSuite", "value": "TestEllaSetSmartHub"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_smart_hub"}]}