{"name": "测试Generate a circular car logo image with a three-pointed star inside the logo", "status": "passed", "description": "测试Generate a circular car logo image with a three-pointed star inside the logo指令", "steps": [{"name": "执行命令: Generate a circular car logo image with a three-pointed star inside the logo", "status": "passed", "steps": [{"name": "执行命令: Generate a circular car logo image with a three-pointed star inside the logo", "status": "passed", "start": 1756797477021, "stop": 1756797498427}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "937e8de7-74b8-4e69-8cc7-a92b485416a4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "568401e2-c8d4-49d5-87e9-71f7e454aada-attachment.png", "type": "image/png"}], "start": 1756797498427, "stop": 1756797498657}], "start": 1756797477021, "stop": 1756797498657}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756797498657, "stop": 1756797498659}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "520edd4a-f86c-4f23-b111-3bc5651be522-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7dcb1304-0e88-4df9-bdd6-6fe349f4f725-attachment.png", "type": "image/png"}], "start": 1756797498659, "stop": 1756797498853}], "attachments": [{"name": "stdout", "source": "5f0f508b-9986-4a16-a9a9-dcd6f19489aa-attachment.txt", "type": "text/plain"}], "start": 1756797477021, "stop": 1756797498854, "uuid": "9795741c-dcbf-42bd-8c90-311bce26da83", "historyId": "81f981a4ddbff762d2de1cd977c5568a", "testCaseId": "81f981a4ddbff762d2de1cd977c5568a", "fullName": "testcases.test_ella.unsupported_commands.test_generate_a_circular_car_logo_image_with_a_three_pointed_star_inside_the_logo.TestEllaOpenPlayPoliticalNews#test_generate_a_circular_car_logo_image_with_a_three_pointed_star_inside_the_logo", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_generate_a_circular_car_logo_image_with_a_three_pointed_star_inside_the_logo"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_generate_a_circular_car_logo_image_with_a_three_pointed_star_inside_the_logo"}]}