{"name": "测试send my recent photos to mom through whatsapp返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only', 'WhatsApp is not installed yet', 'I need to download WhatsApp to continue']，实际响应: '['send my recent photos to mom through whatsapp', 'No number for mom. Who do you want to send it to?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_send_my_recent_photos_to_mom_through_whatsapp.TestEllaSendMyRecentPhotosMomThroughWhatsapp object at 0x0000018BC65E2A10>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC8288AC0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_send_my_recent_photos_to_mom_through_whatsapp(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only', 'WhatsApp is not installed yet', 'I need to download WhatsApp to continue']，实际响应: '['send my recent photos to mom through whatsapp', 'No number for mom. Who do you want to send it to?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_send_my_recent_photos_to_mom_through_whatsapp.py:37: AssertionError"}, "description": "验证send my recent photos to mom through whatsapp指令返回预期的不支持响应", "steps": [{"name": "执行命令: send my recent photos to mom through whatsapp", "status": "passed", "steps": [{"name": "执行命令: send my recent photos to mom through whatsapp", "status": "passed", "start": 1756801344189, "stop": 1756801368437}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e30cfbb9-f9b6-4af3-af79-075e378fec6f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "33d6d0db-9f3f-43da-bcfc-1d75ce9c1c5e-attachment.png", "type": "image/png"}], "start": 1756801368437, "stop": 1756801368721}], "start": 1756801344189, "stop": 1756801368722}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only', 'WhatsApp is not installed yet', 'I need to download WhatsApp to continue']，实际响应: '['send my recent photos to mom through whatsapp', 'No number for mom. Who do you want to send it to?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_send_my_recent_photos_to_mom_through_whatsapp.py\", line 37, in test_send_my_recent_photos_to_mom_through_whatsapp\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756801368722, "stop": 1756801368726}], "attachments": [{"name": "stdout", "source": "2a11a07d-3f3a-4ddc-8e2d-782b1297ae47-attachment.txt", "type": "text/plain"}], "start": 1756801344189, "stop": 1756801368727, "uuid": "2603796c-6fdd-4120-9b13-647a2dbde1cc", "historyId": "80dab16fde357aadc4387b5d440ed276", "testCaseId": "80dab16fde357aadc4387b5d440ed276", "fullName": "testcases.test_ella.unsupported_commands.test_send_my_recent_photos_to_mom_through_whatsapp.TestEllaSendMyRecentPhotosMomThroughWhatsapp#test_send_my_recent_photos_to_mom_through_whatsapp", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_send_my_recent_photos_to_mom_through_whatsapp"}, {"name": "subSuite", "value": "TestEllaSendMyRecentPhotosMomThroughWhatsapp"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_send_my_recent_photos_to_mom_through_whatsapp"}]}