{"name": "测试take a screenshot能正常执行", "status": "passed", "description": "take a screenshot", "steps": [{"name": "执行命令: take a screenshot", "status": "passed", "steps": [{"name": "执行命令: take a screenshot", "status": "passed", "start": 1756784258037, "stop": 1756784283266}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f1457802-1c63-484e-807b-7676212a19d8-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6055659c-0f50-49e0-8c01-31dfd588103f-attachment.png", "type": "image/png"}], "start": 1756784283266, "stop": 1756784283509}], "start": 1756784258037, "stop": 1756784283509}, {"name": "验证文件存在", "status": "passed", "start": 1756784283509, "stop": 1756784283509}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c77eb388-52cb-4f98-bbf9-34599ef5aa13-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "05a306db-da07-4f86-88cd-49525dfdfc26-attachment.png", "type": "image/png"}], "start": 1756784283509, "stop": 1756784283709}], "attachments": [{"name": "stdout", "source": "cebbd5df-32dc-4e48-9cff-9b7e3750fbc3-attachment.txt", "type": "text/plain"}], "start": 1756784258037, "stop": 1756784283710, "uuid": "717dabef-997d-464c-a515-c06fc9cf993a", "historyId": "6bdbaaabff6497c5d3be4727f1a7cd8d", "testCaseId": "6bdbaaabff6497c5d3be4727f1a7cd8d", "fullName": "testcases.test_ella.component_coupling.test_take_a_screenshot.TestEllaTakeScreenshot#test_take_a_screenshot", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_take_a_screenshot"}, {"name": "subSuite", "value": "TestEllaTakeScreenshot"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_take_a_screenshot"}]}