{"name": "测试Enable Call Rejection返回正确的不支持响应", "status": "passed", "description": "验证Enable Call Rejection指令返回预期的不支持响应", "steps": [{"name": "执行命令: Enable Call Rejection", "status": "passed", "steps": [{"name": "执行命令: Enable Call Rejection", "status": "passed", "start": 1756797115859, "stop": 1756797139263}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2ad8f2ae-4380-48d0-bf92-4fc6ac0f1073-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7ea3b3a4-c381-4dea-94f4-6b85623d2b6a-attachment.png", "type": "image/png"}], "start": 1756797139263, "stop": 1756797139507}], "start": 1756797115859, "stop": 1756797139508}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756797139508, "stop": 1756797139508}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3b85bbbd-615a-440b-9619-1676d2c23bfc-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4ea28864-922f-4497-b7c4-a90e6edb3b48-attachment.png", "type": "image/png"}], "start": 1756797139508, "stop": 1756797139758}], "attachments": [{"name": "stdout", "source": "6ff9d1f8-f93a-4a3f-8542-34f6e9358a1e-attachment.txt", "type": "text/plain"}], "start": 1756797115859, "stop": 1756797139759, "uuid": "c3e718c7-36e4-421f-9236-13ad98fa5fab", "historyId": "ff8d76af98b9fdfaa206acbf87daa843", "testCaseId": "ff8d76af98b9fdfaa206acbf87daa843", "fullName": "testcases.test_ella.unsupported_commands.test_enable_call_rejection.TestEllaEnableCallRejection#test_enable_call_rejection", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_call_rejection"}, {"name": "subSuite", "value": "TestEllaEnableCallRejection"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_call_rejection"}]}