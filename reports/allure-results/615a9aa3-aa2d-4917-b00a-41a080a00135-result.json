{"name": "测试turn on smart reminder能正常执行", "status": "passed", "description": "turn on smart reminder", "steps": [{"name": "执行命令: turn on smart reminder", "status": "passed", "steps": [{"name": "执行命令: turn on smart reminder", "status": "passed", "start": 1756792895978, "stop": 1756792917286}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6f29df16-d162-4ea2-a5f8-bf37a729ef2d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "905c8ca8-a997-4835-b58c-5ef917267806-attachment.png", "type": "image/png"}], "start": 1756792917286, "stop": 1756792917497}], "start": 1756792895978, "stop": 1756792917497}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756792917497, "stop": 1756792917498}, {"name": "验证应用已打开", "status": "passed", "start": 1756792917498, "stop": 1756792917498}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5b5e71d3-4fa8-42f2-bba3-b4d92a8e9426-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "69e8e85f-584b-442d-9fa9-3ecd3ea1fb49-attachment.png", "type": "image/png"}], "start": 1756792917498, "stop": 1756792917689}], "attachments": [{"name": "stdout", "source": "a9738d78-42b6-4cd2-b4ed-e5780c7c22f9-attachment.txt", "type": "text/plain"}], "start": 1756792895978, "stop": 1756792917689, "uuid": "b44bc592-bc68-4872-b6b7-34eb714d0762", "historyId": "cf1bdc2b1d9b604939681e5b6ac6f506", "testCaseId": "cf1bdc2b1d9b604939681e5b6ac6f506", "fullName": "testcases.test_ella.system_coupling.test_turn_on_smart_reminder.TestEllaTurnSmartReminder#test_turn_on_smart_reminder", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_smart_reminder"}, {"name": "subSuite", "value": "TestEllaTurnSmartReminder"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_smart_reminder"}]}