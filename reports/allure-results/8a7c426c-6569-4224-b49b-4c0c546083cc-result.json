{"name": "测试minimum volume能正常执行", "status": "passed", "description": "minimum volume", "steps": [{"name": "执行命令: minimum volume", "status": "passed", "steps": [{"name": "执行命令: minimum volume", "status": "passed", "start": 1756790256219, "stop": 1756790277344}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e6d84b6d-08f2-4a22-8d21-b112d5a00a44-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0350a394-946a-432d-8036-4a5a54bad12f-attachment.png", "type": "image/png"}], "start": 1756790277344, "stop": 1756790277569}], "start": 1756790256219, "stop": 1756790277569}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756790277569, "stop": 1756790277571}, {"name": "验证应用已打开", "status": "passed", "start": 1756790277571, "stop": 1756790277571}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d1b4bfc1-cea7-4353-b8fa-70227a331d15-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b183931d-8730-41c1-a18d-adab38bb7e21-attachment.png", "type": "image/png"}], "start": 1756790277571, "stop": 1756790277775}], "attachments": [{"name": "stdout", "source": "742f7a58-8547-47b7-bc3a-f98cbc6c9ebd-attachment.txt", "type": "text/plain"}], "start": 1756790256219, "stop": 1756790277776, "uuid": "f098439b-5ac8-4108-83ad-bed61c284e40", "historyId": "7cd08c87d5de8ec73ac863e8a636c8aa", "testCaseId": "7cd08c87d5de8ec73ac863e8a636c8aa", "fullName": "testcases.test_ella.system_coupling.test_minimum_volume.TestEllaMinimumVolume#test_minimum_volume", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_minimum_volume"}, {"name": "subSuite", "value": "TestEllaMinimumVolume"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_minimum_volume"}]}