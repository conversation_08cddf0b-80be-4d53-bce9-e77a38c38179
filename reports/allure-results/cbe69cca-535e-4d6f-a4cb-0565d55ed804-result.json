{"name": "测试driving mode返回正确的不支持响应", "status": "passed", "description": "验证driving mode指令返回预期的不支持响应", "steps": [{"name": "执行命令: driving mode", "status": "passed", "steps": [{"name": "执行命令: driving mode", "status": "passed", "start": 1756796868504, "stop": 1756796891221}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5eeb09ee-d00f-4a23-a964-f77a7a808a3a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "02c5cb13-de45-485c-b706-646bfd42f34a-attachment.png", "type": "image/png"}], "start": 1756796891221, "stop": 1756796891450}], "start": 1756796868504, "stop": 1756796891450}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756796891450, "stop": 1756796891451}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "38659aa6-6789-43f2-bacd-6617f85e8fbd-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "650ce4bb-88e1-4491-84e0-2ae2231d7bff-attachment.png", "type": "image/png"}], "start": 1756796891451, "stop": 1756796891665}], "attachments": [{"name": "stdout", "source": "098cae5b-bae9-47a3-9ee4-4fe27d316692-attachment.txt", "type": "text/plain"}], "start": 1756796868504, "stop": 1756796891666, "uuid": "911c4738-50d2-458e-9eca-85c27d3087b0", "historyId": "3215de286c6ddd59d6e52a44f2a9967d", "testCaseId": "3215de286c6ddd59d6e52a44f2a9967d", "fullName": "testcases.test_ella.unsupported_commands.test_driving_mode.TestEllaDrivingMode#test_driving_mode", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_driving_mode"}, {"name": "subSuite", "value": "TestEllaDrivingMode"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_driving_mode"}]}