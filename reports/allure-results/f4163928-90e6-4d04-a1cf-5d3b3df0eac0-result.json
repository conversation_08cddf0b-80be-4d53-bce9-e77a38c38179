{"name": "测试set app auto rotate返回正确的不支持响应", "status": "passed", "description": "验证set app auto rotate指令返回预期的不支持响应", "steps": [{"name": "执行命令: set app auto rotate", "status": "passed", "steps": [{"name": "执行命令: set app auto rotate", "status": "passed", "start": 1756801386543, "stop": 1756801409437}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d206394b-9fae-414d-ade3-4fa124ded112-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4db1b35a-7983-4ed3-be72-6b130827bee8-attachment.png", "type": "image/png"}], "start": 1756801409437, "stop": 1756801409686}], "start": 1756801386543, "stop": 1756801409686}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756801409686, "stop": 1756801409687}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "94ad8caf-da8e-45a6-afef-9973a3500aad-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "79b44a78-674c-478b-8ee5-130620af010b-attachment.png", "type": "image/png"}], "start": 1756801409687, "stop": 1756801409963}], "attachments": [{"name": "stdout", "source": "fdf5a658-b58a-4423-ba59-f9f329806424-attachment.txt", "type": "text/plain"}], "start": 1756801386543, "stop": 1756801409963, "uuid": "732100ec-a04b-472e-8aa3-7deb0e5a507c", "historyId": "eda16efd838471b84f33f12ec91662c9", "testCaseId": "eda16efd838471b84f33f12ec91662c9", "fullName": "testcases.test_ella.unsupported_commands.test_set_app_auto_rotate.TestEllaSetAppAutoRotate#test_set_app_auto_rotate", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_app_auto_rotate"}, {"name": "subSuite", "value": "TestEllaSetAppAutoRotate"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_app_auto_rotate"}]}