{"name": "测试set ultra power saving返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['set ultra power saving', \"Sorry, I couldn't locate the setting option(s) for ultra power saving.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_set_ultra_power_saving.TestEllaSetUltraPowerSaving object at 0x0000018BC6671450>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6DBE470>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_set_ultra_power_saving(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Done']，实际响应: '['set ultra power saving', \"Sorry, I couldn't locate the setting option(s) for ultra power saving.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_set_ultra_power_saving.py:34: AssertionError"}, "description": "验证set ultra power saving指令返回预期的不支持响应", "steps": [{"name": "执行命令: set ultra power saving", "status": "passed", "steps": [{"name": "执行命令: set ultra power saving", "status": "passed", "start": 1756803119935, "stop": 1756803143413}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4c95bcbc-2778-46e6-a574-8c5a5ac3f965-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "025797db-9a69-40c5-861d-bbf9ca2e6382-attachment.png", "type": "image/png"}], "start": 1756803143413, "stop": 1756803143687}], "start": 1756803119935, "stop": 1756803143688}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Done']，实际响应: '['set ultra power saving', \"Sorry, I couldn't locate the setting option(s) for ultra power saving.\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_set_ultra_power_saving.py\", line 34, in test_set_ultra_power_saving\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756803143688, "stop": 1756803143694}], "attachments": [{"name": "stdout", "source": "213b8d2a-01a1-4681-a093-a3ec0514dc02-attachment.txt", "type": "text/plain"}], "start": 1756803119935, "stop": 1756803143695, "uuid": "67f7ba5a-6f3f-47f0-b566-16af1afd32c7", "historyId": "71c64c122f83e6fd138db517bbda4aef", "testCaseId": "71c64c122f83e6fd138db517bbda4aef", "fullName": "testcases.test_ella.unsupported_commands.test_set_ultra_power_saving.TestEllaSetUltraPowerSaving#test_set_ultra_power_saving", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_ultra_power_saving"}, {"name": "subSuite", "value": "TestEllaSetUltraPowerSaving"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_ultra_power_saving"}]}