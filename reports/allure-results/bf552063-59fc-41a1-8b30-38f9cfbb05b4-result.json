{"name": "测试it wears a red leather collar", "status": "passed", "description": "测试it wears a red leather collar指令", "steps": [{"name": "执行命令: it wears a red leather collar", "status": "passed", "steps": [{"name": "执行命令: it wears a red leather collar", "status": "passed", "start": 1756798861580, "stop": 1756798884813}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "14cb46ce-8de1-4833-b8e9-e4675eed77c4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "21212d08-5c6f-4900-ab44-2d5edee5f809-attachment.png", "type": "image/png"}], "start": 1756798884813, "stop": 1756798885059}], "start": 1756798861580, "stop": 1756798885059}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756798885059, "stop": 1756798885060}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1750a992-4146-4525-821c-9585e9251693-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "27cc604e-22ea-4c39-9962-1cf83b9897d7-attachment.png", "type": "image/png"}], "start": 1756798885060, "stop": 1756798885282}], "attachments": [{"name": "stdout", "source": "de8a34a9-83f5-41c2-b295-2c7c7146b489-attachment.txt", "type": "text/plain"}], "start": 1756798861580, "stop": 1756798885282, "uuid": "1bed68ed-e9e1-4bc4-80ae-0c39ad2d2645", "historyId": "8f73bb4e2ab622960daf9c39a4008510", "testCaseId": "8f73bb4e2ab622960daf9c39a4008510", "fullName": "testcases.test_ella.unsupported_commands.test_it_wears_a_red_leather_collar.TestEllaOpenPlayPoliticalNews#test_it_wears_a_red_leather_collar", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_it_wears_a_red_leather_collar"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_it_wears_a_red_leather_collar"}]}