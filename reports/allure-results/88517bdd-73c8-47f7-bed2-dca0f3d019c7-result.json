{"name": "测试it wears a yellow leather collar", "status": "passed", "description": "测试it wears a yellow leather collar指令", "steps": [{"name": "执行命令: it wears a yellow leather collar", "status": "passed", "steps": [{"name": "执行命令: it wears a yellow leather collar", "status": "passed", "start": 1756798902247, "stop": 1756798926645}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5190e773-3723-44e2-81bd-098e687c1ea3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6509841a-3ed7-4986-ac95-513605772928-attachment.png", "type": "image/png"}], "start": 1756798926645, "stop": 1756798926939}], "start": 1756798902247, "stop": 1756798926940}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756798926940, "stop": 1756798926941}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d3fad7bc-bdd3-4053-8f8a-cb760f99aaec-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "58cccd04-d22d-471e-a51a-ea1eb4448637-attachment.png", "type": "image/png"}], "start": 1756798926941, "stop": 1756798927259}], "attachments": [{"name": "stdout", "source": "08609218-8892-4640-bdd1-eb76f69e2b2e-attachment.txt", "type": "text/plain"}], "start": 1756798902247, "stop": 1756798927259, "uuid": "174eadfb-f149-497c-87a6-de5734194858", "historyId": "76a63bd8a63882a3a1ada32e63f1c971", "testCaseId": "76a63bd8a63882a3a1ada32e63f1c971", "fullName": "testcases.test_ella.unsupported_commands.test_it_wears_a_yellow_leather_collar.TestEllaOpenPlayPoliticalNews#test_it_wears_a_yellow_leather_collar", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_it_wears_a_yellow_leather_collar"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_it_wears_a_yellow_leather_collar"}]}