{"name": "测试turn on light theme能正常执行", "status": "passed", "description": "turn on light theme", "steps": [{"name": "执行命令: turn on dark theme", "status": "passed", "steps": [{"name": "执行命令: turn on dark theme", "status": "passed", "start": 1756792779187, "stop": 1756792801298}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ce31596e-ea3f-49b5-a550-c3e268b21919-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4b53afcd-2557-4a76-9bda-6696592a13d2-attachment.png", "type": "image/png"}], "start": 1756792801298, "stop": 1756792801501}], "start": 1756792779187, "stop": 1756792801501}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756792801501, "stop": 1756792801502}, {"name": "验证应用已打开", "status": "passed", "start": 1756792801502, "stop": 1756792801502}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6b9647fb-8b73-4b6b-8451-4d3a04549d7d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "04721339-bfce-4b5b-ae50-72ef19e32cef-attachment.png", "type": "image/png"}], "start": 1756792801502, "stop": 1756792801680}], "attachments": [{"name": "stdout", "source": "d1af45ac-c537-470a-9355-6c195ea722c1-attachment.txt", "type": "text/plain"}], "start": 1756792779187, "stop": 1756792801680, "uuid": "eec06b1f-2fbb-4444-a6e4-ef19b96f611e", "historyId": "ae86b8d534909e1e7c8c7adb4ee39e5c", "testCaseId": "ae86b8d534909e1e7c8c7adb4ee39e5c", "fullName": "testcases.test_ella.system_coupling.test_turn_on_light_theme.TestEllaTurnLightTheme#test_turn_on_daka_theme", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_light_theme"}, {"name": "subSuite", "value": "TestEllaTurnLightTheme"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_light_theme"}]}