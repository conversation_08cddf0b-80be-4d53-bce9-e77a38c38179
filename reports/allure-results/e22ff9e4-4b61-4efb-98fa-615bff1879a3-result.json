{"name": "测试Navigate to the address on the screen", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach']，实际响应: '['Navigate to the address on the screen', 'Where are you going?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_navigate_to_the_address_on_the_screen.TestEllaOpenPlayPoliticalNews object at 0x0000018BC657E7A0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC3E55660>\n\n    @allure.title(\"测试Navigate to the address on the screen\")\n    @allure.description(\"测试Navigate to the address on the screen指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_navigate_to_the_address_on_the_screen(self, ella_app):\n        \"\"\"测试Navigate to the address on the screen命令\"\"\"\n        command = \"Navigate to the address on the screen\"\n        # expected_text = ['Done']\n        expected_text = ['Sorry','Oops','out of my reach']\n        app_name = 'google_maps'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n    \n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach']，实际响应: '['Navigate to the address on the screen', 'Where are you going?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_navigate_to_the_address_on_the_screen.py:33: AssertionError"}, "description": "测试Navigate to the address on the screen指令", "steps": [{"name": "执行命令: Navigate to the address on the screen", "status": "passed", "steps": [{"name": "执行命令: Navigate to the address on the screen", "status": "passed", "start": 1756799690897, "stop": 1756799717399}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7fe8cb43-3d4b-4344-83c0-7684b12d4ad2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "40188e61-959a-4759-ae9d-7b3f58853fce-attachment.png", "type": "image/png"}], "start": 1756799717399, "stop": 1756799717615}], "start": 1756799690897, "stop": 1756799717616}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach']，实际响应: '['Navigate to the address on the screen', 'Where are you going?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_navigate_to_the_address_on_the_screen.py\", line 33, in test_navigate_to_the_address_on_the_screen\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756799717616, "stop": 1756799717618}], "attachments": [{"name": "stdout", "source": "37b73121-e732-4dd8-a8d1-7e02bd28b491-attachment.txt", "type": "text/plain"}], "start": 1756799690897, "stop": 1756799717619, "uuid": "0eaa8f13-5a3c-4897-a1d5-414d96652b71", "historyId": "c359e36718cf3ac8fd335c333a6470cf", "testCaseId": "c359e36718cf3ac8fd335c333a6470cf", "fullName": "testcases.test_ella.unsupported_commands.test_navigate_to_the_address_on_the_screen.TestEllaOpenPlayPoliticalNews#test_navigate_to_the_address_on_the_screen", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_navigate_to_the_address_on_the_screen"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_navigate_to_the_address_on_the_screen"}]}