{"name": "测试navigation to the lucky能正常执行", "status": "passed", "description": "navigation to the lucky", "steps": [{"name": "执行命令: navigation to the lucky", "status": "passed", "steps": [{"name": "执行命令: navigation to the lucky", "status": "passed", "start": 1756794584276, "stop": 1756794613952}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d93a8bb4-b2e8-4ba5-83dd-c69e5bb4eb72-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f2d28a63-e15a-485d-9719-0c3c19da298b-attachment.png", "type": "image/png"}], "start": 1756794613952, "stop": 1756794614185}], "start": 1756794584276, "stop": 1756794614186}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1756794614186, "stop": 1756794614187}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1fbfb952-d4d6-4b3d-a27a-9b956639cd61-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3b3ffead-f870-4ddf-958d-b2e7bc176078-attachment.png", "type": "image/png"}], "start": 1756794614187, "stop": 1756794614377}], "attachments": [{"name": "stdout", "source": "691ff1c7-**************-ca2be4840c48-attachment.txt", "type": "text/plain"}], "start": 1756794584276, "stop": 1756794614377, "uuid": "617e4288-df98-4ed8-bf76-515e66ff9086", "historyId": "75c56947a7b1061f7ec858fb20919b50", "testCaseId": "75c56947a7b1061f7ec858fb20919b50", "fullName": "testcases.test_ella.third_coupling.test_navigation_to_the_lucky.TestEllaNavigationToTheLucky#test_navigation_to_the_lucky", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_navigation_to_the_lucky"}, {"name": "subSuite", "value": "TestEllaNavigationToTheLucky"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_navigation_to_the_lucky"}]}