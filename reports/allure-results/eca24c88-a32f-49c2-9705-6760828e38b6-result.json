{"name": "测试order a takeaway能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['I need to download Yandex Eats', 'Generated by AI, for reference only']，实际响应: '['order a takeaway', \"O<PERSON>, out of my reach, ask me again after I've learned it\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.third_coupling.test_order_a_takeaway.TestEllaOrderATakeaway object at 0x0000018BC63513C0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC7F954B0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_order_a_takeaway(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含在期望中\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['I need to download Yandex Eats', 'Generated by AI, for reference only']，实际响应: '['order a takeaway', \"Oops, out of my reach, ask me again after I've learned it\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\third_coupling\\test_order_a_takeaway.py:35: AssertionError"}, "description": "order a takeaway", "steps": [{"name": "执行命令: order a takeaway", "status": "passed", "steps": [{"name": "执行命令: order a takeaway", "status": "passed", "start": 1756794761672, "stop": 1756794784685}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5038e559-cdef-4360-a5d3-4daa7a3a0893-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8627ead6-eb49-4b4b-a082-521676ed35be-attachment.png", "type": "image/png"}], "start": 1756794784685, "stop": 1756794784868}], "start": 1756794761672, "stop": 1756794784868}, {"name": "验证响应包含在期望中", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['I need to download Yandex Eats', 'Generated by AI, for reference only']，实际响应: '['order a takeaway', \"O<PERSON>, out of my reach, ask me again after I've learned it\", '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\third_coupling\\test_order_a_takeaway.py\", line 35, in test_order_a_takeaway\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756794784868, "stop": 1756794784870}], "attachments": [{"name": "stdout", "source": "80802fce-0807-49e3-851b-983173dfd54f-attachment.txt", "type": "text/plain"}], "start": 1756794761672, "stop": 1756794784871, "uuid": "3b666b67-c0b5-4140-907e-59e6f3f81475", "historyId": "de5ff490f92fc399976d91fe0edc371e", "testCaseId": "de5ff490f92fc399976d91fe0edc371e", "fullName": "testcases.test_ella.third_coupling.test_order_a_takeaway.TestEllaOrderATakeaway#test_order_a_takeaway", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_order_a_takeaway"}, {"name": "subSuite", "value": "TestEllaOrderATakeaway"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_order_a_takeaway"}]}