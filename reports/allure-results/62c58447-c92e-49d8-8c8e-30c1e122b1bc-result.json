{"name": "测试how to say hello in french能正常执行", "status": "passed", "description": "how to say hello in french", "steps": [{"name": "执行命令: how to say hello in french", "status": "passed", "steps": [{"name": "执行命令: how to say hello in french", "status": "passed", "start": 1756785489373, "stop": 1756785513151}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b32682ec-2877-4a6c-90f2-dbe3416cb875-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cb37fa74-a694-4e61-be12-5ff667fe6041-attachment.png", "type": "image/png"}], "start": 1756785513151, "stop": 1756785513348}], "start": 1756785489373, "stop": 1756785513348}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756785513348, "stop": 1756785513350}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ae6feda5-ee47-4b29-af94-64d17da913c9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f062588b-e59e-4456-9292-ceb94771b3e0-attachment.png", "type": "image/png"}], "start": 1756785513350, "stop": 1756785513554}], "attachments": [{"name": "stdout", "source": "be81b9f1-b322-4332-aaf9-3a5be7b5762b-attachment.txt", "type": "text/plain"}], "start": 1756785489373, "stop": 1756785513555, "uuid": "f231c209-698b-4d43-9c5f-ef65bd316384", "historyId": "cc44ad4097a589726631a345e0cd01ad", "testCaseId": "cc44ad4097a589726631a345e0cd01ad", "fullName": "testcases.test_ella.dialogue.test_how_to_say_hello_in_french.TestEllaHowSayHelloFrench#test_how_to_say_hello_in_french", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_to_say_hello_in_french"}, {"name": "subSuite", "value": "TestEllaHowSayHelloFrench"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_to_say_hello_in_french"}]}