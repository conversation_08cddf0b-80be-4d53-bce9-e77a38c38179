{"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Multiple settings options found']，实际响应: '['jump to auto rotate screen settings', 'Which feature should I turn on?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_jump_to_auto_rotate_screen_settings.TestEllaJumpAutoRotateScreenSettings object at 0x0000018BC6527070>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC3E54520>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_jump_to_auto_rotate_screen_settings(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['Multiple settings options found']，实际响应: '['jump to auto rotate screen settings', 'Which feature should I turn on?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_jump_to_auto_rotate_screen_settings.py:34: AssertionError"}, "description": "验证jump to auto rotate screen settings指令返回预期的不支持响应", "steps": [{"name": "执行命令: jump to auto rotate screen settings", "status": "passed", "steps": [{"name": "执行命令: jump to auto rotate screen settings", "status": "passed", "start": 1756799032913, "stop": 1756799055842}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a426020f-05a8-43a7-b7a6-b2c2f0f78a0f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9a684e20-1401-4c65-823b-f76bde766f4b-attachment.png", "type": "image/png"}], "start": 1756799055842, "stop": 1756799056131}], "start": 1756799032913, "stop": 1756799056131}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['Multiple settings options found']，实际响应: '['jump to auto rotate screen settings', 'Which feature should I turn on?', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_jump_to_auto_rotate_screen_settings.py\", line 34, in test_jump_to_auto_rotate_screen_settings\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756799056131, "stop": 1756799056134}], "attachments": [{"name": "stdout", "source": "20d290ee-1ebc-43a7-87b4-4ad5f8eb4544-attachment.txt", "type": "text/plain"}], "start": 1756799032913, "stop": 1756799056135, "uuid": "650f073b-46b8-46ca-aa42-44fbd19ed8a0", "historyId": "ab2195315637668cad08b0606ef7ff17", "testCaseId": "ab2195315637668cad08b0606ef7ff17", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_auto_rotate_screen_settings.TestEllaJumpAutoRotateScreenSettings#test_jump_to_auto_rotate_screen_settings", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_auto_rotate_screen_settings"}, {"name": "subSuite", "value": "TestEllaJumpAutoRotateScreenSettings"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_auto_rotate_screen_settings"}]}