{"name": "测试set my fonts返回正确的不支持响应", "status": "passed", "description": "验证set my fonts指令返回预期的不支持响应", "steps": [{"name": "执行命令: set my fonts", "status": "passed", "steps": [{"name": "执行命令: set my fonts", "status": "passed", "start": 1756802171566, "stop": 1756802201203}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b67b5a61-a1ef-4d0c-86b0-b10d6f3c568f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0922ac16-5e7f-4993-9a48-8e5c4419d3db-attachment.png", "type": "image/png"}], "start": 1756802201203, "stop": 1756802201485}], "start": 1756802171564, "stop": 1756802201485}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756802201485, "stop": 1756802201486}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b3be209d-44a4-43ab-80b3-4e0889718023-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "19d85935-d6a3-496d-b508-d317d2bcf1fc-attachment.png", "type": "image/png"}], "start": 1756802201486, "stop": 1756802201749}], "attachments": [{"name": "stdout", "source": "1bce53d4-ac71-4a43-aff5-63f79244b6e0-attachment.txt", "type": "text/plain"}], "start": 1756802171564, "stop": 1756802201750, "uuid": "217ad17f-a120-4695-b7cc-13a9ee48338a", "historyId": "7d3b4e67344145885187c529ee88a9aa", "testCaseId": "7d3b4e67344145885187c529ee88a9aa", "fullName": "testcases.test_ella.unsupported_commands.test_set_my_fonts.TestEllaSetMyFonts#test_set_my_fonts", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_my_fonts"}, {"name": "subSuite", "value": "TestEllaSetMyFonts"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_my_fonts"}]}