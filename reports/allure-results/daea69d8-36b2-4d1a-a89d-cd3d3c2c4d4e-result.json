{"name": "测试change your voice能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['OK, the voice is switched. You can also select other voices.']，实际响应: '['change your voice', 'The following images are generated for you.', '', '', '', '', '', '', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_change_your_voice.TestEllaChangeYourVoice object at 0x0000018BC640CF40>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6DF3280>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_change_your_voice(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['OK, the voice is switched. You can also select other voices.']，实际响应: '['change your voice', 'The following images are generated for you.', '', '', '', '', '', '', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_change_your_voice.py:33: AssertionError"}, "description": "change your voice", "steps": [{"name": "执行命令: change your voice", "status": "passed", "steps": [{"name": "执行命令: change your voice", "status": "passed", "start": 1756795552403, "stop": 1756795580077}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9fc18c45-0a49-446e-adcd-ecedc3c4e424-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "35644910-7b5e-4c0f-b3c9-b4f68a70c6ca-attachment.png", "type": "image/png"}], "start": 1756795580077, "stop": 1756795580293}], "start": 1756795552403, "stop": 1756795580293}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应文本应包含['OK, the voice is switched. You can also select other voices.']，实际响应: '['change your voice', 'The following images are generated for you.', '', '', '', '', '', '', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\unsupported_commands\\test_change_your_voice.py\", line 33, in test_change_your_voice\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n"}, "start": 1756795580293, "stop": 1756795580295}], "attachments": [{"name": "stdout", "source": "5b9e17d8-b064-4ff4-b686-9f256d8fc724-attachment.txt", "type": "text/plain"}], "start": 1756795552403, "stop": 1756795580296, "uuid": "c6dad7c3-3f3a-4325-b87d-ad931153a33a", "historyId": "70c4e0c16f5cb3629f87876768739a8d", "testCaseId": "70c4e0c16f5cb3629f87876768739a8d", "fullName": "testcases.test_ella.unsupported_commands.test_change_your_voice.TestEllaChangeYourVoice#test_change_your_voice", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_change_your_voice"}, {"name": "subSuite", "value": "TestEllaChangeYourVoice"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_change_your_voice"}]}