{"name": "测试turn on the screen record能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=False, 最终=False, 响应='['turn on the screen record', 'Screen recording started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False", "trace": "self = <testcases.test_ella.system_coupling.test_turn_on_the_screen_record.TestEllaTurnScreenRecord object at 0x0000018BC6254FD0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000018BC6DF38E0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_turn_on_the_screen_record(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证已打开\"):\n>           assert  final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=False, 最终=False, 响应='['turn on the screen record', 'Screen recording started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_turn_on_the_screen_record.py:36: AssertionError"}, "description": "turn on the screen record", "steps": [{"name": "执行命令: turn on the screen record", "status": "passed", "steps": [{"name": "执行命令: turn on the screen record", "status": "passed", "start": 1756793069512, "stop": 1756793096427}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b14bdcfe-accd-413d-9a2c-3baff2ce8862-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "38bb542c-82bb-4655-8feb-968a238f228c-attachment.png", "type": "image/png"}], "start": 1756793096427, "stop": 1756793096638}], "start": 1756793069512, "stop": 1756793096639}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756793096639, "stop": 1756793096640}, {"name": "验证已打开", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=False, 最终=False, 响应='['turn on the screen record', 'Screen recording started.', '', '', '', '', '', 'I can answer your questions, summarize content, and provide creative inspiration.', '', '', '']'\nassert False\n", "trace": "  File \"D:\\app_test\\testcases\\test_ella\\system_coupling\\test_turn_on_the_screen_record.py\", line 36, in test_turn_on_the_screen_record\n    assert  final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1756793096640, "stop": 1756793096640}], "attachments": [{"name": "stdout", "source": "8c9a2566-8fc7-4978-8a91-e50f2baa8735-attachment.txt", "type": "text/plain"}], "start": 1756793069512, "stop": 1756793096641, "uuid": "3c37ad73-cbb7-40d9-8ca0-4884627ca1ef", "historyId": "5cf50058091f34fd1ed89d0b8f717355", "testCaseId": "5cf50058091f34fd1ed89d0b8f717355", "fullName": "testcases.test_ella.system_coupling.test_turn_on_the_screen_record.TestEllaTurnScreenRecord#test_turn_on_the_screen_record", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_the_screen_record"}, {"name": "subSuite", "value": "TestEllaTurnScreenRecord"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_the_screen_record"}]}