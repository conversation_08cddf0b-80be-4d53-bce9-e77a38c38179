{"name": "测试wake me up at 7:00 am tomorrow能正常执行", "status": "passed", "description": "wake me up at 7:00 am tomorrow", "steps": [{"name": "执行命令: wake me up at 7:00 am tomorrow", "status": "passed", "steps": [{"name": "执行命令: wake me up at 7:00 am tomorrow", "status": "passed", "start": 1756793347498, "stop": 1756793367627}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d7b70718-fd03-432f-9a4f-2b5b6be90ae9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d79007c1-626f-4ea5-a685-6fe6c1efb5c9-attachment.png", "type": "image/png"}], "start": 1756793367627, "stop": 1756793367827}], "start": 1756793347498, "stop": 1756793367828}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756793367828, "stop": 1756793367829}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e2c03e53-cbd2-42db-b14c-af26343d9131-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "216c2b81-e6ca-41e5-98e5-ec252444d555-attachment.png", "type": "image/png"}], "start": 1756793367829, "stop": 1756793368013}], "attachments": [{"name": "stdout", "source": "bea754d9-2e69-46f5-aced-3f9a3735af4b-attachment.txt", "type": "text/plain"}], "start": 1756793347498, "stop": 1756793368013, "uuid": "0d680fef-27fa-4702-b2b2-d9e107ddc819", "historyId": "28f9087c186df37701fba71f366c084e", "testCaseId": "28f9087c186df37701fba71f366c084e", "fullName": "testcases.test_ella.system_coupling.test_wake_me_up_at_am_tomorrow.TestEllaWakeMeUpAmTomorrow#test_wake_me_up_at_am_tomorrow", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_wake_me_up_at_am_tomorrow"}, {"name": "subSuite", "value": "TestEllaWakeMeUpAmTomorrow"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_wake_me_up_at_am_tomorrow"}]}