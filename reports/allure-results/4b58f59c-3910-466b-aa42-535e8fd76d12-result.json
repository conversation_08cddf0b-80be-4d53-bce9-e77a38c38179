{"name": "测试search my gallery for food pictures能正常执行", "status": "passed", "description": "search my gallery for food pictures", "steps": [{"name": "执行命令: search my gallery for food pictures", "status": "passed", "steps": [{"name": "执行命令: search my gallery for food pictures", "status": "passed", "start": 1756786576007, "stop": 1756786601899}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e5d4b1d8-7644-42e7-8872-0b4a823ea2e4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a305052d-4a11-4027-b052-10a5e2c46e35-attachment.png", "type": "image/png"}], "start": 1756786601899, "stop": 1756786602111}], "start": 1756786576007, "stop": 1756786602112}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756786602112, "stop": 1756786602112}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9f8e311b-f5ba-4bd6-9079-17f308ba7649-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9e5aa8a0-4d37-42c7-af89-1c59b8839ae6-attachment.png", "type": "image/png"}], "start": 1756786602113, "stop": 1756786602330}], "attachments": [{"name": "stdout", "source": "6c470b7e-4141-454d-aa5c-2e8724cf5169-attachment.txt", "type": "text/plain"}], "start": 1756786576006, "stop": 1756786602331, "uuid": "194a532d-a0f4-4a62-9361-f754d0a19d44", "historyId": "eda6bef994b1b0ef78f60f433fb1d4f8", "testCaseId": "eda6bef994b1b0ef78f60f433fb1d4f8", "fullName": "testcases.test_ella.dialogue.test_search_my_gallery_for_food_pictures.TestEllaHelloHello#test_search_my_gallery_for_food_pictures", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_search_my_gallery_for_food_pictures"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_search_my_gallery_for_food_pictures"}]}