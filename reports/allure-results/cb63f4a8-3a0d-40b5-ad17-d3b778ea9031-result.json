{"name": "测试help me write an thanks email能正常执行", "status": "passed", "description": "help me write an thanks email", "steps": [{"name": "执行命令: help me write an thanks email", "status": "passed", "steps": [{"name": "执行命令: help me write an thanks email", "status": "passed", "start": 1756798417454, "stop": 1756798444590}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dca1d81e-d082-46fa-86d6-a34ba7d4763c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cd5a2631-52df-4732-b64b-efaef813a9bf-attachment.png", "type": "image/png"}], "start": 1756798444590, "stop": 1756798444855}], "start": 1756798417454, "stop": 1756798444856}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756798444856, "stop": 1756798444857}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7a9d1957-f3af-4445-9f88-0595a477c763-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "*************-4e59-96f7-2cf08eee1fb7-attachment.png", "type": "image/png"}], "start": 1756798444857, "stop": 1756798445073}], "attachments": [{"name": "stdout", "source": "52c930dc-0f55-4ecc-a62d-854f1f3052bb-attachment.txt", "type": "text/plain"}], "start": 1756798417454, "stop": 1756798445073, "uuid": "394c7c0e-696e-4413-9f9b-78f1aced7ca7", "historyId": "306cbf11cdbcb045eb3c3c716515b1d6", "testCaseId": "306cbf11cdbcb045eb3c3c716515b1d6", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_write_an_thanks_email.TestEllaHelpMeWriteAnThanksEmail#test_help_me_write_an_thanks_email", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella技能"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_write_an_thanks_email"}, {"name": "subSuite", "value": "TestEllaHelpMeWriteAnThanksEmail"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_write_an_thanks_email"}]}