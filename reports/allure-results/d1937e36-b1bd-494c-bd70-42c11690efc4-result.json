{"name": "测试jump to battery and power saving返回正确的不支持响应", "status": "passed", "description": "验证jump to battery and power saving指令返回预期的不支持响应", "steps": [{"name": "执行命令: jump to battery and power saving", "status": "passed", "steps": [{"name": "执行命令: jump to battery and power saving", "status": "passed", "start": 1756799073396, "stop": 1756799102644}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "40bba4d8-1075-4e6a-84ff-75da551ce3ad-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "aa3a9239-3787-451f-881b-25ec265c0758-attachment.png", "type": "image/png"}], "start": 1756799102644, "stop": 1756799102881}], "start": 1756799073395, "stop": 1756799102882}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1756799102882, "stop": 1756799102884}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "64f474a3-884e-4166-9dc1-f342effaa296-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c98c3cbf-af19-40b4-8cc1-5bb943ed6647-attachment.png", "type": "image/png"}], "start": 1756799102884, "stop": 1756799103119}], "attachments": [{"name": "stdout", "source": "a1dd7981-5e98-419a-afcd-898caf3265db-attachment.txt", "type": "text/plain"}], "start": 1756799073395, "stop": 1756799103120, "uuid": "7655167c-89bf-481c-8e0b-d974205dba50", "historyId": "436193bc4e8c44d21b6520da0589f88d", "testCaseId": "436193bc4e8c44d21b6520da0589f88d", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_battery_and_power_saving.TestEllaJumpBatteryPowerSaving#test_jump_to_battery_and_power_saving", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_battery_and_power_saving"}, {"name": "subSuite", "value": "TestEllaJumpBatteryPowerSaving"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_battery_and_power_saving"}]}