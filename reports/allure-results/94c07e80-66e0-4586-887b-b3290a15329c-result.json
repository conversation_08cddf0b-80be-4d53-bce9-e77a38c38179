{"name": "测试next song能正常执行", "status": "passed", "description": "next song", "steps": [{"name": "执行命令: next song", "status": "passed", "steps": [{"name": "执行命令: next song", "status": "passed", "start": 1756786001256, "stop": 1756786023621}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "37299c96-7673-44fd-b0a3-c7740464d9ac-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "dd8b7c3d-425a-4988-b095-2bd6fa23c14f-attachment.png", "type": "image/png"}], "start": 1756786023621, "stop": 1756786023824}], "start": 1756786001256, "stop": 1756786023825}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756786023825, "stop": 1756786023826}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "efb09359-91f5-41b8-ac9d-1aff1ca7362c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "97df3ef5-3131-48be-a14f-eafa5cee6a81-attachment.png", "type": "image/png"}], "start": 1756786023826, "stop": 1756786024035}], "attachments": [{"name": "stdout", "source": "5f772cab-402c-44fc-9590-03ebba7b31d4-attachment.txt", "type": "text/plain"}], "start": 1756786001256, "stop": 1756786024035, "uuid": "4a6d7912-7de3-4a9e-8cc0-eb39d5401433", "historyId": "1d8131ec3deb65d953f2f16c259f261b", "testCaseId": "1d8131ec3deb65d953f2f16c259f261b", "fullName": "testcases.test_ella.dialogue.test_next_song.TestEllaHowIsWeatherToday#test_next_song", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_next_song"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_next_song"}]}