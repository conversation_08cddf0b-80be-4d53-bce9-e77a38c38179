{"name": "测试search the address in the image能正常执行", "status": "passed", "description": "search the address in the image", "steps": [{"name": "执行命令: search the address in the image", "status": "passed", "steps": [{"name": "执行命令: search the address in the image", "status": "passed", "start": 1756801210109, "stop": 1756801234727}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b20f6fcb-2546-48ec-bd30-b652564d1eb6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "aa2760d0-d3aa-46e1-9870-993726a07c4d-attachment.png", "type": "image/png"}], "start": 1756801234728, "stop": 1756801235007}], "start": 1756801210108, "stop": 1756801235008}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1756801235009, "stop": 1756801235010}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f7cbdc0b-cd18-4c19-90f8-8f211245204d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2455c9a1-6b98-4b59-a5a2-9f3e992ab8ff-attachment.png", "type": "image/png"}], "start": 1756801235010, "stop": 1756801235270}], "attachments": [{"name": "stdout", "source": "f43551a4-5b64-4fa8-b0e5-6fba46ec6acf-attachment.txt", "type": "text/plain"}], "start": 1756801210108, "stop": 1756801235270, "uuid": "ea9c9524-c867-4595-bf9f-ce7c160db66f", "historyId": "c50847e2010bac3c5a9bb7ff0b690fb6", "testCaseId": "c50847e2010bac3c5a9bb7ff0b690fb6", "fullName": "testcases.test_ella.unsupported_commands.test_search_the_address_in_the_image.TestEllaSearchAddressImage#test_search_the_address_in_the_image", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_search_the_address_in_the_image"}, {"name": "subSuite", "value": "TestEllaSearchAddressImage"}, {"name": "host", "value": "shcybuchangyi1-pc"}, {"name": "thread", "value": "33032-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_search_the_address_in_the_image"}]}